Parameters:
  GitHubOrg:
    Description: Name of GitHub organization/user (case sensitive)
    Type: String
  GitHubBranch:
    Description: Name of GitHub branch (case sensitive)
    Type: String
  RepositoryName:
    Description: Name of GitHub repository (case sensitive)
    Type: String
  OIDCProviderArn:
    Description: Arn for the GitHub OIDC Provider.
    Type: String
  OIDCAudience:
    Description: Audience supplied to configure-aws-credentials.
    Type: String
  Environment:
    Type: String
  AccountID:
    Type: String
  Componentname:
    Type: String
  Partname:
    Type: String

Conditions:
  CreateOIDCProvider: !Equals [!Ref OIDCProviderArn, '']
Resources:
  GithubOidc:
    Type: AWS::IAM::OIDCProvider
    Condition: CreateOIDCProvider
    Properties:
      Url: https://token.actions.githubusercontent.com
      ClientIdList:
        - sts.amazonaws.com
      ThumbprintList:
        - 6938fd4d98bab03faadb97b34396831e3780aea1
  Role:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Join ['', [!Ref Environment, '-', !Ref Componentname, '-', !Ref Partname, '-github-actions-role']]
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Action: sts:AssumeRoleWithWebIdentity
            Principal:
              Federated: !If
                - CreateOIDCProvider
                - !Ref GithubOidc
                - !Ref OIDCProviderArn
            Condition:
              StringLike:
                token.actions.githubusercontent.com:aud: !Ref OIDCAudience
                token.actions.githubusercontent.com:sub: !Sub repo:${GitHubOrg}/${RepositoryName}:${GitHubBranch}
      Policies:
        - PolicyName:
            !Join ['', [!Ref Environment, '-', !Ref Componentname, '-', !Ref Partname, '-github-actions-inline-policy']]
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action:
                  - 'ssm:GetParameter'
                  - 's3:PutObject'
                  - 's3:GetObject'
                  - 'lambda:InvokeFunction'
                  - 'events:PutEvents'
                  - 'cloudformation:DescribeStacks'
                  - 's3:ListAllMyBuckets'
                  - 'cloudformation:List*'
                  - 'events:List*'
                  - 'dynamodb:List*'
                  - 'codedeploy:*Get*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - dynamodb:Query
                  - dynamodb:PutItem
                  - dynamodb:GetItem
                  - dynamodb:UpdateItem
                Resource:
                  - !Sub 'arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*'
                  - !Sub 'arn:aws:dynamodb:*:*:table/*ams-engine-DomicileLookup'
              - Effect: Allow
                Action:
                  - 'sqs:sendmessage'
                Resource:
                  - !Sub 'arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:*crms*'
              - Effect: Allow
                Action:
                  - 's3:PutObject'
                  - 's3:GetObject'
                  - 's3:ListAllMyBuckets'
                  - 's3:ListBucket'
                  - 's3:GetBucketLocation'
                  - 's3:GetObjectVersion'
                  - 's3:DeleteObject'
                Resource:
                  - !Sub 'arn:aws:s3:::*cdk*'
              - Effect: Allow
                Action:
                  - "secretsmanager:Get*" 
                  - "ssm:Get*"      
                Resource:
                  - "*"
              - Effect: Allow
                Action:
                  - 'ssm:GetParameter'
                  - 'ssm:PutParameter'
                  - 'ssm:DeleteParameter'
                  - 'ssm:AddTagsToResource'
                  - 'ssm:RemoveTagsFromResource'
                Resource:
                  - !Sub 'arn:aws:ssm:ap-southeast-2:************:parameter/cdk-bootstrap/*'
              - Effect: Allow
                Action:
                  - 'lambda:Get*'
                Resource:
                  - !Sub "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:*dbs-api*"
                  - !Sub "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:*mp-api*"
              - Effect: Allow
                Action:
                  - 'iam:CreateRole'
                  - 'iam:CreatePolicy'
                  - 'iam:UpdateRole'
                  - 'iam:UpdateRoleDescription'
                  - 'iam:AttachRolePolicy'
                  - 'iam:CreatePolicyVersion'
                  - 'iam:DetachRolePolicy'
                  - 'iam:DeleteRole'
                  - 'iam:DeleteRolePolicy'
                  - 'iam:DeletePolicy'
                  - 'iam:DeletePolicyVersion'
                  - 'iam:TagRole'
                  - 'iam:TagPolicy'
                  - 'iam:ListRolePolicies'
                  - 'iam:ListAttachedRolePolicies'
                  - 'iam:ListPolicies'
                  - 'iam:ListRoles'
                  - 'iam:PutRolePolicy'
                  - 'iam:GetRole'
                  - 'iam:GetPolicy'
                  - 'iam:GetRolePolicy'
                  - 'iam:PassRole'
                Resource:
                  - !Sub 'arn:aws:iam::************:role/cdk*'
                  - !Sub "arn:aws:iam::${AWS::AccountId}:role/*dbs-api*"
                  - !Sub "arn:aws:iam::${AWS::AccountId}:role/*mp-api*"
                  - !Sub "arn:aws:iam::${AWS::AccountId}:policy/*dbs-api*"
                  - !Sub "arn:aws:iam::${AWS::AccountId}:policy/*mp-api*"
              - Effect: Allow
                Action:
                  - 'cloudformation:CreateStack'
                  - 'cloudformation:CreateChangeSet'
                  - 'cloudformation:DescribeChangeSet'
                  - 'cloudformation:DescribeStacks'
                  - 'cloudformation:ExecuteChangeSet'
                  - 'cloudformation:DescribeStackEvents'
                  - 'cloudformation:ListStackResources'
                  - 'cloudformation:DescribeStackResources'
                  - 'cloudformation:DeleteChangeSet'
                  - 'cloudformation:DeleteStack'
                  - 'cloudformation:Get*'
                  - 'cloudformation:List*'
                  - 'cloudformation:Describe*'
                Resource:
                  - !Sub "arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/*dbs-api*"
                  - !Sub "arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/*mp-api*"
                  - !Sub 'arn:aws:cloudformation:ap-southeast-2:************:stack/CDKToolkit*'
                  - !Sub 'arn:aws:cloudformation:ap-southeast-2:************:stack/bff-settlement-api*'
              - Effect: Allow
                Action:
                  - 'codebuild:StartBuild'
                  - 'codebuild:BatchGetBuilds'
                Resource:
                  - !Sub 'arn:aws:codebuild:${AWS::Region}:${AWS::AccountId}:project/dev-bff-settlement-api'
              - Effect: Allow
                Action:
                  - 'logs:CreateLogGroup'
                  - 'logs:CreateLogStream'
                  - 'logs:DeleteLogGroup'
                  - 'logs:DeleteLogStream'
                  - 'logs:TagResource'
                  - 'logs:UntagResource'
                  - 'logs:GetLogEvents'
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:*bff-settlement-api*'

  DbsApiRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName:
        !Join ['', [!Ref Environment, '-', !Ref Componentname, '-', !Ref Partname, '-dbs-api-github-actions-role']]
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Action: sts:AssumeRoleWithWebIdentity
            Principal:
              Federated: !If
                - CreateOIDCProvider
                - !Ref GithubOidc
                - !Ref OIDCProviderArn
            Condition:
              StringLike:
                token.actions.githubusercontent.com:aud: !Ref OIDCAudience
                token.actions.githubusercontent.com:sub: !Sub repo:${GitHubOrg}/${RepositoryName}:${GitHubBranch}
      Policies:
        - PolicyName:
            !Join [
              '',
              [!Ref Environment, '-', !Ref Componentname, '-', !Ref Partname, '-dbs-api-github-actions-inline-policy'],
            ]
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action:
                  - 'ssm:PutParameter'
                  - 'ssm:DeleteParameter'
                  - 'ssm:AddTagsToResource'
                  - 'ssm:RemoveTagsFromResource'
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter*dbs*'
                  - !Sub 'arn:aws:ssm:ap-southeast-2:************:parameter/cdk-bootstrap/*'
              - Effect: Allow
                Action:
                  - 'ssm:Get*'
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter*'
              - Effect: Allow
                Action:
                  - 'secretsmanager:CreateSecret'
                  - 'secretsmanager:List*'
                  - 'secretsmanager:Describe*'
                  - 'secretsmanager:Get*'
                  - 'secretsmanager:DeleteSecret'
                  - 'secretsmanager:PutSecretValue'
                  - 'secretsmanager:UpdateSecret'
                  - 'secretsmanager:TagResource'
                Resource:
                  - !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:*dbs*'
              - Effect: Allow
                Action:
                  - 'lambda:Get*'
                  - 'lambda:List*'
                Resource:
                  - 'arn:aws:lambda:ap-southeast-2:************:layer:AWS-Parameters-and-Secrets-Lambda-Extension:*'
              - Effect: Allow
                Action:
                  - 'iam:CreateRole'
                  - 'iam:CreatePolicy'
                  - 'iam:UpdateRole'
                  - 'iam:UpdateRoleDescription'
                  - 'iam:AttachRolePolicy'
                  - 'iam:CreatePolicyVersion'
                  - 'iam:DeleteRole'
                  - 'iam:DeleteRolePolicy'
                  - 'iam:DeletePolicy'
                  - 'iam:DeletePolicyVersion'
                  - 'iam:DetachRolePolicy'
                  - 'iam:TagRole'
                  - 'iam:TagPolicy'
                  - 'iam:ListRolePolicies'
                  - 'iam:ListPolicies'
                  - 'iam:ListRoles'
                  - 'iam:PutRolePolicy'
                  - 'iam:GetRole'
                  - 'iam:GetPolicy'
                  - 'iam:GetRolePolicy'
                  - 'iam:PassRole'
                Resource:
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:role/*dbs-api*'
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:policy/*dbs-api*'
                  - !Sub 'arn:aws:iam::************:role/cdk*'
              - Effect: Allow
                Action:
                  - 'cloudformation:CreateStack'
                  - 'cloudformation:CreateChangeSet'
                  - 'cloudformation:DescribeChangeSet'
                  - 'cloudformation:DescribeStacks'
                  - 'cloudformation:ExecuteChangeSet'
                  - 'cloudformation:DescribeStackEvents'
                  - 'cloudformation:ListStackResources'
                  - 'cloudformation:DescribeStackResources'
                  - 'cloudformation:DeleteChangeSet'
                  - 'cloudformation:DeleteStack'
                  - 'cloudformation:Get*'
                  - 'cloudformation:List*'
                  - 'cloudformation:Describe*'
                Resource:
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/*dbs*/*'
                  - !Sub 'arn:aws:cloudformation:ap-southeast-2:************:stack/CDKToolkit*'
              - Effect: Allow
                Action:
                  - 's3:PutObject'
                  - 's3:GetObject'
                  - 's3:ListAllMyBuckets'
                  - 's3:ListBucket'
                  - 's3:GetBucketLocation'
                  - 's3:GetObjectVersion'
                  - 's3:DeleteObject'
                Resource:
                  - !Sub 'arn:aws:s3:::*dbs-api*'
                  - !Sub 'arn:aws:s3:::*cdk*'
              - Effect: Allow
                Action:
                  - 'ec2:Describe*'
                  - 'lambda:CreateEventSourceMapping'
                  - 'lambda:GetEventSourceMapping'
                  - 'lambda:DeleteEventSourceMapping'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'lambda:Get*'
                  - 'lambda:List*'
                  - 'lambda:CreateFunction*'
                  - 'lambda:CreateAlias*'
                  - 'lambda:CreateEventSourceMapping'
                  - 'lambda:AddPermission'
                  - 'lambda:DeleteAlias'
                  - 'lambda:DeleteFunction'
                  - 'lambda:DeleteEventSourceMapping'
                  - 'lambda:RemovePermission'
                  - 'lambda:TagResource'
                  - 'lambda:UpdateAlias'
                  - 'lambda:UntagResource'
                  - 'lambda:UpdateEventSourceMapping'
                  - 'lambda:UpdateFunctionCode'
                  - 'lambda:UpdateFunctionConfiguration'
                  - 'lambda:InvokeFunction'
                  - 'lambda:PutFunctionConcurrency'
                  - 'lambda:PublishVersion'
                Resource:
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:*-dbs-api*'
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:event-source-mapping:*'
              - Effect: Allow
                Action:
                  - 'logs:CreateLogGroup'
                  - 'logs:CreateLogStream'
                  - 'logs:DeleteLogGroup'
                  - 'logs:DeleteLogStream'
                  - 'logs:TagResource'
                  - 'logs:UntagResource'
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:*'
              - Effect: Allow
                Action:
                  - 'events:Describe*'
                  - 'events:PutRule'
                  - 'events:RemoveTargets'
                  - 'events:DeleteRule'
                  - 'events:PutTargets'
                  - 'events:TagResource'
                  - 'events:DescribeEventBus'
                  - 'events:CreateEventBus'
                  - 'events:DeleteEventBus'
                Resource:
                  - !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:rule/*dbs*'
                  - !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/*dbs*'
              - Effect: Allow
                Action:
                  - 'cloudformation:ValidateTemplate'
                  - 'cloudformation:GetTemplate'
                  - 'cloudformation:List*'
                  - 'cloudformation:Describe*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'codepipeline:StartPipelineExecution'
                Resource:
                  - !Sub 'arn:aws:codepipeline:${AWS::Region}:${AWS::AccountId}:*'
              - Effect: Allow
                Action:
                  - 'sqs:createqueue'
                Resource:
                  - !Sub 'arn:aws:codepipeline:${AWS::Region}:${AWS::AccountId}:st*-dbs-stl-mock-cqrs-*'
              - Effect: Allow
                Action:
                  - 'appsync:CreateGraphqlApi'
                  - 'appsync:CreateDomainName'
                  - 'appsync:TagResource'
                  - 'appsync:GetGraphqlApi'
                  - 'appsync:DeleteGraphqlApi'
                  - 'appsync:CreateApiKey'
                  - 'appsync:DeleteApiKey'
                  - 'appsync:StartSchemaCreation'
                  - 'appsync:GetSchemaCreationStatus'
                  - 'appsync:CreateDataSource'
                  - 'appsync:UpdateDataSource'
                  - 'appsync:DeleteDataSource'
                  - 'appsync:CreateResolver'
                  - 'appsync:UpdateResolver'
                  - 'appsync:DeleteResolver'
                Resource:
                  - !Sub 'arn:aws:appsync:${AWS::Region}:${AWS::AccountId}:*'
              - Effect: Allow
                Action:
                  - 'sqs:CreateQueue'
                  - 'sqs:DeleteQueue'
                  - 'sqs:TagQueue'
                  - 'sqs:UntagQueue'
                  - 'sqs:AddPermission'
                  - 'sqs:SetQueueAttributes'
                  - 'sqs:Get*'
                  - 'sqs:List*'
                  - 'sqs:SendMessage'
                Resource:
                  - !Sub 'arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:*dbs*'
              - Effect: Allow
                Action:
                  - "codedeploy:CreateApplication"
                  - "codedeploy:*Get*"
                  - "codedeploy:*List*"
                  - "codedeploy:*Tag*"
                  - "codedeploy:StopDeployment"
                  - "codedeploy:ContinueDeployment"
                  - "codedeploy:CreateDeployment"
                  - "codedeploy:CreateDeploymentConfig"
                  - "codedeploy:CreateDeploymentGroup"
                  - "codedeploy:DeleteApplication"
                  - "codedeploy:DeleteDeploymentConfig"
                  - "codedeploy:DeleteDeploymentGroup"
                  - "codedeploy:RegisterApplicationRevision"
                Resource:
                  - !Sub "arn:aws:codedeploy:${AWS::Region}:${AWS::AccountId}:application:*dbs-api*"
                  - !Sub 'arn:aws:codedeploy:${AWS::Region}:${AWS::AccountId}:deploymentgroup:*dbs-api*'
                  - !Sub 'arn:aws:codedeploy:${AWS::Region}:${AWS::AccountId}:deploymentconfig:*'

  CrmsEngineRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName:
        !Join ['', [!Ref Environment, '-', !Ref Componentname, '-', !Ref Partname, '-crms-engine-github-actions-role']]
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Action: sts:AssumeRoleWithWebIdentity
            Principal:
              Federated: !If
                - CreateOIDCProvider
                - !Ref GithubOidc
                - !Ref OIDCProviderArn
            Condition:
              StringLike:
                token.actions.githubusercontent.com:aud: !Ref OIDCAudience
                token.actions.githubusercontent.com:sub: !Sub repo:${GitHubOrg}/${RepositoryName}:${GitHubBranch}
      Policies:
        - PolicyName:
            !Join [
              '',
              [
                !Ref Environment,
                '-',
                !Ref Componentname,
                '-',
                !Ref Partname,
                '-crms-engine-github-actions-inline-policy',
              ],
            ]
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action:
                  - 'ssm:PutParameter'
                  - 'ssm:DeleteParameter'
                  - 'ssm:AddTagsToResource'
                  - 'ssm:RemoveTagsFromResource'
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter*crms*'
                  - !Sub 'arn:aws:ssm:ap-southeast-2:************:parameter/cdk-bootstrap/*'
              - Effect: Allow
                Action:
                  - 'ssm:Get*'
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter*'
              - Effect: Allow
                Action:
                  - 'secretsmanager:CreateSecret'
                  - 'secretsmanager:List*'
                  - 'secretsmanager:Describe*'
                  - 'secretsmanager:Get*'
                  - 'secretsmanager:DeleteSecret'
                  - 'secretsmanager:PutSecretValue'
                  - 'secretsmanager:UpdateSecret'
                  - 'secretsmanager:TagResource'
                Resource:
                  - !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:*crms*'
              - Effect: Allow
                Action:
                  - 'lambda:Get*'
                  - 'lambda:List*'
                Resource:
                  - 'arn:aws:lambda:ap-southeast-2:************:layer:AWS-Parameters-and-Secrets-Lambda-Extension:*'
              - Effect: Allow
                Action:
                  - 'iam:CreateRole'
                  - 'iam:CreatePolicy'
                  - 'iam:UpdateRole'
                  - 'iam:UpdateRoleDescription'
                  - 'iam:AttachRolePolicy'
                  - 'iam:CreatePolicyVersion'
                  - 'iam:DetachRolePolicy'
                  - 'iam:DeleteRole'
                  - 'iam:DeleteRolePolicy'
                  - 'iam:DeletePolicy'
                  - 'iam:DeletePolicyVersion'
                  - 'iam:DeleteAccessKey'
                  - 'iam:TagRole'
                  - 'iam:TagPolicy'
                  - 'iam:ListRolePolicies'
                  - 'iam:ListPolicies'
                  - 'iam:ListRoles'
                  - 'iam:PutRolePolicy'
                  - 'iam:GetRole'
                  - 'iam:GetPolicy'
                  - 'iam:GetRolePolicy'
                  - 'iam:PassRole'
                Resource:
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:role/*crms-engine*'
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:policy/*crms-engine*'
                  - !Sub 'arn:aws:iam::************:role/cdk*'
              - Effect: Allow
                Action:
                  - 'cloudformation:CreateStack'
                  - 'cloudformation:CreateChangeSet'
                  - 'cloudformation:DescribeChangeSet'
                  - 'cloudformation:DescribeStacks'
                  - 'cloudformation:ExecuteChangeSet'
                  - 'cloudformation:DescribeStackEvents'
                  - 'cloudformation:ListStackResources'
                  - 'cloudformation:DescribeStackResources'
                  - 'cloudformation:DeleteChangeSet'
                  - 'cloudformation:DeleteStack'
                  - 'cloudformation:Get*'
                  - 'cloudformation:List*'
                  - 'cloudformation:Describe*'
                Resource:
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/*crms*/*'
                  - !Sub 'arn:aws:cloudformation:ap-southeast-2:************:stack/CDKToolkit*'
              - Effect: Allow
                Action:
                  - 's3:PutObject'
                  - 's3:GetObject'
                  - 's3:ListAllMyBuckets'
                  - 's3:ListBucket'
                  - 's3:GetBucketLocation'
                  - 's3:GetObjectVersion'
                  - 's3:DeleteObject'
                Resource:
                  - !Sub 'arn:aws:s3:::*crms-engine*'
                  - !Sub 'arn:aws:s3:::*cdk*'
              - Effect: Allow
                Action:
                  - 'sqs:CreateQueue'
                  - 'sqs:DeleteQueue'
                  - 'sqs:TagQueue'
                  - 'sqs:UntagQueue'
                  - 'sqs:AddPermission'
                  - 'sqs:SetQueueAttributes'
                  - 'sqs:Get*'
                  - 'sqs:List*'
                  - 'sqs:SendMessage'
                Resource:
                  - !Sub 'arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:*crms*'
              - Effect: Allow
                Action:
                  - 'ec2:Describe*'
                  - 'lambda:CreateEventSourceMapping'
                  - 'lambda:GetEventSourceMapping'
                  - 'lambda:DeleteEventSourceMapping'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'lambda:Get*'
                  - 'lambda:List*'
                  - 'lambda:CreateFunction*'
                  - 'lambda:CreateAlias*'
                  - 'lambda:CreateEventSourceMapping'
                  - 'lambda:AddPermission'
                  - 'lambda:DeleteAlias'
                  - 'lambda:DeleteFunction'
                  - 'lambda:DeleteEventSourceMapping'
                  - 'lambda:RemovePermission'
                  - 'lambda:TagResource'
                  - 'lambda:UntagResource'
                  - 'lambda:UpdateAlias'
                  - 'lambda:UpdateEventSourceMapping'
                  - 'lambda:UpdateFunctionCode'
                  - 'lambda:UpdateFunctionConfiguration'
                  - 'lambda:InvokeFunction'
                  - 'lambda:PutFunctionConcurrency'
                  - 'lambda:PublishVersion'
                Resource:
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:*-crms-engine*'
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:event-source-mapping:*'
              - Effect: Allow
                Action:
                  - 'logs:CreateLogGroup'
                  - 'logs:CreateLogStream'
                  - 'logs:DeleteLogGroup'
                  - 'logs:DeleteLogStream'
                  - 'logs:TagResource'
                  - 'logs:UntagResource'
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:*'
              - Effect: Allow
                Action:
                  - 'events:Describe*'
                  - 'events:PutRule'
                  - 'events:RemoveTargets'
                  - 'events:DeleteRule'
                  - 'events:PutTargets'
                  - 'events:TagResource'
                  - 'events:DeleteEventBus'
                Resource:
                  - !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:rule/*crms*'
              - Effect: Allow
                Action:
                  - 'cloudformation:ValidateTemplate'
                  - 'cloudformation:GetTemplate'
                  - 'cloudformation:List*'
                  - 'cloudformation:Describe*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'codepipeline:StartPipelineExecution'
                Resource:
                  - !Sub 'arn:aws:codepipeline:${AWS::Region}:${AWS::AccountId}:*'
              - Effect: Allow
                Action:
                  - 'appsync:CreateGraphqlApi'
                  - 'appsync:CreateDomainName'
                  - 'appsync:TagResource'
                  - 'appsync:GetGraphqlApi'
                  - 'appsync:DeleteGraphqlApi'
                  - 'appsync:CreateApiKey'
                  - 'appsync:DeleteApiKey'
                  - 'appsync:StartSchemaCreation'
                  - 'appsync:GetSchemaCreationStatus'
                  - 'appsync:CreateDataSource'
                  - 'appsync:UpdateDataSource'
                  - 'appsync:DeleteDataSource'
                  - 'appsync:CreateResolver'
                  - 'appsync:UpdateResolver'
                  - 'appsync:DeleteResolver'
                Resource:
                  - !Sub 'arn:aws:appsync:${AWS::Region}:${AWS::AccountId}:*'
              - Effect: Allow
                Action:
                  - 'codedeploy:CreateApplication'
                  - 'codedeploy:*Get*'
                  - 'codedeploy:*List*'
                  - 'codedeploy:*Tag*'
                  - 'codedeploy:StopDeployment'
                  - 'codedeploy:ContinueDeployment'
                  - 'codedeploy:CreateDeployment'
                  - 'codedeploy:CreateDeploymentConfig'
                  - 'codedeploy:CreateDeploymentGroup'
                  - 'codedeploy:DeleteApplication'
                  - 'codedeploy:DeleteDeploymentConfig'
                  - 'codedeploy:DeleteDeploymentGroup'
                  - 'codedeploy:RegisterApplicationRevision'
                Resource:
                  - !Sub 'arn:aws:codedeploy:${AWS::Region}:${AWS::AccountId}:*'

  MpApiRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName:
        !Join ['', [!Ref Environment, '-', !Ref Componentname, '-', !Ref Partname, '-mp-api-github-actions-role']]
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Action: sts:AssumeRoleWithWebIdentity
            Principal:
              Federated: !If
                - CreateOIDCProvider
                - !Ref GithubOidc
                - !Ref OIDCProviderArn
            Condition:
              StringLike:
                token.actions.githubusercontent.com:aud: !Ref OIDCAudience
                token.actions.githubusercontent.com:sub: !Sub repo:${GitHubOrg}/${RepositoryName}:${GitHubBranch}
      Policies:
        - PolicyName:
            !Join [
              '',
              [!Ref Environment, '-', !Ref Componentname, '-', !Ref Partname, '-mp-api-github-actions-inline-policy'],
            ]
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action:
                  - 'ssm:PutParameter'
                  - 'ssm:DeleteParameter'
                  - 'ssm:AddTagsToResource'
                  - 'ssm:RemoveTagsFromResource'
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter*mp*'
                  - !Sub 'arn:aws:ssm:ap-southeast-2:************:parameter/cdk-bootstrap/*'
              - Effect: Allow
                Action:
                  - 'ssm:Get*'
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter*'
              - Effect: Allow
                Action:
                  - 'secretsmanager:CreateSecret'
                  - 'secretsmanager:List*'
                  - 'secretsmanager:Describe*'
                  - 'secretsmanager:Get*'
                  - 'secretsmanager:DeleteSecret'
                  - 'secretsmanager:PutSecretValue'
                  - 'secretsmanager:UpdateSecret'
                  - 'secretsmanager:TagResource'
                Resource:
                  - !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:*mp*'
              - Effect: Allow
                Action:
                  - 'lambda:Get*'
                  - 'lambda:List*'
                Resource:
                  - 'arn:aws:lambda:ap-southeast-2:************:layer:AWS-Parameters-and-Secrets-Lambda-Extension:*'
              - Effect: Allow
                Action:
                  - 'iam:CreateRole'
                  - 'iam:CreatePolicy'
                  - 'iam:UpdateRole'
                  - 'iam:UpdateRoleDescription'
                  - 'iam:AttachRolePolicy'
                  - 'iam:CreatePolicyVersion'
                  - 'iam:DetachRolePolicy'
                  - 'iam:DeleteRole'
                  - 'iam:DeleteRolePolicy'
                  - 'iam:DeletePolicy'
                  - 'iam:DeletePolicyVersion'
                  - 'iam:DeleteAccessKey'
                  - 'iam:TagRole'
                  - 'iam:TagPolicy'
                  - 'iam:ListRolePolicies'
                  - 'iam:ListPolicies'
                  - 'iam:ListRoles'
                  - 'iam:PutRolePolicy'
                  - 'iam:GetRole'
                  - 'iam:GetPolicy'
                  - 'iam:GetRolePolicy'
                  - 'iam:PassRole'
                Resource:
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:role/*mp-api*'
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:policy/*mp-api*'
                  - !Sub 'arn:aws:iam::************:role/cdk*'
              - Effect: Allow
                Action:
                  - 'cloudformation:CreateStack'
                  - 'cloudformation:CreateChangeSet'
                  - 'cloudformation:DescribeChangeSet'
                  - 'cloudformation:DescribeStacks'
                  - 'cloudformation:ExecuteChangeSet'
                  - 'cloudformation:DescribeStackEvents'
                  - 'cloudformation:ListStackResources'
                  - 'cloudformation:DescribeStackResources'
                  - 'cloudformation:DeleteChangeSet'
                  - 'cloudformation:DeleteStack'
                  - 'cloudformation:Get*'
                  - 'cloudformation:List*'
                  - 'cloudformation:Describe*'
                Resource:
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/*mp*/*'
                  - !Sub 'arn:aws:cloudformation:ap-southeast-2:************:stack/CDKToolkit*'
              - Effect: Allow
                Action:
                  - 's3:PutObject'
                  - 's3:GetObject'
                  - 's3:ListAllMyBuckets'
                  - 's3:ListBucket'
                  - 's3:GetBucketLocation'
                  - 's3:GetObjectVersion'
                  - 's3:DeleteObject'
                Resource:
                  - !Sub 'arn:aws:s3:::*mp-api*'
                  - !Sub 'arn:aws:s3:::*cdk*'
              - Effect: Allow
                Action:
                  - 'ec2:Describe*'
                  - 'lambda:CreateEventSourceMapping'
                  - 'lambda:GetEventSourceMapping'
                  - 'lambda:DeleteEventSourceMapping'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'lambda:Get*'
                  - 'lambda:List*'
                  - 'lambda:CreateFunction*'
                  - 'lambda:CreateAlias*'
                  - 'lambda:CreateEventSourceMapping'
                  - 'lambda:AddPermission'
                  - 'lambda:DeleteAlias'
                  - 'lambda:DeleteFunction'
                  - 'lambda:DeleteEventSourceMapping'
                  - 'lambda:RemovePermission'
                  - 'lambda:TagResource'
                  - 'lambda:UntagResource'
                  - 'lambda:UpdateAlias'
                  - 'lambda:UpdateEventSourceMapping'
                  - 'lambda:UpdateFunctionCode'
                  - 'lambda:UpdateFunctionConfiguration'
                  - 'lambda:InvokeFunction'
                  - 'lambda:PutFunctionConcurrency'
                  - 'lambda:PublishVersion'
                Resource:
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:*-mp-api*'
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:event-source-mapping:*'
              - Effect: Allow
                Action:
                  - 'logs:CreateLogGroup'
                  - 'logs:CreateLogStream'
                  - 'logs:DeleteLogGroup'
                  - 'logs:DeleteLogStream'
                  - 'logs:TagResource'
                  - 'logs:UntagResource'
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:*'
              - Effect: Allow
                Action:
                  - 'events:Describe*'
                  - 'events:PutRule'
                  - 'events:RemoveTargets'
                  - 'events:DeleteRule'
                  - 'events:PutTargets'
                  - 'events:TagResource'
                  - 'events:CreateEventBus'
                  - 'events:DeleteEventBus'
                Resource:
                  - !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/*mp*'
                  - !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:rule/*mp*'
              - Effect: Allow
                Action:
                  - 'cloudformation:ValidateTemplate'
                  - 'cloudformation:GetTemplate'
                  - 'cloudformation:List*'
                  - 'cloudformation:Describe*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'codepipeline:StartPipelineExecution'
                Resource:
                  - !Sub 'arn:aws:codepipeline:${AWS::Region}:${AWS::AccountId}:*'
              - Effect: Allow
                Action:
                  - 'sqs:createqueue'
                  - 'sqs:getqueueattributes'
                Resource:
                  - !Sub 'arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:st*-mp-stl-mock-cqrs-*'
              - Effect: Allow
                Action:
                  - 'appsync:CreateGraphqlApi'
                  - 'appsync:CreateDomainName'
                  - 'appsync:TagResource'
                  - 'appsync:GetGraphqlApi'
                  - 'appsync:DeleteGraphqlApi'
                  - 'appsync:CreateApiKey'
                  - 'appsync:DeleteApiKey'
                  - 'appsync:StartSchemaCreation'
                  - 'appsync:GetSchemaCreationStatus'
                  - 'appsync:CreateDataSource'
                  - 'appsync:UpdateDataSource'
                  - 'appsync:DeleteDataSource'
                  - 'appsync:CreateResolver'
                  - 'appsync:UpdateResolver'
                  - 'appsync:DeleteResolver'
                Resource:
                  - !Sub 'arn:aws:appsync:${AWS::Region}:${AWS::AccountId}:*'
              - Effect: Allow
                Action:
                  - 'sqs:CreateQueue'
                  - 'sqs:DeleteQueue'
                  - 'sqs:TagQueue'
                  - 'sqs:UntagQueue'
                  - 'sqs:AddPermission'
                  - 'sqs:SetQueueAttributes'
                  - 'sqs:Get*'
                  - 'sqs:List*'
                  - 'sqs:SendMessage'
                Resource:
                  - !Sub 'arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:*mp*'
              - Effect: Allow
                Action:
                  - "codedeploy:CreateApplication"
                  - "codedeploy:*Get*"
                  - "codedeploy:*List*"
                  - "codedeploy:*Tag*"
                  - "codedeploy:StopDeployment"
                  - "codedeploy:ContinueDeployment"
                  - "codedeploy:CreateDeployment"
                  - "codedeploy:CreateDeploymentConfig"
                  - "codedeploy:CreateDeploymentGroup"
                  - "codedeploy:DeleteApplication"
                  - "codedeploy:DeleteDeploymentConfig"
                  - "codedeploy:DeleteDeploymentGroup"
                  - "codedeploy:RegisterApplicationRevision"
                Resource:
                  - !Sub "arn:aws:codedeploy:${AWS::Region}:${AWS::AccountId}:application:*mp-api*"
                  - !Sub 'arn:aws:codedeploy:${AWS::Region}:${AWS::AccountId}:deploymentgroup:*mp-api*'
                  - !Sub 'arn:aws:codedeploy:${AWS::Region}:${AWS::AccountId}:deploymentconfig:*'

Outputs:
  BuildTestRole:
    Value: !GetAtt Role.Arn
  MpApiRole:
    Value: !GetAtt MpApiRole.Arn
  DbsApiRole:
    Value: !GetAtt DbsApiRole.Arn
  CrmsEngineRole:
    Value: !GetAtt CrmsEngineRole.Arn
