import type { Config } from 'jest';

if (process.env['CI']) {
  process.env['LOG_LEVEL'] = 'none';
}

process.env['AWS_XRAY_CONTEXT_MISSING'] = 'IGNORE_ERROR';

const config: Config = {
  roots: ['<rootDir>/src'],
  testMatch: ['**/?(*.)+(spec|test).[t]s?(x)'],
  resolver: '@nx/jest/plugins/resolver',
  moduleFileExtensions: ['ts', 'js'],
  transform: {
    '^.+\\.(ts|tsx)$': '@swc/jest',
  },
  testEnvironment: 'node',
  testEnvironmentOptions: { customExportConditions: ['node', 'require', 'default'] },
  testTimeout: 20000,
  testResultsProcessor: 'jest-sonar-reporter',
  collectCoverage: true,
  collectCoverageFrom: ['**/*.{ts,js}', '!**/node_modules/**'],
  coverageDirectory: 'dist',
  coverageReporters: ['lcov'],
  coverageThreshold: {
    global: {
      functions: 90,
      lines: 90,
      statements: 90,
      branches: 90,
    },
  },
  reporters: [
    'summary',
    ['github-actions', { silent: false }],
    [
      'jest-junit',
      {
        outputDirectory: 'dist/',
        outputName: 'report.xml',
        uniqueOutputName: 'false',
        titleTemplate: '{classname}-{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: 'true',
        includeConsoleOutput: 'true',
        suiteName: 'Test Report',
      },
    ],
    [
      'jest-html-reporter',
      {
        pageTitle: 'Test Report',
        outputPath: 'dist/test-report.html',
      },
    ],
  ],
};

export default config;
