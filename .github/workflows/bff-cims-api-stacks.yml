name: bff-cims-api-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        type: string
        required: true

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      app_name: bff-cims-api
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: bff
      part_name: cims-api
      build_image: ubuntu-8core
      deployment_s3_bucket: true
      sonar_project_key: ${{ vars.SONAR_BFF_CIMS_API_PROJECT_KEY }}
      use_workspace_focus: true
      role_to_assume: ${{ from<PERSON><PERSON>(inputs.role_to_assume)['engine_api'] }}

  publish-contract:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}-bff-cims-api
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/pact-test
        with:
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          role_to_assume: ${{ from<PERSON><PERSON>(inputs.role_to_assume)['engine_api'] }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          work_dir: 'apps/payments/cims/contract/api'
          app_name: bff-cims-api-contract-test

  deploy-catalog-dynamodb:
    name: deploy catalog dynamodb
    runs-on: ubuntu-latest
    needs: pre-deploy
    timeout-minutes: 60
    strategy:
      matrix:
        component:
          - name: mp
            app_name: mp-api
            component_dir: apps/bff-api/mp/api
            sls_appsync: serverlessAppsync.ts
          - name: dbs
            app_name: dbs-api
            component_dir: apps/bff-api/dbs/api
            sls_appsync: serverlessAppsync.ts
          - name: crms
            app_name: crms-engine
            component_dir: apps/bff-api/crms/engine
            sls_appsync: serverlessAppsync.ts
    environment: ${{ inputs.environment }}-${{ matrix.component.app_name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - name: setup env
        run: |
          echo COMPONENT_NAME=${{ matrix.component.name }} >> $GITHUB_ENV
          echo "deploy dynamoDB $COMPONENT_NAME"
      - name: Deploy mock appsync
        uses: ./.github/workflows/common/sls-deploy
        if: inputs.environment == 'st'
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: bff-cims-api
          sls_config: ${{ matrix.component.sls_appsync }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: ${{ matrix.component.component_dir}}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          additional_workspaces: ${{matrix.component.app_name}} crms-engine-system-test

      - name: Deploy app
        uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: bff-cims-api
          sls_config: serverlessDynamodb.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.app_name] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/cims/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}

  deploy-apps:
    name: deploy graphql handlers
    runs-on: ubuntu-latest
    timeout-minutes: 60
    needs:
      - pre-deploy
      - deploy-catalog-dynamodb
    strategy:
      fail-fast: false
      matrix:
        component:
          - name: mp
            app_name: mp-api
            sls_config: serverlessGraphqlHandlers.ts
          - name: dbs
            app_name: dbs-api
            sls_config: serverlessGraphqlHandlers.ts
          - name: crms
            app_name: crms-engine
            sls_config: serverlessGraphqlHandlers.ts
          - name: mp
            app_name: mp-api
            sls_config: serverlessProjection.ts
          - name: dbs
            app_name: dbs-api
            sls_config: serverlessProjection.ts
          - name: crms
            app_name: crms-engine
            sls_config: serverlessProjection.ts
    environment: ${{ inputs.environment }}-${{ matrix.component.app_name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - name: setup env
        run: |
          echo COMPONENT_NAME=${{ matrix.component.name }} >> $GITHUB_ENV
          echo "deploy ${{ matrix.component.sls_config }} $COMPONENT_NAME"
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.component.sls_config }}
          app_name: bff-cims-api
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          work_dir: apps/payments/cims/api
          use_workspace_focus: true
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.app_name] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}

  deploy-warmup:
    name: deploy warmup
    runs-on: ubuntu-latest
    timeout-minutes: 60
    needs:
      - pre-deploy
      - deploy-catalog-dynamodb
    environment: ${{ inputs.environment }}-bff-cims-api
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - name: setup env
        run: |
          echo COMPONENT_NAME=bff >> $GITHUB_ENV
          echo "deploy warmup $COMPONENT_NAME"
          echo ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessWarmup.ts
          app_name: bff-cims-api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/cims/api
          use_workspace_focus: true
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
  system-test:
    name: system test
    runs-on: ubuntu-latest
    timeout-minutes: 60
    needs:
      - pre-deploy
      - deploy-catalog-dynamodb
      - deploy-apps
    strategy:
      fail-fast: false
      matrix:
        component:
          [
            { name: graphqlHandlers, fileName: graphqlHandlers.spec.ts },
            { name: projection, fileName: projection.spec.ts },
            { name: rbac-mpGraphqlHandlers, fileName: rbacMpGraphqlHandlers.spec.ts },
            { name: subscription, fileName: subscription.spec.ts },
          ]
    environment: ${{ inputs.environment }}-bff-cims-api
    if: inputs.environment != 'prod' && inputs.environment != 'staging'
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/restore-archive
        if: needs.pre-deploy.outputs.archived_name != '' && needs.pre-deploy.outputs.archived_file_name != ''
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - if: (matrix.component.name != 'subscription') || (matrix.component.name == 'subscription' && inputs.environment == 'dev')
        run: |
          yarn workspaces focus component-bff cims-system-tests
          yarn nx build cims-system-tests
          cd apps/payments/cims/system-test/
          STAGE=${{ inputs.stage }} yarn system-test src/bff-cims-api/${{ matrix.component.fileName }}

  tag:
    runs-on: ubuntu-latest
    needs:
      - system-test
    environment: ${{ inputs.environment }}-cims
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-cims
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - deploy-catalog-dynamodb
      - deploy-apps
      - deploy-warmup
      - system-test
      - pre-deploy
      - tag
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: bff-cims-api
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
