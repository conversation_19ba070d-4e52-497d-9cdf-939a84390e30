name: integration-tests-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      commitSha:
        required: true
        type: string
      role_to_assume:
        required: true
        type: string

jobs:
  pre-deploy:
    runs-on: ubuntu-8core
    environment: ${{ inputs.environment }}-bff-integration-tests
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
      - name: Install dependencies
        shell: bash
        run: |
          node --version
          yarn --version
          yarn workspaces focus integration-tests component-bff @npco/component-cnp-sdk
          yarn nx reset
      - name: build
        shell: bash
        run: |
          yarn nx build integration-tests
      - name: lint
        shell: bash
        run: |
          yarn nx lint integration-tests
      - id: archive
        uses: ./.github/workflows/common/archive
        with:
          archived_name: ${{ inputs.stage }}-integration-tests-${{ inputs.region }}
          environment: ${{ inputs.environment }}

  deploy-pipeline:
    name: Deploy
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}-bff-integration-tests
    env:
      NPCO_JFROG_REGISTRY: ${{ secrets.NPCO_JFROG_REGISTRY }}
      NPCO_JFROG_ACCESS_TOKEN: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
      STAGE: ${{ inputs.stage}}
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
      - uses: ./.github/workflows/common/restore-archive
        with:
          archived_name: ${{ inputs.stage }}-integration-tests-${{ inputs.region }}
          archived_file_name: ${{ inputs.stage }}-integration-tests-${{ inputs.region  }}.tar.gz
      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['integration-tests']  }}
          aws_region: ${{ inputs.region }}

      - run: |
          yarn workspaces focus integration-tests-cicd integration-tests component-bff @npco/component-cnp-sdk
          yarn nx build integration-tests-cicd
          cd apps/core/integration-tests/cicd
          yarn deploy ${{inputs.region}} ${{ inputs.stage }} apps:pipeline develop

      - run: |
          echo "Starting tests for ${{ inputs.commitSha }}"
          aws codepipeline start-pipeline-execution --name ${{ inputs.stage }}-bff-integration-tests-iac --region ${{ inputs.region }}
