name: integration-tests

on:
  push:
    paths:
      - apps/core/integration-tests/**
      - .github/workflows/integration-tests*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: string

concurrency:
  # only one workflow run at a time per branch
  group: '${{ github.workflow }} @ ${{ github.head_ref || github.ref }}'
  cancel-in-progress: false

jobs:
  detect-env:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env-detect.outputs.environment }}
      stage: ${{ steps.env-detect.outputs.stage }}
    steps:
      - uses: actions/checkout@v4
      - id: env-detect
        uses: ./.github/workflows/common/env-detect
        with:
          environment: ${{ inputs.environment }}
  assume-roles:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: ${{ needs.detect-env.outputs.environment }}
      component_name: bff
      part_name: integration-tests

  integration-tests-sydney:
    secrets: inherit
    needs:
      - detect-env
      - assume-roles
    uses: ./.github/workflows/integration-tests-deploy.yml
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      region: ${{ vars.SYDNEY_REGION }}
      commitSha: ${{ github.sha }}
      role_to_assume: ${{ toJson(fromJson(needs.assume-roles.outputs.role_to_assume)[vars.SYDNEY_REGION]) }}

  integration-tests-london:
    secrets: inherit
    needs:
      - detect-env
      - assume-roles
    uses: ./.github/workflows/integration-tests-deploy.yml
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      region: ${{ vars.LONDON_REGION }}
      commitSha: ${{ github.sha }}
      role_to_assume: ${{ toJson(fromJson(needs.assume-roles.outputs.role_to_assume)[vars.LONDON_REGION]) }}
