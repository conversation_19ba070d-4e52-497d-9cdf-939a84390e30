name: bff-settlement-api-integration-tests

on:
  schedule:
    - cron: '0 13 * * *'
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        required: true
        type: string

env:
  ENVIRONMENT: ${{ inputs.environment || 'dev' }}
  STAGE: ${{ inputs.stage || 'dev' }}
  REGION: ${{ inputs.region || vars.SYDNEY_REGION }}

jobs:
  integration-test:
    name: integration test
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'dev' }}-bff-settlement-api

    permissions:
      id-token: write
      contents: read

    if: >
      ${{ github.event_name == 'schedule' || 
      (github.event_name == 'workflow_call' && (
      inputs.environment || 'dev') == 'dev') }}

    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/node-env
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ inputs.role_to_assume }}
          aws_region: ${{ env.REGION }}

      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          JFROG_EMAIL: ${{ secrets.NPCO_JFROG_EMAIL }}
          JFROG_ACCESS_TOKEN: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          JFROG_REGISTRY: ${{ secrets.NPCO_JFROG_REGISTRY }}
          STAGE: ${{ env.ENVIRONMENT }}
        with:
          project-name: ${{ env.ENVIRONMENT }}-bff-settlement-api
          compute-type-override: BUILD_GENERAL1_MEDIUM
          buildspec-override: |
            version: 0.2
            phases:
              install:
                runtime-versions:
                  nodejs: 18
              pre_build:
                commands:
                  - env
                  - corepack enable
                  - yarn config set npmPublishRegistry https://$JFROG_REGISTRY
                  - yarn config set npmScopes.npco.npmRegistryServer https://$JFROG_REGISTRY
                  - yarn config set npmScopes.npco.npmAuthToken "$JFROG_ACCESS_TOKEN"
                  - yarn config set npmScopes.npco.npmAlwaysAuth true
                  - cat .yarnrc.yml
                  - corepack enable
                  - export AUTH0_DBS_CLIENT_ID=$(aws ssm get-parameter --with-decryption --name /dev-dbs-api/AUTH0_CLIENT_ID | jq -r '.Parameter.Value')
                  - export AUTH0_DBS_CLIENT_SECRET=$(aws ssm get-parameter --with-decryption --name /dev-dbs-api/AUTH0_CLIENT_SECRET | jq -r '.Parameter.Value')
                  - export AUTH0_MP_CLIENT_ID=$(aws ssm get-parameter --with-decryption --name /dev-mp-app/AUTH0_CLIENT_ID | jq -r '.Parameter.Value')
                  - export AUTH0_MP_CLIENT_SECRET=$(aws ssm get-parameter --with-decryption --name /dev-mp-app/AUTH0_CLIENT_SECRET | jq -r '.Parameter.Value')
                  - export AUTH0_MP_API_CLIENT_ID=$(aws ssm get-parameter --with-decryption --name /dev-mp-api/AUTH0_CLIENT_ID | jq -r '.Parameter.Value')
                  - export AUTH0_MP_API_CLIENT_SECRET=$(aws ssm get-parameter --with-decryption --name /dev-mp-api/AUTH0_CLIENT_SECRET | jq -r '.Parameter.Value')
                  - export AUTH0_AMS_CLIENT_ID=$(aws ssm get-parameter --with-decryption --name /dev-ams-engine/AUTH0_CLIENT_ID | jq -r '.Parameter.Value')
                  - export AUTH0_AMS_CLIENT_SECRET=$(aws ssm get-parameter --with-decryption --name /dev-ams-engine/AUTH0_CLIENT_SECRET | jq -r '.Parameter.Value')
                  - export ZELLER_APP_AUTH0_CLIENT_ID=$(aws ssm get-parameter --with-decryption --name /dev-mp-api/ZELLER_APP_AUTH0_CLIENT_ID | jq -r '.Parameter.Value')
                  - export ZELLER_APP_AUTH0_CLIENT_SECRET=$(aws ssm get-parameter --with-decryption --name /dev-mp-api/ZELLER_APP_AUTH0_CLIENT_SECRET | jq -r '.Parameter.Value')
                  - echo $AUTH0_DBS_CLIENT_ID $AUTH0_MP_CLIENT_ID $AUTH0_MP_API_CLIENT_ID $AUTH0_AMS_CLIENT_ID $ZELLER_APP_AUTH0_CLIENT_ID

              build:
                commands:
                  - yarn workspaces focus '@npco/component-bff-settlement-api' component-bff crms-engine-system-test
                  - yarn nx build bff-settlement-api
                  - cd apps/payments/settlement/api
                  - STAGE=$STAGE yarn integration:test
            reports:
              report:
                files:
                  - "apps/payments/settlement/integration-tests/dist/report.xml"
                file-format: "JUNITXML"
          env-vars-for-codebuild: |
            JFROG_EMAIL,
            JFROG_ACCESS_TOKEN,
            JFROG_REGISTRY,
            STAGE

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    needs:
      - integration-test
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: bff-settlement-api
          environment: ${{ env.ENVIRONMENT }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
