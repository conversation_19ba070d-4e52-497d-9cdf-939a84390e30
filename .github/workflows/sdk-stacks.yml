name: sdk-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        required: true
        type: string

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: sdk
      part_name: api
      sonar_project_key: ${{ vars.SONAR_SDK_API_PROJECT_KEY }}
      app_name: sdk-api
      deployment_s3_bucket: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      use_workspace_focus: true
      additional_workspaces: 'cqrs-common'
      deploy_cqrs: true
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}

  deploy-cqrs:
    needs: pre-deploy
    uses: ./.github/workflows/component-cqrs.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: sdk
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      work_dir: apps/payments/sdk/cqrs/cqrs-common
      sonar_project_key: ${{ vars.SONAR_CQRS_COMMON_PROJECT_KEY }}
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['cqrs'] }}
      use_eventbus_projection: false
      use_sqs_projection: false

  deploy-infra:
    name: infra
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
    environment: ${{ inputs.environment }}-sdk-api
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: dynamodb
            sls_config: serverlessDynamodb.ts
          - name: appsync
            sls_config: serverlessAppsync.ts
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: sdk-api
          sls_config: ${{ matrix.stack.sls_config}}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/sdk/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true

  deploy-apps:
    name: deploy apps
    runs-on: ubuntu-latest
    timeout-minutes: 60
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-infra
    environment: ${{ inputs.environment }}-sdk-api
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: Projection
            sls_config: serverlessProjection.ts
          - name: Warmup
            sls_config: serverlessWarmup.ts

    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.stack.sls_config }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          app_name: sdk-api
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/payments/sdk/api'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

  sdk-system-test:
    name: sdk system test
    runs-on: ubuntu-latest
    if: inputs.environment != 'prod' && inputs.environment != 'staging'
    needs:
      - deploy-infra
      - deploy-apps
    environment: ${{ inputs.environment }}-sdk-api
    permissions:
      id-token: write
      contents: read
    env:
      COLUMNS: 120
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/node-env
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - run: |
          yarn install --frozen-lockfile
          yarn nx build sdk-system-test
          cd apps/payments/sdk/system-test
          STAGE=${{ inputs.stage }} AWS_REGION=${{ inputs.region }} yarn system:test

  integration-test:
    name: integration test
    if: inputs.environment == 'dev' && inputs.region == vars.SYDNEY_REGION
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-infra
      - deploy-apps
      - sdk-system-test # waits for system-test to finish before running integration test
    uses: ./.github/workflows/sdk-integration-test.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

  tag:
    runs-on: ubuntu-latest
    needs:
      - deploy-cqrs
      - deploy-apps
    environment: ${{ inputs.environment }}-sdk-engine
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-sdk-engine
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - tag
      - deploy-cqrs
      - deploy-infra
      - deploy-apps
      - integration-test
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: sdk-api
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
