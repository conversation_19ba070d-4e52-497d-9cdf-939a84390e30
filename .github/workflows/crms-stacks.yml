name: crms-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        required: true
        type: string

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      app_name: crms-engine
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: crms
      part_name: engine
      sonar_project_key: ${{ vars.SONAR_CRMS_ENGINE_PROJECT_KEY }}
      deploy_cqrs: true
      deployment_s3_bucket: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      build_image: ubuntu-8core
      use_workspace_focus: true
      role_to_assume: ${{ from<PERSON>son(inputs.role_to_assume)['engine_api'] }}

  publish-contract:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}-crms-engine
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/pact-test
        with:
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          role_to_assume: ${{ from<PERSON><PERSON>(inputs.role_to_assume)['engine_api'] }}
          work_dir: 'apps/bff-api/crms/contract'
          app_name: crms-engine-contract-test
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}

  # deploy cqrs
  deploy-cqrs:
    needs: pre-deploy
    uses: ./.github/workflows/component-cqrs.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: crms
      sonar_project_key: ${{ vars.SONAR_CQRS_COMMON_PROJECT_KEY }}
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      work_dir: apps/bff-api/crms/cqrs/cqrs-common
      use_eventbus_projection: true
      use_sqs_projection: true
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['cqrs'] }}

  # contract tests to add

  # deploy engine
  deploy-common-layer:
    name: common layer
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - publish-contract
    environment: ${{ inputs.environment }}-crms-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: crms-engine
          sls_config: serverlessCommonLayers.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/bff-api/crms/engine'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
  deploy-infra:
    name: infra
    timeout-minutes: 60
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-common-layer
      - publish-contract
    environment: ${{ inputs.environment }}-crms-engine
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: dynamodb
            sls_config: serverlessDynamodb.ts
          - name: appsync
            sls_config: serverlessAppsync.ts
          - name: sqs
            sls_config: serverlessSqs.ts
          - name: assets
            sls_config: serverlessAssets.ts
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: crms-engine
          sls_config: ${{ matrix.stack.sls_config }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/bff-api/crms/engine'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          pre_deploy_script: yarn build:schemas
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-apps:
    name: deploy apps
    runs-on: ubuntu-latest
    timeout-minutes: 60
    needs:
      - pre-deploy
      - deploy-infra
      - deploy-cqrs
    environment: ${{ inputs.environment }}-crms-engine
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: Api
            sls_config: serverlessApi.ts
          - name: Projection
            sls_config: serverlessProjection.ts
          - name: Cardholder
            sls_config: serverlessCardholder.ts
          - name: ConnectedEntities
            sls_config: serverlessConnectedEntities.ts
          - name: Sim
            sls_config: serverlessSim.ts
          - name: Parameter
            sls_config: serverlessParameter.ts
          - name: Richdata
            sls_config: serverlessRichdata.ts
          - name: Billing
            sls_config: serverlessBilling.ts
          - name: PrivateApi
            sls_config: serverlessPrivateApi.ts
          - name: Notifications
            sls_config: serverlessNotifications.ts
          - name: Warmup
            sls_config: serverlessWarmup.ts
          - name: Identity
            sls_config: serverlessIdentity.ts
          - name: Databricks
            sls_config: serverlessDatabricks.ts
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          stage: ${{ inputs.stage }}
          environment: ${{ inputs.environment }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.stack.sls_config }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          app_name: crms-engine
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/bff-api/crms/engine'
          pre_deploy_script: mkdir -p src/fonts && cp ../../../libs/dbs-mp-common/src/fonts/* src/fonts
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  list-system-test-spec-files-matrix:
    runs-on: ubuntu-latest
    outputs:
      first_stage_matrix: ${{ steps.set-matrix.outputs.first_stage_matrix }}
      second_stage_matrix: ${{ steps.set-matrix.outputs.second_stage_matrix }}
    steps:
      - uses: actions/checkout@v4
      - id: set-matrix
        run: |
          cd apps/bff-api/crms/system-test/src
          all_specs=$(ls *.spec.ts)
          second_stage_tests=(
            "companies"
            "customObjectInvitedContactCompany" 
            "customObjectEntityPAHContactCompany" 
            "customObjectDigitalWalletToken" 
            "contactsMultiEntity" 
            "customObjectRejectedContactCompany" 
            "customObjectProjection" 
            "customObjectLinkedContactCompany" 
            "contactsEventQueue"
          )
          second_stage_regex=$(IFS='|'; echo "${second_stage_tests[*]}")

          first_stage_specs=$(echo "$all_specs" | grep -v -E "$second_stage_regex")
          second_stage_specs=$(echo "$all_specs" | grep -E "$second_stage_regex")

          echo "first_stage_matrix=$(echo "$first_stage_specs" | jq -R -s -c 'split("\n")[:-1]')" >> $GITHUB_OUTPUT
          echo "second_stage_matrix=$(echo "$second_stage_specs" | jq -R -s -c 'split("\n")[:-1]')" >> $GITHUB_OUTPUT

  crms-engine-system-test-first-stage:
    name: first stage
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: (inputs.environment == 'st' || inputs.environment == 'dev')
    needs:
      - pre-deploy
      - deploy-apps
      - list-system-test-spec-files-matrix
      - deploy-cqrs
    environment: ${{ inputs.environment }}-crms-engine
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        specFile: ${{fromJson(needs.list-system-test-spec-files-matrix.outputs.first_stage_matrix)}}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - env:
          AWS_REGION: ${{ inputs.region }}
          STAGE: ${{ inputs.stage }}
          SYDNEY_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          LONDON_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          region: ${{ inputs.region }}
        run: |
          yarn workspaces focus component-bff crms-engine crms-engine-system-test
          yarn nx run crms-engine-system-test:system-test apps/bff-api/crms/system-test/src/${{ matrix.specFile }}

  crms-engine-system-test-second-stage:
    name: second stage
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: (inputs.environment == 'st' || inputs.environment == 'dev')
    needs:
      - pre-deploy
      - deploy-apps
      - list-system-test-spec-files-matrix
      - crms-engine-system-test-first-stage
      - deploy-cqrs
    environment: ${{ inputs.environment }}-crms-engine
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        specFile: ${{fromJson(needs.list-system-test-spec-files-matrix.outputs.second_stage_matrix)}}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - env:
          AWS_REGION: ${{ inputs.region }}
          STAGE: ${{ inputs.stage }}
          SYDNEY_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          LONDON_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          region: ${{ inputs.region }}
        run: |
          yarn workspaces focus component-bff crms-engine crms-engine-system-test
          yarn nx run crms-engine-system-test:system-test apps/bff-api/crms/system-test/src/${{ matrix.specFile }}

  vpce-system-test:
    name: private Hubspot Api system test
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}-crms-engine
    if: inputs.environment == 'st'
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-apps
      - list-system-test-spec-files-matrix
      - deploy-cqrs
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          JFROG_EMAIL: ${{ secrets.NPCO_JFROG_EMAIL }}
          JFROG_ACCESS_TOKEN: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          JFROG_REGISTRY: ${{ secrets.NPCO_JFROG_REGISTRY }}
          STAGE: ${{ inputs.environment }}
          SYDNEY_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          LONDON_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          AWS_REGION: ${{ inputs.region }}
          region: ${{ inputs.region }}
        with:
          project-name: ${{inputs.environment}}-crms-engine
          compute-type-override: BUILD_GENERAL1_MEDIUM
          buildspec-override: |
            version: 0.2
            env:
              shell: bash
            phases:
              install:
                runtime-versions:
                  nodejs: 18
              pre_build:
                commands:
                  - env
                  - corepack enable
                  - yarn config set npmPublishRegistry https://$JFROG_REGISTRY
                  - yarn config set npmScopes.npco.npmRegistryServer https://$JFROG_REGISTRY
                  - yarn config set npmScopes.npco.npmAuthToken "$JFROG_ACCESS_TOKEN"
                  - yarn config set npmScopes.npco.npmAlwaysAuth true
                  - cat .yarnrc.yml
              build:
                commands:
                  - yarn workspaces focus component-bff crms-engine crms-engine-system-test
                  - yarn nx build crms-engine-system-test
                  - cd apps/bff-api/crms/system-test
                  - STAGE=${{ inputs.stage }} yarn test src/codebuildTests
            reports:
              report:
                  files:
                    - "apps/bff-api/crms/system-test/dist/report.xml"
                  file-format: "JUNITXML"
          env-vars-for-codebuild: |
            JFROG_EMAIL,
            JFROG_ACCESS_TOKEN,
            JFROG_REGISTRY,
            STAGE,
            SYDNEY_ACCOUNT_ID,
            LONDON_ACCOUNT_ID,
            AWS_REGION,
            region

  integration-test:
    name: integration test
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}-crms-engine
    if: inputs.environment == 'dev' && inputs.region == vars.SYDNEY_REGION
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-infra
      - deploy-apps
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}
      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          JFROG_EMAIL: ${{ secrets.NPCO_JFROG_EMAIL }}
          JFROG_ACCESS_TOKEN: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          JFROG_REGISTRY: ${{ secrets.NPCO_JFROG_REGISTRY }}
          STAGE: ${{ inputs.environment }}
        with:
          project-name: ${{inputs.environment}}-crms-engine
          buildspec-override: |
            version: 0.2
            env:
              shell: bash
            phases:
              pre_build:
                commands:
                  - env
                  - cat .yarnrc.yml
                  - yarn config set npmPublishRegistry https://$JFROG_REGISTRY
                  - yarn config set npmScopes.npco.npmRegistryServer https://$JFROG_REGISTRY
                  - yarn config set npmScopes.npco.npmAuthToken "$JFROG_ACCESS_TOKEN"
                  - yarn config set npmScopes.npco.npmAlwaysAuth true
                  - corepack enable

              build:
                commands:
                  - set +e
                  - yarn workspaces focus component-bff crms-integration-test
                  - export STAGE=${{ inputs.stage }}
                  - yarn nx run crms-integration-test:build
                  - yarn nx run crms-integration-test:integration:test
          env-vars-for-codebuild: |
            JFROG_EMAIL,
            JFROG_ACCESS_TOKEN,
            JFROG_REGISTRY,
            STAGE

  tag:
    runs-on: ubuntu-latest
    needs:
      - deploy-apps
      - deploy-cqrs
    environment: ${{ inputs.environment }}-crms-engine
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-crms-engine
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always() && inputs.environment != 'st'
    needs:
      - tag
      - pre-deploy
      - deploy-cqrs
      - deploy-infra
      - deploy-apps
      - crms-engine-system-test-first-stage
      - crms-engine-system-test-second-stage
      - vpce-system-test
      - integration-test
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: crms-engine
          region: ${{ inputs.region }}
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
