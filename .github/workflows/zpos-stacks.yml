name: zpos-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        type: string
        required: true

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      app_name: zpos-engine
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: zpos
      part_name: engine
      sonar_project_key: ${{ vars.SONAR_ZPOS_CQRS_PROJECT_KEY }}
      deploy_cqrs: true
      deployment_s3_bucket: true
      use_workspace_focus: true
      build_image: 'ubuntu-8core'
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}

  # deploy cqrs
  deploy-cqrs:
    needs: pre-deploy
    uses: ./.github/workflows/component-cqrs.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: zpos
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      work_dir: apps/payments/zpos/cqrs/cqrs-common
      use_eventbus_projection: false
      use_sqs_projection: true
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['cqrs'] }}
      sonar_project_key: ${{ vars.SONAR_CQRS_COMMON_PROJECT_KEY }}

  cqrs-system-test:
    name: cqrs system test
    runs-on: ubuntu-latest
    if: false
    needs:
      - deploy-cqrs
    environment: ${{ inputs.environment }}-zpos-cqrs
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/node-env
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          aws_region: ${{ inputs.region }}

      - run: |
          yarn workspaces focus component-bff zpos-cqrs-system-test
          yarn nx build zpos-cqrs-system-test
          cd apps/payments/zpos/cqrs/system-test
          STAGE=${{ inputs.stage }} yarn system:test

  deploy-common-and-dynamodb:
    name: deploy common resources
    timeout-minutes: 60
    runs-on: ubuntu-latest
    needs: pre-deploy
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: common resources
            sls_config: serverlessCommon.ts
          - name: transactions dymamodb
            sls_config: serverlessDynamodb.ts
    environment: ${{ inputs.environment }}-zpos-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: zpos-engine
          sls_config: ${{ matrix.stack.sls_config}}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/zpos/engine
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  db-migration:
    name: db migration
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}-zpos-engine
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}
      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          DockerHubUser: ${{ secrets.DOCKERHUB_USERNAME }}
          DockerHubPassword: ${{ secrets.DOCKERHUB_PASSWORD }}
          AuroraEnvironment: ${{ inputs.environment == 'st' && 'dev' ||  inputs.environment }}
          EnvironmentName: ${{ inputs.stage }}
          STAGE: ${{ inputs.stage }}
          ComponentName: zpos
          PartName: engine
          AwsRegion: ${{ inputs.region }}
        with:
          project-name: ${{inputs.environment}}-zpos-engine
          buildspec-override: |
            version: 0.2
            env:
              shell: bash
            phases:
              pre_build:
                commands:
                  - docker login -u $DockerHubUser -p $DockerHubPassword
              build:
                commands:
                  - echo Migration started on `date`
                  - echo Running migration on apps
                  - DBName=ZPOSEngine
                  - export DB_URL=`aws ssm get-parameter --name /$AuroraEnvironment-bff-db/DatabaseEndpointURL --query Parameter.Value --output text`
                  - echo $DB_URL
                  -
                  - npcoDbUser=`aws secretsmanager get-secret-value --secret-id  $AuroraEnvironment-bff-db/DbUserSecret --query "SecretString" --output text|docker run --rm -i imega/jq --raw-output -c '.username'`
                  - npcoDbSuperUser=`aws secretsmanager get-secret-value --secret-id  $AuroraEnvironment-bff-db/DbSuperUserSecret --query "SecretString" --output text|docker run --rm -i imega/jq --raw-output -c '.username'`
                  - npcoDbSuperPassword=`aws secretsmanager get-secret-value --secret-id  $AuroraEnvironment-bff-db/DbSuperUserSecret --query "SecretString" --output text|docker run --rm -i imega/jq --raw-output -c '.password'`
                  - echo npcoDbUser $npcoDbSuperUser
                  -
                  - cd apps/payments/zpos/engine
                  - dbName=$DBName
                  - aws --version
                  - schema=$EnvironmentName
                  - export domicile='AUS'
                  - export domicileCamel='Aus'
                  - dbschemaLocation='filesystem:/workdir/dbschema'
                  - |
                    if [[ "$AwsRegion" == 'eu-west-2' ]]; then
                      export domicile='GBR'
                      export domicileCamel='Gbr'
                      dbschemaLocation="filesystem:/workdir/dbschema"
                    else
                      dbschemaLocation="filesystem:/workdir/dbschema"
                    fi
                  - |
                    if [[ "$schema" == "dev" ]]; then
                      echo "reparing dev schema"
                      docker run -v `pwd`:/workdir flyway/flyway repair -url=********************************* -locations=$dbschemaLocation -user=$npcoDbSuperUser -password=$npcoDbSuperPassword -schemas="$schema" -placeholders.domicile=$domicile -placeholders.domicileCamel=$domicileCamel
                    fi                  
                  - echo "docker run -v `pwd`:/workdir flyway/flyway migrate -url=********************************* -locations=$dbschemaLocation -user=$npcoDbSuperUser -password=$npcoDbSuperPassword -schemas=$schema"
                  - docker run -v `pwd`:/workdir flyway/flyway migrate -url=********************************* -locations=$dbschemaLocation -user=$npcoDbSuperUser -password=$npcoDbSuperPassword -schemas=$schema -placeholders.domicile=$domicile -placeholders.domicileCamel=$domicileCamel
                  - docker run -e PGPASSWORD=$npcoDbSuperPassword -v `pwd`:/tmp postgres:11 psql "host=$DB_URL sslmode=verify-ca sslrootcert=/tmp/rds-ca-2019-root.pem dbname=$DBName user=$npcoDbSuperUser" -c "GRANT ALL ON ALL TABLES IN SCHEMA $schema to $npcoDbUser"
                  - docker run -e PGPASSWORD=$npcoDbSuperPassword -v `pwd`:/tmp postgres:11 psql "host=$DB_URL sslmode=verify-ca sslrootcert=/tmp/rds-ca-2019-root.pem dbname=$DBName user=$npcoDbSuperUser" -c "GRANT USAGE ON SCHEMA $schema to $npcoDbUser"
                  - docker run -e PGPASSWORD=$npcoDbSuperPassword -v `pwd`:/tmp postgres:11 psql "host=$DB_URL sslmode=verify-ca sslrootcert=/tmp/rds-ca-2019-root.pem dbname=$DBName user=$npcoDbSuperUser" -c "GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA $schema TO $npcoDbUser"
                  -
          env-vars-for-codebuild: |
            DockerHubUser,
            DockerHubPassword,
            AuroraEnvironment,
            STAGE,
            EnvironmentName,
            ComponentName,
            PartName
            AwsRegion

  deploy-api:
    name: deploy api, payment
    runs-on: ubuntu-latest
    timeout-minutes: 60
    needs:
      - pre-deploy
      - deploy-cqrs
      - db-migration
    environment: ${{ inputs.environment }}-zpos-engine
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: api
            sls_config: serverlessApi.ts
          - name: payment
            sls_config: serverlessPayment.ts
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: zpos-engine
          sls_config: ${{ matrix.stack.sls_config}}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/zpos/engine
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}


  engine-system-test:
    name: zpos engine system test
    runs-on: ubuntu-latest
    timeout-minutes: 60
    environment: ${{ inputs.environment }}-zpos-engine
    if: inputs.environment == 'st' || inputs.environment == 'dev'
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-api
    strategy:
      fail-fast: false
      matrix:
        component:
          [
            { name: api, fileName: api.spec.ts },
            { name: projection, fileName: projections.spec.ts },
            { name: payment, fileName: payments.spec.ts },
          ]
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}
      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          JfrogRegistry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          JfrogToken: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          STAGE: ${{ inputs.stage}}
        with:
          project-name: ${{inputs.environment}}-zpos-engine
          buildspec-override: |
            version: 0.2
            env:
              shell: bash
            phases:
              pre_build:
                commands:
                  - env
                  - cat .yarnrc.yml
                  - yarn config set npmPublishRegistry https://$JfrogRegistry
                  - yarn config set npmScopes.npco.npmRegistryServer https://$JfrogRegistry
                  - yarn config set npmScopes.npco.npmAuthToken "$JfrogToken"
                  - yarn config set npmScopes.npco.npmAlwaysAuth true
                  - corepack enable

              build:
                commands:
                  - yarn workspaces focus component-bff zpos-system-tests
                  - yarn nx build zpos-system-tests
                  - cd apps/payments/zpos/system-test
                  - export STAGE=${{ inputs.stage }}
                  - yarn run system-test src/zpos-engine/${{ matrix.component.fileName }}
          env-vars-for-codebuild: |
            JfrogRegistry,
            JfrogToken,
            STAGE

  verify-contract:
    name: verify contract
    runs-on: ubuntu-latest
    if: inputs.region  == 'ap-southeast-2' && (inputs.environment == 'dev' || inputs.environment == 'st') # remove region once migration is done
    needs:
      - pre-deploy
      - deploy-api
    environment: ${{ inputs.environment }}-zpos-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}
      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          JfrogRegistry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          JfrogToken: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          STAGE: ${{ inputs.stage }}
          Environment: ${{ inputs.environment }}
        with:
          project-name: ${{inputs.environment}}-zpos-engine
          compute-type-override: BUILD_GENERAL1_MEDIUM
          buildspec-override: |
            version: 0.2
            env:
              shell: bash
            phases:
              pre_build:
                commands:
                  - env
                  - corepack enable
                  - yarn config set npmPublishRegistry https://$JfrogRegistry
                  - yarn config set npmScopes.npco.npmRegistryServer https://$JfrogRegistry
                  - yarn config set npmScopes.npco.npmAuthToken "$JfrogToken"
                  - yarn config set npmScopes.npco.npmAlwaysAuth true
                  - cat .yarnrc.yml
              build:
                commands:
                  - env
                  - yarn install --immutable
                  - yarn nx build zpos-engine-contract-test
                  - pwd
                  - cd apps/payments/zpos/contract/engine
                  - export PUBLISH_VERIFICATION_RESULT=true
                  - AURORA_STAGE=$Environment
                  - |
                    if [ "$AURORA_STAGE" == 'st' ]; then
                      AURORA_STAGE=dev
                    fi
                  - export COMMIT_ID=$CODEBUILD_RESOLVED_SOURCE_VERSION
                  - export AURORA_STAGE=$AURORA_STAGE
                  - export CONTRACT_CONSUMER_TAGS=$STAGE
                  - yarn pact:test
          env-vars-for-codebuild: |
            JfrogRegistry,
            JfrogToken,
            Environment,
            STAGE

  tag:
    runs-on: ubuntu-latest
    needs:
      - deploy-cqrs
    environment: ${{ inputs.environment }}-zpos-engine
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-zpos-engine
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - pre-deploy
      - tag
      - deploy-cqrs
      - cqrs-system-test
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: zpos-cqrs
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
