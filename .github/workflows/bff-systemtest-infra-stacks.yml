name: bff-systemtest-infra-stacks

on:
  workflow_call:
    inputs:
      region:
        type: string
        default: ${{ vars.SYDNEY_REGION }}

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: st
      app_name: bff-systemtest-infra
      stage: dev
      region: ${{ vars.SYDNEY_REGION }}
      component_name: bff
      part_name: systemtest-infra
      deployment_s3_bucket: false

  deploy-infra:
    name: deploy
    runs-on: ubuntu-latest
    needs: pre-deploy
    environment: st-bff-systemtest-infra
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: Deploy CMS Cqrs
            sls_config: serverlessCmsCqrs.ts
          - name: Deploy Common
            sls_config: serverless.ts
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: st
          app_name: bff-systemtest-infra
          stage: dev
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.stack.sls_config }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          work_dir: 'apps/core/bff-systemtest-infra'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

  deploy-rbac-infra:
    name: deploy mp/dbs/sdk infra
    runs-on: ubuntu-latest
    needs: pre-deploy
    permissions:
      id-token: write
      contents: read
    strategy:
      matrix:
        component:
          [
            { component-name: mp, task_name: deploy-mp },
            { component-name: dbs, task_name: deploy-dbs },
            { component-name: sdk, task_name: deploy-sdk },
          ]
    environment: st-bff-rbac-engine
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy-with-nx
        with:
          environment: st
          stage: dev
          region: ${{ inputs.region }}
          app_name: bff-rbac-engine
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          component_name: ${{ matrix.inputs.component-name }}
          task_name: ${{ matrix.component.task_name }}

  deploy-component-infra:
    name: deploy mp/dbs/crms/sdk infra
    runs-on: ubuntu-latest
    needs: pre-deploy
    permissions:
      id-token: write
      contents: read
    strategy:
      matrix:
        component:
          - name: mp-appsync
            app_name: mp-api
            sls_config: serverlessAppsync.ts
            work_dir: 'apps/bff-api/mp/api'
          - name: mp-dynamodb
            app_name: mp-api
            sls_config: serverlessDynamodb.ts
            work_dir: 'apps/bff-api/mp/api'
          - name: mp-commonLayers
            app_name: mp-api
            sls_config: serverlessCommonLayers.ts
            work_dir: 'apps/bff-api/mp/api'
          - name: dbs-appsync
            app_name: dbs-api
            sls_config: serverlessAppsync.ts
            work_dir: 'apps/bff-api/dbs/api'
          - name: dbs-dynamodb
            app_name: dbs-api
            sls_config: serverlessDynamodb.ts
            work_dir: 'apps/bff-api/dbs/api'
          - name: dbs-commonLayers
            app_name: dbs-api
            sls_config: serverlessCommonLayers.ts
            work_dir: 'apps/bff-api/dbs/api'
          - name: crms-dynamodb
            app_name: crms-engine
            sls_config: serverlessDynamodb.ts
            work_dir: 'apps/bff-api/crms/engine'
          - name: crms-commonLayers
            app_name: crms-engine
            sls_config: serverlessCommonLayers.ts
            work_dir: 'apps/bff-api/crms/engine'
          - name: sis-dynamodb
            app_name: sis-engine
            sls_config: serverlessDynamodb.yml
            work_dir: 'apps/core/sis/engine'
          - name: sis-commonLayers
            app_name: sis-engine
            sls_config: serverlessCommon.yml
            work_dir: 'apps/core/sis/engine'
          - name: sdk-appsync
            app_name: sdk-api
            sls_config: serverlessAppsync.ts
            work_dir: 'apps/payments/sdk/api'
          - name: sdk-dynamodb
            app_name: sdk-api
            sls_config: serverlessDynamodb.ts
            work_dir: 'apps/payments/sdk/api'
          - name: ams-dynamodb
            app_name: ams-engine
            sls_config: serverlessDynamodb.ts
            work_dir: 'apps/core/ams/engine'
            pre_deploy_script: 'bin/stInfraPreDeploy.sh'

    environment: st-${{ matrix.component.app_name }}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: st
          app_name: ${{ matrix.component.app_name }}
          stage: dev
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.component.sls_config }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          work_dir: ${{ matrix.component.work_dir }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          pre_deploy_script: ${{ matrix.component.pre_deploy_script }}

  deploy-component-stacks:
    name: deploy mp/dbs/crms infra
    runs-on: ubuntu-latest
    needs: pre-deploy
    permissions:
      id-token: write
      contents: read
    strategy:
      matrix:
        component:
          - name: mp
            app_name: mp-api
            sls_config: serverlessMp.ts
            work_dir: 'apps/bff-api/entity/api'
          - name: dbs
            app_name: dbs-api
            sls_config: serverlessDbs.yml
            work_dir: 'apps/payments/device/api'
    environment: st-${{ matrix.component.app_name }}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: st
          pre_deploy_script: sh bin/loadenv.sh dev ${{ matrix.component.name }}
          app_name: ${{ matrix.component.app_name }}
          stage: dev
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.component.sls_config }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          work_dir: ${{ matrix.component.work_dir }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

  deploy-zpos-engine-dynamodb:
    name: deploy zpos-engine dynamodb
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-component-infra
    strategy:
      matrix:
        component:
          - name: mp
            app_name: mp-api
    permissions:
      id-token: write
      contents: read
    environment: st-${{ matrix.component.app_name }}-zpos-api
    steps:
      - name: setup env
        run: |
          echo COMPONENT_NAME=${{ matrix.component.name }} >> $GITHUB_ENV
          echo "deploy dynamoDB $COMPONENT_NAME"
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: st
          app_name: bff-zpos-api
          stage: dev
          region: ${{ inputs.region }}
          sls_config: serverlessDynamodb.ts
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          work_dir: 'apps/payments/zpos/api'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

  deploy-crms-engine-appsync:
    name: deploy crms-engine
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-component-infra
    permissions:
      id-token: write
      contents: read
    environment: st-crms-engine
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: st
          app_name: crms-engine
          stage: dev
          region: ${{ inputs.region }}
          sls_config: serverlessAppsync.ts
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          work_dir: 'apps/bff-api/crms/engine'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - pre-deploy
      - deploy-infra
      - deploy-rbac-infra
      - deploy-component-infra
      - deploy-crms-engine-appsync
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: bff-systemtest-infra
          environment: dev
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
