name: bff-settlement-api-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        required: true
        type: string

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: bff
      part_name: settlement-api
      app_name: bff-settlement-api
      sonar_project_key: ${{ vars.SONAR_BFF_SETTLEMENT_API_PROJECT_KEY }}
      build_image: ubuntu-8core
      deployment_s3_bucket: false
      use_workspace_focus: true
      default_workspace_name: '@npco/component-bff-settlement-api'
      additional_workspaces: crms-engine-system-test
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}

  deploy-mocks:
    name: deploy mocks
    runs-on: ubuntu-latest
    needs: pre-deploy
    strategy:
      matrix:
        component:
          - name: mp
            app_name: mp-api
            sls_appsync: serverlessAppsync.ts
            component_dir: apps/bff-api/mp/api
          - name: dbs
            app_name: dbs-api
            sls_appsync: serverlessAppsync.ts
            component_dir: apps/bff-api/dbs/api
          - name: crms
            app_name: crms-engine
            sls_appsync: serverlessAppsync.ts
            component_dir: apps/bff-api/crms/engine
    environment: ${{ inputs.environment }}-${{ matrix.component.app_name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/restore-archive
        if: needs.pre-deploy.outputs.archived_name != '' && needs.pre-deploy.outputs.archived_file_name != ''
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - uses: ./.github/workflows/common/sls-library
        if: inputs.environment == 'st'
        with:
          app_name: ${{ matrix.component.app_name }}
          environment: ${{ inputs.environment }}
          sonar_org: ${{ vars.SONAR_ORGANIZATION }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          run_test: false
          use_workspace_focus: true
          default_workspace_name: '@npco/component-bff-settlement-api'
          additional_workspaces: ${{matrix.component.app_name}} crms-engine-system-test

      - uses: ./.github/workflows/common/aws-setup
        if: inputs.environment == 'st'
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.app_name] }}
          aws_region: ${{ inputs.region }}

      - name: Deploy mock appsync
        if: inputs.environment == 'st'
        run: |
          echo "Deploying appsync"
          cd ${{ matrix.component.component_dir}}
          yarn build:schemas
          yarn sls deploy --stage ${{ inputs.stage }} --region ${{ inputs.region }} --config ${{ matrix.component.sls_appsync }}

      - id: deploy
        name: Deploy to Sydney
        shell: bash
        if: inputs.environment == 'st' && (matrix.component.name == 'dbs' ||  matrix.component.name == 'mp')
        env:
          STAGE: ${{ inputs.stage }}
        run: |
          cd apps/payments/settlement/api/
          aws cloudformation deploy --template-file mockCqrs.yaml --stack-name "${{ inputs.stage }}-${{ matrix.component.name }}-stl-mock-cqrs-iac-eventBridge" \
          --parameter-overrides EventBusName="${{ inputs.stage }}-${{ matrix.component.name }}-stl-mock" \
          --tags STAGE="${{ inputs.stage }}" COMPONENT_NAME="${{ matrix.component.name }}" PART_NAME="cqrs"

          aws cloudformation deploy --template-file mockSqs.yaml --stack-name "${{ inputs.stage }}-${{ matrix.component.name }}-stl-mock-cqrs-iac-sqs" \
          --parameter-overrides SqsName="${{ inputs.stage }}-${{ matrix.component.name }}-stl-mock" \
          --tags STAGE="${{ inputs.stage }}" COMPONENT_NAME="${{ matrix.component.name }}" PART_NAME="cqrs"
          echo deploy to ${{ inputs.stage }}

  deploy-bff-settlement-api:
    name: deploy stacks
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-mocks
    strategy:
      matrix:
        component:
          - name: mp
            app_name: mp-api
            sls_config: serverlessMp.ts
          - name: dbs
            app_name: dbs-api
            sls_config: serverlessDbs.ts
          - name: crms
            app_name: crms-engine
            sls_config: serverlessCrms.ts
    environment: ${{ inputs.environment }}-${{ matrix.component.app_name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/restore-archive
        if: needs.pre-deploy.outputs.archived_name != '' && needs.pre-deploy.outputs.archived_file_name != ''
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          pre_deploy_script: mkdir -p src/fonts && cp ../../../libs/dbs-mp-common/src/fonts/* src/fonts && sh bin/loadenv.sh ${{ inputs.stage }} ${{ matrix.component.name }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: bff-settlement-api
          sls_config: ${{ matrix.component.sls_config }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.app_name] }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/payments/settlement/api/'
          use_workspace_focus: true
          default_workspace_name: '@npco/component-bff-settlement-api'
          additional_workspaces: crms-engine-system-test
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  system-test:
    name: system test
    runs-on: ubuntu-latest
    needs:
      - deploy-bff-settlement-api
      - pre-deploy
    environment: ${{ inputs.environment }}-bff-settlement-api
    if: inputs.environment != 'prod' && inputs.environment != 'staging'
    strategy:
      matrix:
        component:
          - name: deposits
          - name: settlements
          - name: common
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/restore-archive
        if: needs.pre-deploy.outputs.archived_name != '' && needs.pre-deploy.outputs.archived_file_name != ''
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - env:
          AWS_REGION: ${{ inputs.region }}
          STAGE: ${{ inputs.stage }}
          SYDNEY_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          LONDON_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          region: ${{ inputs.region }}
        run: |
          yarn workspaces focus '@npco/component-bff-settlement-api' component-bff crms-engine-system-test
          yarn nx build bff-settlement-api
          cd apps/payments/settlement/api
          STAGE=${{ inputs.stage }} yarn system:test tests/system/${{ matrix.component.name }}

  integration-test:
    name: integration test
    if: inputs.environment == 'dev'
    needs:
      - system-test
    uses: ./.github/workflows/bff-settlement-api-integration-tests.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}

  tag:
    runs-on: ubuntu-latest
    needs: deploy-bff-settlement-api
    environment: ${{ inputs.environment }}-bff-settlement-api
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-bff-settlement-api
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - pre-deploy
      - deploy-bff-settlement-api
      - system-test
      - integration-test
      - tag
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: bff-settlement-api
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
