name: bff-migration-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        required: true
        type: string

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      app_name: bff-migration
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: bff
      part_name: migration
      sonar_project_key: ${{ vars.SONAR_BFF_MIGRATION_PROJECT_KEY }}
      deployment_s3_bucket: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      use_workspace_focus: true
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}

  deploy:
    name: deploy
    runs-on: ubuntu-latest
    needs: pre-deploy
    environment: ${{ inputs.environment }}-bff-migration
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          pre_deploy_script: sh bin/loadenv.sh ${{ inputs.stage }}
          app_name: bff-migration
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverless.yml
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/core/bff-migration/apps'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  system-test:
    name: system test
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy
    environment: ${{ inputs.environment }}-bff-migration
    if: inputs.environment == 'dev'
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - run: |
          yarn workspaces focus component-bff bff-migration-system-test
          cd apps/core/bff-migration/system-test
          STAGE=${{ inputs.stage }} yarn system:test

  tag:
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-bff-migration
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - pre-deploy
      - deploy
      - tag
      - system-test
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: bff-migration
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
