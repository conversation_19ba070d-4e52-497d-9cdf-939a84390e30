name: bff-banking-api-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        type: string
        required: true

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: bff
      part_name: banking-api
      sonar_project_key: ${{ vars.SONAR_BFF_BANKING_API_PROJECT_KEY }}
      build_image: ubuntu-8core
      app_name: bff-banking-api
      use_workspace_focus: true
  
  predeploy-bff-stacks:
    name: predeploy stacks
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
    strategy:
      matrix:
        component:
          - name: mp
            app_name: mp-api
            component_dir: apps/bff-api/mp/api
          - name: dbs
            app_name: dbs-api
            component_dir: apps/bff-api/dbs/api
          - name: crms
            app_name: crms-engine
            component_dir: apps/bff-api/crms/engine
    environment: ${{ inputs.environment }}-${{ matrix.component.app_name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-library
        if: inputs.environment == 'st'
        with:
          app_name: bff-banking-api
          environment: ${{ inputs.environment }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          run_test: false
          use_workspace_focus: true
          additional_workspaces: ${{matrix.component.app_name}}
      - uses: ./.github/workflows/common/aws-setup
        if: inputs.environment == 'st'
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          aws_region: ${{ inputs.region }}

      - name: Deploy mock appsync
        if: inputs.environment == 'st'
        run: |
          echo "Deploying appsync"
          cd ${{ matrix.component.component_dir}}
          yarn build:schemas
          yarn sls deploy --stage ${{ inputs.stage }} --region ${{ inputs.region }} --config serverlessAppsync.ts
    
  deploy-rematerialisation-sqs:
    name: deploy rematerialisation sqs component
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - predeploy-bff-stacks
    strategy:
      matrix:
        component:
          [
            { name: mp-api, config: serverlessMpDcaRematerialisationSqs },
            { name: crms-engine, config: serverlessCrmsDcaRematerialisationSqs },
          ]
    environment: ${{ inputs.environment }}-${{ matrix.component.name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/restore-archive
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          app_name: bff-banking-api
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.component.config }}.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.name] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/transactionalAccount/banking-api/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}

  deploy-banking-api:
    name: deploy component
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-rematerialisation-sqs
    strategy:
      matrix:
        component:
          [
            { name: mp-api, config: serverlessMp },
            { name: mp-api, config: serverlessMpSavingsAccount },
            { name: mp-api, config: serverlessMpInterestSummary },
            { name: mp-api, config: serverlessMpTransferRemittance },
            { name: mp-api, config: serverlessMpScheduledTransfers },
            { name: mp-api, config: serverlessMpCardLogo },
            { name: mp-api, config: serverlessMpDebitCardAccount },
            { name: crms-engine, config: serverlessCrms },
            { name: crms-engine, config: serverlessCrmsInterestSummary },
            { name: crms-engine, config: serverlessCrmsTransferRemittance },
            { name: dbs-api, config: serverlessDbs },
          ]
    environment: ${{ inputs.environment }}-${{ matrix.component.name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/restore-archive
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: bff-banking-api
          sls_config: ${{ matrix.component.config }}.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.name] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/transactionalAccount/banking-api/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}

  deploy-banking-api-resolvers:
    name: deploy resolvers
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-rematerialisation-sqs
      - deploy-banking-api
    strategy:
      matrix:
        component:
          [{ name: mp-api, config: serverlessMpResolvers }, { name: crms-engine, config: serverlessCrmsResolvers }]
    environment: ${{ inputs.environment }}-${{ matrix.component.name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/restore-archive
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - uses: ./.github/workflows/common/sls-deploy
        if: inputs.environment != 'st'
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: bff-banking-api
          sls_config: ${{ matrix.component.config }}.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.name] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/transactionalAccount/banking-api/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}

  system-test:
    name: system test
    runs-on: ubuntu-latest
    if: inputs.environment != 'prod' && inputs.environment != 'staging'
    needs:
      - pre-deploy
      - deploy-banking-api
      - deploy-banking-api-resolvers
    environment: ${{ inputs.environment }}-bff-banking-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - name: Setup JFrog
        shell: bash
        run: |
          corepack enable
          yarn config set npmPublishRegistry https://${{ secrets.NPCO_JFROG_REGISTRY }}
          yarn config set npmScopes.npco.npmRegistryServer https://${{ secrets.NPCO_JFROG_REGISTRY }}
          yarn config set npmScopes.npco.npmAuthToken "${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}"
          yarn config set npmScopes.npco.npmAlwaysAuth true
          cat .yarnrc.yml

      - uses: ./.github/workflows/common/restore-archive
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - run: |
          export region=${{ inputs.region }}
          export AWS_REGION=${{ inputs.region }}
          yarn workspaces focus bff-banking-api-system-testing component-bff bff-banking-api
          yarn nx build bff-banking-api-system-testing
          STAGE=${{ inputs.stage }} AWS_REGION=${{ inputs.region }}  yarn nx run bff-banking-api-system-testing:system-test apps/transactionalAccount/banking-api/system-testing/
  banking-integration-test:
    secrets: inherit
    needs: system-test
    uses: ./.github/workflows/bff-banking-api-integration.yml
    if: inputs.environment == 'dev' && inputs.region == vars.SYDNEY_REGION
    with:
      environment: ${{ inputs.environment }}
      region: ${{ inputs.region }}
  tag:
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-banking-api
      - deploy-banking-api-resolvers
    environment: ${{ inputs.environment }}-bff-banking-api
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-bff-banking-api
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - pre-deploy
      - predeploy-bff-stacks
      - deploy-banking-api
      - deploy-banking-api-resolvers
      - system-test
      - banking-integration-test
      - tag
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: bff-banking-api
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
          region: ${{ inputs.region }}
