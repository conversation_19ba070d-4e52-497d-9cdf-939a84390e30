name: mp-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        required: true
        type: string

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      app_name: mp-api
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      sonar_project_key: ${{ vars.SONAR_MP_API_PROJECT_KEY }}
      deploy_cqrs: true
      deployment_s3_bucket: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      component_name: mp
      part_name: api
      build_image: ubuntu-8core
      use_workspace_focus: true
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}

  # deploy cqrs
  deploy-cqrs:
    needs:
      - pre-deploy
    uses: ./.github/workflows/component-cqrs.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: mp
      sonar_project_key: ${{ vars.SONAR_CQRS_COMMON_PROJECT_KEY }}
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      work_dir: apps/bff-api/mp/cqrs/cqrs-common
      use_eventbus_projection: true
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['cqrs'] }}
      use_sqs_projection: true

  # deploy api
  deploy-common-layer:
    name: Deploy common layer
    runs-on: ubuntu-latest
    timeout-minutes: 60
    environment: ${{ inputs.environment }}-mp-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessCommonLayers.ts
          app_name: mp-api
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/bff-api/mp/api'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-dynamodb-common:
    name: deploy commonLayers, dynamodb, sqs
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-common-layer
    environment: ${{ inputs.environment }}-mp-api
    timeout-minutes: 60
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: dynamodb
            sls_config: serverlessDynamodb.ts
          - name: Appsync
            sls_config: serverlessAppsync.ts
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.stack.sls_config }}
          app_name: mp-api
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/bff-api/mp/api'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          pre_deploy_script: yarn build:schemas
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  # Dependencies on AppSync
  deploy-assets:
    name: deploy assets
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-dynamodb-common
    environment: ${{ inputs.environment }}-mp-api
    timeout-minutes: 60
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: Assets
            sls_config: serverlessAssets.ts
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.stack.sls_config }}
          app_name: mp-api
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/bff-api/mp/api'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-merged-api:
    name: deploy apps
    runs-on: ubuntu-latest
    timeout-minutes: 60
    needs:
      - pre-deploy
      - deploy-assets
    environment: ${{ inputs.environment }}-mp-api
    if: false
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: mp-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessMergedApi.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/bff-api/mp/api'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-apps:
    name: deploy apps
    runs-on: ubuntu-latest
    timeout-minutes: 60
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-assets
    environment: ${{ inputs.environment }}-mp-api
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: Ecommerce
            sls_config: serverlessEcommerce.ts
          - name: Logo
            sls_config: serverlessLogo.ts
          - name: Sim
            sls_config: serverlessSim.ts
          - name: Richdata
            sls_config: serverlessRichdata.ts
          - name: Billingaccount
            sls_config: serverlessBillingaccount.ts
          - name: Warmup
            sls_config: serverlessWarmup.ts
          - name: Health
            sls_config: serverlessHealth.ts
          - name: Externalapi
            sls_config: serverlessExternalapi.ts
          - name: PosInterface
            sls_config: serverlessPosInterface.ts
          - name: CnpEcommerce
            sls_config: serverlessCnpEcommerce.ts
          - name: ProjectionSqs
            sls_config: serverlessProjectionSqs.ts
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: mp-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.stack.sls_config }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/bff-api/mp/api'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          pre_deploy_script: mkdir -p src/fonts && cp ../../../libs/dbs-mp-common/src/fonts/* src/fonts
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  list-system-test-spec-files-matrix:
    runs-on: ubuntu-latest
    if: inputs.environment == 'st' || inputs.environment == 'dev'
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - uses: actions/checkout@v4
      - id: set-matrix
        run: |
          cd apps/bff-api/mp/api/tests/system
          echo "matrix=$(ls *.spec.ts | jq -R -s -c 'split("\n")[:-1]')" >> $GITHUB_OUTPUT

  mp-api-system-test:
    name: mp api system test
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: inputs.environment == 'st' || inputs.environment == 'dev'
    needs:
      - pre-deploy
      - deploy-apps
      - deploy-cqrs
      - list-system-test-spec-files-matrix
    environment: ${{ inputs.environment }}-mp-api
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        specFile: ${{fromJson(needs.list-system-test-spec-files-matrix.outputs.matrix)}}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - uses: ./.github/workflows/common/restore-archive
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      #allow for automatic build retries for issue with serverless concurrent running system test
      - run: |
          yarn workspaces focus component-bff mp-api
          export region=${{ inputs.region }}
          export AWS_REGION=${{ inputs.region }}
          yarn nx build mp-api
          cd apps/bff-api/mp/api
          STAGE=${{ inputs.stage }} AWS_REGION=${{ inputs.region }} yarn system:test tests/system/${{ matrix.specFile }}

  tag:
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-apps
      - deploy-cqrs
    environment: ${{ inputs.environment }}-mp-api
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-mp-api
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - pre-deploy
      - deploy-apps
      - deploy-dynamodb-common
      - deploy-assets
      - deploy-cqrs
      - mp-api-system-test
      - tag
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: mp-api
          region: ${{ inputs.region }}
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
