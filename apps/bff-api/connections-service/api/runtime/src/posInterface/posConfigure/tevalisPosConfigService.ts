import type { ConnectionPosInterfaceConfigCreateRequestDto } from '@npco/component-dto-connection/dist/connectionPosInterfaceConfigCreateRequestDto';
import { PosConnectionStatus } from '@npco/component-dto-connection/dist/types';
import { ConnectionType, ConnectionTypeParams } from '@npco/component-dto-core/dist/types';

import { PosConfigBaseService } from '../posConfigBaseService';
import type { TevalisInterfaceRecord, PosConfiguration } from '../types';

export class TevalisPosConfigService extends PosConfigBaseService<any> {
  protected typeParam = ConnectionTypeParams.TEVALIS;

  protected provider = ConnectionType.TEVALIS_POS;

  getRequestDto(entityUuid: string, config: PosConfiguration): ConnectionPosInterfaceConfigCreateRequestDto {
    return {
      entityUuid,
      credentials: {
        companyId: config.companyId,
        guid: config.guid,
        guid2: config.guid2,
      },
      venues: config.venues,
    };
  }

  protected override convertRecordToPosConfiguration(record: TevalisInterfaceRecord): PosConfiguration {
    const posConfiguration: PosConfiguration = {
      companyId: record.credentials.companyId,
      guid: record.credentials.guid,
      guid2: record.credentials.guid2,
      venues: record.venues,
    };
    return posConfiguration;
  }

  protected getReconnectDto(
    entityUuid: string,
    config: PosConfiguration,
  ): ConnectionPosInterfaceConfigCreateRequestDto {
    return { ...this.getRequestDto(entityUuid, config), status: PosConnectionStatus.CONNECTED };
  }

  protected getResponse(_amsResponse: any, config: PosConfiguration): PosConfiguration {
    return config;
  }
}
