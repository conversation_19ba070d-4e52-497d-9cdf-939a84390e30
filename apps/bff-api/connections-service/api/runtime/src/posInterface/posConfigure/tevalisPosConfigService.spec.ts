import { BffDynamoDbClient } from '@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient';
import type { ConnectionPosInterfaceConfigCreatedEventDto } from '@npco/component-dto-connection/dist/connectionPosInterfaceConfigCreatedEventDto';
import { PosConnectionStatus } from '@npco/component-dto-connection/dist/types';
import { ConnectionType, ConnectionTypeParams } from '@npco/component-dto-core/dist/types';

import { randomUUID as uuidv4 } from 'crypto';
import { deepEqual, instance, mock, verify, anything, when } from 'ts-mockito';

import { BffEnvironmentService } from '../../config/envService';
import { AmsApiService } from '../../framework/api/amsApiService';
import { PosInterfaceDb } from '../posInterfaceDb';
import type { TevalisPosConfigurationInput } from '../types';

import { TevalisPosConfigService } from './tevalisPosConfigService';

jest.mock('axios');
jest.mock('aws-xray-sdk');
jest.mock('../../config/envService');
jest.mock('../../framework/ssm/ssmService');
jest.mock('@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient');

describe('tevalisPosConfigService test suites', () => {
  let service: TevalisPosConfigService;
  let documentClient: BffDynamoDbClient;
  let env: BffEnvironmentService;
  let amsApiService: AmsApiService<any, any>;
  let posInterfaceDb: PosInterfaceDb;

  const createPosInterfaceConfig = async (update = {}) => {
    const companyId = uuidv4();
    const posInterfaceConfig: ConnectionPosInterfaceConfigCreatedEventDto = {
      entityUuid: uuidv4(),
      connectionUuid: uuidv4(),
      provider: ConnectionType.TEVALIS_POS,
      status: PosConnectionStatus.CONNECTED,
      credentials: {
        companyId,
        guid: uuidv4(),
        guid2: uuidv4(),
      },
      organisationId: companyId,
      venues: [
        {
          id: 'mockVenueId',
          name: 'mockVenueName',
          locations: [
            {
              id: 'mockLocationId',
              name: 'mockLocationName',
              number: 'mockTableNumber',
            },
          ],
        },
      ],
      ...update,
    };
    await posInterfaceDb.savePosInterfaceProjection(posInterfaceConfig);
    return posInterfaceConfig;
  };

  beforeEach(() => {
    env = new BffEnvironmentService({} as any);
    documentClient = new BffDynamoDbClient(env);
    posInterfaceDb = new PosInterfaceDb(env, documentClient);
    amsApiService = mock(AmsApiService);
    service = new TevalisPosConfigService(instance(amsApiService), posInterfaceDb);
  });

  it('should call ams to create configure when configure not exist', async () => {
    when(amsApiService.post(anything(), anything(), anything())).thenResolve([
      {
        id: uuidv4(),
      },
    ]);

    const entityUuid = uuidv4();
    const companyId = uuidv4();
    const guid = uuidv4();
    const guid2 = uuidv4();
    const venues = [
      {
        id: 'mockVenueId',
        name: 'mockVenueName',
        locations: [],
      },
    ];
    const config: TevalisPosConfigurationInput = {
      companyId,
      guid,
      guid2,
      venues,
    };
    const response = await service.configurePos(entityUuid, config);
    expect(response).toEqual(config);

    verify(
      amsApiService.post(
        `${ConnectionTypeParams.TEVALIS}/connect`,
        deepEqual({ credentials: { companyId, guid, guid2 }, entityUuid, venues }),
        deepEqual({ errorMessage: `Error pushing ${ConnectionTypeParams.TEVALIS} configuration create` }),
      ),
    ).called();
  });

  it('should call ams to update configure if exist', async () => {
    when(amsApiService.patch(anything(), anything(), anything())).thenResolve([
      {
        id: uuidv4(),
      },
    ]);

    const entityUuid = uuidv4();
    const companyId = uuidv4();
    const guid = uuidv4();
    const guid2 = uuidv4();

    const created = await createPosInterfaceConfig({
      entityUuid,
      credentials: {
        companyId,
        guid,
        guid2,
      },
    });

    const venues = [
      {
        id: 'mockVenueId',
        name: 'mockVenueName',
        locations: [],
      },
    ];
    const config = {
      companyId,
      guid,
      guid2,
      venues,
    };
    const response = await service.configurePos(entityUuid, config);
    expect(response).toEqual(config);

    verify(
      amsApiService.patch(
        `${ConnectionTypeParams.TEVALIS}/connect/${created.connectionUuid}`,
        deepEqual({ credentials: { companyId, guid, guid2 }, entityUuid, venues, status: 'CONNECTED' }),
        deepEqual({
          aggregateId: created.connectionUuid,
          errorMessage: `Error pushing ${ConnectionTypeParams.TEVALIS} configuration update`,
        }),
      ),
    ).called();
  });

  it('should be able to get pos configuration', async () => {
    const entityUuid = uuidv4();
    const companyId = uuidv4();
    const guid = uuidv4();
    const guid2 = uuidv4();

    const created = await createPosInterfaceConfig({
      entityUuid,
      credentials: {
        companyId,
        guid,
        guid2,
      },
    });
    const response = await service.getPosConfiguration(entityUuid);
    expect(response).toEqual({
      ...created.credentials,
      venues: created.venues,
    });
  });
});
