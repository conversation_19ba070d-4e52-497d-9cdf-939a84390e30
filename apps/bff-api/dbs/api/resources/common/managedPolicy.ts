import { Arn } from '@npco/component-bff-serverless';
import { getRemoteAccountId, getRemoteRegion } from '@npco/component-bff-serverless/dist/param/util';

const region = process.env.AWS_REGION;
const remoteRegion = getRemoteRegion(region);
const remoteAccountId = getRemoteAccountId(region);

export const managedPolicy = {
  Resources: {
    xrayPolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: [
                'xray:PutTraceSegments',
                'xray:PutTelemetryRecords',
                'xray:GetSamplingRules',
                'xray:GetSamplingTargets',
                'xray:GetSamplingStatisticSummaries',
              ],
              Resource: '*',
            },
          ],
        },
      },
    },
    lambdaVpcPolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['ec2:CreateNetworkInterface', 'ec2:DescribeNetworkInterfaces', 'ec2:DeleteNetworkInterface'],
              Resource: '*',
            },
          ],
        },
      },
    },
    sessionCacheTableDBPolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['dynamodb:Query', 'dynamodb:UpdateItem', 'dynamodb:PutItem'],
              Resource: [
                Arn.dynamodb.table('${self:custom.cacheTableName}'),
                Arn.dynamodb.gsi('${self:custom.cacheTableName}', '${self:custom.accessTokenGsi}'),
                Arn.dynamodb.gsi('${self:custom.cacheTableName}', '${self:custom.entityCacheGsi}'),
              ],
            },
            {
              Effect: 'Allow',
              Action: ['dynamodb:Query'],
              Resource: [Arn.dynamodb.table('${self:custom.deviceTableName}')],
            },
          ],
        },
      },
    },
    deviceTableWriteItemRolePolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['dynamodb:PutItem', 'dynamodb:UpdateItem'],
              Resource: [Arn.dynamodb.table('${self:custom.deviceTableName}')],
            },
          ],
        },
      },
    },
    deviceTableQueryRolePolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['dynamodb:Query', 'dynamodb:BatchGetItem', 'dynamodb:Get*'],
              Resource: [
                Arn.dynamodb.table('${self:custom.deviceTableName}'),
                Arn.dynamodb.gsi('${self:custom.deviceTableName}', '${self:custom.entityGsi}'),
                Arn.dynamodb.gsi('${self:custom.deviceTableName}', '${self:custom.shortIdGsi}'),
              ],
            },
          ],
        },
      },
    },
    crossAccountInvocationPolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['lambda:InvokeFunction'],
              Resource: [`arn:aws:lambda:${remoteRegion}:${remoteAccountId}:function:\${self:provider.stackName}*`],
            },
          ],
        },
      },
    },
  },
  Outputs: {
    deviceTableQueryRolePolicyArn: {
      Value: { Ref: 'deviceTableQueryRolePolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-deviceTableQueryRolePolicyArn']] } },
    },
    deviceTableWriteItemRolePolicyArn: {
      Value: { Ref: 'deviceTableWriteItemRolePolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-deviceTableWriteItemRolePolicyArn']] } },
    },
    sessionCacheTableDBPolicyArn: {
      Value: { Ref: 'sessionCacheTableDBPolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-sessionCacheTableDBPolicyArn']] } },
    },
    lambdaVpcPolicyArn: {
      Value: { Ref: 'lambdaVpcPolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-lambdaVpcPolicyArn']] } },
    },
    xrayPolicyArn: {
      Value: { Ref: 'xrayPolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-xrayPolicyArn']] } },
    },
    crossAccountInvocationPolicyArn: {
      Value: { Ref: 'crossAccountInvocationPolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-crossAccountInvocationPolicyArn']] } },
    },
  },
};
