type Mutation {
  identitySignUp(input: IdentitySignUpInput!): Identity @aws_api_key
  identityLogin(input: IdentityLoginInput!): Identity @aws_api_key
  identityCustomerLogin(input: IdentityLoginInput!): Identity @aws_api_key
  checkAccessToken(input: CheckAccessTokenInput!): Identity @aws_api_key
  identityForgotPassword(email: String!): Boolean @aws_api_key
  identityChangePassword(input: IdentityChangePasswordInput!): Boolean
  identityPhoneRegister(deviceUuid: ID!, phone: String!): PhoneRegistration!
  identityPhoneVerify(deviceUuid: ID!, code: String!): PhoneVerification!
  identityForcedLogoff(deviceUuid: ID!, reason: String!): ForcedLogoff @aws_iam
  identityLogoff(deviceUuid: ID!, entityUuid: ID): Boolean
}

type Subscription {
  identityForcedLogoff(deviceUuid: ID!, entityUuid: ID): ForcedLogoff
    @aws_api_key
    @aws_iam
    @aws_subscribe(mutations: ["identityForcedLogoff"])
}

input IdentitySignUpInput {
  deviceUuid: ID!
  email: String!
  password: String!
}

input IdentityLoginInput {
  deviceUuid: ID!
  email: String!
  password: String!
}

input CheckAccessTokenInput {
  deviceUuid: ID!
  accessToken: String!
  refreshToken: String!
}

input IdentityChangePasswordInput {
  email: String!
  password: String!
}

type PhoneRegistration {
  codeSent: Boolean!
  validUntil: String!
}

type PhoneVerification {
  codeVerified: Boolean!
}

type ForcedLogoff @aws_api_key @aws_iam {
  reason: String!
  deviceUuid: ID!
}

type Identity @aws_api_key {
  valid: Boolean
  accessToken: String
  refreshToken: String
  idToken: String
}
