onSecureDocumentUploadedHandler:
  handler: src/lambda/index.onSecureDocumentUploadedHandler
  name: ${self:provider.stackName}-onSecureDocumentUploadedHandler
  tracing: true

  role: onSecureDocUploadedRole
  environment:
    COMPONENT_TABLE: ${self:provider.entityTableName}
    S3_ENTITY_DOCUMENT_UPLOADS: ${self:custom.documentUploadBucketNameV2}
    CQRS_COMMAND_HANDLER: ${self:provider.mpCqrsCommandHandler}
    S3_DOCUMENT_UPLOADS: ${self:custom.merchantPortalDocumentUploadBucketName}

onDocUploadedHandler:
  handler: src/lambda/index.onDocUploadedHandler
  name: ${self:provider.stackName}-onDocUploadedHandler
  tracing: true

  role: onDocUploadedRole
  environment:
    COMPONENT_TABLE: ${self:provider.entityTableName}
    S3_ENTITY_DOCUMENT_UPLOADS: ${self:custom.documentUploadBucketNameV2}
    S3_DOCUMENT_UPLOADS: ${self:custom.merchantPortalDocumentUploadBucketName}
    CQRS_COMMAND_HANDLER: ${self:provider.mpCqrsCommandHandler}

getDocumentUploadUrlsHandler:
  handler: src/lambda/index.getDocumentUploadUrlsHandler
  name: ${self:provider.stackName}-getDocumentUploadUrlsHandler
  tracing: true

  role: getDocumentUploadUrlsRole
  environment:
    COMPONENT_TABLE: ${self:provider.entityTableName}
    SESSION_CACHE_TABLE: ${self:provider.sessionCacheTableName}
    AUTH0_TENANT: ${self:provider.auth0Tenant}
    AUTH0_CLIENT_ID: ${self:provider.auth0ClientId}
    AUTH0_CLIENT_SECRET: ${self:provider.auth0ClientSecret}
    S3_DOCUMENT_UPLOADS: ${self:custom.merchantPortalDocumentUploadBucketName}
    S3_ENTITY_DOCUMENT_UPLOADS: ${self:custom.documentUploadBucketNameV2}

onReceiptDocUploadedHandler:
  handler: src/lambda/receiptDocLambda.onReceiptDocUploadedHandler
  name: ${self:provider.stackName}-onReceiptDocUploadedHandler
  timeout: 900
  memorySize: 10240
  ephemeralStorageSize: 2048
  tracing: true
  role: onReceiptDocumentUploadedRole
  environment:
    CMS_API_ENDPOINT: ${self:custom.cmsEndpoint}
    COMPONENT_TABLE: ${self:provider.entityTableName}
    S3_RECEIPT_DOCUMENT_UPLOADS: ${self:custom.receiptDocumentUploadBucketName}
    S3_RECEIPT_DOCUMENT_PROCESSED: ${self:custom.receiptDocumentDownloadBucketName}
    AMS_API_ENDPOINT: ${self:provider.amsApiEndpoint}
    AMS_API_ENDPOINT_VERSION: ${self:provider.amsApiEndpointVersion}

onReceiptLogoUploadedHandler:
  handler: src/lambda/logoUploadLambda.onReceiptLogoUploadedHandler
  name: ${self:provider.stackName}-onReceiptLogoUploadedHandler
  memorySize: 10240
  timeout: 900
  ephemeralStorageSize: 2048
  tracing: true
  role: onReceiptLogoUploadedRole
  destinations:
    onFailure:
      type: sqs
      arn: ${self:custom.onReceiptLogoUploadedHandlerDlqArn}
  environment:
    COMPONENT_TABLE: ${self:provider.entityTableName}
    S3_RECEIPT_LOGO_UPLOADS: ${self:custom.receiptLogoUploadBucketNameV2}
    S3_RECEIPT_LOGO_PROCESSED: ${self:custom.receiptLogoProcessedBucket}
    PRINTED_RECEIPT_LOGO_SIZE: ${env:PRINTED_RECEIPT_LOGO_SIZE}
    RECEIPT_LOGO_BASE_URL: ${self:custom.receiptLogoBaseUrl}
    SCREENSAVER_LOGO_BASE_URL: ${self:custom.screensaverLogoBaseUrl}
    AMS_API_ENDPOINT: ${self:provider.amsApiEndpoint}
    AMS_API_ENDPOINT_VERSION: ${self:provider.amsApiEndpointVersion}

onCardLogoUploadedHandler:
  handler: src/lambda/logoUploadLambda.onCardLogoUploadedHandler
  name: ${self:provider.stackName}-onCardLogoUploadedHandler
  memorySize: 1024
  timeout: 180
  ephemeralStorageSize: 1024
  tracing: true
  role: onCardLogoUploadedRole
  events:
    - s3:
        bucket: ${self:custom.cardLogoUploadBucket}
        event: s3:ObjectCreated:*
        rules:
          - prefix: ${self:custom.cardLogoOnUploadS3Prefix.${opt:stage}, self:custom.cardLogoOnUploadS3Prefix.systemTest}
        existing: true
  destinations:
    onFailure:
      type: sqs
      arn: ${self:custom.onCardLogoUploadedHandlerDlqArn}
  environment:
    COMPONENT_TABLE: ${self:provider.entityTableName}
    S3_CARD_LOGO_UPLOADS: ${self:custom.cardLogoUploadBucket}
    S3_CARD_LOGO_PROCESSED: ${self:custom.cardLogoProcessedBucket}
    CARD_LOGO_SIZE: ${env:CARD_LOGO_SIZE}
    CARD_LOGO_MONO_PRINT_SIZE: ${env:CARD_LOGO_MONO_PRINT_SIZE}
    CARD_LOGO_BASE_URL: ${self:custom.cardLogoBaseUrl}
    APP_SYNC_ENDPOINT: ${self:provider.appSyncEndpoint}
    IAM_USER_KEY: ${self:custom.iamUserKey}
    IAM_USER_SECRET: ${self:custom.iamUserSecret}
