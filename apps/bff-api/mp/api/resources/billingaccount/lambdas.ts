import { Action } from '@npco/component-bff-serverless';

export const lambdas = {
  getBillingAccountHandler: {
    handler: 'src/lambda/index.getBillingAccountHandler',
    name: 'getBillingAccountHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
      AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    },
    appsync: { fieldName: 'getBillingAccount', typeName: 'Query' },
    policy: {
      managed: [
        'sessionCacheTableDBPolicyArn',
        'entityTableQueryRolePolicyArn',
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
    },
  },
  selectBillingAccountHandler: {
    handler: 'src/lambda/index.selectBillingAccountHandler',
    name: 'selectBillingAccountHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      BILLING_API_ENDPOINT: '${self:custom.env.billingApiEndpoint}',
      AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
      AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    },
    appsync: {
      fieldName: 'selectBillingAccount',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        'sessionCacheTableDBPolicyArn',
        'entityTableQueryRolePolicyArn',
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
    },
    timeout: 30,
  },
  billingAccountProjectionHandler: {
    handler: 'src/lambda/index.billingAccountProjectionHandler',
    name: 'billingAccountProjectionHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    },
    policy: {
      managed: ['entityTableQueryRolePolicyArn', 'entityTableWriteItemRolePolicyArn'],
      inline: {
        customerProjectionLogPolicy: [
          { actions: [Action.sqs.SendMessage], resources: ['${self:custom.mpCqrsProjectionDLQArn}'] },
        ],
      },
    },
    deadLetter: { targetArn: '${self:custom.mpCqrsProjectionDLQArn}' },
  },
};
