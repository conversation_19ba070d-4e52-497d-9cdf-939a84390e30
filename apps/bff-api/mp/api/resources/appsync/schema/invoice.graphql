type Mutation {
  createInvoice(input: CreateInvoiceInput!, entityUuid: ID): Invoice!
  updateInvoice(input: UpdateInvoiceInput!, entityUuid: ID): Invoice!
  updateInvoiceNotes(input: UpdatedInvoiceNotesInput, entityUuid: ID): Invoice!
  sendInvoice(referenceNumber: String!, entityUuid: ID): Invoice!
  sendReminder(input: SendReminderInput!, entityUuid: ID): Invoice!
  skipReminder(input: SkipReminderInput!, entityUuid: ID): Invoice!
  markInvoiceAsPaid(referenceNumber: String!, entityUuid: ID): Invoice!
  recordPayment(input: InvoiceRecordPaymentInput!, entityUuid: ID): Invoice!
  cancelInvoice(referenceNumber: String!, entityUuid: ID): Invoice!
  deleteInvoice(referenceNumber: String!, entityUuid: ID): Invoice!
  saveAndSendInvoice(input: SaveAndSendInvoiceInput!, entityUuid: ID): Invoice!
  publishInvoiceListUpdate(input: InvoiceInput!): Invoice! @aws_iam
}

type Query {
  getInvoice(referenceNumber: String!, entityUuid: ID): Invoice!
  getInvoices(input: GetInvoicesInput!, entityUuid: ID): InvoiceConnection!
  getInvoicesByContact(input: GetInvoicesByContactInput!, entityUuid: ID): InvoiceConnection!
  getInvoicePdf(referenceNumber: String!, entityUuid: ID): InvoicePdfDownload!
  getInvoiceDraftContactUsage(contactUuid: ID!, entityUuid: ID): Int!
  getInvoicePreview(input: PreviewInvoiceInput!, entityUuid: ID): InvoicePdfDownload!
  getCustomisationInvoicePreview(input: CustomisationPreviewInvoiceInput!, entityUuid: ID): InvoicePdfDownload!
  getInvoiceSettingsLogoUploadUrl(entityUuid: ID): String!
  getRecentlyUsedLineItems(entityUuid: ID): [InvoiceItem!]
  getNumberOfAffectedInvoices(entityUuid: ID, categoryUuid: ID!): Int
}

type Subscription {
  onInvoiceListUpdate(entityUuid: ID!, status: InvoiceStatus, id: ID): Invoice
    @aws_oidc
    @aws_subscribe(mutations: ["publishInvoiceListUpdate"])
}

type InvoiceConnection {
  invoices: [Invoice!]
  nextToken: String
}

type InvoicePdfDownload {
  downloadLink: String!
  expire: AWSDateTime!
}

type InvoiceSMS {
  enabled: Boolean
  payerContactPhoneNumber: String
}

input InvoiceSMSInput {
  enabled: Boolean
  payerContactPhoneNumber: String
}

input InvoiceSendScheduleInput {
  enabled: Boolean!
  sendDate: AWSDateTime!
}

type InvoiceSendSchedule @aws_oidc @aws_iam {
  enabled: Boolean!
  sendDate: AWSDateTime!
}

input UpdatedInvoiceNotesInput {
  referenceNumber: String!
  notes: String
}

input GetInvoicesSortInput {
  columnName: String
  ascending: Boolean
  value: String
}

input GetInvoicesStatusFilterInput {
  values: [InvoiceStatus!]
}

input GetInvoicesDateFilterInput {
  columnName: String
  startDate: Int
  endDate: Int
}
input GetInvoicesAmountFilterInput {
  columnName: String
  from: String
  to: String
}

input GetInvoicesFilterInput {
  statusFilter: GetInvoicesStatusFilterInput
  dateFilter: GetInvoicesDateFilterInput
  amountFilter: GetInvoicesAmountFilterInput
  textSearchFilter: String
  payerContactUuidFilter: String
}

input GetInvoicesInput {
  limit: Int
  sort: GetInvoicesSortInput
  filter: GetInvoicesFilterInput
  nextToken: String
}

input GetInvoicesByContactInput {
  limit: Int
  contactUuid: ID!
  nextToken: String
}

type InvoiceCatalogModifierSet {
  id: ID!
  entityUuid: ID!
  invoicesEnabled: Boolean!
  selectionRequired: Boolean!
  name: String!
  modifiers: [InvoiceCatalogModifierSnapshot!]
  status: RevisionStatus!
  lastRevisionTime: AWSDateTime!
}

type InvoiceCatalogModifier {
  id: ID!
  entityUuid: ID!
  catalogModifierListUuid: ID!
  name: String!
  price: String
  currency: String
  image: String
}

type InvoiceCatalogModifierSnapshot {
  id: ID!
  name: String!
  price: String!
  currency: String
  ordinal: Int!
}

type InvoiceCatalogDiscount {
  id: ID!
  type: CatalogDiscountType
  name: String
  config: InvoiceDiscountConfig!
  value: String!
  status: RevisionStatus!
  lastRevisionTime: AWSDateTime!
}

type InvoiceCatalogServiceCharge {
  id: ID!
  type: CatalogServiceChargeType
  name: String
  config: InvoiceServiceChargeConfig!
  value: String!
  status: RevisionStatus!
  lastRevisionTime: AWSDateTime!
}

type InvoiceItemModifier {
  id: ID!
  catalogModifierUuid: String!
  catalogModifierSetUuid: String!
  catalogModifierSet: InvoiceCatalogModifierSet!
  name: String!
  price: String!
  currency: String
  orderIndex: Int!
  quantity: Float!
  subtotalAmount: String
}

input CreateInvoiceInput {
  customer: CreateInvoiceCustomerInput
  title: String
  message: String
  startDate: AWSDateTime
  dueDate: AWSDateTime
  items: [CreateInvoiceItemInput!]
  discount: InvoiceDiscountInput
  discounts: [InvoiceDiscountInputV2!]
  serviceCharges: [InvoiceServiceChargeInput!]
  milestones: [InvoiceMilestoneInput!]
  email: InvoiceEmailInput
  notes: String
  itemsApplyTax: Boolean!
  itemsTaxInclusive: Boolean!
  sendSchedule: InvoiceSendScheduleInput
  sms: InvoiceSMSInput
}

input UpdateInvoiceInput {
  referenceNumber: String!
  customer: UpdateInvoiceCustomerInput
  title: String
  message: String
  startDate: AWSDateTime
  dueDate: AWSDateTime
  items: [UpdateInvoiceItemInput!]
  discount: InvoiceDiscountInput
  discounts: [InvoiceDiscountInputV2!]
  serviceCharges: [InvoiceServiceChargeInput!]
  milestones: [InvoiceMilestoneInput!]
  email: InvoiceEmailInput
  notes: String
  itemsApplyTax: Boolean
  itemsTaxInclusive: Boolean
  sendSchedule: InvoiceSendScheduleInput
  sms: InvoiceSMSInput
}

input SendReminderInput {
  referenceNumber: String!
  reminderUuid: ID
}

input SkipReminderInput {
  referenceNumber: String!
  reminderUuid: ID!
}

enum InvoiceStatus {
  DRAFT
  SENT
  PART_PAID
  PAID
  OVERDUE
  CANCELLED
  ACTIVE
  ENDED
  DELETED
  SCHEDULED
  SEND_FAILED @deprecated(reason: "Use the new`ERROR` status.")
  ERROR
}

enum InvoiceMilestoneStatus {
  NULL
  PAID
  OVERDUE
  CANCELLED
}

enum InvoiceDiscountConfig {
  PERCENTAGE
  AMOUNT
}

enum InvoiceServiceChargeConfig {
  PERCENTAGE
  AMOUNT
}

enum InvoiceItemUnit {
  QUANTITY
  HOUR
  DAY
}

enum InvoiceMilestoneType {
  DEPOSIT
  MILESTONE
  FINAL
}

enum InvoicePaymentType {
  MANUAL
  CNP
}

enum invoiceSubscriptionAction {
  Create
  Update
  Delete
  Cancel
}

enum InvoiceItemType {
  SINGLE
  VARIANT
  ONE_TIME
  PARENT
}

type InvoiceEmailRecipients {
  recipient: String!
  cc: [String!]
  bcc: [String!]
  sendMeCopy: Boolean
}

input InvoiceEmailRecipientsInput {
  recipient: String
  cc: [String!]
  bcc: [String!]
  sendMeCopy: Boolean
}

type InvoiceDiscount @aws_oidc @aws_iam {
  config: InvoiceDiscountConfig!
  value: String!
}

input InvoiceDiscountInput {
  config: InvoiceDiscountConfig
  value: String
}

type InvoiceDiscountV2 @aws_oidc @aws_iam {
  id: ID!
  catalogDiscountUuid: ID
  type: CatalogDiscountType
  name: String
  config: InvoiceDiscountConfig!
  value: String!
  discountedAmount: String!
  ordinal: Int!
  quantity: Float
  catalogDiscount: InvoiceCatalogDiscount
}

type InvoiceServiceCharge @aws_oidc @aws_iam {
  id: ID!
  catalogServiceChargeUuid: ID
  type: CatalogServiceChargeType
  name: String
  config: InvoiceServiceChargeConfig!
  value: String!
  serviceChargeAmount: String!
  ordinal: Int!
  quantity: Float
  catalogServiceCharge: InvoiceCatalogServiceCharge
}

input InvoiceDiscountInputV2 {
  id: ID
  catalogDiscountUuid: ID
  name: String
  config: InvoiceDiscountConfig!
  value: String!
  ordinal: Int!
  quantity: Float
}

input InvoiceServiceChargeInput {
  id: ID
  catalogServiceChargeUuid: ID
  name: String
  config: InvoiceServiceChargeConfig!
  value: String!
  ordinal: Int!
  quantity: Float
}

input InvoiceRecordPaymentInput {
  referenceNumber: String!
  amount: String!
  notes: String
  sendReceiptToCustomer: Boolean
}

type InvoiceItem @aws_oidc @aws_iam {
  id: ID!
  entityUuid: ID!
  name: String!
  price: String!
  currency: String
  type: InvoiceItemType
  orderIndex: Int
  description: String
  unit: InvoiceItemUnit!
  quantity: Float!
  discount: InvoiceDiscount
  discounts: [InvoiceDiscountV2!]
  serviceCharges: [InvoiceServiceCharge!]
  taxes: [InvoiceTax!]
  modifiers: [InvoiceItemModifier!]
  reportingCategoryUuid: String
  updatedTime: AWSDateTime
  catalogItem: InvoiceItemCatalogReference
  includeCalculation: Boolean
  discountAmount: String
  serviceChargeAmount: String
}

input InvoiceItemInput {
  id: ID!
  entityUuid: ID!
  name: String!
  price: String!
  currency: String
  orderIndex: Int
  description: String
  unit: InvoiceItemUnit!
  quantity: Float!
  discount: InvoiceDiscountInput
  taxes: [InvoiceTaxInput!]
  updatedTime: AWSDateTime
  catalogItem: InvoiceItemCatalogReferenceInput
  includeCalculation: Boolean
  discountAmount: String
  serviceChargeAmount: String
}

input PartialInvoiceItemInput {
  catalogItemUuid: String
  name: String!
  price: String!
  currency: String
  orderIndex: Int
  description: String
  unit: InvoiceItemUnit!
  quantity: Float!
  discount: InvoiceDiscountInput
  discounts: [InvoiceDiscountInputV2!]
  serviceCharges: [InvoiceServiceChargeInput!]
  modifiers: [CreateInvoiceItemModifierInput!]
  taxes: [InvoiceTaxInput!]
  updatedTime: AWSDateTime
  includeCalculation: Boolean
}

input InvoicePaymentInput {
  id: ID!
  entityUuid: ID!
  amount: String!
  notes: String
  type: InvoicePaymentType!
  paymentTimeISO: String!
  localPaymentTime: AWSDateTime
  surchargedAmount: String
  surchargedGst: String
  rrn: String
  entityShortId: String
}

type InvoicePaymentAmounts @aws_oidc @aws_iam {
  amount: Int!
  surchargedAmount: Int
  surchargedGst: Int
}

type InvoicePayment @aws_oidc @aws_iam {
  id: ID!
  entityUuid: ID!
  amount: String! @deprecated(reason: "UK currency refactor. Use amounts.amount instead.")
  notes: String
  type: InvoicePaymentType!
  paymentTimeISO: String!
  localPaymentTime: AWSDateTime
  surchargedAmount: String @deprecated(reason: "UK currency refactor. Use amounts.surchargedAmount instead.")
  surchargedGst: String @deprecated(reason: "UK currency refactor. Use amounts.surchargedGst instead.")
  rrn: String
  entityShortId: String
  amounts: InvoicePaymentAmounts!
}

input CreateInvoiceItemModifierInput {
  id: ID
  catalogModifierUuid: ID! # we dont' support one time modifier set
  catalogModifierSetUuid: ID!
  quantity: Float!
  orderIndex: Int! # use orderIndex instead
}

input CreateInvoiceItemInput {
  catalogItemUuid: ID
  name: String!
  price: String!
  currency: String
  type: InvoiceItemType
  orderIndex: Int
  description: String
  unit: InvoiceItemUnit!
  quantity: Float!
  discount: InvoiceDiscountInput
  discounts: [InvoiceDiscountInputV2!]
  serviceCharges: [InvoiceServiceChargeInput!]
  taxes: [InvoiceTaxInput!]
  modifiers: [CreateInvoiceItemModifierInput!]
  reportingCategoryUuid: String
  updatedTime: AWSDateTime
  includeCalculation: Boolean
}

input UpdateInvoiceItemInput {
  id: ID
  catalogItemUuid: ID
  name: String
  price: String
  currency: String
  type: InvoiceItemType
  orderIndex: Int
  description: String
  unit: InvoiceItemUnit
  quantity: Float
  discount: InvoiceDiscountInput
  discounts: [InvoiceDiscountInputV2!]
  serviceCharges: [InvoiceServiceChargeInput!]
  taxes: [InvoiceTaxInput!]
  modifiers: [CreateInvoiceItemModifierInput!]
  reportingCategoryUuid: String
  updatedTime: AWSDateTime
  includeCalculation: Boolean
}

type InvoiceMilestone @aws_oidc @aws_iam {
  id: ID!
  entityUuid: ID!
  type: InvoiceMilestoneType!
  status: InvoiceMilestoneStatus!
  startDate: AWSDateTime!
  dueDate: AWSDateTime!
  amount: String!
  description: String
}

input InvoiceMilestoneInput {
  type: InvoiceMilestoneType
  status: InvoiceMilestoneStatus
  startDate: AWSDateTime
  dueDate: AWSDateTime
  amount: String
  description: String
}

enum InvoiceAction {
  CREATE
  UPDATE
  CANCELLED
  SEND_INVOICE
  RE_SEND_INVOICE
  SEND_AUTO_REMINDER
  SEND_MANUAL_REMINDER
  CNP_PAYMENT
  MANUAL_PAYMENT
  PAYMENT_DUE
  SEND_SCHEDULED_INVOICE
  SEND_SMS_INVOICE
  SEND_EMAIL_INVOICE
}

enum InvoiceActivityStatus {
  SCHEDULED
  COMPLETED
  SKIPPED
  FAILED
}

input InvoiceActivityInput {
  id: ID!
  entityUuid: ID
  contactUuid: ID
  contactName: String
  status: InvoiceActivityStatus!
  type: InvoiceAction!
  completedTime: AWSDateTime
  title: String!
  balance: String
  paidAmount: String
  cnpTxnRefNum: String
  failureReason: String
  dueDate: AWSDateTime
  reminderIndex: String
}

type InvoiceDeliveryResult {
  failed: Boolean!
  reason: String
  confirmationDateISO: String
}

type InvoiceActivity @aws_oidc @aws_iam {
  id: ID!
  entityUuid: ID
  contactUuid: ID
  contactName: String
  status: InvoiceActivityStatus!
  type: InvoiceAction!
  completedTime: AWSDateTime
  title: String!
  balance: String
  paidAmount: String
  cnpTxnRefNum: String
  failureReason: String
  dueDate: AWSDateTime
  reminderIndex: String
  invoiceEmailDeliveryResult: InvoiceDeliveryResult
  invoiceSmsDeliveryResult: InvoiceDeliveryResult
}

input CreateInvoiceCustomerInput {
  payerContactUuid: ID!
  attentionContactUuid: ID
  payerEmail: String!
}

input UpdateInvoiceCustomerInput {
  payerContactUuid: ID
  attentionContactUuid: ID
  payerEmail: String
  payerContactStatus: RevisionStatus
  attentionContactStatus: RevisionStatus
}

enum RevisionStatus {
  NO_CHANGE
  UPDATED
  DELETED
  UNLINKED
}

type InvoiceCustomerContact {
  contactUuid: ID!
  status: RevisionStatus!
  contactType: ContactType!
  firstName: String
  lastName: String
  businessName: String
  updatedTime: AWSDateTime
  lastRevisionTime: AWSDateTime
}

input InvoiceCustomerContactInput {
  contactUuid: ID!
  status: RevisionStatus!
  contactType: ContactType!
  firstName: String
  lastName: String
  businessName: String
  updatedTime: AWSDateTime
  lastRevisionTime: AWSDateTime
}

input PartialInvoiceCustomerContactInput {
  contactUuid: ID!
  contactType: ContactType!
  firstName: String
  lastName: String
  businessName: String
}

input InvoiceItemCatalogReferenceInput {
  id: ID!
  status: RevisionStatus!
  updatedTime: AWSDateTime
  lastRevisionTime: AWSDateTime
}

type InvoiceItemCatalogReference {
  id: ID!
  status: RevisionStatus!
  sku: String
  name: String
  parentName: String # only type=CatalogItemType.VARIANT can have parentName
  parentUuid: ID
  reportingCategoryUuid: String
  updatedTime: AWSDateTime
  lastRevisionTime: AWSDateTime
  invoicesEnabled: Boolean
}

type InvoiceCustomer @aws_oidc @aws_iam {
  id: ID!
  payerContact: InvoiceCustomerContact
  attentionContact: InvoiceCustomerContact
  payerEmail: String
  deliveryAddress: String
}

input InvoiceCustomerInput {
  id: ID!
  payerContact: InvoiceCustomerContactInput
  attentionContact: InvoiceCustomerContactInput
  payerEmail: String
  deliveryAddress: String
}

input PartialInvoiceCustomerInput {
  payerContact: PartialInvoiceCustomerContactInput
  attentionContact: PartialInvoiceCustomerContactInput
  payerEmail: String
}

type InvoiceTax {
  enabled: Boolean!
  name: String!
  percent: Int
}

input InvoiceTaxInput {
  enabled: Boolean
  name: String
  percent: Int
}

input InvoiceEmailInput {
  recipients: InvoiceEmailRecipientsInput
  subject: String
  body: String
  enabled: Boolean
}

type InvoiceEmail @aws_oidc @aws_iam {
  recipients: InvoiceEmailRecipients
  subject: String
  body: String
  enabled: Boolean
}

type InvoiceReminder {
  id: ID!
  dueDate: AWSDateTime!
  status: InvoiceActivityStatus!
  contactUuid: ID!
  payerContactName: String!
}

input InvoicePdfCustomisationPreviewInput {
  selectedTemplateType: PdfTemplateType
  logos: [String]
  selectedLogo: String
  accentColours: [String]
  selectedColor: String
  defaultMessage: String
  termsAndConditions: String
}

input PreviewZellerInvoiceSettingsInput {
  businessAddress: String
  pdfIncludesAddress: Boolean
  discountsEnabled: Boolean
  customisation: InvoicePdfCustomisationPreviewInput
}

input PreviewZellerSettingsInput {
  name: String
  address: SiteAddressInput
  type: SiteType
  surchargesTaxes: SurchargesTaxesSettingsInput
  receipt: ReceiptSettingsInput
  invoice: PreviewZellerInvoiceSettingsInput
}

input PreviewInvoiceInput {
  referenceNumber: String
  payerContactName: String
  customer: PartialInvoiceCustomerInput
  title: String
  message: String
  startDate: AWSDateTime
  dueDate: AWSDateTime
  items: [PartialInvoiceItemInput!]
  discount: InvoiceDiscountInput
  discounts: [InvoiceDiscountInputV2!]
  serviceCharges: [InvoiceServiceChargeInput!]
  email: InvoiceEmailInput
  sms: InvoiceSMSInput
  paidAmount: String
  subtotalAmount: String
  totalAmount: String
  totalSurcharge: String
  totalDiscount: String
  totalServiceCharge: String
  totalGst: String
  itemsTaxInclusive: Boolean
}

input PartialInvoicePreviewCustomerContactInput {
  contactType: ContactType
  firstName: String
  lastName: String
  businessName: String
  address: ContactAddressInput
  abn: String
}

input InvoiceCustomerPreviewInput {
  payerContact: PartialInvoicePreviewCustomerContactInput
  attentionContact: PartialInvoicePreviewCustomerContactInput
  payerEmail: String
}

input CustomisationPreviewInvoiceInput {
  referenceNumber: String
  payerContactName: String
  customer: InvoiceCustomerPreviewInput
  title: String
  message: String
  startDate: AWSDateTime
  dueDate: AWSDateTime
  items: [PartialInvoiceItemInput!]
  discount: InvoiceDiscountInput
  discounts: [InvoiceDiscountInputV2!]
  serviceCharges: [InvoiceServiceChargeInput!]
  email: InvoiceEmailInput
  sms: InvoiceSMSInput
  paidAmount: String
  subtotalAmount: String
  totalAmount: String
  totalSurcharge: String
  totalDiscount: String
  totalServiceCharge: String
  totalGst: String
  itemsTaxInclusive: Boolean
  siteSettings: PreviewZellerSettingsInput
}

input SaveAndSendInvoiceInput {
  referenceNumber: String
  customer: UpdateInvoiceCustomerInput
  title: String
  message: String
  startDate: AWSDateTime
  dueDate: AWSDateTime
  items: [UpdateInvoiceItemInput!]
  discount: InvoiceDiscountInput
  discounts: [InvoiceDiscountInputV2!]
  serviceCharges: [InvoiceServiceChargeInput!]
  milestones: [InvoiceMilestoneInput!]
  email: InvoiceEmailInput
  sms: InvoiceSMSInput
  notes: String
  itemsApplyTax: Boolean
  itemsTaxInclusive: Boolean
  sendSchedule: InvoiceSendScheduleInput
}

# Order amounts in cents
type InvoiceAmounts @aws_oidc @aws_iam {
  paidAmount: Int
  dueAmount: Int
  subtotalAmount: Int
  totalAmount: Int
  totalSurcharge: Int
  totalGst: Int
  totalDiscount: Int
  totalServiceCharge: Int
}

type Invoice @aws_oidc @aws_iam {
  id: ID!
  entityUuid: ID!
  status: InvoiceStatus!
  referenceNumber: String!
  payerContactName: String
  customer: InvoiceCustomer
  title: String
  message: String
  startDate: AWSDateTime
  dueDate: AWSDateTime
  siteSettings: ZellerInvoiceSite
  items: [InvoiceItem!]
  discount: InvoiceDiscount
  discounts: [InvoiceDiscountV2!]
  serviceCharges: [InvoiceServiceCharge!]
  milestones: [InvoiceMilestone!]
  email: InvoiceEmail
  activities: [InvoiceActivity!]
  notes: String
  payments: [InvoicePayment!]
  paidAmount: String @deprecated(reason: "UK currency refactor. Use amounts.paidAmount instead.")
  dueAmount: String @deprecated(reason: "UK currency refactor. Use amounts.dueAmount instead.")
  subtotalAmount: String @deprecated(reason: "UK currency refactor. Use amounts.subtotalAmount instead.")
  totalAmount: String @deprecated(reason: "UK currency refactor. Use amounts.totalAmount instead.")
  totalSurcharge: String @deprecated(reason: "UK currency refactor. Use amounts.totalSurcharge instead.")
  totalGst: String @deprecated(reason: "UK currency refactor. Use amounts.totalGst instead.")
  totalDiscount: String @deprecated(reason: "UK currency refactor. Use amounts.totalDiscount instead.")
  totalServiceCharge: String @deprecated(reason: "UK currency refactor. Use amounts.totalServiceCharge instead.")
  itemsApplyTax: Boolean!
  itemsTaxInclusive: Boolean!
  paymentLink: String
  createdTime: Int
  paidTime: Int
  sentTime: Int
  sendSchedule: InvoiceSendSchedule
  sms: InvoiceSMS
  requiredEmailUpdateBeforeSend: [String!]
  requiredPhoneUpdateBeforeSend: [String!]
  amounts: InvoiceAmounts!
}

input InvoiceInput {
  id: ID!
  entityUuid: ID!
  status: InvoiceStatus!
  referenceNumber: String!
  payerContactName: String
  customer: InvoiceCustomerInput
  title: String
  message: String
  startDate: AWSDateTime
  dueDate: AWSDateTime
  siteSettings: ZellerInvoiceSiteInput
  items: [InvoiceItemInput!]
  discount: InvoiceDiscountInput
  milestones: [InvoiceMilestoneInput!]
  email: InvoiceEmailInput
  sms: InvoiceSMSInput
  activities: [InvoiceActivityInput!]
  notes: String
  payments: [InvoicePaymentInput!]
  paidAmount: String
  dueAmount: String
  subtotalAmount: String
  totalAmount: String
  totalSurcharge: String
  totalGst: String
  totalDiscount: String
  totalServiceCharge: String
  itemsApplyTax: Boolean!
  itemsTaxInclusive: Boolean!
  paymentLink: String
  createdTime: Int
  paidTime: Int
  sentTime: Int
  sendSchedule: InvoiceSendScheduleInput
}
