enum CommonErrorType {
  NOT_FOUND
  SERVER_ERROR
  INVALID_REQUEST
  BAD_USER_INPUT
  RESOURCE_ALREADY_EXISTS
  UNAUTHORIZED
  FORBIDDEN
  MFA_REQUIRED
  CUSTOMER_EMAIL_ALREADY_EXISTS
  CUSTOMER_IDENTITY_ALREADY_EXISTS
  MFA_SENSITIVE_ACCESS_EXPIRED
  CUSTOMER_IDENTITY_ERROR
  TOO_MANY_REQUESTS
}

type CommonError {
  type: CommonErrorType
}

type Query {
  getMyPersonalInfo: PersonalInfo!
  getCustomer(customerUuid: ID, entityUuid: ID): Customer!
  getCustomerMarketing(entityUuid: ID): CustomerMarketing!
  getCustomers(entityUuid: ID): [Customer]!
  getCustomerDocumentUploadUrl(customerUuid: ID, entityUuid: ID): String!
  getCustomerIconUploadForm(customerUuid: ID!, entityUuid: ID): PresignedPostUpload!
  requireBVCheck(entityUuid: ID): Boolean!
  getCustomerEntityMapping(filter: CustomerEntityMappingFilterInput): CustomerEntityMapping!
}

enum CompanyProfileData {
  MODIFIED
  MANUALLY_CREATED
  PRISTINE
}

type Mutation {
  createCustomer(input: [CreateCustomerInput!]!, entityUuid: ID): [Customer!]
  updateCustomer(customer: UpdateCustomerInput!, entityUuid: ID): Boolean!
  deleteCustomer(customerUuid: ID!, entityUuid: ID): Boolean!
  sendInviteEmail(customerUuid: ID!, entityUuid: ID): Boolean!
  requestCustomerDocumentVerification(input: VerifyCustomerDocumentsInput!, entityUuid: ID): Boolean!
  publishCustomerVerificationResult(input: VerifyCustomerDocumentsResultsInput!): VerifyCustomerDocumentsResults!
    @aws_iam
  requestEmailChange(input: RequestEmailChangeInput): RequestEmailChangeResult
  completeEmailChange(input: CompleteEmailChangeInput): CompleteEmailChangeResult @aws_api_key
  removeCustomerIcon(customerUuid: ID!): Boolean!
  finaliseCustomerKYC: FinaliseCustomerKYCResult
  generateBVCheckToken: GenerateBVCheckTokenResponse!
  createBVCheck(usePhotoCapture: Boolean): Boolean!
  updateKycCheckpoint(kycCheckpoints: [KycCheckpoint!]!): KycCheckpointResponse!
  initialDocumentVerificationKeyExchange(
    input: DocumentVerificationClientPublicKeyInput!
  ): DocumentVerificationServerKey!
  requestCustomerDocumentVerificationV2(input: VerifyCustomerDocumentsV2Input!, entityUuid: ID): Boolean!
  addMarketingModalSeen(marketingModalName: String!, entityUuid: ID): Boolean!
  updateCustomerEntityMapping(input: UpdateCustomerEntityMappingInput!): Boolean!
  updateVisibleTabs(input: UpdateVisibleTabsInput!): Boolean!
  createRegisteringIndividual(input: RegisteringIndividualInput!): PersonalInfo!
}

type Subscription {
  verifyCustomerDocuments(customerUuid: ID!): VerifyCustomerDocumentsResults
    @aws_oidc
    @aws_subscribe(mutations: ["publishCustomerVerificationResult"])
}

type PersonalInfo {
  id: ID!
  email: String
  emailVerified: Boolean
  firstname: String
  middlename: String
  lastname: String
  phone: String
  phoneVerified: Boolean
  address: CustomerAddress
  kycStatus: KycStatus
  idvAttempts: IdvAttempts
  kycCheckpoints: [KycCheckpoint]
}

type DocumentVerificationServerKey {
  serverPublicKey: String!
}

input DocumentVerificationClientPublicKeyInput {
  clientPublicKey: String!
}

type CustomerMarketing {
  banner: String
  banners: [String]
  modal: String
}

input VerifyCustomerDocumentsV2Input {
  firstName: String!
  middleName: String
  lastName: String!
  address: CustomerAddressInput!
  dob: AWSDate!
  documentType: DocumentType!
  document: String! #Empty String for NO_SECOND_ID
  documentMac: String!
}

enum CustomerRole {
  ADMIN
  MANAGER
}

type CustomerSite {
  id: ID!
  name: String!
  address: SiteAddress
  pin: String
  refundRequiresPin: Boolean
  devices: [Device]
  customers: [Customer]
}

type CustomerAddress {
  street: String
  suburb: String
  state: String
  postcode: String
  country: String
}

type ProductTourStatus {
  showAdminMerchantPortalWelcome: Boolean
  showInvoiceInstructions: Boolean
  showInvoicesWelcome: Boolean
  showItemsWelcome: Boolean
  showItemInstructions: Boolean
  showInvoicesCustomisationWelcome: Boolean
  showInvoicesScheduleSendWelcome: Boolean
  showInvoicesSendBySmsWelcome: Boolean
  showInvoiceSendViaInfo: Boolean
  showInvoicingCustomisationSettingsWelcome: Boolean
  showTapToPayInstructions: Boolean
  showTapToPayMayJune: Boolean
  showSavingsAccountWelcome: Boolean
  showSavingsAccountMarch: Boolean
  showSavingsAccountMay: Boolean
  showCorporateCardsMayOffer: Boolean
  showCorporateCardsAdminWelcome: Boolean
  showCorporateCardsWalkthrough: Boolean
  showCorporateCardsSettingsWalkthrough: Boolean
  showCustomScreensaverPromo: Boolean
  showNotificationsWelcome: Boolean
  showInvoiceApril: Boolean
  showOnboardingShop: Boolean
  showCatalogueItemsWelcome: Boolean
  profileAvatarWalkthrough: Boolean
  showContactAccountTransferInstructions: Boolean @deprecated(reason: "no longer required")
  showServiceChargesWelcome: Boolean
}

type IdvAttempts {
  passport: Int
  driversLicence: Int
  medicareCard: Int
}

type CustomerPermissions {
  allowZellerInvoices: Boolean!
  allowXeroPaymentServices: Boolean!
  allowItemManagement: Boolean!
  allowDiscountManagement: Boolean!
}

enum KycCheckpoint {
  IDV
  SELFIE_VERIFICATION
}

type KycCheckpointResponseResult {
  id: ID
  kycCheckpoints: [KycCheckpoint!]!
}

type KycCheckpointResponseErrors {
  type: CommonErrorType
}

type KycCheckpointResponse {
  result: KycCheckpointResponseResult
  error: CommonError
}

type Customer {
  id: ID!
  entityUuid: ID
  email: String
  emailVerified: Boolean
  nickname: String
  firstname: String
  middlename: String
  lastname: String
  phone: String
  phoneVerified: Boolean
  role: CustomerRole
  address: CustomerAddress
  registeringIndividual: Boolean
  director: Boolean
  secretary: Boolean
  ceo: Boolean
  shareholder: Boolean
  beneficialOwner: Boolean
  beneficialOwnerAlt: Boolean
  beneficiary: Boolean
  partner: Boolean
  trustee: Boolean
  settlor: Boolean
  chair: Boolean
  treasurer: Boolean
  governmentRole: String
  generalContact: Boolean
  financialContact: Boolean
  type: EntityType
  companyTrustName: String
  abn: String
  # getCustomerSitesResolver
  sites: [CustomerSite]
  siteCount: Int
  productTourStatus: ProductTourStatus
  isInvitationPending: Boolean
  invitedBy: InvitedBy
  acn: String
  permissions: CustomerPermissions
  companyProfileData: CompanyProfileData
  # getReferralCode resolver
  referralCode: String
  icon: Icon
  createdAt: AWSDateTime
  kycStatus: KycStatus
  idvAttempts: IdvAttempts
  kycCheckpoints: [KycCheckpoint]
}

enum KycStatus {
  VERIFIED
  NOT_REQUIRED
  REQUIRED
  RC_REJECTED
  RC_ABANDONED
  IN_REVIEW
}

type InvitedBy {
  customerUuid: ID!
  firstName: String
  lastName: String
  middleName: String
  email: String
}

input CustomerAddressInput {
  street: String
  suburb: String
  state: String
  postcode: String
  country: String
}

input UpdateCustomerInput {
  id: ID!
  nickname: String
  firstname: String
  middlename: String
  lastname: String
  email: String
  phone: String
  role: CustomerRole
  address: CustomerAddressInput
  dob: AWSDate
  director: Boolean
  secretary: Boolean
  ceo: Boolean
  shareholder: Boolean
  beneficialOwner: Boolean
  beneficialOwnerAlt: Boolean
  beneficiary: Boolean
  partner: Boolean
  trustee: Boolean
  settlor: Boolean
  chair: Boolean
  treasurer: Boolean
  governmentRole: String
  generalContact: Boolean
  financialContact: Boolean
  type: EntityType
  companyTrustName: String
  abn: String
  showOnboardingShop: Boolean
  showAdminMerchantPortalWelcome: Boolean
  showInvoiceInstructions: Boolean
  showInvoiceApril: Boolean
  showInvoicesWelcome: Boolean
  showItemsWelcome: Boolean
  showItemInstructions: Boolean
  showInvoicesCustomisationWelcome: Boolean
  showInvoicesScheduleSendWelcome: Boolean
  showInvoicesSendBySmsWelcome: Boolean
  showInvoiceSendViaInfo: Boolean
  showInvoicingCustomisationSettingsWelcome: Boolean
  showTapToPayInstructions: Boolean
  showTapToPayMayJune: Boolean
  showSavingsAccountWelcome: Boolean
  showSavingsAccountMarch: Boolean
  showSavingsAccountMay: Boolean
  showCorporateCardsMayOffer: Boolean
  showCorporateCardsAdminWelcome: Boolean
  showCorporateCardsWalkthrough: Boolean
  showCorporateCardsSettingsWalkthrough: Boolean
  showCustomScreensaverPromo: Boolean
  showNotificationsWelcome: Boolean
  showCatalogueItemsWelcome: Boolean
  profileAvatarWalkthrough: Boolean
  acn: String
  companyProfileData: CompanyProfileData
  permissions: CustomerPermissionsInput
  assignSites: [String!]
  unassignSites: [String!]
  showServiceChargesWelcome: Boolean
}

input CreateCustomerInput {
  email: String
  firstname: String
  middlename: String
  lastname: String
  nickname: String
  createIdentity: Boolean
  phone: String
  role: CustomerRole
  address: CustomerAddressInput
  dob: AWSDate
  director: Boolean
  secretary: Boolean
  ceo: Boolean
  shareholder: Boolean
  beneficialOwner: Boolean
  beneficialOwnerAlt: Boolean
  beneficiary: Boolean
  partner: Boolean
  trustee: Boolean
  settlor: Boolean
  chair: Boolean
  treasurer: Boolean
  governmentRole: String
  generalContact: Boolean
  financialContact: Boolean
  type: EntityType
  companyTrustName: String
  abn: String
  acn: String
  companyProfileData: CompanyProfileData
  permissions: CustomerPermissionsInput
  assignSites: [String!]
}

input CustomerPermissionsInput {
  allowZellerInvoices: Boolean
  allowXeroPaymentServices: Boolean
  allowItemManagement: Boolean
  allowDiscountManagement: Boolean
}

enum VerifyState {
  VIC
  ACT
  NSW
  QLD
  TAS
  NT
  SA
  WA
}

enum VerifyMedicareCardColours {
  BLUE
  GREEN
  YELLOW
}

input VerifyCustomerDocumentsInput {
  customerUuid: ID!
  firstName: String
  middleName: String
  lastName: String
  address: CustomerAddressInput
  dob: AWSDate
  # New Fields for V2
  documentType: DocumentType
  passport: PassportInput
  driversLicence: DriversLicenceInput
  medicare: MedicareInput
}

enum VerifyResult {
  ACCEPTED
  REJECTED
  ERROR
  NOT_VERIFIED
}

input PassportInput {
  country: String!
  number: String!
  dateOfIssue: AWSDate @deprecated(reason: "no longer required")
  expiry: AWSDate @deprecated(reason: "no longer required")
}

input DriversLicenceInput {
  state: VerifyState!
  number: String!
  expiry: AWSDate @deprecated(reason: "no longer required")
  cardNumber: String!
}

input MedicareInput {
  number: String!
  position: Int!
  middleName: String
  colour: VerifyMedicareCardColours!
  expiry: String!
}

type VerifyCustomerDocumentsResults @aws_iam @aws_oidc {
  customerUuid: ID!
  documentType: DocumentType
  result: VerifyResult
  error: String
}

input VerifyCustomerDocumentsResultsInput {
  customerUuid: ID!
  documentType: DocumentType
  result: VerifyResult
  error: String
}

input RequestEmailChangeInput {
  newEmail: String!
}

input CompleteEmailChangeInput {
  email: String!
  code: String!
}

enum RequestEmailChangeStatus {
  VERIFICATION_EMAIL_SENT
  MFA_REQUIRED
  FAILED
}

enum CompleteEmailChangeStatus {
  CODE_EXPIRED_OR_INVALID
  INVALID_EMAIL
  COMPLETED
  FAILED
}

type RequestEmailChangeResult {
  status: RequestEmailChangeStatus!
  message: String
}

type CompleteEmailChangeResult @aws_api_key {
  status: CompleteEmailChangeStatus!
  message: String
}

type FinaliseCustomerKYCResult {
  result: CustomerKYCResult!
  uploadDocument: [OnboardingUploadDocumentType!]
}

type GenerateBVCheckTokenResponse {
  token: String
}

enum CustomerKYCResult {
  VERIFIED
  IN_REVIEW
}

enum OnboardingUploadDocumentType {
  IDENTITY_DOCUMENT
  PROOF_OF_ADDRESS
  VISA_LABEL
}

type CustomerEntityMapping {
  entityRelations: [CustomerEntityRelation!]
  defaultEntityUuid: String!
}

type CustomerEntityRelation {
  # addional resolver getCustomerEntityRelation
  entity: Entity!
  entityUuid: String!
  role: CustomerRole!
  isVisible: Boolean!
  isActive: Boolean!
  order: Int
}

input CustomerEntityMappingFilterInput {
  isVisible: Boolean
}

input UpdateVisibleTabsInput {
  orderedTabs: [OrderedTabInput!]!
}

input OrderedTabInput {
  entityUuid: String!
  isActive: Boolean!
}

input UpdateCustomerEntityMappingInput {
  defaultEntityUuid: String!
}

input RegisteringIndividualInput {
  country: String! #ISO 3166-1 alpha-3
  email: String!
}
