import { getRemoteAccountId, getRemoteRegion } from '@npco/component-bff-serverless/dist/param/util';

const region = process.env.AWS_REGION;
const remoteRegion = getRemoteRegion(region);
const remoteAccountId = getRemoteAccountId(region);

export const managedPolicy = {
  Resources: {
    xrayPolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: [
                'xray:PutTraceSegments',
                'xray:PutTelemetryRecords',
                'xray:GetSamplingRules',
                'xray:GetSamplingTargets',
                'xray:GetSamplingStatisticSummaries',
              ],
              Resource: '*',
            },
          ],
        },
      },
    },
    lambdaVpcPolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['ec2:CreateNetworkInterface', 'ec2:DescribeNetworkInterfaces', 'ec2:DeleteNetworkInterface'],
              Resource: '*',
            },
          ],
        },
      },
    },
    sessionCacheTableDBPolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['dynamodb:Query', 'dynamodb:UpdateItem', 'dynamodb:PutItem', 'dynamodb:GetItem'],
              Resource: [
                'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.sessionCacheTableName}',
                'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.sessionCacheTableName}/index/${self:custom.accessTokenGsi}',
                'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.sessionCacheTableName}/index/${self:custom.entityCacheGsi}',
              ],
            },
            {
              Effect: 'Allow',
              Action: ['dynamodb:Query', 'dynamodb:GetItem'],
              Resource: [
                'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.entityTableName}',
              ],
            },
            {
              Effect: 'Allow',
              Action: ['lambda:InvokeFunction'],
              Resource: ['arn:aws:lambda:${self:provider.region}:*:function:${self:custom.mpCqrsCommandHandler}*'],
            },
          ],
        },
      },
    },
    entityTableWriteItemRolePolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['dynamodb:UpdateItem', 'dynamodb:PutItem'],
              Resource: [
                'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.entityTableName}',
              ],
            },
          ],
        },
      },
    },
    entityTableQueryRolePolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['dynamodb:Query', 'dynamodb:BatchGetItem', 'dynamodb:Get*'],
              Resource: [
                'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.entityTableName}',
                'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.entityTableName}/index/${self:custom.entityGsi}',
                'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.entityTableName}/index/${self:custom.shortIdGsi}',
              ],
            },
          ],
        },
      },
    },
    firebaseAdminParameterRolePolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['ssm:GetParameter'],
              Resource: [
                'arn:aws:ssm:${self:provider.region}:${self:custom.accountId}:parameter${self:custom.firebaseAdminEmailSsmName}',
                'arn:aws:ssm:${self:provider.region}:${self:custom.accountId}:parameter${self:custom.firebaseAdminPrivateKeySsmName}',
              ],
            },
          ],
        },
      },
    },
    crossAccountInvocationPolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['lambda:InvokeFunction'],
              Resource: [`arn:aws:lambda:${remoteRegion}:${remoteAccountId}:function:\${self:provider.stackName}*`],
            },
          ],
        },
      },
    },
  },
  Outputs: {
    entityTableQueryRolePolicyArn: {
      Value: { Ref: 'entityTableQueryRolePolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-entityTableQueryRolePolicyArn']] } },
    },
    entityTableWriteItemRolePolicyArn: {
      Value: { Ref: 'entityTableWriteItemRolePolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-entityTableWriteItemRolePolicyArn']] } },
    },
    sessionCacheTableDBPolicyArn: {
      Value: { Ref: 'sessionCacheTableDBPolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-sessionCacheTableDBPolicyArn']] } },
    },
    lambdaVpcPolicyArn: {
      Value: { Ref: 'lambdaVpcPolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-lambdaVpcPolicyArn']] } },
    },
    xrayPolicyArn: {
      Value: { Ref: 'xrayPolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-xrayPolicyArn']] } },
    },
    firebaseAdminParameterPolicyArn: {
      Value: { Ref: 'firebaseAdminParameterRolePolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-firebaseAdminParameterPolicyArn']] } },
    },
    crossAccountInvocationPolicyArn: {
      Value: { Ref: 'crossAccountInvocationPolicy' },
      Export: { Name: { 'Fn::Join': ['', ['${self:custom.service}', '-crossAccountInvocationPolicyArn']] } },
    },
  },
};
