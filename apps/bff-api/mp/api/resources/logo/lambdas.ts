import { Action } from '@npco/component-bff-serverless';

export const lambdas = {
  createLogoUploadUrlHandler: {
    handler: 'src/lambda/logoUploadLambda.createLogoUploadUrlHandler',
    name: 'createLogoUploadUrlHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      S3_RECEIPT_LOGO_UPLOADS: '${self:custom.rawLogoUploadsBucket}',
      AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
      AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    },
    appsync: {
      fieldName: 'getReceiptSettingsLogoUploadUrl',
      typeName: 'Query',
      
    },
    policy: {
      managed: [
        'sessionCacheTableDBPolicyArn',
        'entityTableQueryRolePolicyArn',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
      inline: {
        createLogoUploadUrlS3Policy: [
          { actions: [Action.s3.PutObject], resources: ['arn:aws:s3:::${self:custom.rawLogoUploadsBucket}/*'] },
        ],
      },
    },
  },
  removeLogoHandler: {
    handler: 'src/lambda/logoUploadLambda.removeLogoHandler',
    name: 'removeLogoHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      SITE_GSI: '${self:custom.siteGsi}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      S3_RECEIPT_LOGO_PROCESSED: '${self:custom.receiptLogoProcessedBucket}',
      AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
      AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    },
    appsync: { fieldName: 'removeSiteLogo', typeName: 'Mutation',  },
    policy: {
      managed: ['sessionCacheTableDBPolicyArn', 'entityTableQueryRolePolicyArn'],
      inline: {
        removeLogoPolicy: [
          {
            actions: [Action.s3.DeleteObject],
            resources: ['arn:aws:s3:::${self:custom.receiptLogoProcessedBucket}/*'],
          },
        ],
      },
    },
  },
  getScreensaverLogoUploadDetailsHandler: {
    handler: 'src/lambda/logoUploadLambda.getScreensaverLogoUploadDetailsHandler',
    name: 'getScreensaverLogoUploadDetailsHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      S3_RECEIPT_LOGO_UPLOADS: '${self:custom.rawLogoUploadsBucket}',
      AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
      AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    },
    appsync: {
      fieldName: 'getScreensaverLogoUploadDetails',
      typeName: 'Query',
      
    },
    policy: {
      managed: [
        'sessionCacheTableDBPolicyArn',
        'entityTableQueryRolePolicyArn',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
      inline: {
        getScreensaverLogoUploadDetailsS3Policy: [
          { actions: [Action.s3.PutObject], resources: ['arn:aws:s3:::${self:custom.rawLogoUploadsBucket}/*'] },
        ],
      },
    },
  },
  updateScreensaverHandler: {
    handler: 'src/lambda/logoUploadLambda.updateScreensaverHandler',
    name: 'updateScreensaverHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      S3_RECEIPT_LOGO_PROCESSED: '${self:custom.receiptLogoProcessedBucket}',
      SCREENSAVER_LOGO_BASE_URL: '${self:custom.screensaverLogoBaseUrl}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
      AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    },
    appsync: { fieldName: 'updateScreensaver', typeName: 'Mutation',  },
    policy: {
      managed: [
        'sessionCacheTableDBPolicyArn',
        'entityTableQueryRolePolicyArn',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
      inline: {
        updateScreensaverS3Policy: [
          {
            actions: [Action.s3.DeleteObject],
            resources: ['arn:aws:s3:::${self:custom.receiptLogoProcessedBucket}/*'],
          },
        ],
      },
    },
  },
};
