IDENTITY_AUTH0_AUDIENCE=https://dashboard.myzeller.com
IDENTITY_AUTH0_TENANT=https://zeller-staging.au.auth0.com
OPENID_ISSUER_URL=https://auth.myzeller.show/
LOG_LEVEL=info
PARAMETERS_SECRETS_EXTENSION_LOG_LEVEL=none

NODE_RUNTIME=18
NODE_JS_RUNTIME=nodejs18.x

COMPONENT_NAME=mp
PART_NAME=api

# Dynamodb
COMPONENT_TABLE=Entities
SESSION_CACHE_TABLE=SessionCache
SITE_GSI=siteGsi
DEPOSIT_GSI=depositGsi
ENTITY_GSI=entityGsi
SHORT_ID_GSI=shortIdGsi
TYPE_GSI=typeGsiV2
SITE_NAME_GSI=siteNameGsi
SORT_KEY_GSI=sortKeyGsi
ENTITY_MODELSERIAL_GSI=modelSerialGsi
CARDHOLDER_GSI=carholderGsi
DEVICE_GSI=deviceGsi
ENTITY_CACHE_GSI=entityCacheGsi
DEPOSITS_PENDING_GSI=depositsPendingGsi
ENTITY_TRANSACTION_TOTAL_GSI=entityTransactionTotal200GsiV4
ORIGINAL_TRANSACTION_GSI=originalTransactionGsi
TRANSACTION_DATE_GSI=transactionDateGsi
ACCESS_TOKEN_GSI=accessTokenGsi
ENTITY_MODEL_SERIAL_GSI=entityModelSerialGsi
DEBIT_CARD_ID_GSI=debitCardIdGsi
SECONDARY_GSI_V1=secondaryGsiV1
CONTACT_UUID_GSI=contactUuidGsi
ENTITY_SORT_KEY_GSI=entitySortKeyGsi

CONTRIBUTOR_INSIGHTS=true

DEPOSITS_PENDING_SCHEDULER=cron(0 15 ? * 1 *)
BACKUP_SCHEDULER=cron(0 16 1 * ? *)

LAMBDA_TIMEOUT_IN_SECONDS=30

IDENTITY_AUTH0_JWT_MAX_AGE=86400
SMSCODE_TABLE=Smscodes
SMSCODE_TTL=600
SESSION_TTL=14400

SMS_VERIFICATION_RATE_LIMIT=3600000

MERCHANT_TABLE=Merchant
BANKING_PRODUCT_TABLE=BankingProduct

IAM_APPSYNC_PUBLISHER=appsync-publisher

# AMS
AMS_API_ENDPOINT_VERSION=v1

ERS_API_ENDPOINT_VERSION=v1

# CMS
CMS_API_ENDPOINT_VERSION=v1

#Keep warm
KEEP_WARM_SCHEDULER=cron(6/5 * ? * * *)
KEEP_WARM_SOURCE=keep.warm
LAMBDA_KEEP_WARM_STATE=DISABLED

# VPC
VPC_ENV_NAME=staging
STATIC_ENV_NAME=staging

# Ecommerce
ECOMMERCE_CHANNEL_ID=643938

# Frontend assets
PRINTED_RECEIPT_LOGO_SIZE=334x500
LOGO_UPLOAD_BUCKET_ALLOWED_ORIGINS=https://dashboard.myzeller.show,https://crms-crm.myzeller.show

# Card Logo
CARD_LOGO_SIZE=100x100
CARD_LOGO_MONO_PRINT_SIZE=400x168

# Multiple region
DEPLOY_MULTI_REGION=false
PRIMARY_REGION=ap-southeast-2

# global event bus name
GLOBAL_EVENT_BUS_NAME=staging-eventBus-global
# Billing
BILLING_API_ENDPOINT_VERSION=v1

# CRMS
CRMS_API_ENDPOINT=https://api.crms.myzeller.show
CRMS_API_ENDPOINT_VERSION=v1

# MFA
MFA_ENROLMENT_ENABLED=true
MFA_REQUIRED=false

# API GW
MP_DASHBOARD_ORIGIN=https://dashboard.myzeller.staging/
SHOULD_CREATE_CUSTOM_DOMAIN=false
DOMAIN_NAME=*.myzeller.staging
CERTIFICATE_NAME=
CERTIFICATE_ARN_SINGAPORE=
CERTIFICATE_ARN_SYDNEY=
API_VERSION=v1

# Banking Wrapper
BANKING_WRAPPER_ENABLED=true

# Conditional Fund Transfer Feature Flag
CONDITIONAL_FUND_TRANSFER_FEATURE_FLAG=true

#Firebase
ZELLER_APP_FIREBASE_PROJECT_ID=zeller-app
ALLOW_ZELLER_APP_FIREBASE_TEST_TOKEN=false

# Zeller Session ID
IS_ZELLER_SESSION_ID_ENABLED=true

# Selfie Verification API
SELFIE_CHECK_VERIFICATION_ENDPOINT_VERSION=v1
SELFIE_CHECK_VERIFICATION_ENABLED=true

ENABLE_AMS_DCA_CREATION=true

# Business Identifier Search
BUSINESS_IDENTIFIER_SEARCH_API_ENDPOINT_VERSION=v1

# Idv feature flag
IDV_ENABLED=true

# Payment Gateway
PGS_CNP_ENDPOINT=staging-pgs-api-cnp-apiGatewayEndpoint
PGS_CPOC_ENDPOINT=staging-pgs-api-cpoc-apiGatewayEndpoint

# RBAC
IS_RBAC_ENFORCED=false
IS_RBAC_ENFORCE_ROLE=false

# Multi-entity
MULTI_ENTITY_ENABLED=true
