import { appIdentityMiddleware } from '@npco/component-bff-core/dist/middleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import { bootstrapNestJSMiddleware } from '@npco/component-dbs-mp-common/dist/middleware/bootstrapNestJsMiddleware';
import type { NestAppEntityContext } from '@npco/component-dbs-mp-common/dist/types';

import type { Context, Handler } from 'aws-lambda';

import { BusinessIdentifierService } from '../../services/businessIdentifierService';
import { EntityModule } from '../../services/entityModule';

export const searchBusinessIdentifierHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  (event: { args: { businessIdentifier: string } }, context: Context) => {
    const { app, entityUuid, customerUuid } = context as NestAppEntityContext;
    return app
      .get(BusinessIdentifierService)
      .searchBusinessIdentifier(event.args.businessIdentifier, customerUuid, entityUuid);
  },
  [bootstrapNestJSMiddleware(EntityModule), appIdentityMiddleware(true)],
);
