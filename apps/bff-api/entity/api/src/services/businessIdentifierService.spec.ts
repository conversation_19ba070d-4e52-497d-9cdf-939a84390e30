import { ServerError } from '@npco/component-bff-core/dist/error';
import { EnvironmentService } from '@npco/component-dbs-mp-common/dist/config';

import { instance, mock, resetCalls, when } from 'ts-mockito';
import { v4 as uuidv4 } from 'uuid';

import { BusinessIdentifierApiService } from './businessIdentifierApiService';
import { BusinessIdentifierService } from './businessIdentifierService';

const mockEntityUuid = uuidv4();
const mockCustomerUuid = uuidv4();
const mockResponseWithOutEntityUuid = {
  businessIdentifier: 'test',
  found: true,
};
const mockResponseWithEntityUuid = {
  entityUuid: mockEntityUuid,
  businessIdentifier: 'test',
  found: true,
};

function setTestEnvironmentVariables() {
  process.env.REGION = 'ap-southeast-2';
}

describe('Business Identifier test suite', () => {
  let service: BusinessIdentifierService;
  const mockApiService = mock(BusinessIdentifierApiService);
  const envService = new EnvironmentService();

  beforeEach(() => {
    service = new BusinessIdentifierService(envService);
    service.apiService = instance(mockApiService);
    resetCalls(mockApiService);
    setTestEnvironmentVariables();
  });

  describe('with entityUuid', () => {
    it('Should return response', async () => {
      when(mockApiService.searchBusinessIdentifier).thenReturn(() =>
        Promise.resolve({ mockResponse: mockResponseWithEntityUuid } as any),
      );
      const response = await service.searchBusinessIdentifier('test', mockCustomerUuid, mockEntityUuid);
      expect(response).toEqual({ mockResponse: mockResponseWithEntityUuid });
    });

    it('Should handle error and return system error ', async () => {
      when(mockApiService.searchBusinessIdentifier).thenReturn(() => Promise.reject(new Error('System error')));
      const response = await service.searchBusinessIdentifier('test', mockCustomerUuid, mockEntityUuid);
      expect(response).toEqual({
        entityUuid: mockEntityUuid,
        businessIdentifier: 'test',
        country: 'AUS',
        found: false,
        error: 'System error',
      });
    });
    it('Should handle error and return GATEWAY_TIMEOUT ', async () => {
      when(mockApiService.searchBusinessIdentifier).thenReturn(() =>
        Promise.reject(new ServerError('GATEWAY_TIMEOUT')),
      );
      const response = await service.searchBusinessIdentifier('test', mockCustomerUuid, mockEntityUuid);
      expect(response).toEqual({
        entityUuid: mockEntityUuid,
        businessIdentifier: 'test',
        country: 'AUS',
        found: false,
        error: 'GATEWAY_TIMEOUT',
      });
    });
  });

  describe('without entityUuid', () => {
    it('Should return response', async () => {
      when(mockApiService.searchBusinessIdentifier).thenReturn(() =>
        Promise.resolve({ mockResponse: mockResponseWithOutEntityUuid } as any),
      );
      const response = await service.searchBusinessIdentifier('test', mockCustomerUuid, undefined);
      expect(response).toEqual({ mockResponse: mockResponseWithOutEntityUuid });
    });

    it('Should handle error and return system error ', async () => {
      when(mockApiService.searchBusinessIdentifier).thenReturn(() => Promise.reject(new Error('System error')));
      const response = await service.searchBusinessIdentifier('test', mockCustomerUuid, undefined);
      expect(response).toEqual({
        businessIdentifier: 'test',
        country: 'AUS',
        found: false,
        error: 'System error',
      });
    });
    it('Should handle error and return GATEWAY_TIMEOUT', async () => {
      when(mockApiService.searchBusinessIdentifier).thenReturn(() =>
        Promise.reject(new ServerError('GATEWAY_TIMEOUT')),
      );
      const response = await service.searchBusinessIdentifier('test', mockCustomerUuid, undefined);
      expect(response).toEqual({
        businessIdentifier: 'test',
        country: 'AUS',
        found: false,
        error: 'GATEWAY_TIMEOUT',
      });
    });
  });
});
