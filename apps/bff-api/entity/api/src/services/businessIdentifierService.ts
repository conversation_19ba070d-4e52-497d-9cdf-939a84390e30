import { getDefaultDomicileFromRegion } from '@npco/component-bff-core/dist/utils/domicile';
import { warn } from '@npco/component-bff-core/dist/utils/logger';
import { EnvironmentService } from '@npco/component-dbs-mp-common/dist/config';
import type { EntityInitialSearchCompletedEventDto } from '@npco/component-dto-entity';
import { EntityInitialSearchRequestedEventDto } from '@npco/component-dto-entity';

import { Injectable } from '@nestjs/common';

import { BusinessIdentifierApiService } from './businessIdentifierApiService';

@Injectable()
export class BusinessIdentifierService {
  apiService: BusinessIdentifierApiService;

  constructor(private readonly environmentService: EnvironmentService) {
    this.apiService = new BusinessIdentifierApiService(
      this.environmentService,
      this.environmentService.businessIdentifierSearchEndpointPath,
      'businessIdentifier',
    );
  }

  searchBusinessIdentifier = async (businessIdentifier: string, customerUuid: string, entityUuid?: string) => {
    const requestDto = new EntityInitialSearchRequestedEventDto({
      entityUuid,
      customerUuid,
      businessIdentifier,
    });
    let result: EntityInitialSearchCompletedEventDto;
    try {
      result = await this.apiService.searchBusinessIdentifier(customerUuid, requestDto);
    } catch (e: any) {
      warn(`Failed to search business identifier ${businessIdentifier}`, customerUuid);
      result = {
        entityUuid,
        businessIdentifier,
        country: getDefaultDomicileFromRegion(process.env.REGION ?? 'ap-southeast-2'),
        found: false,
        error: e?.message ?? 'System error',
      };
    }
    return result;
  };
}
