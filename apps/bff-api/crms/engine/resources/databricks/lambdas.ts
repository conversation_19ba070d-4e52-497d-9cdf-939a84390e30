import { Action, Arn, type ServerlessFunctions } from '@npco/component-bff-serverless';

import { lambdaCommon } from '../common/lambdaCommon';

export const lambdas: ServerlessFunctions = {
  getTransactionOutlierChartDataHandler: {
    handler: 'src/lambda/databricksLambdas.getTransactionOutlierChartDataHandler',
    name: 'getTransactionOutlierChartData',
    ...lambdaCommon,
    appsync: {
      fieldName: 'getTransactionOutlierChartData',
      typeName: 'Query',
    },
    environment: {
      DATABRICKS_CRMS_VPC: '${self:custom.crmsDatabrickVpc}',
      DATABRICKS_CRMS_HOST: '${self:custom.databricksHost}',
      DATABRICKS_CRMS_CLIENTID: '${self:custom.databricksClientId}',
      DATABRICKS_CRMS_CLIENT_SECRET_SSM: '${self:custom.databricksClientSecretSsm}',
    },
    layers: ['${self:custom.lambdaParameterExtensionArn}'],
    policy: {
      inline: {
        getSsmParameterPolicy: [
          {
            actions: [Action.ssm.GetParameter],
            resources: [Arn.ssm.parameter('${self:custom.databricksClientSecretSsm}')],
          },
        ],
      },
    },
  },
};
