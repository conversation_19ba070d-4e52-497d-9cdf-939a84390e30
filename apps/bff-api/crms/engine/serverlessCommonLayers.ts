import {
  ApiAppServerlessStack,
  Action,
  Arn,
  ManagedPolicy,
  ServerlessPlugin,
  createManagedPolicies,
} from '@npco/component-bff-serverless';

import { envConfig, esbuild } from './resources/common';
import { getRemoteAccountId, getRemoteRegion } from '@npco/component-bff-serverless/dist/param/util';

const region = process.env.AWS_REGION;
const remoteRegion = getRemoteRegion(region);
const remoteAccountId = getRemoteAccountId(region);

const policies = createManagedPolicies([
  ManagedPolicy.allGsiQueryRolePolicyArn,
  ManagedPolicy.entityTableQueryRolePolicy,
  ManagedPolicy.crmsEntityTableWriteRolePolicy,
  ManagedPolicy.entityTableDeleteRolePolicy,
  ManagedPolicy.xrayPolicy,
  ManagedPolicy.lambdaVpcPolicy,
  ManagedPolicy.pgsV2RootCaSecretPolicyArn,
  ManagedPolicy.crossAccountInvocationPolicyArn,
]);

const sls = new ApiAppServerlessStack('commonLayers', envConfig, {
  plugins: [ServerlessPlugin.ResourceTagging],
  environment: {
    ...envConfig.dotenvConfig,
    COMPONENT_TABLE: envConfig.componentTableName,
  },
  custom: {
    ...envConfig.getDynamoDb(),
    esbuild,
    service: envConfig.service,
    entityTableName: '${self:custom.dynamodbStackName}-Entities',
    merchantTableName: '${self:custom.dynamodbStackName}-${env:MERCHANT_TABLE}',
  },
  resources: {
    ...policies.resources?.Resources,
    merchantTableQueryRolePolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: [Action.dynamodb.Query, Action.dynamodb.BatchGetItem],
              Resource: [Arn.dynamodb.table('${self:custom.merchantTableName}')],
            },
          ],
        },
      },
    },
  },
  outputs: {
    ...policies.resources?.Outputs,
    merchantTableQueryRolePolicyArn: {
      Value: {
        Ref: 'merchantTableQueryRolePolicy',
      },
      Export: {
        Name: {
          'Fn::Join': ['', ['${self:custom.service}', '-merchantTableQueryRolePolicyArn']],
        },
      },
    },
  },
});

module.exports = sls.build();
