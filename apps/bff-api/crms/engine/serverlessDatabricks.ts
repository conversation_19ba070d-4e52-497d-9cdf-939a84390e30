import { ApiAppServerlessStack, ssmSharedVpcImport as vpcImport } from '@npco/component-bff-serverless';

import { envConfig, pluginsApp } from './resources/common';
import { lambdas } from './resources/databricks/lambdas';


const esbuild = {
  esbuild: {
    bundle: true,
    external: ['lz4'],
    minify: false,
    sourcemap: false,
    exclude: [],
    target: 'node18',
  },
};

const sls = new ApiAppServerlessStack('databricks', envConfig, {
  plugins: [...pluginsApp],
  custom: {
    ...envConfig.getAppsync(),
    vpcImport,
    crmsDatabrickVpc: envConfig.ssmParam('DATABRICKS_CRMS_VPC'),
    databricksHost: envConfig.ssmParam('DATABRICKS_CRMS_ENDPOINT'),
    databricksClientId: envConfig.ssmParam('DATABRICKS_CRMS_CLIENTID'),
    databricksClientSecretSsm: '/${opt:stage}-crms-engine/DATABRICKS_CRMS_SERVICE_PRINCIPAL_KEY',
    lambdaParameterExtensionArn: 'arn:aws:lambda:${opt:region}:${ssm:Parameters-and-Secrets-Lambda-Extension}:layer:AWS-Parameters-and-Secrets-Lambda-Extension:17',
    ...esbuild,
  },
  functions: lambdas,
  environment: {
    ...envConfig.dotenvConfig,
  },
});

module.exports = sls.build();
