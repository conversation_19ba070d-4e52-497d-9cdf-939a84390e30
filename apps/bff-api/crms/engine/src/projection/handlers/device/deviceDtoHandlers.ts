import type { DeviceCreatedEventDto, DeviceInfoUpdateDto, DeviceUpdatedEventDto } from '@npco/component-dto-device';

import { getDeviceManagementService, getDeviceService } from '../../../dependencies/dbsMpCommonDependencies';
import type { NonHubspotHandler } from '../../types';

export class <PERSON><PERSON><PERSON>reated<PERSON><PERSON><PERSON> implements NonHubspotHandler<DeviceCreatedEventDto> {
  async handle(dto: DeviceCreatedEventDto, _aggregateId: string) {
    const deviceService = getDeviceService();
    await deviceService.updateDeviceSettingsProjection(dto);
  }
}

export class DeviceUpdatedHandler implements NonHubspotHandler<DeviceUpdatedEventDto> {
  async handle(dto: DeviceUpdatedEventDto, _aggregateId: string) {
    const deviceService = getDeviceService();
    await deviceService.updateDeviceSettingsProjection(dto);
  }
}

export class DeviceInformationUpdated<PERSON>and<PERSON> implements NonHubspotHandler<DeviceInfoUpdateDto> {
  async handle(dto: DeviceInfoUpdateDto, _aggregateId: string) {
    const deviceManagementService = getDeviceManagementService();
    await deviceManagementService.saveDeviceInformationUpdateProjection(dto);
  }
}
