import { info } from '@npco/component-bff-core/dist/utils/logger';
import type { DeviceStatus } from '@npco/component-dto-device';
import { DeviceCreatedEventDto } from '@npco/component-dto-device';
import { CrmsDeviceCreatedEvent } from '@npco/component-events-crms';

import { HookAction } from '@nestpack/hooks';
import type { IHookAction } from '@nestpack/hooks';
import { DeviceService } from 'bff-device-api/dist';

@HookAction(CrmsDeviceCreatedEvent)
export class DeviceCreatedHandler implements IHookAction {
  constructor(private readonly deviceService: DeviceService) {}

  async handle(event: CrmsDeviceCreatedEvent) {
    info(`hook handle ${this.constructor.name} ${JSON.stringify(event)}`);
    const dto = new DeviceCreatedEventDto({
      deviceUuid: event.aggregateId,
      ...event.payload,
      status: event.payload.status as unknown as DeviceStatus,
    } as DeviceCreatedEventDto);
    await this.deviceService.updateDeviceSettingsProjection(dto);
  }
}
