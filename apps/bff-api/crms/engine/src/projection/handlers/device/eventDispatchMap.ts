import type { CrmsEventDispatchMap } from '../../types';

import { DeviceCreated<PERSON>andler, DeviceUpdatedHandler, DeviceInformationUpdatedHandler } from './deviceDtoHandlers';

export const highPriorityQueueEventDispatchMap: CrmsEventDispatchMap = {
  Device: {
    Created: {
      aggregateField: 'deviceUuid',
      nonHubspotHandler: new DeviceCreatedHandler(),
    },
    Updated: {
      aggregateField: 'deviceUuid',
      nonHubspotHandler: new DeviceUpdatedHandler(),
    },
    InformationUpdated: {
      aggregateField: 'deviceUuid',
      nonHubspotHandler: new DeviceInformationUpdatedHandler(),
    },
  },
};
