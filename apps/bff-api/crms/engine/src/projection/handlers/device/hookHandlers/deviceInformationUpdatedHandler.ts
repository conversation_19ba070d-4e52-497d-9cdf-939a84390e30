import { info } from '@npco/component-bff-core/dist/utils/logger';
import { DeviceInfoUpdateDto } from '@npco/component-dto-device';
import { CrmsDeviceInformationUpdatedEvent } from '@npco/component-events-crms';

import { HookAction } from '@nestpack/hooks';
import type { IHookAction } from '@nestpack/hooks';
import { DeviceManagementService } from 'bff-device-api/dist';

@HookAction(CrmsDeviceInformationUpdatedEvent)
export class DeviceInformationUpdatedHandler implements IHookAction {
  constructor(private readonly deviceService: DeviceManagementService) {}

  async handle(event: CrmsDeviceInformationUpdatedEvent) {
    info(`hook handle ${this.constructor.name} ${JSON.stringify(event)}`);
    const dto = new DeviceInfoUpdateDto({
      id: event.aggregateId,
      ...event.payload,
    });
    await this.deviceService.saveDeviceInformationUpdateProjection(dto);
  }
}
