import { info } from '@npco/component-bff-core/dist/utils/logger';
import type { DeviceStatus } from '@npco/component-dto-device';
import { DeviceUpdatedEventDto } from '@npco/component-dto-device';
import { CrmsDeviceUpdatedEvent } from '@npco/component-events-crms';

import { HookAction } from '@nestpack/hooks';
import type { IHookAction } from '@nestpack/hooks';
import { DeviceService } from 'bff-device-api/dist';

@HookAction(CrmsDeviceUpdatedEvent)
export class DeviceUpdatedHandler implements IHookAction {
  constructor(private readonly deviceService: DeviceService) {}

  async handle(event: CrmsDeviceUpdatedEvent) {
    info(`hook handle ${this.constructor.name} ${JSON.stringify(event)}`);
    const dto = new DeviceUpdatedEventDto({
      deviceUuid: event.aggregateId,
      ...event.payload,
      status: event.payload.status as unknown as DeviceStatus,
    } as DeviceUpdatedEventDto);
    await this.deviceService.updateDeviceSettingsProjection(dto);
  }
}
