import { posInterfaceProjectionEvents } from '@npco/component-dbs-mp-common/src/posInterface/posInterfaceProjection';

import type { CrmsEventDispatchMap, HubspotEventDispatchMap } from '../types';

import { highPriorityQueueEventDispatchMap as thirdPartyAccountHPQ } from './3rdPartyAccount/eventDispatchMap';
import { highPriorityQueueEventDispatchMap as cardholderHPQ } from './cardholder/eventDispatchMap';
import {
  customObjectQueueEventDispatchMap as cbsProjectionCustomObject,
  highPriorityQueueEventDispatchMap as cbsProjectionHPQ,
} from './cbsProjection/eventDispatchMap';
import {
  customObjectQueueEventDispatchMap as customerCustomObject,
  highPriorityQueueEventDispatchMap as customerHPQ,
  hubspotDefaultQueueEventDispatchMap as customerHSDefault,
} from './customer/eventDispatchMap';
import {
  customObjectQueueEventDispatchMap as customerEntityCustomObject,
  highPriorityQueueEventDispatchMap as customerEntityHPQ,
} from './customerEntity/eventDispatchMap';
import { highPriorityQueueEventDispatchMap as settlementsHPQ } from './deposit/eventDispatchMap';
import { highPriorityQueueEventDispatchMap as deviceHPQ } from './device/eventDispatchMap';
import {
  documentDeprecatedEventDispatchMap,
  highPriorityQueueEventDispatchMap as documentHPQ,
  hubspotDefaultQueueEventDispatchMap as documentHSDefault,
} from './document/eventDispatchMap';
import {
  customObjectQueueEventDispatchMap as entityCustomObject,
  entityDeprecatedEventDispatchMap,
  highPriorityQueueEventDispatchMap as entityHPQ,
  hubspotDefaultQueueEventDispatchMap as entityHSDefault,
} from './entity/eventDispatchMap';
import { highPriorityQueueEventDispatchMap as hubspotRuleHPQ } from './hubspotRule/eventDispatchMap';
import { highPriorityQueueEventDispatchMap as orderHPQ } from './order/eventDispatchMap';
import {
  paymentInstrumentDeprecatedEventDispatchMap,
  highPriorityQueueEventDispatchMap as paymentInstrumentHPQ,
} from './paymentInstrument/eventDispatchMap';
import { highPriorityQueueEventDispatchMap as siteHPQ } from './site/eventDispatchMap';
import { highPriorityQueueEventDispatchMap as transactionHPQ } from './transaction/eventDispatchMap';

// BANK-5110, will be removed when all events switched to HPQ
// the CrmsEventDispatchMap for the EventBridgeEventData pattern in the sqsDomainProjection
// will gradually be replaced by `eventDispatchMap` below and the highPriorityQueue
// will be removed once all the events are migrated into `eventDispatchMap` and events switch over to HPQ
export const deprecatedEventDispatchMap: CrmsEventDispatchMap = {
  ...documentDeprecatedEventDispatchMap,
  ...entityDeprecatedEventDispatchMap,
  ...paymentInstrumentDeprecatedEventDispatchMap,
};

export const highPriorityQueueEventDispatchMap: CrmsEventDispatchMap = {
  ...cardholderHPQ,
  ...cbsProjectionHPQ,
  ...customerHPQ,
  ...customerEntityHPQ,
  ...documentHPQ,
  ...entityHPQ,
  ...hubspotRuleHPQ,
  ...paymentInstrumentHPQ,
  ...siteHPQ,
  ...thirdPartyAccountHPQ,
  ...transactionHPQ,
  ...orderHPQ,
  ...posInterfaceProjectionEvents,
  ...settlementsHPQ,
  ...deviceHPQ,
};

export const hubspotDefaultQueueEventDispatchMap: HubspotEventDispatchMap = {
  ...customerHSDefault,
  ...documentHSDefault,
  ...entityHSDefault,
};

export const customObjectQueueEventDispatchMap: HubspotEventDispatchMap = {
  ...cbsProjectionCustomObject,
  ...customerCustomObject,
  ...customerEntityCustomObject,
  ...entityCustomObject,
};
