import { SqsClient } from '@npco/component-bff-core/dist/aws/sqsClient';
import { aggregateTracing } from '@npco/component-bff-core/dist/middleware';
import {
  debug,
  error,
  info,
  setLoggerAggregateId,
  unsetLoggerAggregateId,
  warn,
} from '@npco/component-bff-core/dist/utils/logger';
import type { IProjectedEventData } from '@npco/component-dbs-mp-common';
import { DynamodbService } from '@npco/component-dbs-mp-common';
import type { AttributeMap } from '@npco/component-dbs-mp-common/dist/dynamodb/types';
import { DbRecordType } from '@npco/component-dto-core';
import type { PaymentInstrumentUnlinkedDto } from '@npco/component-dto-payment-instrument';
import { AwsEventBridgeEventProxy } from '@npco/component-events-proxy';

import type { SendMessageCommandInput } from '@aws-sdk/client-sqs';
import { Injectable } from '@nestjs/common';
import { HooksService } from '@nestpack/hooks';
import type { SQSBatchItemFailure, SQSBatchResponse } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';

import { CrmsEnvironmentService, ErrorType, HubspotApi } from '../common';
import { HubspotOauthAuthenticator } from '../common/api/hubspotApiAuthenticator';
import type { BatchUpdateValue, EntityMetricEvent } from '../common/types';
import { AssociationType, ObjectType } from '../common/types';
import { HubspotMetricsMapping, hubspotMetricsFromMetricEventPayload } from '../entity/entityProperties';
import { convertDbItemToEntity } from '../entity/entityUtils';
import { HubspotService, HubspotBulkUpdateService, HubspotDocScanningService } from '../hubspot';
import { getSqsUrlByQueueName } from '../util/getSqsUrlByQueueName';

import {
  deprecatedEventDispatchMap,
  highPriorityQueueEventDispatchMap,
  hubspotDefaultQueueEventDispatchMap,
} from './handlers/eventDispatchMap';
import { HubspotQueue } from './types';
import type {
  CrmsEventDispatchMap,
  EventBridgeEventData,
  HubspotDeprecatedHandler,
  HubspotEventDispatchMap,
  HubspotQueueMessage,
  NonHubspotHandler,
  PartialSqsRecord,
  SnsRiskEngineEvent,
} from './types';

@Injectable()
export class CrmsProjectionService {
  sqs: SqsClient;

  hubspotApi: HubspotApi;

  hubspotBatchUpdateApi: HubspotApi;

  constructor(
    private readonly hooksService: HooksService,
    private readonly envService: CrmsEnvironmentService,
    private readonly dynamodbService: DynamodbService,
    private readonly hubspotService: HubspotService,
    private readonly hubspotBulkUpdateService: HubspotBulkUpdateService,
    private readonly hubspotDocScanningService: HubspotDocScanningService,
  ) {
    this.sqs = new SqsClient(this.envService.region);
    this.hubspotApi = new HubspotApi(this.envService.hubspotApiEndpoint, new HubspotOauthAuthenticator(hubspotService));
    this.hubspotBatchUpdateApi = new HubspotApi(
      this.envService.hubspotApiEndpoint,
      new HubspotOauthAuthenticator(this.hubspotBulkUpdateService),
    );
  }

  private readonly deleteMessage = async (event: any, sqsQueue: string) => {
    const output = await this.sqs.deleteMessage(sqsQueue, event.receiptHandle);
    debug(`delete message from sqs ${JSON.stringify(event)} ${JSON.stringify(output)}`);
  };

  private readonly checkErrors = (
    /* istanbul ignore next: difficult to test the default value */ errorEvents: any = [],
  ) => {
    info(`checkErrors:length ${errorEvents.length}`);
    if (errorEvents.length > 0) {
      throw new Error(`process event ${JSON.stringify(errorEvents)} failed.`);
    }
  };

  projectEvent = async (
    events: {
      body: string;
      receiptHandle: string;
    }[],
  ) => {
    debug(`projectEvent: events are -> ${JSON.stringify(events)}`);
    const errorEvents: any = [];
    for (const event of events) {
      unsetLoggerAggregateId();
      const eventData: EventBridgeEventData = JSON.parse(event.body);
      const [, aggregate, actionName] = eventData['detail-type'].split('.');
      const eventStrategy = deprecatedEventDispatchMap[aggregate]?.[actionName];
      setLoggerAggregateId(eventData.detail.aggregateId);

      await aggregateTracing(eventData.detail.aggregateId, async () => {
        try {
          if (eventStrategy) {
            if (eventStrategy.hubspotDeprecatedHandler) {
              await this.executeHubspotHandler(eventStrategy.hubspotDeprecatedHandler, eventData);
            }

            if (eventStrategy.nonHubspotHandler) {
              await this.executeNonHubspotHandler(
                eventStrategy.isDomainEvent,
                eventStrategy.nonHubspotHandler,
                eventData,
              );
            }
          } else {
            // the goal is to move all the logic to the processorMap, and eventually remove this block
            // to be migrated by ticket https://npco-dev.atlassian.net/browse/TECH-501
            // prevent adding newer events to the Nest Hook logic, but rather add them to the eventDispatchMap
            const domainEvent = new AwsEventBridgeEventProxy(eventData).toDomainEvent();
            info(`get domain event ${JSON.stringify(domainEvent)}`, domainEvent.aggregateId);
            await this.hooksService.runHook(domainEvent);
          }

          await this.deleteMessage(event, this.envService.projectionSqsUrl);
        } catch (err: any) {
          errorEvents.push(eventData);
          error(err.message, eventData.detail.aggregateId);
        }
      });
    }
    this.checkErrors(errorEvents);
  };

  projectHighPriorityQueueEvent = async (events: PartialSqsRecord[]): Promise<SQSBatchResponse> => {
    const eventsByAggregateId = this.groupEventsByAggregateId(events, highPriorityQueueEventDispatchMap);
    const batchItemFailures: SQSBatchItemFailure[] = [];

    for (const [aggregateId, groupedEvents] of Object.entries(eventsByAggregateId)) {
      info(`projectHighPrioriyQueueEvent -> ${aggregateId} ${groupedEvents.length}`);
      const eventsToProcess = [...groupedEvents];
      while (eventsToProcess.length > 0) {
        try {
          const event = eventsToProcess[0];
          await this.processHighPriorityQueueEvent(event);
          eventsToProcess.shift();
        } catch (err: any) {
          warn(`projectHighPrioriyQueueEvent processError -> ${err?.stack}`);
          break;
        }
      }
      eventsToProcess.forEach((event) => {
        batchItemFailures.push({ itemIdentifier: event.messageId });
      });
    }

    return { batchItemFailures };
  };

  private readonly processHighPriorityQueueEvent = async (event: PartialSqsRecord) => {
    unsetLoggerAggregateId();
    const eventData: IProjectedEventData<any> = JSON.parse(event.body);
    const [, aggregate, actionName] = eventData['detail-type'].split('.');
    const eventStrategy = highPriorityQueueEventDispatchMap[aggregate]?.[actionName];
    const aggregateId = eventStrategy?.aggregateField ? eventData.detail[eventStrategy.aggregateField] : undefined;

    setLoggerAggregateId(aggregateId);
    debug(`projectHighPriorityQueueEvent: event -> ${JSON.stringify(eventData)}`);

    await aggregateTracing(aggregateId, async () => {
      if (!eventStrategy) {
        error(
          `projectHighPriorityQueueEvent: event strategy not found for ${aggregate}.${actionName}, please add it to the eventDispatchMap`,
        );
        throw new Error(`Event strategy not found for ${aggregate}.${actionName}`);
      }

      if (eventStrategy.hubspotQueues) {
        const queueMessage: HubspotQueueMessage = {
          messageBody: JSON.stringify(eventData),
          messageDeduplicationId: uuidv4(),
          messageGroupId: aggregateId,
        };
        await Promise.all(
          eventStrategy.hubspotQueues.map(async (hubspotQueue: HubspotQueue) => {
            await this.sendMessageToHubspotQueue(hubspotQueue, queueMessage);
          }),
        );
      }
      if (eventStrategy.hubspotHandler) {
        await eventStrategy.hubspotHandler.handle(eventData, aggregateId);
      }
      if (eventStrategy.nonHubspotHandler) {
        const dto = eventData.detail;
        await eventStrategy.nonHubspotHandler.handle(dto, aggregateId);
      }

      if (eventStrategy.getServiceInstance && eventStrategy.functionName) {
        const dto = eventData.detail;
        const service = eventStrategy.getServiceInstance();
        await service[eventStrategy.functionName](dto);
      }

      await this.deleteMessage(event, this.envService.projectionSqsUrl);
    });
  };

  private readonly sendMessageToHubspotQueue = async (hubspotQueue: HubspotQueue, message: HubspotQueueMessage) => {
    if (hubspotQueue === HubspotQueue.DEFAULT_PROPERTY || hubspotQueue === HubspotQueue.CUSTOM_OBJECT) {
      const input: SendMessageCommandInput = {
        QueueUrl: getSqsUrlByQueueName(hubspotQueue, this.envService),
        MessageBody: message.messageBody,
        MessageDeduplicationId: message.messageDeduplicationId,
        MessageGroupId: message.messageGroupId,
      };
      info(`send message to sqs ${JSON.stringify(input)}`);
      await this.sqs.sendRawMessage(input);
    }
  };

  private readonly executeNonHubspotHandler = async (
    isDomainEvent: boolean | undefined,
    nonHubspotHandler: NonHubspotHandler<any>,
    eventData: EventBridgeEventData,
  ) => {
    const data = isDomainEvent ? new AwsEventBridgeEventProxy(eventData).toDomainEvent() : eventData;
    const aggregateId = isDomainEvent ? data.aggregateId : eventData.detail.aggregateId;
    await nonHubspotHandler.handle(data, aggregateId);
  };

  private readonly executeHubspotHandler = async (
    hubspotDeprecatedHandler: HubspotDeprecatedHandler,
    eventData: EventBridgeEventData,
  ) => {
    await hubspotDeprecatedHandler.handle(eventData);
  };

  projectHubspotDefaultEvent = async (events: PartialSqsRecord[]) => {
    const eventsByAggregateId = this.groupEventsByAggregateId(events, hubspotDefaultQueueEventDispatchMap);
    const batchItemFailures: SQSBatchItemFailure[] = [];

    for (const [aggregateId, groupedEvents] of Object.entries(eventsByAggregateId)) {
      info(`projectHubspotDefaultEvent -> ${aggregateId} ${groupedEvents.length}`);
      const eventsToProcess = [...groupedEvents];
      while (eventsToProcess.length > 0) {
        try {
          const event = eventsToProcess[0];
          await this.processHubspotDefaultEvent(event);
          eventsToProcess.shift();
        } catch (err: any) {
          warn(`projectHubspotDefaultEvent processError -> ${err?.stack}`);
          break;
        }
      }
      eventsToProcess.forEach((event) => {
        batchItemFailures.push({ itemIdentifier: event.messageId });
      });
    }

    return { batchItemFailures };
  };

  private readonly processHubspotDefaultEvent = async (event: PartialSqsRecord) => {
    const eventData: IProjectedEventData<any> = JSON.parse(event.body);
    const [, aggregate, actionName] = eventData['detail-type'].split('.');
    const eventStrategy = hubspotDefaultQueueEventDispatchMap[aggregate]?.[actionName];
    const dto = eventData.detail;
    const aggregateField = eventStrategy?.aggregateField;
    const aggregateId = aggregateField ? dto[aggregateField] : undefined;
    await aggregateTracing(aggregateId, async () => {
      try {
        if (!eventStrategy?.handler) {
          const log = this.getLogFunction(aggregate, actionName);
          log(
            `projectHubspotDefaultEvent: event strategy not found for ${aggregate}.${actionName}, please add it to the eventDispatchMap`,
          );
          throw new Error(`Event strategy not found for ${aggregate}.${actionName}`);
        }

        info(`projectHubspotDefaultEvent: get event ${JSON.stringify(eventData)}`, aggregateId);

        await eventStrategy.handler.handle(dto, aggregateId);
        await this.deleteMessage(event, this.envService.hubspotDefaultSqsUrl);
      } catch (err: any) {
        if (this.isRetryOrRateLimitError(err)) {
          warn(`Failed to project events, need to retry. ${aggregate}.${actionName}`, aggregateId);
          throw new Error(`process event ${JSON.stringify(event)} failed.`);
        } else {
          warn(`projectHubspotEvent:error: ${JSON.stringify(err)}`, aggregateId);
          await this.deleteMessage(event, this.envService.hubspotDefaultSqsUrl);
        }
      }
    });
  };

  private readonly getLogFunction = (aggregate: string, actionName: string) => {
    const aggregateAction = `${aggregate}.${actionName}`;
    const warnLevelAggregates = ['CustomerEntity.Linked'];
    return warnLevelAggregates.includes(aggregateAction) ? warn : error;
  };

  private readonly groupEventsByAggregateId = (
    events: PartialSqsRecord[],
    eventDispatchMap: HubspotEventDispatchMap | CrmsEventDispatchMap,
  ): Record<string, PartialSqsRecord[]> => {
    const eventsByAggregateId: Record<string, PartialSqsRecord[]> = {};
    const GROUP_NAME_FOR_EVENTS_WITHOUT_AGGREGATE_ID = 'eventsWithoutAggregateId';
    events.forEach((event) => {
      const eventData: IProjectedEventData<any> = JSON.parse(event.body);
      const [, aggregate, actionName] = eventData['detail-type'].split('.');
      const eventStrategy = eventDispatchMap[aggregate]?.[actionName];
      const dto = eventData.detail;
      const aggregateField = eventStrategy?.aggregateField;
      const aggregateId = aggregateField ? dto[aggregateField] : GROUP_NAME_FOR_EVENTS_WITHOUT_AGGREGATE_ID;
      if (!eventsByAggregateId[aggregateId]) {
        eventsByAggregateId[aggregateId] = [];
      }
      eventsByAggregateId[aggregateId].push(event);
    });
    return eventsByAggregateId;
  };

  handleHubspotEvent = async (event: any): Promise<void> => {
    const snsData: SnsRiskEngineEvent = JSON.parse(event.Message);
    const properties = {
      hs_pipeline: this.envService.hubspotRiskEnginePipeline,
      hs_pipeline_stage: this.envService.hubspotRiskEnginePipelineStage,
      hs_ticket_priority: this.envService.hubspotRiskEnginePriority,
      subject: `${this.envService.hubspotRiskEngineSubject} #${snsData.scoreboardId || ''}`,
      content: JSON.stringify(snsData, null, 2),
    };
    const ticket = await this.hubspotApi.createTicket({ properties });
    debug(`handleHubspotEvent: ticket is -> ${ticket}`);
    const item = await this.getDbItem(snsData.entityId, DbRecordType.ENTITY);
    debug(`handleHubspotEvent: our item is -> ${item}`);
    if (item) {
      const entity = convertDbItemToEntity(item);
      debug(`handleHubspotEvent: our entity is -> ${item}`);
      await this.hubspotApi.associateObject(
        ObjectType.TICKETS,
        ticket.id,
        ObjectType.COMPANY,
        entity.hubspotCompanyId ?? '',
        AssociationType.TICKET_COMPANY,
      );
    } else {
      throw new Error(`Entity not found ${snsData.entityId}`);
    }
  };

  riskEngineHubspotEvent = async (
    event: {
      body: string;
      receiptHandle: string;
    }[],
  ) => {
    const data: SnsRiskEngineEvent[] = event.map((e) => JSON.parse(e.body));
    const errorEvents: any = [];
    for (let i = 0; i < data.length; i += 1) {
      const eventData = data[i];
      await aggregateTracing(eventData.entityId || '', async () => {
        try {
          await this.handleHubspotEvent(eventData);
          await this.deleteMessage(event[i], this.envService.hubspotRiskEngineSqsUrl);
        } catch (err: any) {
          warn(`riskEngineHubspotEvent: get error -> ${err}`);
          /* istanbul ignore if */
          if (this.isRetryOrRateLimitError(err)) {
            warn('Failed to handle hubspot risk event, need to retry.');
            errorEvents.push(eventData);
          } else {
            await this.deleteMessage(event[i], this.envService.hubspotRiskEngineSqsUrl);
          }
        }
      });
    }
    this.checkErrors(errorEvents);
  };

  getDbItem = async (id: string, type: DbRecordType): Promise<AttributeMap | null> => {
    const item = await this.dynamodbService.queryIdByType('', id, type, {
      ignoreEntity: true,
    });
    /* istanbul ignore else */
    if (item.Items && item.Items.length > 0) {
      return item.Items[0];
    }
    /* istanbul ignore next */
    return null;
  };

  saveProjectionRecord = async (id: string, dto: any, type: DbRecordType | string): Promise<void> => {
    await this.dynamodbService
      .saveConditionally(this.envService.componentTableName, { id, type }, dto, this.getDbItem, this, [id, type])
      .catch(
        /* istanbul ignore next */ (err) => {
          error(`Projection data was not updated: ${JSON.stringify(err)}`);
        },
      );
  };

  getPaymentInstrumentWithoutContactDbItem = async (id: string): Promise<AttributeMap | null> => {
    const item = await this.getDbItem(id, DbRecordType.PAYMENT_INSTRUMENT);
    if (item) {
      delete item.contactUuid;
    }
    return item;
  };

  savePaymentInstrumentUnlinkedProjectionRecord = async (
    id: string,
    dto: PaymentInstrumentUnlinkedDto,
  ): Promise<void> => {
    await this.dynamodbService
      .saveConditionally(
        this.envService.componentTableName,
        { id, type: DbRecordType.PAYMENT_INSTRUMENT },
        dto,
        this.getPaymentInstrumentWithoutContactDbItem,
        this,
        [id],
      )
      .catch(
        /* istanbul ignore next */ (err) => {
          error(`Projection data was not updated: ${JSON.stringify(err)}`);
        },
      );
  };

  deleteProjectionRecord = async (id: string, type: string): Promise<void> => {
    info(`deleteProjectionRecord ${id} ${type}`, id);
    await this.dynamodbService.delete({
      TableName: this.envService.componentTableName,
      Key: { id, type },
    });
  };

  projectBulkUpdateEvent = async (
    event: {
      body: string;
      receiptHandle: string;
    }[],
  ) => {
    const eventData = event.map((e) => ({ body: JSON.parse(e.body), receiptHandle: e.receiptHandle }));
    info(`bulk update event ${JSON.stringify(eventData)}`);
    const errorEvents = [];
    // eslint-disable-next-line no-restricted-syntax
    for (const value of eventData) {
      const batchedEvents: EntityMetricEvent[] = value.body;
      info(`process dms metrics: ${JSON.stringify(batchedEvents)}`);
      const batchedUpdateValues: BatchUpdateValue[] = await this.findBatchedEventValues(batchedEvents);
      info(`complete batchedUpdateValues: ${JSON.stringify(batchedUpdateValues)}`);
      if (batchedUpdateValues.length > 0) {
        try {
          await this.hubspotBatchUpdateApi.batchUpdateObjects(ObjectType.COMPANY, batchedUpdateValues);
          await this.deleteMessage(value, this.envService.bulkUpdateSqsUrl);
        } catch (err: any) {
          if (this.isRetryOrRateLimitError(err)) {
            warn(`Failed to project events, need to retry. ${err.message}`);
            errorEvents.push(value);
          } else {
            warn(`Error while projectBulkUpdateEvent: ${err.message}`);
          }
        }
      } else {
        error(`no update on batched event ${JSON.stringify(event)}`);
      }
      this.checkErrors(errorEvents);
    }
  };

  private readonly findBatchedEventValues = async (batchedEvents: EntityMetricEvent[]) => {
    const batchedUpdateValue = [];
    // eslint-disable-next-line no-restricted-syntax
    for (const batchedEvent of batchedEvents) {
      info(`processing batchedEvent: ${JSON.stringify(batchedEvent)}`);
      const entity = await this.getDbItem(batchedEvent.entityId, DbRecordType.ENTITY);
      const metricsId = batchedEvent.metricsId;
      if (entity?.hubspotCompanyId && metricsId.toLowerCase() in HubspotMetricsMapping) {
        const updateProperties = [
          {
            name: HubspotMetricsMapping[metricsId.toLowerCase() as keyof typeof HubspotMetricsMapping],
            value: batchedEvent.metricsValue,
          },
        ];
        info(`updateProperties: ${JSON.stringify(updateProperties)}`);
        hubspotMetricsFromMetricEventPayload.forEach((metrics) => {
          if (
            metricsId.toLowerCase() === metrics.sourceMetricsId &&
            metrics.validateRawPayload(batchedEvent.rawPayload)
          ) {
            updateProperties.push({
              name: metrics.metricsId,
              value: metrics.getMetricsValueFromRawPayload(batchedEvent.rawPayload),
            });
          }
        });

        batchedUpdateValue.push({
          objectId: entity.hubspotCompanyId,
          properties: updateProperties,
        });
        info(`batchedUpdateValue: ${JSON.stringify(batchedUpdateValue)}`);
      } else {
        warn(
          `Can't save metrics for ${JSON.stringify(batchedEvent)}, ${JSON.stringify(entity)}, ${metricsId}`,
          batchedEvent.entityId,
        );
      }
    }
    return batchedUpdateValue;
  };

  documentScanningResultsEvent = async (
    event: {
      body: string;
      receiptHandle: string;
    }[],
  ) => {
    const data: { Message: string }[] = event.map((e) => JSON.parse(e.body));
    info(`documentScanningResultsEvent data is ${JSON.stringify(data)}`);
    const errorEvents: any = [];
    for (let i = 0; i < data.length; i += 1) {
      const eventData = data[i];
      try {
        const snsData: { guid: string; scanStatus: string; customDisplayStatus: string } = JSON.parse(
          eventData.Message,
        );
        await this.hubspotDocScanningService.handleDocumentScanningEvent(snsData);
        await this.deleteMessage(event[i], this.envService.documentScanningSqsUrl);
      } catch (err: any) {
        warn(`documentScanningResultsEvent: get error -> ${err}`);
        /* istanbul ignore if */
        if (this.isRetryOrRateLimitError(err)) {
          warn('Failed to handle document scanning event, need to retry.');
          errorEvents.push(eventData);
        } else {
          await this.deleteMessage(event[i], this.envService.documentScanningSqsUrl);
        }
      }
    }
    this.checkErrors(errorEvents);
  };

  private readonly isRetryOrRateLimitError = (err: any) =>
    err && (err.errorType === ErrorType.RETRY || err.errorType === ErrorType.RATE_LIMIT);
}
