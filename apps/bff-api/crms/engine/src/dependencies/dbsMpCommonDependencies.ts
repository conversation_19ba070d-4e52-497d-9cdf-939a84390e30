import { DepositService } from '@npco/component-bff-settlement-api/src/depositService';
import { EnvService } from '@npco/component-bff-settlement-api/src/envService';
import { SettlementProjectionService } from '@npco/component-bff-settlement-api/src/settlementProjectionService';
import {
  BillingAccountApi,
  BillingAccountService,
  CardholderService,
  DynamodbService as CommonDynamodbService,
  EnvironmentService as CommonEnvService,
  DebitCardAccountCardService,
  DebitCardAccountService,
  DebitCardAccountTxnService,
  LambdaService,
  RichDataService,
} from '@npco/component-dbs-mp-common';
import { BillingAccountDb } from '@npco/component-dbs-mp-common/dist/billingAccount/billingAccountDb';
import { CommonService } from '@npco/component-dbs-mp-common/dist/module/commonService';
import { SessionService } from '@npco/component-dbs-mp-common/dist/session';

import { DeviceManagementService, DeviceService } from 'bff-device-api/dist';
import { DeviceMetrics } from 'bff-device-api/dist/services/devices';
import { ReferralService } from 'bff-entity-api/src/services/referral/referralService';
import { EntitySubcategoryService } from 'bff-entity-api/src/services/subcategory';
import { EntityTagService } from 'bff-entity-api/src/services/tag';
import { SiteService } from 'bff-site-api/src/services/siteService';
import { CnpTransactionService } from 'bff-transaction-api/src/services/cnp/cnpTransactionService';
import { CpocTransactionService } from 'bff-transaction-api/src/services/cpoc/cpocTransactionService';
import { TransactionService } from 'bff-transaction-api/src/services/transactionService';
import { EnvService as OrderEnvService } from 'bff-zpos-api/src/config/envService';
import { OrderRepository } from 'bff-zpos-api/src/repositories/orderRepository';
import { ProjectionService as OrderProjectionService } from 'bff-zpos-api/src/services/projection/projectionService';

let commonEnvService: CommonEnvService | null = null;
let commonService: CommonService | null = null;
let commonDynamodbService: CommonDynamodbService | null = null;
let referralService: ReferralService | null = null;
let lambdaService: LambdaService | null = null;
let cardHolderService: CardholderService | null = null;
let transactionService: TransactionService | null = null;
let sessionService: SessionService | null = null;
let depositService: DepositService | null = null;
let settlementProjectionService: SettlementProjectionService | null = null;
let billingAccountApi: BillingAccountApi | null = null;
let billingAccountDb: BillingAccountDb | null = null;
let richDataService: RichDataService | null = null;
let commonDebitCardAccountTxnService: DebitCardAccountTxnService | null = null;
let commonDebitCardAccountCardService: DebitCardAccountCardService | null = null;
let commonDebitCardAccountService: DebitCardAccountService | null = null;
let billingAccountService: BillingAccountService | null = null;
let siteService: SiteService | null = null;
let entitySubCategoryService: EntitySubcategoryService | null = null;
let entityTagService: EntityTagService | null = null;
let cnpTransactionService: CnpTransactionService | null = null;
let cpocTransactionService: CpocTransactionService | null = null;
let orderProjectionService: OrderProjectionService | null = null;
let deviceMetrics: DeviceMetrics | null;
let deviceService: DeviceService | null = null;
let deviceManagementService: DeviceManagementService | null = null;

export const getCommonEnvService = () => {
  commonEnvService ??= new CommonEnvService();
  return commonEnvService;
};

export const getLambdaService = () => {
  lambdaService ??= new LambdaService();
  return lambdaService;
};

export const getCommonDynamodbService = () => {
  commonDynamodbService ??= new CommonDynamodbService(getCommonEnvService());
  return commonDynamodbService;
};

export const getCommonService = () => {
  commonService ??= new CommonService(getCommonDynamodbService(), getCommonEnvService(), getLambdaService());
  return commonService;
};

export const getReferralService = () => {
  referralService ??= new ReferralService(getCommonEnvService(), getCommonDynamodbService());
  return referralService;
};

export const getCardHolderService = () => {
  cardHolderService ??= new CardholderService(getCommonDynamodbService(), getCommonEnvService());
  return cardHolderService;
};

export const getSessionService = () => {
  sessionService ??= new SessionService(getCommonDynamodbService(), getCommonEnvService());
  return sessionService;
};

export const getDeviceMetrics = () => {
  deviceMetrics ??= new DeviceMetrics(getCommonDynamodbService(), getCommonEnvService(), getSessionService());
  return deviceMetrics;
};

export const getTransactionService = () => {
  transactionService ??= new TransactionService(getCommonService(), getCardHolderService(), getSessionService());
  return transactionService;
};

export const getDepositService = () => {
  depositService ??= new DepositService(getCommonDynamodbService(), new EnvService());
  return depositService;
};

export const getSettlementProjectionService = () => {
  settlementProjectionService ??= new SettlementProjectionService(getCommonDynamodbService(), new EnvService());
  return settlementProjectionService;
};

const getBillingAccountApi = () => {
  billingAccountApi ??= new BillingAccountApi(getCommonEnvService());
  return billingAccountApi;
};

const getBillingAccountDb = () => {
  billingAccountDb ??= new BillingAccountDb(getCommonDynamodbService(), getCommonEnvService());
  return billingAccountDb;
};

const getCommonDebitCardAccountCardService = () => {
  commonDebitCardAccountCardService ??= new DebitCardAccountCardService(
    getCommonEnvService(),
    getCommonDynamodbService(),
  );
  return commonDebitCardAccountCardService;
};

export const getRichDataService = () => {
  richDataService ??= new RichDataService(getCommonEnvService(), getCommonDynamodbService());
  return richDataService;
};

const getCommonDebitCardAccountTxnService = () => {
  commonDebitCardAccountTxnService ??= new DebitCardAccountTxnService(
    getCommonEnvService(),
    getCommonDynamodbService(),
    getRichDataService(),
  );
  return commonDebitCardAccountTxnService;
};

const getCommonDebitCardAccountService = () => {
  commonDebitCardAccountService ??= new DebitCardAccountService(
    getCommonEnvService(),
    getCommonDynamodbService(),
    getCommonDebitCardAccountCardService(),
    getCommonDebitCardAccountTxnService(),
  );
  return commonDebitCardAccountService;
};

export const getBillingAccountService = () => {
  billingAccountService ??= new BillingAccountService(
    getBillingAccountApi(),
    getBillingAccountDb(),
    getCommonDebitCardAccountService(),
  );
  return billingAccountService;
};

export const getSiteService = () => {
  siteService ??= new SiteService(getCommonEnvService(), getCommonDynamodbService());
  return siteService;
};

export const getEntitySubCategoryService = () => {
  entitySubCategoryService ??= new EntitySubcategoryService(getCommonDynamodbService(), getCommonEnvService());
  return entitySubCategoryService;
};

export const getEntityTagService = () => {
  entityTagService ??= new EntityTagService(getCommonDynamodbService(), getCommonEnvService());
  return entityTagService;
};

export const getCnpTransactionService = () => {
  cnpTransactionService ??= new CnpTransactionService(getCommonService(), getCardHolderService(), getSessionService());
  return cnpTransactionService;
};

export const getCpocTransactionService = () => {
  cpocTransactionService ??= new CpocTransactionService(
    getCommonService(),
    getCardHolderService(),
    getSessionService(),
  );
  return cpocTransactionService;
};

export const getOrderProjectionService = () => {
  orderProjectionService ??= new OrderProjectionService(
    new OrderRepository(getCommonDynamodbService(), new OrderEnvService()),
  );
  return orderProjectionService;
};

export const getDeviceService = () => {
  deviceService ??= new DeviceService(
    getCommonDynamodbService(),
    getCommonEnvService(),
    getDeviceMetrics(),
    getSessionService(),
  );
  return deviceService;
};

export const getDeviceManagementService = () => {
  deviceManagementService ??= new DeviceManagementService(
    getCommonEnvService(),
    getSessionService(),
    getCommonDynamodbService(),
  );

  return deviceManagementService;
};
