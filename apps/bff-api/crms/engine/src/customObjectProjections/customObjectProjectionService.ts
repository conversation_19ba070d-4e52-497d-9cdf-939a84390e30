import { SqsClient } from '@npco/component-bff-core/dist/aws/sqsClient';
import { error, info } from '@npco/component-bff-core/dist/utils/logger';

import { Injectable } from '@nestjs/common';
import type { SQSBatchItemFailure, SQSBatchResponse, SQSRecord } from 'aws-lambda';

import { CrmsEnvironmentService } from '../common';
import { getEnvService } from '../dependencies/commonDependencies';
import type { CustomObjectQueueProjectionDto } from '../projection';
import { customObjectQueueEventDispatchMap } from '../projection/handlers/eventDispatchMap';

type PartialSqsRecord = Pick<SQSRecord, 'body' | 'receiptHandle' | 'messageId'>;

@Injectable()
export class CustomObjectProjectionService {
  sqs: SqsClient;

  constructor(private readonly envService: CrmsEnvironmentService = getEnvService()) {
    this.sqs = new SqsClient(this.envService.region);
  }

  projectEvent = async (events: PartialSqsRecord[]): Promise<SQSBatchResponse> => {
    const eventsByAggregateId = this.groupEventsByAggregateId(events);
    const batchItemFailures: SQSBatchItemFailure[] = [];

    for (const [aggregateId, groupedEvents] of Object.entries(eventsByAggregateId)) {
      info(`projectCustomObjectEvent -> ${aggregateId} ${groupedEvents.length}`);
      const eventsToProcess = [...groupedEvents];
      while (eventsToProcess.length > 0) {
        try {
          const event = eventsToProcess[0];
          await this.processCustomObjectEvent(event);
          eventsToProcess.shift();
        } catch (err: any) {
          error(`Failed to project events, need to retry. Error: ${err.message}`);
          info(`projectCustomObjectEvent errors -> ${err?.stack}`);
          break;
        }
      }
      eventsToProcess.forEach((event) => {
        batchItemFailures.push({ itemIdentifier: event.messageId });
      });
    }

    return { batchItemFailures };
  };

  private readonly processCustomObjectEvent = async (event: PartialSqsRecord) => {
    const eventData: CustomObjectQueueProjectionDto = JSON.parse(event.body);
    info(
      `customObjectProjectionService:projectEvent -> get eventData ${JSON.stringify(eventData)}`,
      eventData.aggregateId,
    );

    const { aggregateId, uri } = eventData;
    const [, aggregate, actionName] = uri.split('.');
    const eventStrategy = customObjectQueueEventDispatchMap[aggregate]?.[actionName];

    if (!eventStrategy?.customObjectHandler) {
      error(`customObjectProjectionService:Handler not found ${aggregate} ${JSON.stringify(eventData, null, 2)}`);
      throw new Error('Handler not found');
    }

    const result = await eventStrategy.customObjectHandler.handle(eventData, aggregateId);
    if (!result?.eventWasValid) {
      error(
        `Projection failed for ${aggregate} ${aggregateId} due to invalid event. Event will not be retried.`,
        aggregateId,
      );
      await this.sqs.sendFifoMessage(this.envService.customObjectDeadLetterSqsUrl, event.body, aggregateId);
      await this.deleteMessage(event, this.envService.customObjectSqsUrl);
    } else {
      await this.deleteMessage(event, this.envService.customObjectSqsUrl);
    }
  };

  private readonly deleteMessage = async (event: any, sqsQueue: string) => {
    const output = await this.sqs.deleteMessage(sqsQueue, event.receiptHandle);
    info(`delete message from sqs ${JSON.stringify(event)} ${JSON.stringify(output)}`);
  };

  private readonly groupEventsByAggregateId = (events: PartialSqsRecord[]): Record<string, PartialSqsRecord[]> => {
    const eventsByAggregateId: Record<string, PartialSqsRecord[]> = {};
    events.forEach((event) => {
      const eventData: CustomObjectQueueProjectionDto = JSON.parse(event.body);
      const { aggregateId } = eventData;
      if (!eventsByAggregateId[aggregateId]) {
        eventsByAggregateId[aggregateId] = [];
      }
      eventsByAggregateId[aggregateId].push(event);
    });
    return eventsByAggregateId;
  };
}
