import { DynamodbService } from '@npco/component-dbs-mp-common/dist/dynamodb';
import {
  DbRecordType,
  Source,
} from '@npco/component-dto-core/dist/types';
import { getDeviceSettingsDbItemOrThrow } from '@npco/component-dbs-mp-common/dist/devices/getDeviceSettingsDbItem';
import { DeviceCreatedEventDto, DeviceInfoUpdateDto, DeviceStatus, DeviceUpdatedEventDto, PosMethod, PosMode, ZellerPosFavouriteType } from '@npco/component-dto-device';
import { getFullDeviceCreatedDto, getFullDeviceUpdatedDto, getFullInformationUpdatedDto,  } from '@npco/component-dbs-mp-common/dist/devices/testcases/utils';

import { ConfigModule } from '@nestjs/config/dist/config.module';
import { Test } from '@nestjs/testing/test';
import type { TestingModule } from '@nestjs/testing/testing-module';
import { v4 as uuidv4 } from 'uuid';

import { CrmsEnvironmentService } from '../common';
import { ProjectionModule } from '../projection/projectionModule';

import { projectionSqsHighPriorityHandler } from './projectionLambdas';
import { ConnectionType } from 'bff-device-api/dist/services/device_management/types';
import { DeviceManagementService } from 'bff-device-api/dist';
import { connectionTypes } from 'bff-device-api/dist/services/device_management';

jest.mock('../common/service/envService');
jest.mock('../common/service/dynamodbService');
jest.mock('@npco/component-dbs-mp-common/dist/dynamodb/dynamodbService');
jest.mock('@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient');

jest.mock('@npco/component-bff-core/dist/aws/sqsClient', () => ({
  SqsClient: jest.fn(() => ({ deleteMessage: jest.fn() })),
}));
jest.mock('@npco/component-bff-core/dist/middleware/aggregateTracing', () => {
  return {
    aggregateTracing: (aggregateId: string, callback?: any) => callback(),
  };
});

const mockError = jest.fn();
jest.mock('@npco/component-bff-core/dist/utils/logger', () => ({
  __esModule: true,
  ...jest.requireActual('@npco/component-bff-core/dist/utils/logger'),
  error: (...args: any) => mockError(...args),
}));

describe('Device projection test suite', () => {
  let module: TestingModule;
  let dbService: DynamodbService;
  let envService: CrmsEnvironmentService;
  let deviceMgmtService: DeviceManagementService;
  beforeAll(() => {
    process.env.AWS_SDK_LOAD_CONFIG = 'ap-southeast-2';
  });

  beforeEach(async () => {
    mockError.mockClear();
    module = await Test.createTestingModule({
      imports: [
        ProjectionModule,
        ConfigModule.forRoot({
          isGlobal: true,
        }),
      ],
    }).compile();
    dbService = module.get(DynamodbService);
    envService = module.get(CrmsEnvironmentService);
    deviceMgmtService = module.get(DeviceManagementService);
  });

  const sqsHighPriorityEvent = <T>(uri: string, dto: T) => {
    return new Promise((resolve, reject) => {
      projectionSqsHighPriorityHandler(
        {
          Records: [
            {
              body: JSON.stringify({
                'detail-type': uri,
                detail: dto,
              }),
              messageId: 'messageId',
            },
          ],
        },
        {} as any,
        (err, result) => {
          console.log(err, result)
          if (err) {
            reject(err);
          }
          resolve(result);
        },
      );
    });
  };

  describe('Created event test suite', () => {
    it('should be able to ingest minimal Created event', async () => {
      const dto = new DeviceCreatedEventDto({
        deviceUuid: uuidv4(),
        entityUuid: uuidv4(),
        siteUuid: uuidv4(),
      });

      await sqsHighPriorityEvent('crms.Device.Created', dto);
      const actual = await getDeviceSettingsDbItemOrThrow(dbService, dto.entityUuid, dto.deviceUuid);

      expect(actual).toEqual({
        ...dto,
        id: dto.deviceUuid,
        type: DbRecordType.DEVICE_SETTINGS,
        version: 0,
      });
    });

    it('should be able to ingest full Created event', async () => {
      const dto = getFullDeviceCreatedDto();

      await sqsHighPriorityEvent('crms.Device.Created', dto);
      const actual = await getDeviceSettingsDbItemOrThrow(dbService, dto.entityUuid, dto.deviceUuid);

      expect(actual).toEqual({
        ...dto,
        id: dto.deviceUuid,
        status: DeviceStatus.ACTIVE,
        type: DbRecordType.DEVICE_SETTINGS,
        deviceName: expect.any(String),
        version: 0,

        // fields not materialised
        name: undefined,
      });
    });

    it('should materialise Created correctly over Updated event', async () => {
      const deviceUuid = uuidv4();
      const deviceUpdatedDto = getFullDeviceUpdatedDto({
        deviceUuid,
      });
      const deviceCreatedDto = getFullDeviceCreatedDto({
        deviceUuid,
      });
      await sqsHighPriorityEvent('crms.Device.Updated', deviceUpdatedDto);
      await sqsHighPriorityEvent('crms.Device.Created', deviceCreatedDto);

      const actual = await getDeviceSettingsDbItemOrThrow(dbService, deviceCreatedDto.entityUuid, deviceCreatedDto.deviceUuid);

      expect(actual).toEqual({
        ...deviceCreatedDto,
        id: deviceCreatedDto.deviceUuid,
        status: DeviceStatus.ACTIVE,
        type: DbRecordType.DEVICE_SETTINGS,
        deviceName: expect.any(String),
        version: 1,
        posSettings: {
          mode: PosMode.INTEGRATED,
          posRegisterName: expect.any(String),
          exitRequiresPin: false,
          port: 1234,
          zellerPosSettings: {
            favourites: [
              {
                id: 'id',
                type: ZellerPosFavouriteType.ITEM
              }
            ]
          },
          posSoftwareName: expect.any(String),
          ipAddress: expect.any(String),
          active: Source.LINKLY,
          posReceipt: false,
          connectionMethod: PosMethod.LAN
        },

        // fields not materialised
        name: undefined,
      });
    });
  });

  describe('Updated event test suite', () => {
    it('should be able to ingest minimal Updated event', async () => {
      const dto = new DeviceUpdatedEventDto({
        deviceUuid: uuidv4(),
        entityUuid: uuidv4(),
      });

      await sqsHighPriorityEvent('crms.Device.Updated', dto);
      const actual = await getDeviceSettingsDbItemOrThrow(dbService, dto.entityUuid as string, dto.deviceUuid);

      expect(actual).toEqual({
        ...dto,
        id: dto.deviceUuid,
        type: DbRecordType.DEVICE_SETTINGS,
        version: 0
      });
    });

    it('should be able to ingest full Initiated event', async () => {
      const dto = getFullDeviceUpdatedDto();

      await sqsHighPriorityEvent('crms.Device.Updated', dto);

      const actual = await getDeviceSettingsDbItemOrThrow(dbService, dto.entityUuid as string, dto.deviceUuid);

      expect(actual).toEqual({
        ...dto,
        id: dto.deviceUuid,
        deviceName: dto.name,
        type: DbRecordType.DEVICE_SETTINGS,
        version: 0,

        // fields not materialised
        name: undefined,
      });
    });

    it('should materialise Updated correctly over Created event', async () => {
      const deviceUuid = uuidv4();
      const deviceCreatedDto = getFullDeviceCreatedDto({
        deviceUuid,
      });

      const deviceUpdatedDto = getFullDeviceUpdatedDto({
        deviceUuid,
      });

      await sqsHighPriorityEvent('crms.Device.Created', deviceCreatedDto);
      await sqsHighPriorityEvent('crms.Device.Updated', deviceUpdatedDto);

      const actual = await getDeviceSettingsDbItemOrThrow(dbService, deviceUpdatedDto.entityUuid as string, deviceUpdatedDto.deviceUuid);

      expect(actual).toEqual({
        ...deviceUpdatedDto,
        id: deviceUpdatedDto.deviceUuid,
        status: DeviceStatus.ACTIVE,
        type: DbRecordType.DEVICE_SETTINGS,
        deviceName: expect.any(String),
        version: 1,
        posSettings: {
          mode: PosMode.INTEGRATED,
          posRegisterName: expect.any(String),
          exitRequiresPin: false,
          port: 1234,
          zellerPosSettings: {
            favourites: [
              {
                id: 'id',
                type: ZellerPosFavouriteType.ITEM
              }
            ]
          },
          posSoftwareName: expect.any(String),
          ipAddress: expect.any(String),
          active: Source.LINKLY,
          posReceipt: false,
          connectionMethod: PosMethod.LAN
        },

        siteUuid: deviceCreatedDto.siteUuid,


        // fields not materialised
        name: undefined,
      });
    });
  });

  describe('InformationUpdated event test suite', () => {
    it('should be able to ingest minimal InformationUpdated event', async () => {
      const dto = new DeviceInfoUpdateDto({
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        timestampUtc: new Date().toISOString(),
      });

      await sqsHighPriorityEvent('crms.Device.InformationUpdated', dto);
      const actual = await deviceMgmtService.getDeviceInfo(dto.id);

      expect(actual).toEqual({
        id: dto.id,
        type: DbRecordType.DEVICE_INFO,
        version: 0,
        network: {
          activeConnection: connectionTypes[ConnectionType.UNKNOWN],
        }
      });
    });

    it('should be able to ingest full Initiated event', async () => {
      const dto = getFullInformationUpdatedDto();

      await sqsHighPriorityEvent('crms.Device.InformationUpdated', dto);

      const actual = await deviceMgmtService.getDeviceInfo(dto.id);

      expect(actual).toEqual({
        customerId: expect.any(String),
        id: dto.id,
        type: DbRecordType.DEVICE_INFO,
        version: 0,
        customerName: expect.any(String),
        hardware: {
          pedCurrentTemp: 123,
          androidOs: expect.any(String),
          ksn: expect.any(String),
          pedStatus: 123,
          numCapk: 123,
          pciFirmwareVersion: expect.any(String),
          emvL1Version: expect.any(String),
          emvL2Version: expect.any(String),
          pedVersion: expect.any(String),
          ramTotal: expect.any(String),
          flashTotal: expect.any(String),
          model: expect.any(String),
          hardwareVersion: expect.any(String),
          firmwareVersion: expect.any(String),
          xdpi: expect.any(String),
          androidModel: expect.any(String),
          density: expect.any(String),
          os: expect.any(String),
          uiSoftwareVersion: expect.any(String),
          kernel: expect.any(String),
          heightPixels: expect.any(String),
          androidDevice: expect.any(String),
          numAids: 123,
          widthPixels: expect.any(String),
          densityDpi: expect.any(String),
          androidBuild: expect.any(String),
          pedModel: expect.any(String),
          serial: expect.any(String),
          androidKernel: expect.any(String),
          secureCpuId: expect.any(String),
          securityVersion: expect.any(String),
          numDukptKeys: 123,
          ramAvail: expect.any(String),
          flashAvail: expect.any(String),
          numMasterKeys: 123,
          ydpi: expect.any(String),
          softwareVersion: expect.any(String)
        },
        network: {
          wifiInfo: {
            standard: expect.any(String),
            dns1Address: expect.any(String),
            strengthRssi: 123,
            strength: expect.any(String),
            bssid: expect.any(String),
            dns2Address: expect.any(String),
            maxTransferSpeed: 123,
            channel: 123,
            ipAddress: expect.any(String),
            ssid: expect.any(String),
            speed: 123,
            securityModel: expect.any(String),
            frequency: expect.any(String),
            lastUpdated: expect.any(String),
            macAddress: expect.any(String),
            transferSpeed: 123,
            state: 123,
            gatewayAddress: expect.any(String)
          },
          cellularInfo: {
            dns1Address: expect.any(String),
            strengthRssi: 123,
            country: expect.any(String),
            serialNumber: expect.any(String),
            strength: expect.any(String),
            dns2Address: expect.any(String),
            networkName: expect.any(String),
            ipAddress: expect.any(String),
            subscriberId: expect.any(String),
            operatorName: expect.any(String),
            operator: expect.any(String),
            network: expect.any(String),
            lastUpdated: expect.any(String),
            cellDetails: [],
            state: 123,
            gatewayAddress: expect.any(String),
            networkType: expect.any(String)
          },
          ethernetInfo: {
            dns1Address: expect.any(String),
            lastUpdated: expect.any(String),
            macAddress: expect.any(String),
            dns2Address: expect.any(String),
            transferSpeed: expect.any(String),
            maxTransferSpeed: expect.any(String),
            ipAddress: expect.any(String),
            gatewayAddress: expect.any(String),
            dhcp: true
          },
        }
      });
    });
  });
});
