import { S3Client } from '@npco/component-bff-core/dist/aws/s3Client';
import { DomicileLookupDb } from '@npco/component-bff-core/dist/domicile/domicileLookupDb';
import { debug, error, info, warn } from '@npco/component-bff-core/dist/utils/logger';
import { DepositService } from '@npco/component-bff-settlement-api/src/depositService';
import { getFeeRateSettings } from '@npco/component-dbs-mp-common/dist/entity/feeUtils';
import type { Entity, FeeRateSettings } from '@npco/component-dbs-mp-common/dist/types';
import { AddressState, DbRecordType, HttpStatusCode, KYCScreeningResult, PaymentType } from '@npco/component-dto-core';
import type { Customer } from '@npco/component-dto-customer';
import type {
  EntityAmexMerchantSubmissionCompletedDto,
  EntityCreatedEventDto,
  EntityFeeRatesEffectiveDto,
  EntityFirstTransactionCreatedDto,
  EntityFullSearchCompletedEventDto,
  EntityHsMetricUpdatedEventDto,
  EntityPaymentSettingsDto,
  EntityReferredEventDto,
  EntityRefundOverdraftUpdatedDto,
  EntityScreeningCompletedEventDto,
  EntityThreeDsAmountThresholdGroupUpdatedDto,
  EntityUpdatedEventDto,
  EntityPrimaryAccountHolderChangedEventDto,
  PaymentLimit,
  PaymentSettings,
  EntityDomicileAndCurrencyAttachedEventDto,
} from '@npco/component-dto-entity';
import { RiskReviewResult, RiskReviewStatus } from '@npco/component-dto-entity';
import type { EntityBaseEventDto } from '@npco/component-dto-entity/dist/entityBaseEventDto';

import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { ReferralService } from 'bff-entity-api/src/services/referral/referralService';
import { EOL } from 'os';

import type { PartialFailure } from '../common';
import {
  AmsApi,
  convertAbrDateToDateString,
  convertCompanyFileCompanyAddressToString,
  convertCompanyFileDateTypeToDateString,
  convertCompanyFileDescriptionTagToString,
  convertCompanyFileOfficerToString,
  convertCompanyFileOfficerToStringWithRole,
  convertCompanyFileShareCapitalToString,
  convertCompanyFileShareHolderToString,
  convertCompanyFileValueToString,
  CrmsDynamodbService,
  CrmsEnvironmentService,
  HubspotApi,
  HubspotApiError,
  TmsEntityThreeDSApi,
} from '../common';
import { HubspotOauthAuthenticator } from '../common/api/hubspotApiAuthenticator';
import { applyPartialSuccess } from '../common/partialFailureRoleback';
import { BaseService } from '../common/service/baseService';
import type {
  AbrSummaryType,
  AsicDetailsType,
  CompanyFile,
  CompanyFileAbrSummaryType,
  CompanyFileAsicOrganisationExtractType,
  CompanyFileCompanyAddress,
  CompanyFileOfficerType,
  HubspotDomainMapping,
  HubspotDomainMappings,
  JsonAny,
  UpdateEntityCompanyFileDto,
} from '../common/types';
import { HubspotWebhookEvent, ObjectType, SubscriptionType, WebhookEvent } from '../common/types';
import { CrmsCustomerContactService } from '../customer';
import { customerProperties } from '../customer/customerProperties';
import { sendGrowSurfParticipantRequest } from '../customer/customerUtils';
import type { CrmsCustomerEntity } from '../customerEntity';
import { CrmsCustomerEntityService } from '../customerEntity';
import { HubspotService } from '../hubspot/hubspotService';
import { HubspotTicketService } from '../hubspot/hubspotTicketService';
import {
  containsNoContactError,
  getHubspotObjectIdFromErrorMessage,
  hasInvalidObjectIds,
  isHubspotObjectDuplicateError,
} from '../hubspot/hubspotUtils';
import type { ExternalCreateTicketRequest, HubspotResponse } from '../hubspot/types';
import { HubspotRecordType } from '../hubspot/types';

import type { EntityCompanyServiceInterface } from './entityCompanyServiceInterface';
import { EntityDb } from './entityDb';
import { accountStatusKeys, entityProperties } from './entityProperties';
import { convertDbItemToCustomerEntity } from './entityUtils';
import { isNextOnboardingStatusAllowed } from './isNextOnboardingStatusAllowed';
import type { CrmsEntity, CrmsEntityUpdatedEventDto } from './types';

@Injectable()
export class CrmsEntityCompanyService extends BaseService implements EntityCompanyServiceInterface {
  entityDb: EntityDb;

  domicileDb: DomicileLookupDb;

  hubspotApi: HubspotApi;

  amsApi: AmsApi;

  tmsEntityThreeDSApi: TmsEntityThreeDSApi;

  protected readonly s3: S3Client;

  constructor(
    private readonly envService: CrmsEnvironmentService,
    private readonly hubspotService: HubspotService,
    private readonly dynamodbService: CrmsDynamodbService,
    private readonly referralService: ReferralService,
    private readonly ticketService: HubspotTicketService,
    private readonly depositService: DepositService,
    private readonly customerService: CrmsCustomerContactService,
    private readonly customerEntityService: CrmsCustomerEntityService,
  ) {
    super();
    this.hubspotApi = new HubspotApi(this.envService.hubspotApiEndpoint, new HubspotOauthAuthenticator(hubspotService));
    this.amsApi = new AmsApi(this.envService.amsEndpoint, this.envService.amsEndpointVersion);
    this.tmsEntityThreeDSApi = new TmsEntityThreeDSApi(this.envService.tmsEntityThreeDSEndpoint);
    this.entityDb = new EntityDb(this.envService, this.dynamodbService);
    this.domicileDb = new DomicileLookupDb(this.envService);
    this.s3 = new S3Client(this.envService.region || 'ap-southeast-2');
  }

  private readonly prepareDto = (model: any) => {
    const dto = {
      ...model,
      accountStatus: accountStatusKeys.reduce((acc: any, key: any) => {
        acc[key] = model[key];
        return acc;
      }, {}),
    };
    return this.dynamodbService.utils.filterKeys(dto, accountStatusKeys);
  };

  readonly saveEntityUpdatedProjection = async (dto: CrmsEntityUpdatedEventDto) => {
    await this.entityDb.saveEntityProjection(dto);
  };

  readonly saveEntityCreateResponse = (dto: EntityCreatedEventDto) =>
    this.entityDb.saveEntityProjection({
      ...dto,
      transactionMetaData: {
        yetToMakeTransaction: true,
      },
    });

  readonly saveReferralProjection = async (dto: EntityReferredEventDto) => {
    await this.entityDb.saveEntityProjection({ entityUuid: dto.referred, referredBy: dto.entityUuid } as any);
    await this.referralService.saveReferralProjection(dto);
  };

  readonly saveEntityFirstTransactionCreatedProjection = async (dto: EntityFirstTransactionCreatedDto) =>
    this.entityDb.saveEntityFirstTransactionCreatedProjection(dto);

  private readonly addHubspotCompanyIdToAmsAndAssociateCustomers = async (
    entityUuid: string,
    hubspotCompanyId: string,
  ) => {
    await this.entityDb.saveEntityHubspotCompanyId(entityUuid, hubspotCompanyId);
    try {
      await this.domicileDb.setDomicileContext({ entityUuid });
      await this.amsApi.addCompanyHubspotId(entityUuid, hubspotCompanyId);
    } catch (err: any) /* istanbul ignore next */ {
      error(`Failed to create entity hubspot id on ams ${entityUuid} ${hubspotCompanyId}`, entityUuid);
      error(err);
    }
    await this.associateCustomers(entityUuid, hubspotCompanyId);
  };

  private readonly getAbrSummary = (summary: CompanyFileAbrSummaryType): AbrSummaryType => {
    /* istanbul ignore next */
    const abrSummary: AbrSummaryType = {
      legalName: summary.legalName,
      abn: `${summary.abn} (last updated: \
${convertAbrDateToDateString(summary.abnSuppliedDate)})`,
      abnStatus: `${summary.abnStatus} from \
${convertAbrDateToDateString(summary.abnStatusFromDate)}`,
      abnEntityType: summary.entityTypeIndicator,
      location: `${summary.businessAddress?.state} ${summary.businessAddress?.postcode}`,
    };

    /* istanbul ignore next */
    abrSummary.businessNames = summary.otherNames?.otherName
      .filter((name) => name.attributes.nameTypeCode === 'BN')
      .map((name) => name.$value)
      .join(EOL);

    /* istanbul ignore next */
    abrSummary.tradingNames = summary.otherNames?.otherName
      .filter((name) => name.attributes.nameTypeCode === 'TRD' || name.attributes.nameTypeCode === 'OTN')
      .map((name) => name.$value)
      .join(EOL);

    return abrSummary;
  };

  private readonly getAsicDetailsType = (extract: CompanyFileAsicOrganisationExtractType): AsicDetailsType => {
    const { companyDetails, organisationDetails } = extract;

    let details: AsicDetailsType = {};
    /* istanbul ignore else */
    if (companyDetails) {
      const asicDetails: AsicDetailsType = {
        name: companyDetails.organisationName,
        registrationDate: convertCompanyFileDateTypeToDateString(companyDetails.registrationDate),
        nextReviewDate: convertCompanyFileDateTypeToDateString(companyDetails.reviewDate),
        registeredIn: convertCompanyFileValueToString(companyDetails.stateOfRegistration),
        governanceType: convertCompanyFileDescriptionTagToString(companyDetails.governanceType),
      };
      details = { ...asicDetails };
    }
    /* istanbul ignore else */
    if (organisationDetails?.organisationDetail[0]) {
      const organisationDetail = organisationDetails.organisationDetail[0];
      /* istanbul ignore next */
      const asicDetails: AsicDetailsType = {
        nameStartDate: convertCompanyFileDateTypeToDateString(organisationDetail.nameStartDate),
        organisationStatus: convertCompanyFileDescriptionTagToString(organisationDetail.organisationStatus),
        organisationType: convertCompanyFileDescriptionTagToString(organisationDetail.organisationType),
        organisationClass: convertCompanyFileDescriptionTagToString(organisationDetail.organisationClass),
        organisationSubclass: convertCompanyFileDescriptionTagToString(organisationDetail.organisationSubclass),
        documentNumber: organisationDetail.attributes?.documentNumber,
        disclosingEntity: organisationDetail.disclosingEntityIndicator === 'Y',
      };
      details = { ...details, ...asicDetails };
    }

    return details;
  };

  private readonly getAsicAddressByType = (
    companyAddresses: { companyAddress: CompanyFileCompanyAddress[] },
    code: string,
  ): string =>
    convertCompanyFileCompanyAddressToString(
      companyAddresses.companyAddress.find((address) => convertCompanyFileValueToString(address.addressType) === code),
    );

  private readonly getAsicOfficers = (
    companyOfficers: { companyOfficer: CompanyFileOfficerType[] },
    codes: string,
  ): string =>
    companyOfficers.companyOfficer
      .filter((officer) => convertCompanyFileValueToString(officer.role) === codes)
      .map((officer) => convertCompanyFileOfficerToString(officer))
      .join(`${EOL}${EOL}`);

  readonly covertCompanyFileToDto = (companyFile: CompanyFile): UpdateEntityCompanyFileDto => {
    /* istanbul ignore next */
    const dto: UpdateEntityCompanyFileDto = {
      bureauFileNumber: companyFile.bureauFileNumber,
      adversePresent: !!companyFile.creditData?.adversePresent,
      lastAsicSearchDate: companyFile.lastAsicSearchDate?.split('T')[0] ?? '',
    };
    const { australianBusinessRegistrySummary, asicOrganisationExtract } = companyFile;

    /* istanbul ignore else */
    if (australianBusinessRegistrySummary) {
      dto.abrSummary = this.getAbrSummary(australianBusinessRegistrySummary);
    }

    /* istanbul ignore else */
    if (asicOrganisationExtract) {
      dto.asicDetails = this.getAsicDetailsType(asicOrganisationExtract);

      const { companyAddresses, companyOfficers, shareStructure, shareHoldings } = asicOrganisationExtract;

      /* istanbul ignore else */
      if (companyAddresses) {
        dto.registeredOffice = this.getAsicAddressByType(companyAddresses, 'RG');
        dto.principalPlaceOfBusiness = this.getAsicAddressByType(companyAddresses, 'PA');
        dto.contactAddressForASIC = this.getAsicAddressByType(companyAddresses, 'CC');
      }

      /* istanbul ignore else */
      if (companyOfficers) {
        dto.directors = this.getAsicOfficers(companyOfficers, 'DR');
        dto.secretaries = this.getAsicOfficers(companyOfficers, 'SR');
        dto.otherAppointments = companyOfficers.companyOfficer
          .filter((officer) => !['DR', 'SR'].includes(convertCompanyFileValueToString(officer.role)))
          .map((officer) => convertCompanyFileOfficerToStringWithRole(officer))
          .join(`${EOL}${EOL}`);
      }

      /* istanbul ignore next */
      dto.shareStructure = shareStructure?.shareCapital
        .map((capital) => convertCompanyFileShareCapitalToString(capital))
        .join(`${EOL}${EOL}`);

      /* istanbul ignore next */
      dto.shareHolders = shareHoldings?.shareHolding
        .map((shareHoling) => convertCompanyFileShareHolderToString(shareHoling))
        .join(`${EOL}${EOL}`);
    }

    return dto;
  };

  private readonly stateNameMap = new Map([
    ['VICTORIA', AddressState.VIC],
    ['NEW SOUTH WALES', AddressState.NSW],
    ['AUSTRALIAN CAPITAL TERRITORY', AddressState.ACT],
    ['QUEENSLAND', AddressState.QLD],
    ['TASMANIA', AddressState.TAS],
    ['NORTHERN TERRITORY', AddressState.NT],
    ['SOUTH AUSTRALIA', AddressState.SA],
    ['WESTERN AUSTRALIA', AddressState.WA],
    [AddressState.VIC, AddressState.VIC],
    [AddressState.NSW, AddressState.NSW],
    [AddressState.ACT, AddressState.ACT],
    [AddressState.QLD, AddressState.QLD],
    [AddressState.TAS, AddressState.TAS],
    [AddressState.NT, AddressState.NT],
    [AddressState.SA, AddressState.SA],
    [AddressState.WA, AddressState.WA],
  ]);

  private readonly normaliseStateString = (string: string): string => {
    return string.trim().toUpperCase();
  };

  readonly getNormalisedState = (dto: EntityCreatedEventDto | EntityUpdatedEventDto): AddressState | undefined => {
    const stateString = dto?.registeredAddress?.state?.length
      ? dto?.registeredAddress?.state
      : dto?.businessAddress?.state ?? '';

    return this.stateNameMap.get(this.normaliseStateString(stateString));
  };

  private readonly upsertHubspotObject = async (
    incoming: EntityCreatedEventDto | EntityUpdatedEventDto,
    current: CrmsEntity | null,
  ): Promise<HubspotResponse | null> => {
    if (current?.hubspotCompanyId) {
      const output = await this.hubspotApi.updateObject(
        ObjectType.COMPANY,
        current.hubspotCompanyId,
        this.buildHubspotProperties(incoming, this.prepareDto(current)),
      );
      info(`update entity response ${JSON.stringify(output)}`);
      return output;
    }
    return this.createHubspotObject(incoming);
  };

  private readonly createHubspotObject = async (
    incoming: EntityCreatedEventDto | EntityUpdatedEventDto,
  ): Promise<HubspotResponse | null> => {
    try {
      const { properties } = this.buildHubspotProperties(incoming, {} as EntityCreatedEventDto | EntityUpdatedEventDto);
      const output = await this.hubspotApi.createObject(ObjectType.COMPANY, {
        properties: { ...properties, company_uuid: incoming.entityUuid },
      });
      info(`create entity response ${JSON.stringify(output)}`);
      if (output?.id) {
        await this.addHubspotCompanyIdToAmsAndAssociateCustomers(incoming.entityUuid, output.id);
      }
      return output;
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message ?? err?.message;
      debug(`createHubspotObject errorMessage -> ${errorMessage}`);

      if (isHubspotObjectDuplicateError(errorMessage)) {
        const hubspotObjectId = getHubspotObjectIdFromErrorMessage(errorMessage);
        if (hubspotObjectId) {
          return this.updateHubspotObjectOnDuplicationError(hubspotObjectId, incoming);
        }
        warn(`Failed to extract Hubspot object ID from error message: ${errorMessage}`);
      }
      warn(`failed to create Hubspot object for entityUuid: ${incoming.entityUuid}`, incoming.entityUuid);
      throw err;
    }
  };

  private readonly updateHubspotObjectOnDuplicationError = async (
    hubspotObjectId: string,
    incoming: EntityCreatedEventDto | EntityUpdatedEventDto,
  ): Promise<HubspotResponse | null> => {
    const output = await this.hubspotApi.updateObject(
      ObjectType.COMPANY,
      hubspotObjectId,
      this.buildHubspotProperties(incoming, {} as EntityCreatedEventDto | EntityUpdatedEventDto),
    );
    info(`update entity response ${JSON.stringify(output)}`);
    return output;
  };

  private readonly buildCustomerCreatedHubspotProperties = (customer: CrmsCustomerEntity, customerCore?: Customer) => {
    const hubspotProperties = this.buildHubspotProperties(
      {
        ...customerCore,
        ...customer,
      },
      customerCore,
      undefined,
      this.getCustomerContactPropertiesMap,
    );
    return {
      properties: {
        ...hubspotProperties.properties,
        contact_uuid: customer.id,
        kyc_entity_type: customerCore?.type,
      },
    };
  };

  private readonly createContactHubspotObject = async (customerEntity: CrmsCustomerEntity) => {
    const customer = await this.entityDb.getCustomerOrReturnUndefined(customerEntity.id);
    const hubspotProperties = this.buildCustomerCreatedHubspotProperties(customerEntity, customer);
    const hubstpoObject = await this.hubspotApi.createObject(ObjectType.CONTACT, hubspotProperties);

    if (hubstpoObject?.id) {
      const customerUuid = customer?.customerUuid ?? customerEntity.id ?? customer?.id;
      const updatedDto = {
        ...customer,
        hubspotContactId: hubstpoObject.id,
      };

      await sendGrowSurfParticipantRequest(
        {
          ...updatedDto,
          id: customerUuid,
        },
        this.envService,
      );
      await this.domicileDb.setDomicileContext({ customerUuid });
      await this.amsApi.updateCustomer({
        customerUuid,
        hubspotContactId: updatedDto.hubspotContactId,
      });
    }

    return hubstpoObject;
  };

  private readonly findOrCreateHubspotContact = async (
    customer: CrmsCustomerEntity,
  ): Promise<{ id: string } | undefined> => {
    let contact;
    if (customer.email) {
      contact = (await this.hubspotApi.searchObjectByDomainId(ObjectType.CONTACT, 'email', customer.email)).results[0];
    }

    contact ??= await this.createContactHubspotObject(customer);

    return contact;
  };

  private readonly performAssociateContactToCompany = async (
    hubspotContactId: string,
    hubspotCompanyId: string,
    customerEntity: CrmsCustomerEntity,
  ) => {
    try {
      await this.hubspotApi.associateContactToCompany(hubspotContactId, hubspotCompanyId);

      return hubspotContactId;
    } catch (err: any) {
      debug(`CrmsEntityCompanyService - performAssociateContactToCompany -> error: ${err.message}`, hubspotContactId);

      if (err instanceof HubspotApiError && (containsNoContactError(err.message) || hasInvalidObjectIds(err.data))) {
        if (!customerEntity.email) {
          warn(`CrmsEntityCompanyService -> Customer does not have an email address`, customerEntity.customerUuid);
          return undefined;
        }

        const contact = await this.findOrCreateHubspotContact(customerEntity);
        if (!contact) {
          warn(
            `CrmsEntityCompanyService -> Find contact by email did not exist or failed to create contact`,
            customerEntity.customerUuid,
          );
          return undefined;
        }

        await this.hubspotApi.associateContactToCompany(hubspotCompanyId, contact.id);
        return contact.id;
      }

      throw err;
    }
  };

  createEntity = async (dto: EntityCreatedEventDto) => {
    const normalisedState = this.getNormalisedState(dto);
    if (normalisedState) {
      // eslint-disable-next-line no-param-reassign
      dto.normalisedState = normalisedState;
    }

    let currentEntity: CrmsEntity | null = null;
    try {
      currentEntity = await this.entityDb.getEntity(dto.entityUuid);
    } catch (err: any) {
      warn(`Entity ${dto.entityUuid} not found in materialised view, will create new entity. Error: ${err.message}`);
    }

    const output = await this.upsertHubspotObject(dto, currentEntity);
    await this.saveEntityCreateResponse(dto);

    if (this.envService.multiEntityEnabled && output?.id) {
      const hubspotCompanyId: string = output.id;
      await this.customerEntityService.associateCompanyToContactCompanies(dto.entityUuid, hubspotCompanyId);
      await this.associateContactToCompany(dto.entityUuid, hubspotCompanyId);
      await this.customerEntityService.associateContactLabelToCompanyByEntity(dto.entityUuid);
    }

    return output;
  };

  pureMaterialiseEntity = async (entityUuid: string, update: any) => {
    const { hubspotCompanyId, ...restEntityPayload } = update;
    if (hubspotCompanyId) {
      info(`pure materialise entity with existing hubspotCompanyId ${hubspotCompanyId}`, entityUuid);
      return this.entityDb.saveEntity(entityUuid, restEntityPayload);
    }
    return this.entityDb.saveEntity(entityUuid, update);
  };

  associateCustomers = async (entityUuid: string, hubspotCompanyId: string) => {
    const result = await this.dynamodbService.queryAllByType(entityUuid, DbRecordType.CUSTOMER);
    if (result.Items?.length) {
      // eslint-disable-next-line no-restricted-syntax
      for (const customer of result.Items) {
        if (customer.hubspotContactId) {
          await this.hubspotApi.associateContactToCompany(customer.hubspotContactId, hubspotCompanyId);
        }
      }
    }
  };

  associateContactToCompany = async (entityUuid: string, hubspotCompanyId: string) => {
    const customerEntityDbItems = await this.entityDb.getCustomerEntitiesByEntityUuid(entityUuid);

    const customerEntities: CrmsCustomerEntity[] = customerEntityDbItems.map((item) =>
      convertDbItemToCustomerEntity(item),
    );

    for (const customerEntity of customerEntities) {
      const customer = await this.entityDb.getCustomerDbItem(customerEntity.id);

      if (
        !customer?.hubspotContactId ||
        customer.hubspotAssociations?.companiesAssociated?.includes(hubspotCompanyId)
      ) {
        return;
      }

      const hubspotContactId = await this.performAssociateContactToCompany(
        customer.hubspotContactId,
        hubspotCompanyId,
        customerEntity,
      );

      if (hubspotContactId) {
        await this.entityDb.saveCustomerAssociatedHubspotCompanyIds(customer.id, hubspotCompanyId);
      }
    }
  };

  updateEntity = async (dto: EntityUpdatedEventDto) => {
    info(`update entity ${JSON.stringify(dto)}`);
    const entity = await this.entityDb.getEntity(dto.entityUuid);
    const updatedDto = { ...dto };
    if (
      updatedDto.onboardingStatus &&
      !isNextOnboardingStatusAllowed({
        currentStatus: entity.onboardingStatus,
        nextStatus: updatedDto.onboardingStatus,
      })
    ) {
      warn('dont materialise the onboarding event');
      delete updatedDto.onboardingStatus;
    }
    if (updatedDto.registeredAddress || (updatedDto.businessAddress && !entity.registeredAddress)) {
      const newNormalisedState = this.getNormalisedState(updatedDto);
      if (entity.registeredAddress?.state !== newNormalisedState) {
        updatedDto.normalisedState = this.getNormalisedState(updatedDto);
      }
    }
    const output = await this.upsertHubspotObject(updatedDto, entity);
    const hubspotCompanyId = entity?.hubspotCompanyId ?? output?.id;
    await this.saveEntityUpdatedProjection({
      ...updatedDto,
      hubspotCompanyId,
    });
    if (dto.accountStatus?.canSettle) {
      await this.depositService.entityUpdatedProjectionCanSettle(dto);
    }

    if (this.envService.multiEntityEnabled) {
      await this.customerEntityService.associateCompanyToContactCompanies(dto.entityUuid, hubspotCompanyId);
      await this.associateContactToCompany(dto.entityUuid, hubspotCompanyId);
      await this.customerEntityService.associateContactLabelToCompanyByEntity(dto.entityUuid);
    }
  };

  updateEntityCompanyFile = async (dto: EntityFullSearchCompletedEventDto) => {
    info(`update company profile ${JSON.stringify(dto)}`, dto.entityUuid);
    /* istanbul ignore else */
    if (dto.companyFile) {
      const entity = await this.entityDb.getEntity(dto.entityUuid);
      /* istanbul ignore else */
      if (entity?.hubspotCompanyId) {
        debug(`Update hubspot - ${entity.hubspotCompanyId}`, dto.entityUuid);
        const hubspotApiDto = this.covertCompanyFileToDto(dto.companyFile);
        await this.hubspotApi.updateObject(
          ObjectType.COMPANY,
          entity.hubspotCompanyId,
          this.buildHubspotProperties<UpdateEntityCompanyFileDto>(hubspotApiDto, this.prepareDto(entity)),
        );
        // Do not need materialise anything
      }
    }
  };

  updateHsEntityScreeningReviewRequiredFlag = async (dto: EntityScreeningCompletedEventDto) => {
    if (dto.result === KYCScreeningResult.NO_MATCH) {
      info('KYC Screening Result is no match', dto.entityUuid);
      return;
    }

    const entity = await this.entityDb.getEntity(dto.entityUuid);
    if (!entity?.hubspotCompanyId) {
      info('Entity or entity HS company Id missing', dto.entityUuid);
      return;
    }

    if (entity.screeningReviewRequired) {
      info('Screening review required already set to true in HS', dto.entityUuid);
      return;
    }
    info('Updating screening review required to true', dto.entityUuid);
    const hubspotProperties = this.buildHubspotProperties({ screeningReviewRequired: true }, {});

    await this.hubspotApi.updateObject(ObjectType.COMPANY, entity.hubspotCompanyId, hubspotProperties);
    await this.saveEntityUpdatedProjection({
      screeningReviewRequired: true,
      entityUuid: dto.entityUuid,
    });
  };

  saveEntityRefundOverdraftProjection = async (dto: EntityRefundOverdraftUpdatedDto) => {
    info(`Updating entity refund overdraft projection,  ${JSON.stringify(dto)}`, dto.entityUuid);
    return this.entityDb.saveEntity(dto.entityUuid, {
      refundOverdraftSettings: { limit: dto.limit, reason: dto.reason },
    });
  };

  attachSitDomicileAndCurrencyProjection = async (dto: EntityDomicileAndCurrencyAttachedEventDto) => {
    info(`Updating entity set domicile and currency projection,  ${JSON.stringify(dto)}`, dto.entityUuid);
    return this.entityDb.saveEntity(dto.entityUuid, {
      domicile: dto.domicile,
      currency: dto.currency,
    });
  };

  saveEntityFeeRateSettingsProjection = async (dto: EntityFeeRatesEffectiveDto) => {
    info(`save entity fee rate settings ${JSON.stringify(dto)}`, dto.entityUuid);
    const entity = await this.entityDb.getEntity(dto.entityUuid);
    const feeRateSettings = getFeeRateSettings(dto) as FeeRateSettings;
    if (entity?.hubspotCompanyId) {
      debug(`Update hubspot - ${entity.hubspotCompanyId}`, dto.entityUuid);
      await this.hubspotApi.updateObject(
        ObjectType.COMPANY,
        entity.hubspotCompanyId,
        this.buildHubspotProperties<typeof feeRateSettings>({ feeRateSettings } as any, this.prepareDto(entity)),
      );
    }
    await this.entityDb.saveEntity(dto.entityUuid, { feeRateSettings });
  };

  savePaymentSettingsProjection = async (dto: EntityPaymentSettingsDto) => {
    info(`save entity payment settings ${JSON.stringify(dto)}`, dto.entityUuid);
    const paymentSettings: PaymentSettings = dto.paymentLimits.reduce((acc: PaymentSettings, limit: PaymentLimit) => {
      const limits = {
        maximum: Number(limit.maximum),
        minimum: Number(limit.minimum),
      };
      if (limit.paymentType === PaymentType.CNP) {
        acc.cnpPaymentLimits = limits;
      }
      if (limit.paymentType === PaymentType.MOTO) {
        acc.motoPaymentLimits = limits;
      }
      if (limit.paymentType === PaymentType.CP) {
        acc.paymentLimits = limits;
      }
      if (limit.paymentType === PaymentType.CPOC) {
        acc.cpocPaymentLimits = limits;
      }
      return acc;
    }, {});
    await this.entityDb.saveEntity(dto.entityUuid, { paymentSettings });
  };

  chunk = (arr: any[], n: number): any[] => (arr.length ? [arr.slice(0, n), ...this.chunk(arr.slice(n), n)] : []);

  constructBatchedMetrics = async (dto: EntityHsMetricUpdatedEventDto) => {
    const raw = await this.downloadFromS3(dto);
    debug(`get metric raw data ${JSON.stringify(raw)}`);
    const batchedMetrics = this.chunk(raw, 100);
    info(`batched metrics: ${JSON.stringify(batchedMetrics)}`);
    if (batchedMetrics.length === 0) {
      error(`no event found from the dto ${JSON.stringify(dto)}`);
      return [];
    }
    return batchedMetrics;
  };

  updateEntityReferred = async (dto: EntityReferredEventDto) => {
    info(`update entity referred ${JSON.stringify(dto)}`, dto.entityUuid);
    const referredEntity = await this.entityDb.getEntity(dto.referred);
    if (referredEntity?.hubspotCompanyId) {
      debug(`Update hubspot - ${referredEntity.hubspotCompanyId}`, dto.referred);
      await this.hubspotApi.updateObject(ObjectType.COMPANY, referredEntity.hubspotCompanyId, {
        properties: {
          referred_by: dto.entityUuid,
        },
      });
    }
    await this.saveReferralProjection(dto);
  };

  updateEntityThreeDSAmountThresholdGroup = async (dto: EntityThreeDsAmountThresholdGroupUpdatedDto) => {
    info(`update entity threeDS amount threshold group ${JSON.stringify(dto)}`, dto.entityUuid);
    const entity = await this.entityDb.getEntity(dto.entityUuid);

    if (entity?.hubspotCompanyId) {
      const data = {
        properties: {
          three_ds_amount_threshold_group: dto.threeDSAmountThresholdGroup,
        },
      };
      await this.hubspotApi.updateObject(ObjectType.COMPANY, entity.hubspotCompanyId, data);
    }

    await this.entityDb.saveEntity(dto.entityUuid, { threeDSAmountThresholdGroup: dto.threeDSAmountThresholdGroup });
  };

  downloadFromS3 = async (dto: EntityHsMetricUpdatedEventDto): Promise<any> => {
    try {
      const s3Link = dto.content.link.replace(`${this.envService.dmsBatchBucket}/`, '');
      const getObjectParams = { Bucket: this.envService.dmsBatchBucket, Key: s3Link };
      info(`Download from S3 ${JSON.stringify(getObjectParams)}`);
      const result = await this.s3.getObjectAndTransformToString(getObjectParams);
      debug(`Batch metrics ${JSON.stringify(dto.content)}`);
      return JSON.parse(result ?? '[]');
    } catch (e) {
      error(e);
      error(`Failed to get batch metrics from s3 ${JSON.stringify(dto.content)}`);
    }
    return [];
  };

  updateEntityMetrics = async (dto: EntityUpdatedEventDto) => {
    const newMetrics = { ...dto.metrics };
    delete newMetrics.rawPayload;
    const entity = await this.entityDb.getEntity(dto.entityUuid);
    let metrics = [];
    if (entity && Array.isArray(entity.metrics)) {
      metrics = entity.metrics.map((m) => {
        if (m.metricsId === newMetrics.metricsId) {
          return newMetrics;
        }
        return m;
      });
      if (!metrics.find((m) => m.metricsId === newMetrics.metricsId)) {
        metrics.push(newMetrics);
      }
    } else {
      metrics = [newMetrics];
    }
    if (entity?.hubspotCompanyId) {
      const { properties } = this.buildHubspotProperties<EntityUpdatedEventDto>(dto, this.prepareDto(entity));
      if (JSON.stringify(properties) !== '{}') {
        await this.hubspotApi.updateObject(ObjectType.COMPANY, entity.hubspotCompanyId, { properties });
        await this.saveEntityUpdatedProjection({
          ...dto,
          metrics,
          hubspotCompanyId: entity.hubspotCompanyId,
        });
      }
    }
  };

  readonly convertRiskReviewResultUpdateDto = ({ dto, entity }: { dto: any; entity: Entity }): any => {
    if (!dto.riskReview?.result) {
      return dto;
    }

    const riskReview = {
      status: RiskReviewStatus.COMPLETED,
      result: dto.riskReview.result,
    };

    const acceptedRiskReviewStatusForRiskReviewResult = new Map([
      [RiskReviewResult.ACCEPTED, [RiskReviewStatus.REQUIRED, RiskReviewStatus.COMPLETED]],
      [
        RiskReviewResult.REJECTED,
        [RiskReviewStatus.NOT_REQUIRED, RiskReviewStatus.REQUIRED, RiskReviewStatus.COMPLETED],
      ],
      [
        RiskReviewResult.DEPLATFORMED,
        [RiskReviewStatus.NOT_REQUIRED, RiskReviewStatus.REQUIRED, RiskReviewStatus.COMPLETED],
      ],
      [RiskReviewResult.ABANDONED, [RiskReviewStatus.REQUIRED, RiskReviewStatus.COMPLETED]],
      [RiskReviewResult.REVIEW, [RiskReviewStatus.NOT_REQUIRED, RiskReviewStatus.REQUIRED, RiskReviewStatus.COMPLETED]],
    ]);
    if (!entity.riskReview?.status) {
      throw new Error('Invalid Entity Risk Review current status');
    }
    /* istanbul ignore next */
    if (!acceptedRiskReviewStatusForRiskReviewResult.get(dto.riskReview.result)?.includes(entity.riskReview.status)) {
      throw new Error(
        `Invalid Input: New Risk Review Result - ${dto.riskReview.result}, Current Risk Review Status - ${entity.riskReview.status}`,
      );
    }
    return { riskReview };
  };

  updateHsAmexGsmfFields = async (dto: EntityAmexMerchantSubmissionCompletedDto) => {
    if (dto.amexGsmf) {
      const entity = await this.entityDb.getEntity(dto.entityUuid);

      info('Updating company amex fields', entity.hubspotCompanyId);
      const data = {
        properties: {
          ...(dto.amexGsmf?.participantSe && { amex_participant_se: dto.amexGsmf.participantSe }),
          ...(dto.amexGsmf?.sellerId && { amex_seller_id: dto.amexGsmf.sellerId }),
          ...(dto.amexGsmf?.messageId && { amex_message_id: dto.amexGsmf.messageId }),
        },
      };
      await this.hubspotApi.updateObject(ObjectType.COMPANY, entity.hubspotCompanyId, data);
    }
  };

  setPrimaryAccountHolder = async (dto: EntityPrimaryAccountHolderChangedEventDto) => {
    return this.customerService.primaryAccountHolderUpdated(dto);
  };

  private readonly generateRollbackModel = (entity: any, rawUpdateDto: any) => {
    if (!rawUpdateDto.riskReview?.result) {
      return entity;
    }
    const model = entity;
    model.riskReview = {
      status: model.riskReview?.status ?? '',
      result: model.riskReview?.result ?? '',
      reason: model.riskReview?.reason,
    };

    return model;
  };

  private readonly webhookUpdate = async (entityFromMaterialisedView: any, event: HubspotWebhookEvent) => {
    const propMap = this.getPropertiesMap();
    const property: HubspotDomainMapping | undefined = propMap.find((p) => p.hubspot === event.propertyName);

    if (!(event.propertyName && property)) {
      return;
    }

    const entityUuid = entityFromMaterialisedView.id;
    const rawUpdateDto = this.createDomainObjectFromHubspot(property, event.propertyValue);
    if (rawUpdateDto?.entityUuid && rawUpdateDto?.entityUuid !== entityUuid) {
      warn('Updating the entityUuid is forbidden via webhook');
      throw new Error('Updating the entityUuid is forbidden via webhook');
    }
    try {
      await this.runWebhookUpdateActionOrDefaultAction(
        property,
        entityFromMaterialisedView,
        rawUpdateDto,
        entityUuid,
        event,
      );
    } catch (err: any) {
      await this.handleFailedUpdateRollback(err, entityFromMaterialisedView, rawUpdateDto, entityUuid, event);
    }
  };

  private async handleFailedUpdateRollback(
    err: any,
    entity: any,
    rawUpdateDto: any,
    entityUuid: string,
    event: HubspotWebhookEvent,
  ) {
    const log = err?.response?.status >= 500 ? error : warn;
    log(`Failed to update entity: ${err.message}`, entityUuid);
    info(`Rollback hubspot company change ${JSON.stringify(event)}`, entityUuid);
    await this.hubspotApi.updateObject(
      ObjectType.COMPANY,
      `${event.objectId}`,
      this.buildHubspotProperties(entity, {
        ...this.generateRollbackModel(entity, rawUpdateDto),
        ...rawUpdateDto,
      }),
    );
  }

  private async runWebhookUpdateActionOrDefaultAction(
    property: HubspotDomainMapping,
    entityFromMaterialisedView: any,
    rawUpdateDto: any,
    entityUuid: string,
    event: HubspotWebhookEvent,
  ) {
    const updateDto = this.convertRiskReviewResultUpdateDto({ dto: rawUpdateDto, entity: entityFromMaterialisedView });
    this.amsApi.setHeaders(this.getMutationAttributionHeaders(event));
    if (property?.webhookUpdateAction) {
      await property.webhookUpdateAction(this, { ...updateDto, entityUuid });
    } else {
      await this.saveEntityUpdatedProjection({ ...updateDto, entityUuid });
      await this.domicileDb.setDomicileContext({ entityUuid });
      const result = await this.amsApi.updateEntity({ ...updateDto, entityUuid });
      if (result?.status === HttpStatusCode.Ok && result?.partialUpdateErrors) {
        await this.handlePartialFailure(
          property,
          entityFromMaterialisedView,
          rawUpdateDto,
          result.partialUpdateErrors,
          event,
        );
      }
    }
    if (updateDto.riskReview?.result) {
      /* istanbul ignore next */
      if (entityFromMaterialisedView.riskReview?.status !== RiskReviewStatus.COMPLETED) {
        await this.hubspotApi.updateObject(ObjectType.COMPANY, `${event.objectId}`, {
          properties: {
            risk_review_status: RiskReviewStatus.COMPLETED,
          },
        });
      }
    }
  }

  private readonly handlePartialFailure = async (
    property: HubspotDomainMapping | undefined,
    entityStateBeforeEvent: any,
    rawUpdateDto: JsonAny,
    errors: PartialFailure<EntityBaseEventDto>[],
    event: HubspotWebhookEvent,
  ) => {
    const entityUuid = entityStateBeforeEvent.id;
    const concatErrorMessage = errors
      .map((e) => e.errorMessage)
      .reduce((thisMessage, fullMessage) => `${fullMessage}\n${thisMessage}`, '');

    error(`Failed to update entity: ${concatErrorMessage}`, entityUuid);

    info(`Rollback hubspot company change ${JSON.stringify(event)}`, entityUuid);
    const partialSuccess = applyPartialSuccess(entityStateBeforeEvent, rawUpdateDto, errors);

    const updateModel = this.buildHubspotProperties(partialSuccess, {
      ...this.generateRollbackModel(partialSuccess, rawUpdateDto),
      ...rawUpdateDto,
    });

    await this.hubspotApi.updateObject(ObjectType.COMPANY, `${event.objectId}`, updateModel);

    const propertiesFailedToSet = errors
      .map((x) => x.path as string) // NOSONAR
      .reduce((thisPath, allPaths) => `${thisPath} , ${allPaths}`, '');

    const createTicketRequest: ExternalCreateTicketRequest = {
      recordType: HubspotRecordType.ENTITY,
      recordUuid: entityUuid,
      name: `Could not set value on entity`,
      pipeline: 0,
      pipelineStage: 0,
      description:
        `Some properties on an COMPANY (with id ${entityUuid}) were recently changed, ` +
        `however not all the changes successfully saved. 
        The properties we tried to update, but failed to, were ${propertiesFailedToSet}.`,
    };

    await this.ticketService.createTicket({ body: createTicketRequest });
  };

  handleWebhookPropertyChangeEvent = async (event: HubspotWebhookEvent) => {
    try {
      const entity = await this.entityDb.getEntityByHubspotId(`${event.objectId}`);
      await this.webhookUpdate(entity, event);
    } catch (err: any) {
      warn(`Failed to update entity: ${err.message}`);
    }
  };

  handleWebhookMergeEvent = async (event: HubspotWebhookEvent) => {
    const mergedObjectId = event.mergedObjectIds?.find((id) => id !== event.primaryObjectId);

    if (event.primaryObjectId && mergedObjectId) {
      const primaryEntity = await this.entityDb.getEntityByHubspotId(`${event.primaryObjectId}`).catch(() => {});
      const mergedEntity = await this.entityDb.getEntityByHubspotId(`${mergedObjectId}`).catch(() => {});

      if (primaryEntity && mergedEntity) {
        error(
          `Detected NOT auto-fixable wrong Hubspot merge action: primary entity: ${primaryEntity.id}, merged entity: ${mergedEntity.id}`,
          mergedEntity.id,
        );
        return;
      }

      // primary entity should take priority, and should the hubspot object created by crms rather than the Growth team
      const targetEntity = mergedEntity ?? primaryEntity;

      if (targetEntity) {
        if (mergedEntity) {
          error(
            `Detected auto-fixable wrong Hubspot merge action. Primary entity: ${event.primaryObjectId}, merged entity: ${mergedObjectId}`,
            targetEntity.id,
          );
        }

        try {
          info(`Updating hubspotCompanyId for entity ${targetEntity.id} to ${event.objectId}`, targetEntity.id);
          const updateDto = { entityUuid: targetEntity.id, hubspotCompanyId: `${event.objectId}` };
          await this.saveEntityUpdatedProjection(updateDto);
          await this.domicileDb.setDomicileContext({ entityUuid: updateDto.entityUuid });
          await this.amsApi.updateEntity(updateDto);
        } catch (err: any) {
          warn(`Failed to update entity: ${err.message}`, targetEntity.id);
        }
      } else {
        debug(`Didn't find entity for the merged hubspot id. No wrong Hubspot merge action.`);
      }
    }
  };

  @OnEvent(WebhookEvent.entity, { async: true })
  async webhookListener(event: HubspotWebhookEvent) {
    const webhookEventHandlerMap = {
      [SubscriptionType.COMPANY_PROPERTYCHANGE]: this.handleWebhookPropertyChangeEvent,
      [SubscriptionType.COMPANY_MERGE]: this.handleWebhookMergeEvent,
    };

    debug(`entity webhook callback ${JSON.stringify(event)}`);
    if (event.subscriptionType in webhookEventHandlerMap) {
      await webhookEventHandlerMap[event.subscriptionType as keyof typeof webhookEventHandlerMap](event);
    } else {
      debug(`Skipping event, event type: ${event.subscriptionType} is not supported.`);
    }
  }

  getPropertiesMap = () => entityProperties;

  getCustomerContactPropertiesMap = (): HubspotDomainMappings => customerProperties;
}
