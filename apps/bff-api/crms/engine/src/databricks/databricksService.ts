import { getCachedSsmParameter } from '@npco/component-bff-core/dist/lambda/lambdaExtension';
import { differenceInDays } from '@npco/component-bff-core/dist/utils/dateUtils';
import { info } from '@npco/component-bff-core/dist/utils/logger';

import type { DatabricksEnvironmentService } from '../common/service/databricksEnvService';

import { DatabricksAuthService } from './databricksAuthService';
import type { TransactionOutlierChartDataInputFilter } from './types';

export class DatabricksService {
  private readonly databricksClient: DatabricksAuthService;

  constructor(readonly envService: DatabricksEnvironmentService) {
    this.databricksClient = new DatabricksAuthService(
      this.envService.databricksClientId,
      this.envService.databricksHost,
      this.envService.databricksHttpPath,
    );
  }

  public async getTransactionOutlierChartData(entityUuid: string, filter: TransactionOutlierChartDataInputFilter) {
    const databricksClientSecret = await getCachedSsmParameter(this.envService.databricksClientSecretSsm);
    const dayRange = differenceInDays(filter.startDate, filter.endDate);
    info(`dayRange: ${dayRange}`);

    const query = `
      with 
      
      base as (
        select
          transaction_uuid,
          transaction_type,
          transaction_time_aet,
          transaction_date_aet,
          case
            when transaction_type in ('PURCHASE', 'PURCHASE_ADVICE') then transaction_amount
            when transaction_type in ('REFUND', 'REVERSAL') then transaction_amount * -1
            end / 100 as transaction_amount_dollar,
          ceiling(transaction_amount_dollar, -1) as rounded_transaction_amount
        from prod_zeller_data_gold.payments.fact_transactions
        where 1=1
          -- configurable filter
          and transaction_date_aet > date_add(day, (:day_range * -1), from_utc_timestamp(current_timestamp(), 'Australia/Melbourne'))
          and entity_uuid = :entity_uuid
          -- default filter
          and transaction_approved
          and not transaction_reversed
          -- testing purpose
          and transaction_type = 'PURCHASE'
      ),
      window as (
        select 
          transaction_date_aet,
          transaction_amount_dollar,
          rounded_transaction_amount,
          transaction_uuid,
          percentile_disc(0.25) within group (order by transaction_amount_dollar) over (partition by date_trunc('month', transaction_date_aet)) as p25,
          percentile_disc(0.75) within group (order by transaction_amount_dollar) over (partition by date_trunc('month', transaction_date_aet)) as p75,
          p75 - p25 as iqr,
          p75 + :sensitivity * iqr as threshold,
          case 
            when transaction_amount_dollar > threshold then 'Outlier'
            else 'Normal'
          end as outlier_detection
        from base
      ),
      final as (
        select
          transaction_date_aet,
          rounded_transaction_amount,
          any_value(threshold),
          any_value(outlier_detection) as outlier_detection,
          case
            when any_value(outlier_detection) = 'Outlier' then last(transaction_uuid)
          end as sample_transaction_uuid,
          count(distinct transaction_uuid) as volume
        from window
        group by 1,2
      )
      select * from final`;

    const params = {
      namedParameters: {
        entity_uuid: entityUuid,
        day_range: dayRange,
        sensitivity: 1.5,
      },
    };

    return this.databricksClient.executeQuery(query, databricksClientSecret, params);
  }
}
