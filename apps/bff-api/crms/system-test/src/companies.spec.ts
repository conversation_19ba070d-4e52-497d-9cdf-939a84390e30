import { DynamodbClient, invokeSync<PERSON><PERSON>b<PERSON> } from '@npco/bff-systemtest-utils';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import {
  AmexAcquisitionStatus,
  DbRecordType,
  EntityType,
  KYCScreeningResult,
  ScreeningRequestedType,
  ScreeningStatus,
  StandInField,
  StandInOperation,
  Status,
} from '@npco/component-dto-core';
import type { CustomerCreatedEventDto } from '@npco/component-dto-customer';
import type { EntityScreeningCompletedEventDto } from '@npco/component-dto-entity';
import {
  EntityCreatedEventDto,
  EntityReferredEventDto,
  EntityStandInRulesUpdatedEventDto,
  OnboardingStatus,
  EntityUpdatedEventDto,
} from '@npco/component-dto-entity';

import { S3 } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';

import { region, stage } from './utils/globalVariables';
import { bootstrapHubspotAppOauthToken } from './utils/hubspot/bootstrapHubspotOauthToken';
import { getDynamoDbTableName } from './utils/hubspot/getDynamoDbTable';
import { ObjectType } from './utils/hubspot/hubspotProperties';
import { HubspotPublicApp } from './utils/hubspot/hubspotTypes';
import {
  createContactObject,
  getAssociations,
  getCompany,
  HubspotMetricsMapping,
  searchObjectByDomainId,
} from './utils/hubspot/hubspotUtil';
import { retry } from './utils/retry';
import { createCompany } from './utils/simplified-services/createCompany';
import { createContact } from './utils/simplified-services/createContact';
import { createContactEvent } from './utils/simplified-services/createContactEvent';
import { sleep } from './utils/sleep';
import { getEngineStackName } from './utils/stacks/getEngineStackName';
import { testIf } from './utils/testIf';
import { queryEventHook, sendHighPriorityQueueSqsMessage, sendWebhookEvent } from './utils/utils';

beforeAll(async () => {
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.DEFAULT_EXTENSION);
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.BULK_UPDATE);
});

describe('companies test suite', () => {
  let company: any;
  const s3: S3 = new S3({ region });
  const db = new DynamodbClient({ region });
  const registeredAddress = {
    state: 'VIC',
    street1: 'street1',
    street2: 'street2',
    suburb: 'Melbourne',
    postcode: '3000',
    country: 'au',
  };
  const businessAddress = {
    state: 'VIC',
    street1: 'street1',
    street2: 'street2',
    suburb: 'Melbourne',
    postcode: '3000',
    country: 'au',
  };
  const updateRegAddress = (prop: keyof typeof registeredAddress) => {
    registeredAddress[prop] = uuidv4();
    return registeredAddress;
  };
  const updateBusAddress = (prop: keyof typeof businessAddress) => {
    businessAddress[prop] = uuidv4();
    return businessAddress;
  };

  const getRandomHsObjectId = () => Math.floor(10000000 + Math.random() * 90000000);

  const entityCreatedDto = () =>
    new EntityCreatedEventDto({
      entityUuid: uuidv4(),
      shortId: uuidv4(),
      name: uuidv4(),
      type: EntityType.COMPANY,
      abn: uuidv4(),
      acn: uuidv4(),
      tradingName: 'tradingName',
      category: '124',
      categoryGroup: '12',
      registeredAddress: {
        state: 'VIC',
        street1: 'street1',
        street2: 'street2',
        suburb: 'Melbourne',
        postcode: '3000',
        country: 'au',
      },
      businessAddress: {
        state: 'VIC',
        street1: 'street1',
        street2: 'street2',
        suburb: 'Melbourne',
        postcode: '3000',
        country: 'au',
      },
      accountStatus: {
        canAcquireCnp: true,
        canAcquireVt: true,
        canAcquireMobile: true,
        canAcquireAmex: true,
        canAcquireMoto: true,
        canAcquire: true,
        canRefund: true,
        canStandIn: true,
        hadForcedRefund: true,
        hasChargeback: true,
        hadDirectDebitFailure: true,
        hasDirectDebitRequest: true,
      } as any,
      facebook: 'facebook',
      twitter: 'twitter',
      website: 'website',
      onboardingStatus: OnboardingStatus.NONE,
      estimatedAnnualRevenue: 1,
      sourceIp: '',
      manualEntry: true,
      status: Status.ACTIVE,
      standInRules: [
        {
          operation: StandInOperation.ABOVE,
          field: StandInField.OFFLINE_AMOUNT,
          value: 'value',
        },
      ],
      screening: {
        status: ScreeningStatus.REQUIRED,
        screeningType: ScreeningRequestedType.DEFAULT,
      },
      establishingBusiness: true,
      countryOfOrigin: 'AUS',
      domicile: Domicile.AU,
      currency: 'AUD',
    });

  const queryEntity = async (entityUuid: string, type = 'entity.core') => {
    return db.query({
      TableName: getDynamoDbTableName(),
      KeyConditionExpression: 'id = :entityUuid AND #type = :type',
      ExpressionAttributeNames: {
        '#type': 'type',
      },
      ExpressionAttributeValues: {
        ':entityUuid': entityUuid,
        ':type': type,
      },
    });
  };

  it('should be able to create company', async () => {
    const event = entityCreatedDto();
    const sqsMessage = {
      'detail-type': 'crms.Entity.Created',
      detail: event,
    };

    await sendHighPriorityQueueSqsMessage(event.entityUuid, sqsMessage);
    await sleep(500);
    await retry(async () => {
      const companies = (await searchObjectByDomainId(ObjectType.COMPANY, 'company_uuid', event.entityUuid))!.data;
      console.log('load from hubspot for create company:', JSON.stringify(companies));
      expect(companies.results.length).toBe(1);
      company = companies.results[0];
      expect(company.properties.name).toBe(event.name);
      expect(company.properties.company_uuid).toBe(event.entityUuid);
      expect(company.properties).toEqual({
        abn_australian_business_number: event.abn,
        acn_australian_company_number: event.acn,
        trading_name: 'tradingName',
        address: 'street1',
        address2: 'street2',
        annualrevenue: '1',
        city: 'Melbourne',
        company_uuid: event.entityUuid,
        country: 'au',
        country_of_origin: 'AUS',
        createdate: expect.any(String),
        facebook_company_page: 'facebook',
        hs_lastmodifieddate: expect.any(String),
        hs_object_id: expect.any(String),
        kyc_successfully_completed: null,
        amex_submission: null,
        mcc: '12-124',
        name: event.name,
        name_screening_complete: JSON.stringify({
          status: ScreeningStatus.REQUIRED,
          screeningType: ScreeningRequestedType.DEFAULT,
        }),
        normalised_state: 'VIC',
        onboarding_status: 'NONE',
        registered_city: 'Melbourne',
        registered_country: 'au',
        registered_postal_code: '3000',
        registered_state: 'VIC',
        registered_street_address: 'street1',
        registered_street_address_2: 'street2',
        entity_type: 'COMPANY',
        state: 'VIC',
        twitterhandle: 'twitter',
        website: 'website',
        zip: '3000',
        account_status:
          'canAcquire;canAcquireMoto;canAcquireCnp;canAcquireVt;canAcquireMobile;canAcquireAmex;canRefund;canStandIn;hadForcedRefund;hasChargeback;hadDirectDebitFailure;hasDirectDebitRequest',
        settlement_account_number: null,
        settlement_bsb: null,
        settlement_account_name: null,
        how_do_customers_find_you: null,
        instagram: null,
        products_services_sold: null,
        pan_diversity_90_day: null,
        fee_percent: null,
        fee_percent_moto: null,
        fee_percent_cpoc: null,
        fee_fixed_cpoc: null,
        fee_percent_zinv: null,
        fee_fixed_zinv: null,
        fee_percent_intl_zinv: null,
        fee_fixed_intl_zinv: null,
        fee_percent_xinv: null,
        fee_fixed_xinv: null,
        fee_percent_intl_xinv: null,
        fee_fixed_intl_xinv: null,
        fee_percent_vt: null,
        fee_fixed_vt: null,
        fee_percent_pbl: null,
        fee_fixed_pbl: null,
        fee_percent_intl_pbl: null,
        fee_fixed_intl_pbl: null,
        status: 'ACTIVE',
        settlement_programme: null,
        avt_lifetime_average_ticket_size: null,
        card_present_vs_card_not_present: null,
        days_since_last_acquired_transaction: null,
        decline_rate_7_day: null,
        decline_rate_90_day: null,
        decline_rate_lifetime: null,
        gpv_7_day: null,
        gpv_90_day: null,
        gpv_lifetime: null,
        held_deposit_current_pending: null,
        lifetime_settlements: null,
        n7_day_settlements: null,
        n90_day_settlements: null,
        pan_diversity_7_day: null,
        pan_diversity_lifetime: null,
        refunds_7_day: null,
        refunds_90_day: null,
        refunds_average_ticket_lifetime: null,
        refunds_lifetime: null,
        metrics_ats_life_time: null,
        establishing_business: 'true',
        referral_code: event.shortId,
        referred_by: null,
        screening_review_required: null,
        transaction_count_lifetime: null,
        transaction_count_90_days: null,
        transaction_count___7_days: null,
        banking_migration_state: null,
        number_of_invoice_created: null,
        number_of_invoice_sent: null,
        last_invoice_created_date: null,
        last_invoice_sent_date: null,
        draft_invoice_created___last_7_days: null,
        lifetime_number_of_draft_invoices: null,
        last_draft_invoice_created_date: null,
        number_of_invoice_sent___last_7_days: null,
        pos_integrated: null,
        gpv___7_day_cpoc_ios: null,
        gpv___7_day_cpoc_android: null,
        gpv_90_day_cpoc_ios: null,
        gpv___90_day_cpoc_android: null,
        gpv___lifetime_cpoc_ios: null,
        gpv___lifetime_cpoc_android: null,
        gpv_90d_cpoc: null,
        gpv_90d_potentially_fraudulent: null,
        gpv_lifetime_cnp: null,
        gpv_lifetime_cnp_settled: null,
        gpv_lifetime_cp: null,
        gpv_lifetime_cpoc: null,
        gpv_lifetime_potentially_fraudulent: null,
        domicile: event.domicile,
        currency: event.currency,
      });
      const dbItem = await queryEntity(event.entityUuid);
      expect(dbItem.Items!.length).toBe(1);
      expect(dbItem.Items![0].name).toBe(event.name);
      expect(dbItem.Items![0].hubspotCompanyId).toBeDefined();
      expect(dbItem.Items![0].hubspotCompanyId).not.toBeNull();
      expect(dbItem.Items![0]).toEqual({
        abn: event.abn,
        acn: event.acn,
        tradingName: 'tradingName',
        businessAddress: {
          country: 'au',
          postcode: '3000',
          state: 'VIC',
          street1: 'street1',
          street2: 'street2',
          suburb: 'Melbourne',
        },
        countryOfOrigin: 'AUS',
        normalisedState: 'VIC',
        category: '124',
        categoryGroup: '12',
        entityType: 'COMPANY',
        estimatedAnnualRevenue: 1,
        facebook: 'facebook',
        hubspotCompanyId: expect.any(String),
        id: event.entityUuid,
        entityUuid: event.entityUuid,
        manualEntry: true,
        name: event.name,
        onboardingStatus: 'NONE',
        registeredAddress: {
          country: 'au',
          postcode: '3000',
          state: 'VIC',
          street1: 'street1',
          street2: 'street2',
          suburb: 'Melbourne',
        },
        screening: {
          status: ScreeningStatus.REQUIRED,
          screeningType: ScreeningRequestedType.DEFAULT,
        },
        canAcquireCnp: true,
        canAcquireVt: true,
        canAcquireMoto: true,
        canAcquireMobile: true,
        canAcquireAmex: true,
        canAcquire: true,
        canRefund: true,
        canStandIn: true,
        hadForcedRefund: true,
        hasChargeback: true,
        hadDirectDebitFailure: true,
        hasDirectDebitRequest: true,
        sourceIp: '',
        twitter: 'twitter',
        type: 'entity.core',
        website: 'website',
        status: 'ACTIVE',
        version: expect.any(Number),
        standInRules: event.standInRules,
        establishingBusiness: true,
        shortId: event.shortId,
        transactionMetaData: {
          yetToMakeTransaction: true,
        },
        domicile: event.domicile,
        currency: event.currency,
      });
    });
  });

  it('should to associate contact to company', async () => {
    const customerUuid = uuidv4();
    const event = entityCreatedDto();

    await db.putItem(getDynamoDbTableName(), {
      id: customerUuid,
      type: `${DbRecordType.CUSTOMER_ENTITY}${event.entityUuid}`,
      entityUuid: event.entityUuid,
      customerUuid,
    });

    const hubstpotContact = await createContactObject(customerUuid, {
      firstname: `system-test-${uuidv4()}`,
    });

    await sleep(10000);

    await db.putItem(getDynamoDbTableName(), {
      id: customerUuid,
      type: DbRecordType.CUSTOMER,
      entityUuid: event.entityUuid,
      hubspotContactId: hubstpotContact.id,
    });

    await sleep();

    const sqsMessage = {
      'detail-type': 'crms.Entity.Created',
      detail: event,
    };
    await sendHighPriorityQueueSqsMessage(event.entityUuid, sqsMessage);

    await retry(
      async () => {
        const companies = (await searchObjectByDomainId(ObjectType.COMPANY, 'company_uuid', event.entityUuid))!.data;
        const associations = (await getAssociations(ObjectType.CONTACT, hubstpotContact.id, ObjectType.COMPANY)).data;

        expect(associations.results.length).toBe(1);
        expect(associations.results[0].id).toBe(companies.results[0].id);
        expect(associations.results[0].type).toBe('contact_to_company');
      },
      2,
      10000,
    );
  });

  it('should be able to update "screening_review_required" on screening completed', async () => {
    const event: EntityScreeningCompletedEventDto = {
      entityUuid: company.properties.company_uuid,
      result: KYCScreeningResult.POTENTIAL_MATCH,
    };
    const sqsMessage = {
      'detail-type': 'crms.Entity.ScreeningCompleted',
      detail: event,
    };
    await sendHighPriorityQueueSqsMessage(event.entityUuid, sqsMessage);
    await sleep(1000);

    await retry(
      async () => {
        const c = (await getCompany(company.id)).data;
        expect(c.properties).toEqual({
          ...company.properties,
          hs_lastmodifieddate: expect.any(String),
          screening_review_required: 'true',
        });
        company = c;
      },
      5,
      2000,
    );
  });

  test.each([
    {
      property: 'name',
      key: 'name',
      value: uuidv4(),
    },
    {
      key: 'abn',
      property: 'abn_australian_business_number',
      value: uuidv4(),
    },
    {
      key: 'acn',
      property: 'acn_australian_company_number',
      value: uuidv4(),
    },
    {
      key: 'tradingName',
      property: 'trading_name',
      value: uuidv4(),
    },
    {
      key: 'registeredAddress',
      property: 'registered_state',
      value: updateRegAddress('state'),
      child: 'state',
    },
    {
      key: 'registeredAddress',
      property: 'registered_street_address',
      value: updateRegAddress('street1'),
      child: 'street1',
    },
    {
      key: 'registeredAddress',
      property: 'registered_street_address_2',
      value: updateRegAddress('street2'),
      child: 'street2',
    },
    {
      key: 'registeredAddress',
      property: 'registered_city',
      value: updateRegAddress('suburb'),
      child: 'suburb',
    },
    {
      key: 'registeredAddress',
      property: 'registered_postal_code',
      value: updateRegAddress('postcode'),
      child: 'postcode',
    },
    {
      key: 'registeredAddress',
      property: 'registered_country',
      value: updateRegAddress('country'),
      child: 'country',
    },
    {
      key: 'businessAddress',
      property: 'state',
      value: updateBusAddress('state'),
      child: 'state',
    },
    {
      key: 'businessAddress',
      property: 'address',
      value: updateBusAddress('street1'),
      child: 'street1',
    },
    {
      key: 'businessAddress',
      property: 'address2',
      value: updateBusAddress('street2'),
      child: 'street2',
    },
    {
      key: 'businessAddress',
      property: 'city',
      value: updateBusAddress('suburb'),
      child: 'suburb',
    },
    {
      key: 'businessAddress',
      property: 'zip',
      value: updateBusAddress('postcode'),
      child: 'postcode',
    },
    {
      key: 'businessAddress',
      property: 'country',
      value: updateBusAddress('country'),
      child: 'country',
    },
    {
      key: 'facebook',
      property: 'facebook_company_page',
      value: uuidv4(),
    },
    {
      key: 'twitter',
      property: 'twitterhandle',
      value: uuidv4(),
    },
    {
      key: 'website',
      property: 'website',
      value: uuidv4(),
    },
    {
      key: 'estimatedAnnualRevenue',
      property: 'annualrevenue',
      value: 2,
    },
    {
      property: 'onboarding_status',
      key: 'onboardingStatus',
      value: 'ONBOARDED',
    },
    {
      property: 'settlement_account_name',
      key: 'depositAccountName',
      value: uuidv4(),
    },
    {
      property: 'settlement_bsb',
      key: 'depositAccountBsb',
      value: 'bsb001',
    },
    {
      property: 'instagram',
      key: 'instagram',
      value: uuidv4(),
    },
    {
      property: 'products_services_sold',
      key: 'goodsServicesProvided',
      value: uuidv4(),
    },
    {
      property: 'how_do_customers_find_you',
      key: 'customerDiscovery',
      value: uuidv4(),
    },
    {
      property: 'settlement_account_number',
      key: 'depositAccountNumber',
      value: '1234',
    },
    {
      property: 'settlement_bsb',
      key: 'depositAccountBsb',
      value: uuidv4(),
    },
    {
      property: 'account_status',
      child: 'canAcquire',
      dbKey: 'canAcquire',
      key: 'accountStatus',
      value: {
        canAcquire: false,
      },
      expected: false,
      result:
        'canAcquireMoto;canAcquireCnp;canAcquireVt;canAcquireMobile;canAcquireAmex;canRefund;canStandIn;hadForcedRefund;hasChargeback;hadDirectDebitFailure;hasDirectDebitRequest',
    },
    {
      property: 'status',
      key: 'status',
      value: 'INACTIVE',
    },
    {
      property: 'status',
      key: 'status',
      value: 'ACTIVE',
    },
    {
      property: 'status',
      key: 'status',
      value: 'DISABLED',
    },
    {
      property: 'status',
      key: 'status',
      value: 'DELETED',
    },
    {
      property: 'amex_submission',
      key: 'amexSubmission',
      value: {
        status: AmexAcquisitionStatus.REQUIRED,
      },
      result: JSON.stringify({
        status: AmexAcquisitionStatus.REQUIRED,
      }),
    },
    {
      property: 'amex_submission',
      key: 'amexSubmission',
      value: {
        status: AmexAcquisitionStatus.COMPLETED,
      },
      result: JSON.stringify({
        status: AmexAcquisitionStatus.COMPLETED,
      }),
    },
    {
      property: 'amex_submission',
      key: 'amexSubmission',
      value: {
        status: AmexAcquisitionStatus.ERROR,
        result: {
          errors: [{ err_msg: 'err_msg' }],
          warnings: [{ waring_msg: 'warning_msg' }],
        },
      },
      result: JSON.stringify({
        status: AmexAcquisitionStatus.ERROR,
        result: {
          errors: [{ err_msg: 'err_msg' }],
          warnings: [{ waring_msg: 'warning_msg' }],
        },
      }),
    },
    {
      property: 'settlement_programme',
      key: 'remitToCard',
      value: true,
      result: 'Zeller Account',
    },
    {
      property: 'settlement_programme',
      key: 'remitToCard',
      value: false,
      result: '3rd Party Bank',
    },
    {
      property: 'name_screening_complete',
      key: 'screening',
      value: {
        status: ScreeningStatus.REQUIRED,
        screeningType: ScreeningRequestedType.DRIVERS_LICENCE,
      },
      result: JSON.stringify({
        status: ScreeningStatus.REQUIRED,
        screeningType: ScreeningRequestedType.DRIVERS_LICENCE,
      }),
    },
    {
      property: 'establishing_business',
      key: 'establishingBusiness',
      value: false,
      result: 'false',
    },
  ])('should be able to update company property in hubspot %p', async (data) => {
    const event: any = new EntityUpdatedEventDto({
      entityUuid: company.properties.company_uuid,
      [data.key]: data.value,
    });
    const sqsMessage = {
      'detail-type': 'crms.Entity.Updated',
      detail: event,
    };
    console.log(`Sending the company update with ${data.key}`, JSON.stringify(sqsMessage));
    await sendHighPriorityQueueSqsMessage(event.entityUuid, sqsMessage);
    await sleep(1000);
    await retry(
      async () => {
        company = (await getCompany(company.id)).data;
        console.log('load from hubspot for update company:', JSON.stringify(company));
        const dbItem = await queryEntity(event.entityUuid);
        console.log('db item:', dbItem);
        expect(dbItem.Items!.length).toBe(1);
        const expected = data.hasOwnProperty('expected') ? data.expected : event[data.key];
        const result = data.dbKey ? dbItem.Items![0][data.dbKey] : dbItem.Items![0][data.key];
        console.log(result, expected);
        expect(result).toEqual(expected);
        if (data.result) {
          expect(company.properties[data.property]).toEqual(data.result);
        } else {
          const update = data.child ? event[data.key][data.child] : event[data.key];
          expect(company.properties[data.property]).toEqual(String(update));
        }
      },
      40, // sometimes this takes a long time, but usually quick, so using many retries
      1000,
    );
  });

  it('should update normalised_state property in hubspot', async () => {
    const event: any = new EntityUpdatedEventDto({
      entityUuid: company.properties.company_uuid,
      registeredAddress: {
        state: 'QLD',
      },
    });
    const sqsMessage = {
      'detail-type': 'crms.Entity.Updated',
      detail: event,
    };
    await sendHighPriorityQueueSqsMessage(event.entityUuid, sqsMessage);
    await sleep(1000);

    await retry(
      async () => {
        company = (await getCompany(company.id)).data;
        const dbItem = await queryEntity(event.entityUuid);
        const dbItemEntity = dbItem.Items![0];
        expect(company.properties.registered_state).toEqual('QLD');
        expect(company.properties.normalised_state).toEqual('QLD');
        expect(dbItemEntity.registeredAddress.state).toEqual('QLD');
        expect(dbItemEntity.normalisedState).toEqual('QLD');
      },
      20,
      1000,
    );
  });

  it('should not override normalised_state if business address is updated ', async () => {
    const event: any = new EntityUpdatedEventDto({
      entityUuid: company.properties.company_uuid,
      businessAddress: {
        state: 'WA',
      },
    });
    const sqsMessage = {
      'detail-type': 'crms.Entity.Updated',
      detail: event,
    };
    await sendHighPriorityQueueSqsMessage(event.entityUuid, sqsMessage);
    await sleep(1000);

    await retry(
      async () => {
        company = (await getCompany(company.id)).data;
        const dbItem = await queryEntity(event.entityUuid);
        const dbItemEntity = dbItem.Items![0];
        expect(company.properties.state).toEqual('WA');
        expect(company.properties.normalised_state).toEqual('QLD');
        expect(dbItemEntity.businessAddress.state).toEqual('WA');
        expect(dbItemEntity.normalisedState).toEqual('QLD');
      },
      20,
      1000,
    );
  });

  it('should not populate normalised_state if invalid', async () => {
    const event = entityCreatedDto();
    delete event.businessAddress;
    event.registeredAddress!.state = 'NOT A STATE';
    const sqsMessage = {
      'detail-type': 'crms.Entity.Created',
      detail: event,
    };
    await sendHighPriorityQueueSqsMessage(event.entityUuid, sqsMessage);
    await sleep(500);

    await retry(
      async () => {
        const companies = (await searchObjectByDomainId(ObjectType.COMPANY, 'company_uuid', event.entityUuid))!.data;
        const company: any = companies.results[0];
        const dbItem = await queryEntity(event.entityUuid);
        const dbItemEntity = dbItem.Items![0];
        expect(company.properties.registered_state).toEqual('NOT A STATE');
        expect(company.properties.normalised_state).toBeNull();
        expect(dbItemEntity.registeredAddress.state).toEqual('NOT A STATE');
        expect(dbItemEntity.normalisedState).toBeUndefined();
      },
      20,
      1000,
    );
  });

  it('should not allow change onboarding status if it is already onboarded', async () => {
    const event: any = new EntityUpdatedEventDto({
      entityUuid: company.properties.company_uuid,
      onboardingStatus: OnboardingStatus.REVIEW,
    });
    const sqsMessage = {
      'detail-type': 'crms.Entity.Updated',
      detail: event,
    };
    await sendHighPriorityQueueSqsMessage(event.entityUuid, sqsMessage);
    await retry(
      async () => {
        company = (await getCompany(company.id)).data;
        console.log('load from hubspot for update company:', JSON.stringify(company));
        const dbItem = await queryEntity(event.entityUuid);
        console.log('db item:', dbItem);
        expect(dbItem.Items!.length).toBe(1);
        expect(dbItem.Items![0].onboardingStatus).toEqual(OnboardingStatus.ONBOARDED);
        expect(company.properties.onboarding_status).toEqual(OnboardingStatus.ONBOARDED);
      },
      20,
      1000,
    );
  });

  test.each([
    {
      subscriptionType: 'company.propertyChange',
      propertyName: 'name',
      propertyValue: uuidv4(),
      projectionName: 'name',
    },
    {
      subscriptionType: 'company.propertyChange',
      propertyName: 'abn_australian_business_number',
      propertyValue: uuidv4(),
      projectionName: 'abn',
    },
    {
      subscriptionType: 'company.propertyChange',
      propertyName: 'acn_australian_company_number',
      propertyValue: uuidv4(),
      projectionName: 'acn',
    },
    {
      subscriptionType: 'company.propertyChange',
      propertyName: 'onboarding_status',
      propertyValue: OnboardingStatus.REVIEW,
      projectionName: 'onboardingStatus',
    },
    {
      subscriptionType: 'company.propertyChange',
      propertyName: 'settlement_programme',
      propertyValue: 'Zeller Account',
    },
    {
      subscriptionType: 'company.propertyChange',
      propertyName: 'settlement_programme',
      propertyValue: '3rd Party Bank',
    },
    {
      subscriptionType: 'company.propertyChange',
      propertyName: 'referred_by',
      propertyValue: uuidv4(),
    },
  ])('should be able to handle company update webhook events %p', async (data) => {
    const company = await createCompany();
    const event = [
      {
        eventId: uuidv4(),
        subscriptionId: uuidv4(),
        portalId: uuidv4(),
        occurredAt: new Date().getTime(),
        subscriptionType: data.subscriptionType,
        attemptNumber: 0,
        objectId: company.id,
        propertyName: data.propertyName,
        propertyValue: data.propertyValue,
        changeSource: 'UI',
      },
    ];
    await sleep(500);
    await sendWebhookEvent(JSON.stringify(event));
    await retry(
      async () => {
        const output = await queryEventHook(company.id, event[0].occurredAt);

        expect(output.Items!.length).toBe(1);
        expect(output.Items![0].propertyName).toBe(data.propertyName);
        expect(output.Items![0].propertyValue).toBe(data.propertyValue);
      },
      20,
      1000,
    );
    if (data.projectionName) {
      await retry(
        async () => {
          const entityId = company.properties.company_uuid;
          console.log('query entity:', entityId);
          const dbItem = await queryEntity(entityId);
          console.log('db item:', dbItem);
          expect(dbItem.Items!.length).toBe(1);
          expect(dbItem.Items![0][data.projectionName]).toEqual(data.propertyValue);
        },
        20,
        1000,
      );
    }
  });

  it('should be able to handle company merge webhook event from primary to merged object', async () => {
    const primaryObjectId = getRandomHsObjectId();
    const mergedCompany = await createCompany();
    const mergedObjectId = Number(mergedCompany.id);
    const newObjectId = getRandomHsObjectId();
    const event = [
      {
        eventId: uuidv4(),
        subscriptionId: uuidv4(),
        portalId: uuidv4(),
        occurredAt: new Date().getTime(),
        subscriptionType: 'company.merge',
        attemptNumber: 0,
        objectId: newObjectId,
        newObjectId,
        changeSource: 'CRM_UI',
        primaryObjectId,
        mergedObjectIds: [mergedObjectId, primaryObjectId],
      },
    ];

    await retry(async () => {
      const entityId = mergedCompany.properties.company_uuid;
      const dbItem = await queryEntity(entityId);
      console.log('db item:', dbItem);
      expect(dbItem.Items!.length).toBe(1);
    });

    await sendWebhookEvent(JSON.stringify(event));

    await retry(async () => {
      const output = await queryEventHook(`${event[0].objectId}`, event[0].occurredAt);
      expect(output.Items!.length).toBe(1);
      expect(output.Items![0].primaryObjectId).toBe(primaryObjectId);
      expect(output.Items![0].mergedObjectIds).toEqual([mergedObjectId, primaryObjectId]);
      expect(output.Items![0].objectId).toBe(newObjectId);
      expect(output.Items![0].newObjectId).toBe(newObjectId);
    });
    await retry(async () => {
      const entityId = mergedCompany.properties.company_uuid;
      console.log('query entity:', entityId);
      const dbItem = await queryEntity(entityId);
      console.log('db item:', dbItem);
      expect(dbItem.Items!.length).toBe(1);
      expect(dbItem.Items![0].hubspotCompanyId).toEqual(`${event[0].objectId}`);
    });
  });

  it('should be able to handle company merge webhook event from merged object to primary', async () => {
    const primaryCompany = await createCompany();
    const primaryObjectId = Number(primaryCompany.id);
    const mergedObjectId = getRandomHsObjectId();
    const newObjectId = getRandomHsObjectId();
    const event = [
      {
        eventId: uuidv4(),
        subscriptionId: uuidv4(),
        portalId: uuidv4(),
        occurredAt: new Date().getTime(),
        subscriptionType: 'company.merge',
        attemptNumber: 0,
        objectId: newObjectId,
        newObjectId,
        changeSource: 'CRM_UI',
        primaryObjectId,
        mergedObjectIds: [mergedObjectId, primaryObjectId],
      },
    ];

    await retry(async () => {
      const entityId = primaryCompany.properties.company_uuid;
      const dbItem = await queryEntity(entityId);
      console.log('db item:', dbItem);
      console.log('event:', event[0]);
      expect(dbItem.Items!.length).toBe(1);
    });

    await sendWebhookEvent(JSON.stringify(event));

    await retry(async () => {
      const output = await queryEventHook(`${event[0].objectId}`, event[0].occurredAt);
      expect(output.Items!.length).toBe(1);
      expect(output.Items![0].primaryObjectId).toBe(primaryObjectId);
      expect(output.Items![0].mergedObjectIds).toEqual([mergedObjectId, primaryObjectId]);
      expect(output.Items![0].objectId).toBe(newObjectId);
      expect(output.Items![0].newObjectId).toBe(newObjectId);
    });
    await retry(async () => {
      const entityId = primaryCompany.properties.company_uuid;
      console.log('query entity:', entityId);
      const dbItem = await queryEntity(entityId);
      console.log('db item:', dbItem);
      expect(dbItem.Items!.length).toBe(1);
      expect(dbItem.Items![0].hubspotCompanyId).toEqual(`${event[0].objectId}`);
    });
  });

  it('should be able to save entity metric events', async () => {
    const dmsS3Bucket = `${
      stage.startsWith('st') && stage !== 'staging' ? 'st' : 'dev'
    }-dms-engines-${region}-metrics-events`;
    const entityId = company.properties.company_uuid;
    const metrics = [
      {
        entityId,
        metricsId: 'gpv_lifetime',
        metricsValue: '1',
        uri: 'crms.HubspotRuleGpvLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_90d',
        metricsValue: '2',
        uri: 'crms.HubspotRuleGpv90DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_7d',
        metricsValue: '3',
        uri: 'crms.HubspotRuleGpv7DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'average_transaction_size_lifetime',
        metricsValue: '4',
        uri: 'crms.HubspotRuleAtsLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'unique_pan_lifetime',
        metricsValue: '5',
        uri: 'crms.HubspotRuleCardPanDiversityLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'unique_pan_90d',
        metricsValue: '6',
        uri: 'crms.HubspotRuleCardPanDiversity90DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'unique_pan_7d',
        metricsValue: '7',
        uri: 'crms.HubspotRuleCardPanDiversity7DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'card_present_ratio_lifetime',
        metricsValue: '8',
        uri: 'crms.HubspotRuleCardPresentRatioBatch.Updated',
      },
      {
        entityId,
        metricsId: 'decline_ratio_lifetime',
        metricsValue: '9',
        uri: 'crms.HubspotRuleDeclineLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'decline_ratio_7d',
        metricsValue: '10',
        uri: 'crms.HubspotRuleDecline7DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'decline_ratio_90d',
        metricsValue: '11',
        uri: 'crms.HubspotRuleDecline90DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'refunds_lifetime',
        metricsValue: '12',
        uri: 'crms.HubspotRuleRefundLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'refund_7d',
        metricsValue: '13',
        uri: 'crms.HubspotRuleRefund7DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'refund_90d',
        metricsValue: '14',
        uri: 'crms.HubspotRuleRefund90DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'average_refund_amount_lifetime',
        metricsValue: '15',
        uri: 'crms.HubspotRuleRefundAverageLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'deposit_amount_lifetime',
        metricsValue: '16',
        uri: 'crms.HubspotRuleDepositLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'deposit_amount_7d',
        metricsValue: '17',
        uri: 'crms.HubspotRuleDeposit7DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'deposit_amount_90d',
        metricsValue: '18',
        uri: 'crms.HubspotRuleDeposit90DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'current_deposit_withheld_amount',
        metricsValue: '19',
        uri: 'crms.HubspotRuleDepositCurrentBatch.Updated',
      },
      {
        entityId,
        metricsId: 'days_since_last_transactions',
        metricsValue: '20',
        uri: 'crms.HubspotRuleDaysSinceLastTxnBatch.Updated',
      },
      {
        entityId,
        metricsId: 'transaction_count_lifetime',
        metricsValue: '21',
        uri: 'crms.HubspotRuleTxnLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'transaction_count_90d',
        metricsValue: '22',
        uri: 'crms.HubspotRuleTxn90DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'transaction_count_7d',
        metricsValue: '23',
        uri: 'crms.HubspotRuleTxn7DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'number_of_invoice_created',
        metricsValue: '24',
        rawPayload: {
          LAST_INVOICE_CREATED_DATE: '2023-02-09',
        },
        uri: 'crms.HubspotRuleInvoiceCreatedBatch.Updated',
      },
      {
        entityId,
        metricsId: 'number_of_invoice_sent',
        metricsValue: '25',
        rawPayload: {
          LAST_INVOICE_SENT_DATE: 'NULL',
        },
        uri: 'crms.HubspotRuleInvoiceSentBatch.Updated',
      },
      {
        entityId,
        metricsId: 'draft_invoices_7d',
        metricsValue: '26',
        uri: 'crms.HubspotRuleInvoiceSentBatch.Updated',
      },
      {
        entityId,
        metricsId: 'number_of_invoices_drafted',
        metricsValue: '27',
        rawPayload: {
          LAST_INVOICE_DRAFT_DATE: '2023-02-09',
        },
        uri: 'crms.HubspotRuleInvoiceSentBatch.Updated',
      },
      {
        entityId,
        metricsId: 'sent_invoices_7d',
        metricsValue: '28',
        uri: 'crms.HubspotRuleInvoiceSentBatch.Updated',
      },
      {
        entityId,
        metricsId: 'is_pos_connected',
        metricsValue: 'No',
        uri: 'crms.HubspotRulePosConnectedBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_7d_cpoc_ios',
        metricsValue: '29',
        uri: 'crms.HubspotRuleGpv7DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_7d_cpoc_android',
        metricsValue: '30',
        uri: 'crms.HubspotRuleGpv7DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_90d_cpoc_ios',
        metricsValue: '31',
        uri: 'crms.HubspotRuleGpv90DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_90d_cpoc_android',
        metricsValue: '32',
        uri: 'crms.HubspotRuleGpv90DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_lifetime_cpoc_ios',
        metricsValue: '33',
        uri: 'crms.HubspotRuleGpvLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_lifetime_cpoc_android',
        metricsValue: '34',
        uri: 'crms.HubspotRuleGpvLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_90d_potentially_fraudulent',
        metricsValue: '35',
        uri: 'crms.HubspotRuleGpv90DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_90d_cpoc',
        metricsValue: '36',
        uri: 'crms.HubspotRuleGpv90DayBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_lifetime_cpoc',
        metricsValue: '37',
        uri: 'crms.HubspotRuleGpvLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_lifetime_cnp',
        metricsValue: '38',
        uri: 'crms.HubspotRuleGpvLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_lifetime_cp',
        metricsValue: '39',
        uri: 'crms.HubspotRuleGpvLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_lifetime_cnp_settled',
        metricsValue: '40',
        uri: 'crms.HubspotRuleGpvLifeTimeBatch.Updated',
      },
      {
        entityId,
        metricsId: 'gpv_lifetime_potentially_fraudulent',
        metricsValue: '41',
        uri: 'crms.HubspotRuleGpvLifeTimeBatch.Updated',
      },
    ];
    const now = new Date();
    const domainEventPayloads: any[] = [];
    let proms: any = metrics.map((m) => {
      const key = `inbound/system-test-${m.metricsId}_${now.toISOString()}.json`;
      domainEventPayloads.push({
        uri: m.uri,
        content: {
          provider: 's3',
          link: `${dmsS3Bucket}/${key}`,
        },
      });
      console.log('put to s3', key, m);
      return s3.putObject({
        Key: key,
        Bucket: dmsS3Bucket,
        Body: JSON.stringify([{ ...m, uri: undefined }]),
        Expires: new Date(now.setHours(now.getHours() + 1)),
      });
    });
    await Promise.all(proms);
    proms = domainEventPayloads.map((e) => {
      const sqsMessage = {
        'detail-type': e.uri,
        detail: e,
      };
      return sendHighPriorityQueueSqsMessage(uuidv4(), sqsMessage);
    });
    await Promise.all(proms);
    await retry(async () => {
      const newCompany = (await getCompany(company.id)).data;
      console.log('load from hubspot for update company:', JSON.stringify(newCompany));
      metrics.forEach((m) => {
        const hsProperty = HubspotMetricsMapping[m.metricsId as keyof typeof HubspotMetricsMapping];
        expect(newCompany.properties[hsProperty]).toBe(m.metricsValue);
      });
    }, 180);
  });

  it('should be able to create date for invoice metrics', async () => {
    const newCompany = (await getCompany(company.id)).data;
    expect(newCompany.properties.last_invoice_created_date).toBe('2023-02-09');
    expect(newCompany.properties.last_invoice_sent_date).toBe(null);
    expect(newCompany.properties.last_draft_invoice_created_date).toBe('2023-02-09');
  });

  it('should be able to handle company stand in rules update event', async () => {
    const event: any = new EntityStandInRulesUpdatedEventDto({
      entityUuid: company.properties.company_uuid,
      standInRules: [
        {
          operation: StandInOperation.ABOVE,
          field: StandInField.OFFLINE_COUNT,
          value: uuidv4(),
        },
      ],
    });
    const sqsMessage = {
      'detail-type': 'crms.Entity.StandInRulesUpdated',
      detail: event,
    };
    console.log(`Sending the company update with standInRules`, JSON.stringify(sqsMessage));
    await sendHighPriorityQueueSqsMessage(event.entityUuid, sqsMessage);
    await retry(async () => {
      company = (await getCompany(company.id)).data;
      console.log('load from hubspot for update company:', JSON.stringify(company));
      const dbItem = await queryEntity(event.entityUuid);
      console.log('db item:', dbItem);
      expect(dbItem.Items!.length).toBe(1);
      expect(dbItem.Items![0].standInRules).toEqual(event.standInRules);
    });
  });

  testIf(
    stage === 'dev',
    'should be able to validate onboarding when calling canFinaliseEntityOnboarding',
    async () => {
      const entityUuid = uuidv4();
      await createCompany(entityUuid);

      const contact: CustomerCreatedEventDto = {
        ...createContactEvent(),
        entityUuid,
        registeringIndividual: true,
      };
      await createContact(contact);

      await retry(async () => {
        const result = await invokeSyncLambda(
          getEngineStackName('entity-validateFinaliseEntityOnboarding'),
          {
            args: { entityUuid },
          },
          true,
        );

        expect(result).toEqual({
          missingIndividualName: false,
          missingDob: true,
          missingIndividualAddress: false,
          missingPhone: false,
          missingEmail: false,
          missingEntityName: false,
          missingEntityAddress: false,
          invalidOnboardingStatus: true, // entity has no onboarding status
          onboardingStatus: undefined,
        });
      });
    },
  );

  describe('referral', () => {
    const referredEntityUuid = uuidv4();

    it('should create referring entity', async () => {
      const entityCreateEvent = entityCreatedDto();
      entityCreateEvent.entityUuid = referredEntityUuid;
      const sqsMessage = {
        'detail-type': 'crms.Entity.Created',
        detail: entityCreateEvent,
      };
      await sendHighPriorityQueueSqsMessage(entityCreateEvent.entityUuid, sqsMessage);

      await retry(
        async () => {
          const companies = (await searchObjectByDomainId(
            ObjectType.COMPANY,
            'company_uuid',
            entityCreateEvent.entityUuid,
          ))!.data;
          console.log('load from hubspot for new entity update:', JSON.stringify(companies));
          expect(companies.results.length).toBe(1);
        },
        5,
        1000,
        true,
      );
    });

    it('should be able to materialise referred by', async () => {
      const event: any = new EntityReferredEventDto({
        entityUuid: company.properties.company_uuid,
        referred: referredEntityUuid,
        created: uuidv4(),
      });
      const sqsMessage = {
        'detail-type': 'crms.Entity.Referred',
        detail: event,
      };

      console.log(`Sending the company referral`, JSON.stringify(sqsMessage));
      await sendHighPriorityQueueSqsMessage(event.entityUuid, sqsMessage);
      await sleep(500);
      await retry(
        async () => {
          const dbItem = await queryEntity(referredEntityUuid);
          console.log('db item:', dbItem);
          expect(dbItem.Items!.length).toBe(1);
          expect(dbItem.Items![0].id).toEqual(event.referred);
          expect(dbItem.Items![0].referredBy).toEqual(event.entityUuid);

          const companies = (await searchObjectByDomainId(ObjectType.COMPANY, 'company_uuid', referredEntityUuid))!
            .data;
          console.log('load from hubspot for referred update:', JSON.stringify(companies));
          expect(companies.results.length).toBe(1);
          company = companies.results[0];
          expect(company.properties.company_uuid).toBe(event.referred);
          expect(company.properties.referred_by).toBe(event.entityUuid);
        },
        5,
        1000,
        true,
      );
    });
  });
});
