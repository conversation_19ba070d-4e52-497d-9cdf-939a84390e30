import { createDomicileValue } from '@npco/bff-systemtest-utils/dist/domicile/utils';
import { DynamodbClient } from '@npco/bff-systemtest-utils/dist/dynamodb/dynamodbClient';
import type { CustomerCreatedEventDto } from '@npco/component-dto-customer';

import { getContact } from '../hubspot/hubspotUtil';
import { retry } from '../retry';
import { region, sendHighPriorityQueueSqsMessage } from '../utils';

import { queryContact } from './queryContact';

export const createContact = async (event: CustomerCreatedEventDto, db = new DynamodbClient({ region })) => {
  await createDomicileValue(db, event.entityUuid, event.customerUuid);
  const sqsMessage = {
    'detail-type': 'crms.Customer.Created',
    detail: event,
  };
  console.log('create contact:', event);
  await sendHighPriorityQueueSqsMessage(event.customerUuid, sqsMessage);

  let hubspotContactId: string | null = null;

  await retry(async () => {
    const customer = await queryContact(event.customerUuid);
    expect(customer.Items?.length).toBe(1);
    expect(customer.Items?.[0].id).toBe(event.customerUuid);
    expect(customer.Items?.[0].hubspotContactId).toEqual(expect.any(String));
    hubspotContactId = customer.Items?.[0].hubspotContactId;
    expect(hubspotContactId).not.toBeNull();
  }, 30);

  const result = await retry(async () => {
    const contacts = (await getContact(hubspotContactId!)).data;
    expect(contacts.id).toEqual(hubspotContactId);
    return contacts;
  }, 3);

  return result;
};
