import { DynamodbClient } from '@npco/bff-systemtest-utils';
import { DbRecordType } from '@npco/component-dto-core';

import { region } from '../globalVariables';
import { getDynamoDbTableName } from '../hubspot/getDynamoDbTable';

export const queryContact = async (customerUuid: string) => {
  const db = new DynamodbClient({ region });
  return db.query({
    TableName: getDynamoDbTableName(),
    KeyConditionExpression: 'id = :customerUuid AND #type = :type',
    ExpressionAttributeNames: {
      '#type': 'type',
    },
    ExpressionAttributeValues: {
      ':customerUuid': customerUuid,
      ':type': DbRecordType.CUSTOMER,
    },
  });
};

export const queryCustomerEntity = async (id: string, entityUuid?: string, consistentRead = false) => {
  if (!entityUuid) {
    throw new Error('queryCustomerEntity: entityUuid is required');
  }
  const db = new DynamodbClient({ region });
  return db.query({
    TableName: getDynamoDbTableName(),
    KeyConditionExpression: 'id = :id AND #type = :type',
    ExpressionAttributeNames: {
      '#type': 'type',
    },
    ExpressionAttributeValues: {
      ':id': id,
      ':type': `${DbRecordType.CUSTOMER_ENTITY}${entityUuid}`,
    },
    ConsistentRead: consistentRead,
  });
};
