import { createDomicileValue } from '@npco/bff-systemtest-utils/dist/domicile/utils';
import { DynamodbClient } from '@npco/bff-systemtest-utils/dist/dynamodb/dynamodbClient';
import { EntityType } from '@npco/component-dto-core';
import { EntityCreatedEventDto, RiskRating } from '@npco/component-dto-entity';

import { v4 as uuidv4 } from 'uuid';

import { ObjectType } from '../hubspot/hubspotProperties';
import { searchObjectByDomainId } from '../hubspot/hubspotUtil';
import { retry } from '../retry';
import { region, sendHighPriorityQueueSqsMessage } from '../utils';

export const createCompany = async (entityUuid = uuidv4(), db = new DynamodbClient({ region })) => {
  await createDomicileValue(db, entityUuid);
  const entityEvent = new EntityCreatedEventDto({
    entityUuid,
    name: uuidv4(),
    type: EntityType.COMPANY,
    manualEntry: true,
    sourceIp: '',
    abn: uuidv4(),
    acn: uuidv4(),
    registeredAddress: {
      state: 'VIC',
      street1: 'street1',
      street2: 'street2',
      suburb: 'Melbourne',
      postcode: '3000',
      country: 'au',
    },
    businessAddress: {
      state: 'VIC',
      street1: 'street1',
      street2: 'street2',
      suburb: 'Melbourne',
      postcode: '3000',
      country: 'au',
    },
    facebook: 'facebook',
    twitter: 'twitter',
    website: 'website',
    estimatedAnnualRevenue: 1,
    riskRating: RiskRating.LOW,
    countryOfOrigin: 'AUS',
  });
  const sqsMessage = {
    'detail-type': 'crms.Entity.Created',
    detail: entityEvent,
  };

  await sendHighPriorityQueueSqsMessage(entityEvent.entityUuid, sqsMessage);

  const result = await retry(
    async () => {
      const companies = (await searchObjectByDomainId(ObjectType.COMPANY, 'company_uuid', entityEvent.entityUuid)).data;
      console.log('load from hubspot for company,', entityEvent.entityUuid, companies);
      expect(companies.results.length).toBe(1);
      return companies.results[0];
    },
    2,
    10000,
  );

  return result;
};
