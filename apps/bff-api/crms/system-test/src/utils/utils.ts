import { DynamodbClient, getSsmValue } from '@npco/bff-systemtest-utils';

import { SQS } from '@aws-sdk/client-sqs';
import axios from 'axios';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import 'isomorphic-fetch';

import { getApiEndpoint } from './app-sync/getApiEndpoint';
import { stage } from './globalVariables';
import { getDynamoDbTableName } from './hubspot/getDynamoDbTable';
import { getHubspotSecret } from './hubspot/hubspotUtil';
import { documentScanningSqsEndpoint, getDocumentScanningSqs } from './sqs/getDocumentScanningSqs';
import { getRiskEngineSqs, riskEngineSqsEndpoint } from './sqs/getRiskEngineSqs';
import { getCqrsSqsStackName } from './stacks/getCqrsSqsStackName';
import { getProjectionSqsStackName, getProjectionStackName } from './stacks/getProjectionSqsStackName';
import { getStackOutputs } from './stacks/getStackOutputs';

(global as any).WebSocket = require('ws');

export const region = process.env.AWS_REGION || 'ap-southeast-2';

const db = new DynamodbClient({ region });

export const sendRiskEngineSqsMessage = async (message: string) => {
  await (
    await getRiskEngineSqs()
  ).sendMessage({
    MessageBody: message,
    QueueUrl: riskEngineSqsEndpoint,
  });
};

export const sendDocumentScanningSqsMessage = async (message: string) => {
  const response = await (
    await getDocumentScanningSqs()
  ).sendMessage({
    MessageBody: message,
    QueueUrl: documentScanningSqsEndpoint,
  });
  console.log('sendDocumentScanningSqsMessage response is ', response);
};

let hubspotSecret: any;
let apiEndpoint: any;
export const webhookConfig = async () => {
  if (!hubspotSecret) {
    hubspotSecret = await getHubspotSecret();
  }
  if (!apiEndpoint) {
    apiEndpoint = await getApiEndpoint();
  }
};

export const sendWebhookEvent = async (data: any, urlpath = '') => {
  await webhookConfig();
  const url = `${apiEndpoint}/v1/hubspot/queue/webhook${urlpath}`;
  const sourceString = `${hubspotSecret}${data}`;
  const hash = crypto.createHash('sha256').update(sourceString).digest('hex');
  // console.log('send to webhook endpoint:', url, data);
  await axios.post(url, data, {
    headers: {
      'X-HubSpot-Signature': hash,
      'X-HubSpot-Signature-Version': 'v1',
      'Content-Type': 'application/json',
    },
  });
};

let hubspotLowPrioritySecret: any;

export const getHubspotLowPrioritySecret = async () => {
  if (!hubspotLowPrioritySecret) {
    const secureStringToken = await getSsmValue(`/dev-crms-engine/HUBSPOT_LOW_PRIORITY_WEBHOOK_APP_CLIENT_SECRET`);
    console.log('HUBSPOT_LOW_PRIORITY_WEBHOOK_APP_CLIENT_SECRET:', secureStringToken);
    hubspotLowPrioritySecret = secureStringToken;
  }
  return hubspotLowPrioritySecret;
};

export const sendWebhookLowPriorityEvent = async (data: any) => {
  await getHubspotLowPrioritySecret();
  await webhookConfig();
  const url = `${apiEndpoint}/v1/hubspot/queue/webhook/low-priority`;
  const sourceString = `${hubspotLowPrioritySecret}POST${url.replace(`/${stage}`, '')}${data}`;
  const hash = crypto.createHash('sha256').update(sourceString).digest('hex');
  await axios
    .post(url, data, {
      headers: {
        'X-HubSpot-Signature': hash,
        'X-HubSpot-Signature-Version': 'v2',
        'Content-Type': 'application/json',
      },
    })
    .catch((err) => console.error(err));
};

export const queryEventHook = async (objectId: string, accurredAt: number) => {
  return db.query({
    TableName: getDynamoDbTableName(),
    KeyConditionExpression: 'id = :objectId AND #type = :type',
    ExpressionAttributeNames: {
      '#type': 'type',
    },
    ExpressionAttributeValues: {
      ':objectId': objectId,
      ':type': `hubspot.webhook.${accurredAt}`,
    },
  });
};

export const dbPut = async (item: any) => {
  return db
    .put({
      Item: item,
      TableName: getDynamoDbTableName(),
    })
    .catch((err) => console.error(err));
};

let cmsProjectionSqsEndpoint: string;
let CmsProjectionSqs: SQS | null = null;

let cqrsProjectionSqsEndpoint: string;
let CqrsProjectionSqs: SQS | null = null;

export const getCqrsProjectionSqs = async () => {
  // const projectionSqs = await getStackOutputs(getProjectionSqsStackName());
  // console.log('projectionSqs is -> ', projectionSqs);
  if (!CqrsProjectionSqs) {
    const stackOutputs = await getStackOutputs(getCqrsSqsStackName());
    cqrsProjectionSqsEndpoint = stackOutputs.find((output: any) => output.OutputKey === 'QueueURL').OutputValue;
    // console.log('sqs endpoint:', cqrsProjectionSqsEndpoint);
    CqrsProjectionSqs = new SQS({ endpoint: cqrsProjectionSqsEndpoint });
  }
  return CqrsProjectionSqs;
};

export const sendCqrsProjectionSqsMessage = async (message: string) => {
  await (
    await getCqrsProjectionSqs()
  ).sendMessage({
    MessageBody: message,
    QueueUrl: cqrsProjectionSqsEndpoint,
  });
};

export const getCmsProjectionSqs = async () => {
  // const projectionSqs = await getStackOutputs(getProjectionSqsStackName());
  // console.log('projectionSqs is -> ', projectionSqs);
  if (!CmsProjectionSqs) {
    const stackOutputs = await getStackOutputs(getProjectionSqsStackName());
    cmsProjectionSqsEndpoint = stackOutputs.find((output: any) => output.OutputKey === 'CmsQueueURL').OutputValue;
    // console.log('sqs endpoint:', cmsProjectionSqsEndpoint);
    CmsProjectionSqs = new SQS({ endpoint: cmsProjectionSqsEndpoint });
  }
  return CmsProjectionSqs;
};

export const getStage = () => stage;

let crmsHighPriorityQueueSqsEndpoint: string;
let crmsHighPriorityQueueSqs: SQS | null = null;

export const getCrmsHighPriorityQueueProjectionSqs = async () => {
  if (!crmsHighPriorityQueueSqs) {
    const stackOutputs = await getStackOutputs(getProjectionStackName());
    console.log('stackOutputs is -> ', stackOutputs);
    crmsHighPriorityQueueSqsEndpoint = stackOutputs.find((output: any) => output.OutputKey === 'QueueUrl').OutputValue;
    crmsHighPriorityQueueSqs = new SQS({ endpoint: crmsHighPriorityQueueSqsEndpoint, region });
  }
  return crmsHighPriorityQueueSqs;
};

export const sendHighPriorityQueueSqsMessage = async (aggregateId: string, message: any) => {
  await (
    await getCrmsHighPriorityQueueProjectionSqs()
  ).sendMessage({
    MessageBody: JSON.stringify(message),
    QueueUrl: crmsHighPriorityQueueSqsEndpoint,
    MessageGroupId: aggregateId,
    MessageDeduplicationId: uuidv4(),
  });
};
