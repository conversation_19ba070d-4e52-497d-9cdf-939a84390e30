import { DynamodbClient, describeIf, retry, sleep } from '@npco/bff-systemtest-utils';
import { CustomerRole, DbRecordType } from '@npco/component-dto-core';
import { CustomerEntityRejectedEventDto } from '@npco/component-dto-customer';
import { bootstrapHubspotAppOauthToken } from 'crms-engine-system-test/src/utils/hubspot/bootstrapHubspotOauthToken';
import { getAssociations, searchObjectByDomainIdFilters } from 'crms-engine-system-test/src/utils/hubspot/hubspotUtil';
import { createCompany } from 'crms-engine-system-test/src/utils/simplified-services/createCompany';
import { createContact } from 'crms-engine-system-test/src/utils/simplified-services/createContact';
import { createContactEvent } from 'crms-engine-system-test/src/utils/simplified-services/createContactEvent';

import { v4 as uuidv4 } from 'uuid';

import { getDynamoDbTableName } from './utils/hubspot/getDynamoDbTable';
import { ObjectType } from './utils/hubspot/hubspotProperties';
import { HubspotPublicApp } from './utils/hubspot/hubspotTypes';
import { queryContact } from './utils/simplified-services/queryContact';
import { region, sendHighPriorityQueueSqsMessage } from './utils/utils';

beforeAll(async () => {
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.DEFAULT_EXTENSION);
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.CUSTOM_OBJECT);
});

describeIf(process.env.MULTI_ENTITY_ENABLED === 'true', 'Contact-Companies Custom Object', () => {
  const entityUuid = uuidv4();
  const customerUuid = uuidv4();
  const db = new DynamodbClient({ region });

  let contact: any;

  const queryCustomerEntity = async (id: string, entityId: string) => {
    console.log(
      `query customer entity id ${id} of type ${DbRecordType.CUSTOMER_ENTITY} in table ${getDynamoDbTableName()}`,
    );
    return db.query({
      TableName: getDynamoDbTableName(),
      KeyConditionExpression: 'id = :id AND #type = :type',
      ExpressionAttributeNames: {
        '#type': 'type',
      },
      ExpressionAttributeValues: {
        ':id': id,
        ':type': `${DbRecordType.CUSTOMER_ENTITY}${entityId}`,
      },
    });
  };

  beforeAll(async () => {
    await createCompany(entityUuid);

    contact = await createContact({
      ...createContactEvent(),
      entityUuid,
      customerUuid,
      role: CustomerRole.MANAGER,
      primaryAccountHolder: false,
      firstname: `system-test-${customerUuid}`,
    });

    await sleep(10000);
  });

  describe('CustomerEntity.Unlinked', () => {
    const customerEntityRejectedEventDto = new CustomerEntityRejectedEventDto({
      entityUuid,
      customerUuid,
    });
    beforeAll(async () => {
      const sqsMessage = {
        'detail-type': 'crms.CustomerEntity.Rejected',
        detail: customerEntityRejectedEventDto,
      };
      await sendHighPriorityQueueSqsMessage(customerUuid, sqsMessage);

      await sleep(5000);
    });

    it('should removed hubspot custom object', async () => {
      await retry(
        async () => {
          const hubspotObject = (
            await searchObjectByDomainIdFilters(ObjectType.CONTACT_COMPANY, [
              {
                propertyName: 'contact_uuid',
                value: customerUuid,
                operator: 'EQ',
              },
              {
                propertyName: 'company_uuid',
                value: entityUuid,
                operator: 'EQ',
              },
            ])
          ).data;

          expect(hubspotObject.results.length).toBe(0);
        },
        2,
        10000,
      );
    });

    it('should be able to remove materialised custom object item in materialised view', async () => {
      await retry(
        async () => {
          const dbItem = await queryCustomerEntity(customerUuid, entityUuid);
          console.log('customer entity db item is -> ', JSON.stringify(dbItem));
          expect(dbItem.Items?.length).toBe(0);
        },
        2,
        10000,
      );
    });

    it('should be able to remove contact-company association', async () => {
      await retry(async () => {
        const associations = (await getAssociations(ObjectType.CONTACT, contact.id, ObjectType.COMPANY)).data;

        expect(associations.results.length).toBe(0);
      });

      await retry(async () => {
        const dbItem = await queryContact(customerUuid);
        expect(dbItem.Items?.[0].hubspotAssociations.companiesAssociated.length).toBe(0);
      });
    });
  });
});
