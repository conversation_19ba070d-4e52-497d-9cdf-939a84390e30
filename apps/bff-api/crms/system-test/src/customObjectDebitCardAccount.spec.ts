import { DynamodbClient, retry } from '@npco/bff-systemtest-utils';
import { DbRecordType, ISO4217 } from '@npco/component-dto-core';
import {
  DebitCardAccountCreatedEventDto,
  DebitCardAccountStatus,
  DebitCardAccountType,
  SavingsAccountCreatedEventDto,
  SavingsAccountProductType,
} from '@npco/component-dto-issuing-account';
import {
  DebitCardAccountCardCreatedEventDto,
  DebitCardColour,
  DebitCardFormat,
  DebitCardStatus,
  DebitCardType,
} from '@npco/component-dto-issuing-card';
import {
  DebitCardAccountTransactionCreatedEventDto,
  DebitCardTransactionStatusV2,
  DebitCardTransactionTypeV2,
} from '@npco/component-dto-issuing-transaction';
import { bootstrapHubspotAppOauthToken } from 'crms-engine-system-test/src/utils/hubspot/bootstrapHubspotOauthToken';
import { getDynamoDbTableName } from 'crms-engine-system-test/src/utils/hubspot/getDynamoDbTable';
import { getAssociations, getDebitCardAccount } from 'crms-engine-system-test/src/utils/hubspot/hubspotUtil';
import { createCompany } from 'crms-engine-system-test/src/utils/simplified-services/createCompany';
import { sendHighPriorityQueueSqsMessage } from 'crms-engine-system-test/src/utils/utils';

import { v4 as uuidv4 } from 'uuid';

import { region } from './utils/globalVariables';
import { ObjectType } from './utils/hubspot/hubspotProperties';
import { HubspotPublicApp } from './utils/hubspot/hubspotTypes';

beforeAll(async () => {
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.DEFAULT_EXTENSION);
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.CUSTOM_OBJECT);
});

describe('debit card accounts test suite', () => {
  let dcaCreatedEvent: any;

  let hubspotObjectId: string;

  let debitCardAccountName: string;

  const debitCardAccountUuid = uuidv4();

  const createDebitCardAccountDto = (entityUuid: string) =>
    new DebitCardAccountCreatedEventDto({
      id: debitCardAccountUuid,
      debitCardAccountUuid,
      entityUuid,
      name: uuidv4(),
      status: DebitCardAccountStatus.ACTIVE,
      type: DebitCardAccountType.ZLR_DEBIT,
      source: 'banking',
      balance: {
        value: '100',
        currency: ISO4217.AUD,
      },
      accountDetails: {
        bsb: '000000',
        account: '*********0',
      },
      feature: {
        transfer: true,
        cp: true,
        cnp: true,
        atm: true,
        de: true,
        npp: true,
        bpay: true,
      },
      updatedTime: Date.now(),
      timestamp: `${Date.now()}`,
      icon: {},
    });

  const createDebitCardDto = (dcaUuid: string, entityUuid = uuidv4()) =>
    new DebitCardAccountCardCreatedEventDto({
      id: uuidv4(),
      debitCardAccountUuid: dcaUuid,
      entityUuid,
      name: uuidv4(),
      source: 'banking',
      status: DebitCardStatus.ACTIVE,
      debitCardType: DebitCardType.MASTERCARD_DEBIT,
      type: DebitCardType.MASTERCARD_DEBIT,
      format: DebitCardFormat.VIRTUAL,
      accessibleProfile: true,
      lastUseTime: Date.now(),
      colour: DebitCardColour.BLACK,
      maskedPan: '111____1234',
      updatedTime: Date.now(),
      timestamp: `${Date.now()}`,
    });

  const createDebitCardTransactionDto = (
    dcaUuid: string,
    debitCardId: string,
    type: DebitCardTransactionTypeV2,
    entityUuid = uuidv4(),
  ) =>
    new DebitCardAccountTransactionCreatedEventDto({
      id: uuidv4(),
      entityUuid,
      debitCardAccountUuid: dcaUuid,
      debitCardId,
      source: 'banking',
      status: DebitCardTransactionStatusV2.APPROVED,
      type,
      payerDetails: {
        accountDetails: {
          bsb: '000000',
          account: '*********',
        },
      },
      description: 'testDescription',
      amount: {
        currency: ISO4217.AUD,
        value: '100',
      },
      timestamp: `${Date.now()}`,
      updatedTime: Date.now(),
    });

  const createSavingsAccountDto = (
    accountUuid?: string,
    entityUuid?: string,
    update?: {
      savingsAccountType?: SavingsAccountProductType;
      effectiveInterestRate?: number;
      bonusInterestRate?: number;
      baseInterestRate?: number;
      bonusRateEndsAt?: string;
    },
  ) => {
    const base = new SavingsAccountCreatedEventDto({
      id: accountUuid ?? uuidv4(),
      debitCardAccountUuid: accountUuid ?? uuidv4(),
      accountType: DebitCardAccountType.SAVINGS,
      entityUuid: entityUuid ?? uuidv4(),
      displayName: uuidv4(),
      status: DebitCardAccountStatus.ACTIVE,
      balance: {
        value: '100',
        currency: ISO4217.AUD,
      },
      savingsAccountType: SavingsAccountProductType.SZSA001,
      effectiveInterestRate: 1.9,
      baseInterestRate: 1.35,
      tfnProvided: true,
      feature: {
        transfer: true,
        cp: true,
        cnp: true,
        atm: true,
        de: true,
        npp: true,
        bpay: true,
      },
      updatedTime: Date.now(),
      createdAt: Date.now(),
      timestamp: `${Date.now()}`,
    });
    return {
      ...base,
      ...update,
    };
  };

  const queryAccount = async (id: string, type = DbRecordType.DEBIT_CARD_ACCOUNT) => {
    const db = new DynamodbClient({ region });
    console.log(`query account id ${id} of type ${type} in table ${getDynamoDbTableName()}`);
    return db.query({
      TableName: getDynamoDbTableName(),
      KeyConditionExpression: 'id = :id AND begins_with(#type, :type)',
      ExpressionAttributeNames: {
        '#type': 'type',
      },
      ExpressionAttributeValues: {
        ':id': id,
        ':type': type,
      },
    });
  };

  it('should be able to create debit card account', async () => {
    const entityUuid = uuidv4();

    await createCompany(entityUuid);

    const event = createDebitCardAccountDto(entityUuid);
    dcaCreatedEvent = event;
    debitCardAccountName = event.name;

    await sendHighPriorityQueueSqsMessage(event.id, {
      'detail-type': 'crms.ProjectionDebitCardAccount.create',
      detail: event,
    });

    await retry(async () => {
      const dbItem = await queryAccount(event.id);
      expect(dbItem.Items!.length).toBe(1);
      expect(dbItem.Items![0].name).toBe(event.name);
      expect(dbItem.Items![0].sortKey).toMatch(/^issuing.account.dca./);
      expect(dbItem.Items![0].id).toEqual(event.id);
      expect(dbItem.Items![0].hubspotObjectId).toEqual(expect.any(String));
      hubspotObjectId = dbItem.Items![0].hubspotObjectId;
    });

    await retry(
      async () => {
        const hubspotDebitCardAccount = (await getDebitCardAccount(hubspotObjectId)).data;
        hubspotObjectId = hubspotDebitCardAccount.id;
        expect(hubspotDebitCardAccount.properties.name).toBe(event.name);
        expect(hubspotDebitCardAccount.properties.debit_card_account_uuid).toBe(event.debitCardAccountUuid);
        expect(hubspotDebitCardAccount.properties).toEqual({
          debit_card_account_uuid: event.id,
          name: event.name,
          account_type: 'ZLR_DEBIT',
          balance: JSON.stringify(event.balance),
          status: event.status,
          bsb: event.accountDetails.bsb,
          masked_account_number: event.accountDetails.account.slice(-4),
          number_of_cards: null,
          savings_account_effective_interest_rate: null,
          savings_account_product_type: null,
          latest_de_in_amount: null,
          latest_de_in_sending_account: null,
          latest_de_in_description: null,
          latest_transfer_in_timestamp: null,
          updated_time: `${event.updatedTime}`,
          hs_createdate: expect.any(String),
          hs_lastmodifieddate: expect.any(String),
          hs_object_id: expect.any(String),
        });
      },
      2,
      10000,
    );

    await retry(
      async () => {
        const associatedCompanies = (
          await getAssociations(ObjectType.DEBIT_CARD_ACCOUNT, hubspotObjectId, ObjectType.COMPANY)
        ).data;
        console.log('associated companies ->', JSON.stringify(associatedCompanies));
        expect(associatedCompanies.results[0].type).toBe('debit_card_account_to_company');
      },
      2,
      10000,
    );
  });

  it('should be able to handle HS duplicate error and update debit card account on HS', async () => {
    const db = new DynamodbClient({ region });
    console.log(`remove hubspot fields from account id ${debitCardAccountUuid} in table ${getDynamoDbTableName()}`);
    const result = await db.update({
      TableName: getDynamoDbTableName(),
      Key: { id: debitCardAccountUuid, type: `${DbRecordType.DEBIT_CARD_ACCOUNT}#10#${debitCardAccountName}` },
      UpdateExpression: 'REMOVE #hubspotObjectId, #hubspotProperties, #hubspotAssociations',
      ExpressionAttributeNames: {
        '#hubspotObjectId': 'hubspotObjectId',
        '#hubspotProperties': 'hubspotProperties',
        '#hubspotAssociations': 'hubspotAssociations',
      },
    });
    console.log('result', result);
    const dbItem = await queryAccount(debitCardAccountUuid);
    expect(dbItem.Items![0].id).toBe(debitCardAccountUuid);
    expect(dbItem.Items![0].hubspotObjectId).toBeUndefined();
    expect(dbItem.Items![0].hubspotProperties).toBeUndefined();
    expect(dbItem.Items![0].hubspotAttributes).toBeUndefined();

    const currentDebitCardAccount = dbItem.Items![0];
    const updatedEvent = createDebitCardAccountDto(currentDebitCardAccount.entityUuid);
    updatedEvent.id = currentDebitCardAccount.id;
    updatedEvent.debitCardAccountUuid = currentDebitCardAccount.id;
    updatedEvent.status = DebitCardAccountStatus.CLOSED;
    updatedEvent.name = 'updated account name';
    dcaCreatedEvent = {
      ...dcaCreatedEvent,
      name: updatedEvent.name,
      status: updatedEvent.status,
    };

    await sendHighPriorityQueueSqsMessage(updatedEvent.id, {
      'detail-type': 'crms.ProjectionDebitCardAccount.create',
      detail: updatedEvent,
    });

    await retry(async () => {
      const updatedDbItem = await queryAccount(debitCardAccountUuid);
      expect(updatedDbItem.Items!.length).toBe(1);
      expect(updatedDbItem.Items![0].name).toBe(updatedEvent.name);
      expect(updatedDbItem.Items![0].id).toEqual(debitCardAccountUuid);
      expect(updatedDbItem.Items![0].hubspotObjectId).toEqual(expect.any(String));
      expect(updatedDbItem.Items![0].hubspotProperties).toEqual(expect.any(Array));
      expect(updatedDbItem.Items![0].hubspotProperties.length).toBeGreaterThan(0);
      expect(updatedDbItem.Items![0].hubspotAssociations).toBeDefined();
      hubspotObjectId = updatedDbItem.Items![0].hubspotObjectId;
    });

    await retry(
      async () => {
        const debitCardAccount = (await getDebitCardAccount(hubspotObjectId)).data;
        console.log('load from hubspot for updated account:', JSON.stringify(debitCardAccount));
        expect(debitCardAccount.id).toBe(hubspotObjectId);
        expect(debitCardAccount.properties.name).toBe(updatedEvent.name);
        expect(debitCardAccount.properties.debit_card_account_uuid).toBe(debitCardAccountUuid);
        expect(debitCardAccount.properties.status).toBe(updatedEvent.status);
      },
      2,
      10000,
    );
  });

  it('should emit debitCard event and trigger update to the number of cards in HS debitCardAccount object', async () => {
    const debitCardDto = createDebitCardDto(dcaCreatedEvent.id, dcaCreatedEvent.entityUuid);
    await sendHighPriorityQueueSqsMessage(debitCardDto.id, {
      'detail-type': 'crms.ProjectionDebitCardAccountCard.create',
      detail: debitCardDto,
    });

    await retry(
      async () => {
        const debitCardAccount = (await getDebitCardAccount(hubspotObjectId)).data;
        console.log('load from hubspot for updated account (by number of cards):', JSON.stringify(debitCardAccount));
        expect(debitCardAccount.properties.debit_card_account_uuid).toBe(debitCardDto.debitCardAccountUuid);
        expect(debitCardAccount.properties.name).toBe(dcaCreatedEvent.name);
        expect(debitCardAccount.properties.number_of_cards).toEqual('1');
      },
      2,
      10000,
    );
  });

  it('should emit DE_IN transaction event and trigger update to HS debitCardAccount object', async () => {
    const event = createDebitCardTransactionDto(
      debitCardAccountUuid,
      uuidv4(),
      DebitCardTransactionTypeV2.DE_IN,
      dcaCreatedEvent.entityUuid,
    );
    await sendHighPriorityQueueSqsMessage(event.id, {
      'detail-type': 'crms.ProjectionDebitCardAccountTransaction.create',
      detail: event,
    });

    await retry(
      async () => {
        const debitCardAccount = (await getDebitCardAccount(hubspotObjectId)).data;
        console.log('load from hubspot for updated account (by DE_IN txn):', JSON.stringify(debitCardAccount));
        expect(debitCardAccount.properties.debit_card_account_uuid).toBe(event.debitCardAccountUuid);
        expect(debitCardAccount.properties.name).toBe(dcaCreatedEvent.name);
        expect(debitCardAccount.properties.latest_de_in_amount).toEqual(JSON.stringify(event.amount));
        expect(debitCardAccount.properties.latest_de_in_sending_account).toEqual(JSON.stringify(event.payerDetails));
        expect(debitCardAccount.properties.latest_de_in_description).toEqual(event.description);
      },
      2,
      10000,
    );
  });

  it('should emit TRANSFER_IN transaction event and trigger update to HS debitCardAccount object', async () => {
    const event = createDebitCardTransactionDto(
      debitCardAccountUuid,
      uuidv4(),
      DebitCardTransactionTypeV2.TRANSFER_IN,
      dcaCreatedEvent.entityUuid,
    );
    await sendHighPriorityQueueSqsMessage(event.id, {
      'detail-type': 'crms.ProjectionDebitCardAccountTransaction.create',
      detail: event,
    });

    await retry(
      async () => {
        const debitCardAccount = (await getDebitCardAccount(hubspotObjectId)).data;
        console.log('load from hubspot for updated account (by TRANSFER_IN txn):', JSON.stringify(debitCardAccount));
        expect(debitCardAccount.properties.debit_card_account_uuid).toBe(event.debitCardAccountUuid);
        expect(debitCardAccount.properties.name).toBe(dcaCreatedEvent.name);
        expect(debitCardAccount.properties.latest_transfer_in_timestamp).toEqual(`${event.updatedTime}`);
      },
      2,
      10000,
    );
  });

  describe('savings account test suite', () => {
    it('should be able to create standard savings account', async () => {
      let hubspotDebitCardAccountId: string;
      const entityUuid = uuidv4();
      const accountUuid = uuidv4();

      await createCompany(entityUuid);
      const event = createSavingsAccountDto(accountUuid, entityUuid);
      await sendHighPriorityQueueSqsMessage(event.id, {
        'detail-type': 'crms.ProjectionSavingsAccount.create',
        detail: event,
      });

      await retry(async () => {
        const dbItem = await queryAccount(event.id, DbRecordType.ISSUING_SAVINGS_ACCOUNT);
        expect(dbItem.Items!.length).toBe(1);
        expect(dbItem.Items![0].name).toBe(event.displayName);
        expect(dbItem.Items![0].sortKey).toMatch(/^issuing.account.savings./);
        expect(dbItem.Items![0].savingsAccountDetails).toEqual({
          baseInterestRate: '1.35',
          bonusInterestRate: '0.55',
          effectiveInterestRate: '1.90',
          savingsAccountProductType: SavingsAccountProductType.SZSA001,
          tfnProvided: true,
        });
        expect(dbItem.Items![0].hubspotObjectId).toEqual(expect.any(String));
        hubspotObjectId = dbItem.Items![0].hubspotObjectId;
      });

      await retry(
        async () => {
          const savingsAccount = (await getDebitCardAccount(hubspotObjectId)).data;
          hubspotDebitCardAccountId = savingsAccount.id;
          expect(savingsAccount.properties.name).toBe(event.displayName);
          expect(savingsAccount.properties.debit_card_account_uuid).toBe(event.debitCardAccountUuid);
          expect(savingsAccount.properties.account_type).toEqual('SAVINGS');
          expect(savingsAccount.properties.savings_account_effective_interest_rate).toEqual('1.90');
          expect(savingsAccount.properties.savings_account_product_type).toEqual(event.savingsAccountType);
        },
        2,
        10000,
      );
      await retry(
        async () => {
          const associatedCompanies = (
            await getAssociations(ObjectType.DEBIT_CARD_ACCOUNT, hubspotDebitCardAccountId, ObjectType.COMPANY)
          ).data;
          console.log('associated companies ->', JSON.stringify(associatedCompanies));
          expect(associatedCompanies.results[0].type).toBe('debit_card_account_to_company');
        },
        2,
        10000,
      );
    });

    it('should be able to create bonus savings account', async () => {
      let hubspotDebitCardAccountId: string;
      const entityUuid = uuidv4();
      const accountUuid = uuidv4();
      await createCompany(entityUuid);
      const event = createSavingsAccountDto(accountUuid, entityUuid, {
        savingsAccountType: SavingsAccountProductType.BZSA001,
        bonusInterestRate: 1.35,
        bonusRateEndsAt: '2023-12-01',
      });
      await sendHighPriorityQueueSqsMessage(event.id, {
        'detail-type': 'crms.ProjectionSavingsAccount.create',
        detail: event,
      });

      await retry(async () => {
        const dbItem = await queryAccount(event.id, DbRecordType.ISSUING_SAVINGS_ACCOUNT);
        expect(dbItem.Items!.length).toBe(1);
        expect(dbItem.Items![0].name).toBe(event.displayName);
        expect(dbItem.Items![0].sortKey).toMatch(/^issuing.account.savings./);
        expect(dbItem.Items![0].savingsAccountDetails).toEqual({
          baseInterestRate: '1.35',
          bonusInterestRate: '1.35',
          bonusRateEndsAt: event.bonusRateEndsAt,
          effectiveInterestRate: '1.90',
          savingsAccountProductType: SavingsAccountProductType.BZSA001,
          tfnProvided: true,
        });
        expect(dbItem.Items![0].hubspotObjectId).toEqual(expect.any(String));
        hubspotObjectId = dbItem.Items![0].hubspotObjectId;
      });

      await retry(
        async () => {
          const savingsAccount = (await getDebitCardAccount(hubspotObjectId)).data;
          hubspotDebitCardAccountId = savingsAccount.id;
          expect(savingsAccount.properties.name).toBe(event.displayName);
          expect(savingsAccount.properties.debit_card_account_uuid).toBe(event.debitCardAccountUuid);
          expect(savingsAccount.properties.account_type).toEqual('SAVINGS');
          expect(savingsAccount.properties.savings_account_effective_interest_rate).toEqual('1.90');
          expect(savingsAccount.properties.savings_account_product_type).toEqual(event.savingsAccountType);
        },
        2,
        10000,
      );
      await retry(
        async () => {
          const associatedCompanies = (
            await getAssociations(ObjectType.DEBIT_CARD_ACCOUNT, hubspotDebitCardAccountId, ObjectType.COMPANY)
          ).data;
          console.log('associated companies ->', JSON.stringify(associatedCompanies));
          expect(associatedCompanies.results[0].type).toBe('debit_card_account_to_company');
        },
        2,
        10000,
      );
    });
  });
});
