import { DynamodbClient, sleep, retry } from '@npco/bff-systemtest-utils';
import { DbRecordType } from '@npco/component-dto-core';
import { DigitalWalletTokenStatus, DigitalWalletTokenUpdatedEventDto } from '@npco/component-dto-digital-wallet-token';
import { bootstrapHubspotAppOauthToken } from 'crms-engine-system-test/src/utils/hubspot/bootstrapHubspotOauthToken';
import { getDynamoDbTableName } from 'crms-engine-system-test/src/utils/hubspot/getDynamoDbTable';
import {
  getAssociations,
  getDigitalWalletToken,
  searchObjectByDomainId,
} from 'crms-engine-system-test/src/utils/hubspot/hubspotUtil';
import { createCompany } from 'crms-engine-system-test/src/utils/simplified-services/createCompany';
import { createContact } from 'crms-engine-system-test/src/utils/simplified-services/createContact';
import { createContactEvent } from 'crms-engine-system-test/src/utils/simplified-services/createContactEvent';
import { createDebitCard } from 'crms-engine-system-test/src/utils/simplified-services/createDebitCard';
import { sendHighPriorityQueueSqsMessage } from 'crms-engine-system-test/src/utils/utils';

import { v4 as uuidv4 } from 'uuid';

import { region } from './utils/globalVariables';
import { ObjectType } from './utils/hubspot/hubspotProperties';
import { HubspotPublicApp } from './utils/hubspot/hubspotTypes';

beforeAll(async () => {
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.DEFAULT_EXTENSION);
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.CUSTOM_OBJECT);
});

describe('Digital Wallet Token', () => {
  const digitalWalletTokenUuid = uuidv4();

  let event: DigitalWalletTokenUpdatedEventDto;

  let hubspotObjectId: string;

  const createDigitalWalletTokenDto = ({
    customerUuid,
    debitCardUuid,
    entityUuid,
  }: {
    customerUuid: string;
    debitCardUuid: string;
    entityUuid: string;
  }) =>
    new DigitalWalletTokenUpdatedEventDto({
      id: digitalWalletTokenUuid,
      debitCardAccountUuid: uuidv4(),
      entityUuid,
      debitCardUuid,
      customerUuid,
      deviceUuid: uuidv4(),
      status: DigitalWalletTokenStatus.ACTIVE,
      requestedAt: 'requestedAt',
      activatedAt: 'activatedAt',
      appVersion: '1.0.0',
      panSource: 'MOBILE_BANKING_APP',
      tokenProvider: {
        reference: 'reference',
        requesterId: uuidv4(),
        requesterName: 'requesterName',
      },
      device: {
        type: 'type',
        deviceId: uuidv4(),
        name: `system-test-${uuidv4()}`,
      },
      cardHolderName: 'cardHolderName',
      cardDisplayName: 'cardDisplayName',
      maskedPan: 'maskedPan',
      riskScore: 'DECISION_YELLOW',
      riskReasonCode: ['05', '0D'],
      terminated: {
        userUuid: 'userUuid',
        timestamp: 'timestamp',
      },
      suspended: {
        zellerInternalUserIdentifier: 'zellerInternalUserIdentifier',
        timestamp: 'timestamp',
      },
      unsuspended: {
        zellerInternalUserIdentifier: 'zellerInternalUserIdentifier',
        timestamp: 'timestamp',
      },
      updatedTime: Date.now(),
    });

  const queryDigitalWalletToken = async (
    id: string,
    type: DbRecordType = DbRecordType.DEBIT_CARD_ACCOUNT_CARD_DIGITAL_WALLET_TOKEN,
  ) => {
    const db = new DynamodbClient({ region });
    console.log(`query digital wallet id id ${id} of type ${type} in table ${getDynamoDbTableName()}`);
    return db.query({
      TableName: getDynamoDbTableName(),
      KeyConditionExpression: 'id = :id AND begins_with(#type, :type)',
      ExpressionAttributeNames: {
        '#type': 'type',
      },
      ExpressionAttributeValues: {
        ':id': id,
        ':type': type,
      },
    });
  };

  const queryDigitalWalletTokenHistory = async (
    id: string,
    type = DbRecordType.DEBIT_CARD_ACCOUNT_CARD_HISTORY_DIGITAL_WALLET_TOKEN,
  ) => {
    const db = new DynamodbClient({ region });
    console.log(`query digital wallet history id ${id} of type ${type} in table ${getDynamoDbTableName()}`);
    return db.query({
      TableName: getDynamoDbTableName(),
      KeyConditionExpression: 'id = :id AND begins_with(#type, :type)',
      ExpressionAttributeNames: {
        '#type': 'type',
      },
      ExpressionAttributeValues: {
        ':id': id,
        ':type': type,
      },
    });
  };

  beforeAll(async () => {
    const entityUuid = uuidv4();
    const debitCardUuid = uuidv4();

    const contactEvent = createContactEvent();

    await createCompany(entityUuid);
    await createContact({
      ...contactEvent,
      entityUuid,
    });

    await createDebitCard(entityUuid, debitCardUuid);

    event = createDigitalWalletTokenDto({
      entityUuid,
      debitCardUuid,
      customerUuid: contactEvent.customerUuid,
    });
  });

  describe('should be able to CREATE digital wallet token', () => {
    beforeAll(async () => {
      const sqsMessage = {
        'detail-type': 'crms.ProjectionDigitalWalletToken.update',
        detail: event,
      };
      console.log('CREATE: sending sqs for digital wallet token -> ', JSON.stringify(sqsMessage));
      await sendHighPriorityQueueSqsMessage(event.id, sqsMessage);
    });

    it('should be able to create Hubspot digital wallet token object', async () => {
      await retry(
        async () => {
          const hubspotObject = (
            await searchObjectByDomainId(ObjectType.DIGITAL_WALLET_TOKEN, 'digital_wallet_token_uuid', event.id)
          ).data;
          console.log('search digital wallet object -> ', JSON.stringify(hubspotObject));

          expect(hubspotObject.results.length).toBeGreaterThanOrEqual(1);
          const digitalWalletTokenObject = hubspotObject.results[0];
          expect(digitalWalletTokenObject.properties.digital_wallet_token_uuid).toBe(event.id);
          expect(digitalWalletTokenObject.properties.name).toBe(event.device.name);
          expect(digitalWalletTokenObject.properties.requester_name).toBe(event.tokenProvider.requesterName);
          expect(digitalWalletTokenObject.properties.type).toBe(event.device.type);
          expect(digitalWalletTokenObject.properties.updated_time).toBe(event.updatedTime.toString());
          expect(digitalWalletTokenObject.properties.masked_pan).toBe('6789');
          expect(digitalWalletTokenObject.properties.risk_score).toBe(event.riskScore);

          hubspotObjectId = digitalWalletTokenObject.id;

          const associatedDebitCards = (
            await getAssociations(ObjectType.DIGITAL_WALLET_TOKEN, digitalWalletTokenObject.id, ObjectType.DEBIT_CARD)
          ).data;

          console.log('associated debit cards ->', JSON.stringify(associatedDebitCards));

          expect(associatedDebitCards.results[0].type).toBe('digitalwallettokens_to_debitcards');
        },
        2,
        10000,
      );
    });

    it('should be able to associate digital wallet token with companies and contacts', async () => {
      await retry(
        async () => {
          const associatedCompanies = (
            await getAssociations(ObjectType.DIGITAL_WALLET_TOKEN, hubspotObjectId, ObjectType.COMPANY)
          ).data;
          const associatedContacts = (
            await getAssociations(ObjectType.DIGITAL_WALLET_TOKEN, hubspotObjectId, ObjectType.CONTACT)
          ).data;

          console.log('associated companies ->', JSON.stringify(associatedCompanies));
          console.log('associated contacts ->', JSON.stringify(associatedContacts));

          expect(associatedCompanies.results[0].type).toBe('digitalwallettokens_to_company');
          expect(associatedContacts.results[0].type).toBe('digitalwallettokens_to_contact');
        },
        2,
        10000,
      );
    });

    it('should be able to materialise digital wallet token item in materialised view', async () => {
      await retry(async () => {
        const dbItem = await queryDigitalWalletToken(event.id!);
        console.log('digital wallet token update is -> ', JSON.stringify(dbItem));
        expect(dbItem.Items?.length).toBe(1);
        expect(dbItem.Items?.[0].id).toEqual(event.id);
        expect(dbItem.Items?.[0].type).toEqual(DbRecordType.DEBIT_CARD_ACCOUNT_CARD_DIGITAL_WALLET_TOKEN);
        expect(dbItem.Items?.[0].hubspotObjectId).toEqual(hubspotObjectId);
        expect(dbItem.Items?.[0].hubspotProperties.length).toBeGreaterThan(0);
        expect(dbItem.Items?.[0].hubspotAssociations.debitCardAssociated).toBe(true);
      });
    });

    it('should be able to materialise digital wallet token history item in materialised view', async () => {
      await retry(async () => {
        const dwtHistoryDbItem = await queryDigitalWalletTokenHistory(event.id!);
        console.log('digital wallet token history is -> ', JSON.stringify(dwtHistoryDbItem));
        expect(dwtHistoryDbItem.Items?.length).toBe(1);
        expect(dwtHistoryDbItem.Items?.[0].id).toEqual(event.id);
        expect(dwtHistoryDbItem.Items?.[0].type).toEqual(
          `${DbRecordType.DEBIT_CARD_ACCOUNT_CARD_HISTORY_DIGITAL_WALLET_TOKEN}${event.updatedTime}`,
        );
      });
    });
  });

  describe('should be able to UPDATE digital wallet token', () => {
    let updatedEvent: DigitalWalletTokenUpdatedEventDto;

    it('should be able to handle HS duplicate error and update digital wallet token on HS', async () => {
      const db = new DynamodbClient({ region });
      console.log(
        `remove hubspot fields from digital wallet token id ${digitalWalletTokenUuid} in table ${getDynamoDbTableName()}`,
      );
      const result = await db.update({
        TableName: getDynamoDbTableName(),
        Key: { id: digitalWalletTokenUuid, type: `${DbRecordType.DEBIT_CARD_ACCOUNT_CARD_DIGITAL_WALLET_TOKEN}` },
        UpdateExpression: 'REMOVE #hubspotObjectId, #hubspotProperties, #hubspotAssociations',
        ExpressionAttributeNames: {
          '#hubspotObjectId': 'hubspotObjectId',
          '#hubspotProperties': 'hubspotProperties',
          '#hubspotAssociations': 'hubspotAssociations',
        },
      });
      console.log('result', result);
      const dbItem = await queryDigitalWalletToken(digitalWalletTokenUuid);
      expect(dbItem.Items![0].id).toBe(digitalWalletTokenUuid);
      expect(dbItem.Items![0].hubspotObjectId).toBeUndefined();
      expect(dbItem.Items![0].hubspotProperties).toBeUndefined();
      expect(dbItem.Items![0].hubspotAttributes).toBeUndefined();

      const currentDigitalWalletToken = dbItem.Items![0];
      const updatedDwtEvent = createDigitalWalletTokenDto({
        entityUuid: currentDigitalWalletToken.entityUuid,
        debitCardUuid: currentDigitalWalletToken.debitCardUuid,
        customerUuid: currentDigitalWalletToken.customerUuid,
      });
      updatedDwtEvent.id = currentDigitalWalletToken.id;
      updatedDwtEvent.debitCardAccountUuid = currentDigitalWalletToken.id;
      updatedDwtEvent.status = DigitalWalletTokenStatus.SUSPENDED;

      await sendHighPriorityQueueSqsMessage(updatedDwtEvent.id, {
        'detail-type': 'crms.ProjectionDigitalWalletToken.update',
        detail: updatedDwtEvent,
      });

      await retry(async () => {
        const updatedDbItem = await queryDigitalWalletToken(digitalWalletTokenUuid);
        expect(updatedDbItem.Items!.length).toBe(1);
        expect(updatedDbItem.Items![0].status).toBe(updatedDwtEvent.status);
        expect(updatedDbItem.Items![0].id).toEqual(digitalWalletTokenUuid);
        expect(updatedDbItem.Items![0].hubspotObjectId).toEqual(expect.any(String));
        expect(updatedDbItem.Items![0].hubspotProperties).toEqual(expect.any(Array));
        expect(updatedDbItem.Items![0].hubspotProperties.length).toBeGreaterThan(0);
        expect(updatedDbItem.Items![0].hubspotAssociations).toBeDefined();
      });

      await retry(
        async () => {
          const digitalWalletToken = (await getDigitalWalletToken(hubspotObjectId)).data;
          console.log('load from hubspot for updated digital wallet token:', JSON.stringify(digitalWalletToken));
          expect(digitalWalletToken.id).toBe(hubspotObjectId);
          expect(digitalWalletToken.properties.digital_wallet_token_uuid).toBe(digitalWalletTokenUuid);
          expect(digitalWalletToken.properties.status).toBe(updatedDwtEvent.status);
        },
        2,
        10000,
      );
    });

    it('should be able to update Hubspot digital wallet token object', async () => {
      updatedEvent = {
        ...event,
        status: DigitalWalletTokenStatus.TERMINATED,
        device: {
          ...event.device,
          type: 'UPDATED_TYPE',
        },
        updatedTime: Date.now() + 100,
      };

      const sqsMessage = {
        'detail-type': 'crms.ProjectionDigitalWalletToken.update',
        detail: updatedEvent,
      };

      console.log('UPDATE: sending sqs for digital wallet token -> ', sqsMessage);

      await sleep(10000);
      await sendHighPriorityQueueSqsMessage(updatedEvent.id, sqsMessage);

      await retry(
        async () => {
          const digitalWalletTokenObject = (await getDigitalWalletToken(hubspotObjectId)).data;
          console.log('search digital wallet object -> ', JSON.stringify(digitalWalletTokenObject));

          expect(digitalWalletTokenObject.properties.digital_wallet_token_uuid).toBe(updatedEvent.id);
          expect(digitalWalletTokenObject.properties.name).toBe(updatedEvent.device.name);
          expect(digitalWalletTokenObject.properties.requester_name).toBe(updatedEvent.tokenProvider.requesterName);
          expect(digitalWalletTokenObject.properties.type).toBe(updatedEvent.device.type);
          expect(digitalWalletTokenObject.properties.updated_time).toBe(updatedEvent.updatedTime.toString());
          expect(digitalWalletTokenObject.properties.masked_pan).toBe('6789');
          expect(digitalWalletTokenObject.properties.risk_score).toBe(updatedEvent.riskScore);

          const associatedDebitCards = (
            await getAssociations(ObjectType.DIGITAL_WALLET_TOKEN, digitalWalletTokenObject.id, ObjectType.DEBIT_CARD)
          ).data;

          console.log('associated debit cards ->', JSON.stringify(associatedDebitCards));

          expect(associatedDebitCards.results[0].type).toBe('digitalwallettokens_to_debitcards');
        },
        2,
        10000,
      );
    });

    it('should be able to associate digital wallet token with companies and contacts', async () => {
      await retry(
        async () => {
          const associatedCompanies = (
            await getAssociations(ObjectType.DIGITAL_WALLET_TOKEN, hubspotObjectId, ObjectType.COMPANY)
          ).data;
          const associatedContacts = (
            await getAssociations(ObjectType.DIGITAL_WALLET_TOKEN, hubspotObjectId, ObjectType.CONTACT)
          ).data;

          console.log('associated companies ->', JSON.stringify(associatedCompanies));
          console.log('associated contacts ->', JSON.stringify(associatedContacts));

          expect(associatedCompanies.results[0].type).toBe('digitalwallettokens_to_company');
          expect(associatedContacts.results[0].type).toBe('digitalwallettokens_to_contact');
        },
        2,
        10000,
      );
    });

    it('should be able to materialise digital wallet token item in materialised view', async () => {
      await retry(async () => {
        const dbItem = await queryDigitalWalletToken(updatedEvent.id!);
        console.log('digital wallet token update is -> ', JSON.stringify(dbItem));
        expect(dbItem.Items?.length).toBe(1);
        expect(dbItem.Items?.[0].id).toEqual(updatedEvent.id);
        expect(dbItem.Items?.[0].type).toEqual(DbRecordType.DEBIT_CARD_ACCOUNT_CARD_DIGITAL_WALLET_TOKEN);
        expect(dbItem.Items?.[0].deviceType).toEqual(updatedEvent.device.type);
        expect(dbItem.Items?.[0].hubspotObjectId).toEqual(hubspotObjectId);
        expect(dbItem.Items?.[0].hubspotProperties.length).toBeGreaterThan(0);
        expect(dbItem.Items?.[0].hubspotAssociations.debitCardAssociated).toBe(true);
      });
    });

    it('should be able to materialise digital wallet token history item in materialised view', async () => {
      await retry(async () => {
        const dwtHistoryDbItem = await queryDigitalWalletTokenHistory(event.id!);
        console.log('digital wallet token history is -> ', JSON.stringify(dwtHistoryDbItem));
        expect(dwtHistoryDbItem.Items?.length).toBe(3);
        expect(dwtHistoryDbItem.Items?.[0].id).toEqual(event.id);
        expect(dwtHistoryDbItem.Items?.[0].type).toEqual(
          `${DbRecordType.DEBIT_CARD_ACCOUNT_CARD_HISTORY_DIGITAL_WALLET_TOKEN}${event.updatedTime}`,
        );
        expect(dwtHistoryDbItem.Items?.[0].updatedAt).toEqual(new Date(Number(event.updatedTime)).toISOString());
        expect(dwtHistoryDbItem.Items?.[1].id).toEqual(event.id);
        expect(dwtHistoryDbItem.Items?.[2].id).toEqual(event.id);
        expect(dwtHistoryDbItem.Items?.[2].type).toEqual(
          `${DbRecordType.DEBIT_CARD_ACCOUNT_CARD_HISTORY_DIGITAL_WALLET_TOKEN}${updatedEvent.updatedTime}`,
        );
        expect(dwtHistoryDbItem.Items?.[2].updatedAt).toEqual(new Date(Number(updatedEvent.updatedTime)).toISOString());
      });
    });
  });
});
