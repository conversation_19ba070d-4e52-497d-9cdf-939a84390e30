import { DynamodbClient, invokeSyncLambdaParsed, retry, sleep } from '@npco/bff-systemtest-utils';
import { DbRecordType, EntityCategories, ISO4217 } from '@npco/component-dto-core';
import {
  DebitCardAccountStatus,
  DebitCardAccountType,
  DebitCardAccountUpdatedEventDto,
} from '@npco/component-dto-issuing-account';
import type { DebitCardAccountCardCreatedEventDto } from '@npco/component-dto-issuing-card';
import {
  DebitCardAccountCardUpdatedEventDto,
  DebitCardColour,
  DebitCardProductType,
  DebitCardStatus,
  DebitCardType,
  VelocityWindowEnum,
} from '@npco/component-dto-issuing-card';
import {
  DebitCardAccountTransactionUpdatedEventDto,
  DebitCardTransactionDeclinedReason,
  DebitCardTransactionStatusV2,
  DebitCardTransactionTypeV2,
} from '@npco/component-dto-issuing-transaction';
import { MerchantCreatedEventDto, MerchantUpdatedEventDto } from '@npco/component-dto-merchant';
import { MerchantDetailsSource } from '@npco/component-dto-richdata';
import { sendHighPriorityQueueSqsMessage } from 'crms-engine-system-test/src/utils/utils';

import { v4 as uuidv4 } from 'uuid';

import { createDebitCardAccountDto, createDebitCardAccountTransactionDto, createDebitCardDto } from './utils/cms';
import { region } from './utils/globalVariables';
import { getDynamoDbMerchantTableName, getDynamoDbTableName } from './utils/hubspot/getDynamoDbTable';
import { getLambdaNameByConvention } from './utils/lambda/getLambdaName';

const queryDb = async (id: string, type: string) => {
  const db = new DynamodbClient({ region });
  console.log(`query id ${id} of type ${type} in table ${getDynamoDbTableName()}`);
  return db.query({
    TableName: getDynamoDbTableName(),
    KeyConditionExpression: 'id = :id AND begins_with(#type, :type)',
    ExpressionAttributeNames: {
      '#type': 'type',
    },
    ExpressionAttributeValues: {
      ':id': id,
      ':type': type,
    },
  });
};

const queryMerchantDb = async (id: string, type: string) => {
  const db = new DynamodbClient({ region });
  console.log(`query id ${id} of type ${type} in table ${getDynamoDbMerchantTableName()}`);
  return db.query({
    TableName: getDynamoDbMerchantTableName(),
    KeyConditionExpression: 'id = :id AND begins_with(#type, :type)',
    ExpressionAttributeNames: {
      '#type': 'type',
    },
    ExpressionAttributeValues: {
      ':id': id,
      ':type': type,
    },
  });
};

describe('Custom Object Projection events test suite', () => {
  const entityUuid = uuidv4();

  describe('Debit Card Account test suite', () => {
    const aggregateId = uuidv4();

    it('should be able materialise the Debit Card Account Created projection', async () => {
      const dcaDto = { ...createDebitCardAccountDto(entityUuid), id: aggregateId };
      const sqsMessage = {
        'detail-type': 'crms.ProjectionDebitCardAccount.create',
        detail: dcaDto,
      };

      console.log('dca create being sent is -> ', sqsMessage);
      await sendHighPriorityQueueSqsMessage(dcaDto.id, sqsMessage);
      await sleep(500);

      await retry(async () => {
        const accountRes = await queryDb(aggregateId, DbRecordType.DEBIT_CARD_ACCOUNT);
        console.log('account is -> ', accountRes);
        expect(accountRes.Items?.length).toEqual(1);
        const account = accountRes.Items![0];
        expect(account.id).toBe(sqsMessage.detail.id);
      }, 10);
    });

    it('should be able materialise the Debit Card Account Updated projection', async () => {
      const dcaDto = new DebitCardAccountUpdatedEventDto({
        id: aggregateId,
        debitCardAccountUuid: aggregateId,
        entityUuid,
        name: 'updatedName',
        icon: {
          colour: 'colour',
          letter: 'letter',
          image: 'image',
        },
        accountDetails: {
          bsb: 'bsb',
          account: 'account',
          name: 'name',
        },
        status: DebitCardAccountStatus.ACTIVE,
        type: DebitCardAccountType.ZLR_DEBIT,
        feature: {
          transfer: true,
          cp: true,
          cnp: true,
          atm: true,
          npp: true,
          bpay: true,
          de: true,
        },
        bpayDetails: {
          billerCode: 'billerCode',
          reference: 'reference',
        },
        balance: {
          value: '100',
          currency: ISO4217.AUD,
        },
        nppDetails: {
          payId: 'payId',
        },
        updatedTime: new Date().getTime(),
        timestamp: new Date().getTime().toString(),
      });
      const sqsMessage = {
        'detail-type': 'crms.ProjectionDebitCardAccount.update',
        detail: dcaDto,
      };

      console.log('dca update being sent is -> ', sqsMessage);
      await sendHighPriorityQueueSqsMessage(dcaDto.id, sqsMessage);
      await sleep(100);
      await retry(async () => {
        const accountRes = await queryDb(aggregateId, DbRecordType.DEBIT_CARD_ACCOUNT);
        console.log('account update is -> ', accountRes);
        expect(accountRes.Items?.length).toEqual(1);
        const account = accountRes.Items![0];
        expect(account.name).toEqual(dcaDto.name);
      }, 10);
    });
  });

  describe('Debit Card Account Card test suite', () => {
    const aggregateId = uuidv4();
    it('should be able materialise the Debit Card Account Card Created projection', async () => {
      const velocityControlMerchantModified = {
        actingCustomerUuid: uuidv4(),
        updatedAt: new Date().toISOString(),
      };
      const dcacDto = {
        ...createDebitCardDto(uuidv4(), entityUuid),
        id: aggregateId,
        productType: DebitCardProductType.EXPENSE,
        velocityControl: {
          velocityControlUuid: uuidv4(),
          amountLimit: {
            value: '100',
            currency: ISO4217.AUD,
          },
          maxTransactionValue: {
            value: '100',
            currency: ISO4217.AUD,
          },
          availableAmount: {
            value: '100',
            currency: ISO4217.AUD,
          },
          resetsAt: new Date().toISOString(),
          velocityWindow: VelocityWindowEnum.WEEK,
          merchantModified: velocityControlMerchantModified,
        },
      };
      const sqsMessage = {
        'detail-type': 'crms.ProjectionDebitCardAccountCard.create',
        detail: dcacDto,
      };

      console.log('dcac create being sent is -> ', sqsMessage);
      await sendHighPriorityQueueSqsMessage(dcacDto.id, sqsMessage);
      await sleep(500);

      await retry(async () => {
        const cardRes = await queryDb(aggregateId, DbRecordType.DEBIT_CARD_ACCOUNT_CARD);
        console.log('card is -> ', cardRes);
        expect(cardRes.Items?.length).toEqual(1);
        const card = cardRes.Items![0];
        expect(card.velocityControl).toBeDefined();
      }, 10);
    });

    it('invalid debit card event will push straight to dlq and not cause lambda to return failed response', async () => {
      const debitCardUuid = uuidv4();

      const event = createDebitCardDto(uuidv4(), entityUuid, {
        id: debitCardUuid,
        customerUuid: uuidv4(),
        productType: DebitCardProductType.DEBIT,
      });

      const invalidEvent: Omit<DebitCardAccountCardCreatedEventDto, 'entityUuid'> & { entityUuid?: string } = {
        ...event,
      };
      delete invalidEvent.entityUuid;

      const eventBr = {
        aggregateId: uuidv4(),
        payload: invalidEvent,
        uri: 'crms.ProjectionDebitCardAccount.create',
      };

      const payload = {
        Records: [
          {
            body: JSON.stringify(eventBr),
            messageId: '2342',
            receiptHandle: '',
            attributes: undefined as any,
            messageAttributes: undefined as any,
            md5OfBody: '',
            eventSource: '',
            eventSourceARN: '',
            awsRegion: '',
          },
        ],
      };

      const result = await invokeSyncLambdaParsed(
        getLambdaNameByConvention('customObjectSqsHandler', 'projection'),
        payload,
      );

      expect(result.StatusCode).toEqual(200);
    });

    it('should be able materialise the Debit Card Account Card Updated projection', async () => {
      const dcacDto = new DebitCardAccountCardUpdatedEventDto({
        id: aggregateId,
        debitCardAccountUuid: uuidv4(),
        entityUuid,
        cardSequenceNumber: 1,
        name: 'updatedName',
        owner: 'owner',
        maskedPan: 'maskedPan',
        status: DebitCardStatus.ACTIVE,
        colour: DebitCardColour.BLACK,
        type: DebitCardType.MASTERCARD_DEBIT,
        lastUseTime: 1,
        debitCardType: 'debitCardType',
        updatedTime: new Date().getTime(),
        timestamp: new Date().getTime().toString(),
      });
      const sqsMessage = {
        'detail-type': 'crms.ProjectionDebitCardAccountCard.update',
        detail: dcacDto,
      };

      console.log('dcac update being sent is -> ', sqsMessage);
      await sendHighPriorityQueueSqsMessage(dcacDto.id, sqsMessage);
      await sleep(500);

      await retry(async () => {
        const cardRes = await queryDb(aggregateId, DbRecordType.DEBIT_CARD_ACCOUNT_CARD);
        console.log('card update is -> ', cardRes);
        expect(cardRes.Items?.length).toEqual(1);
        const card = cardRes.Items![0];
        expect(card.name).toEqual(dcacDto.name);
      }, 10);
    });
  });

  describe('Debit Card Account Card Transaction test suite', () => {
    const aggregateId = uuidv4();
    const merchantId = uuidv4();
    const timestamp = new Date().getTime().toString();
    it('should be able materialise the Debit Card Account Transaction Created projection', async () => {
      const dcaTxnDto = { ...createDebitCardAccountTransactionDto(entityUuid, merchantId, uuidv4()), id: aggregateId };
      const sqsMessage = {
        'detail-type': 'crms.ProjectionDebitCardAccountTransaction.create',
        detail: dcaTxnDto,
      };

      console.log('txn create being sent is -> ', sqsMessage);
      await sendHighPriorityQueueSqsMessage(dcaTxnDto.id, sqsMessage);
      await sleep(500);

      await retry(async () => {
        const txnRes = await queryDb(aggregateId, DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION);
        console.log('txn create is -> ', txnRes);
        expect(txnRes.Items?.length).toEqual(1);
      }, 10);
    });

    it('should be able materialise the Debit Card Account Transaction Updated projection', async () => {
      const dcaTxnUpdateDto = new DebitCardAccountTransactionUpdatedEventDto({
        id: aggregateId,
        debitCardAccountUuid: uuidv4(),
        entityUuid,
        type: DebitCardTransactionTypeV2.ATM_IN,
        status: DebitCardTransactionStatusV2.APPROVED,
        amount: {
          value: '100',
          currency: ISO4217.AUD,
        },
        merchant: {
          id: merchantId,
          abn: 'abn',
          name: 'updatedMerchantName',
          appearAs: 'appearAs',
          address: {
            street1: 'street1',
            street2: 'street2',
            suburb: 'suburb',
            state: 'state',
            postcode: 'postcode',
            country: 'country',
          },
          icon: {
            colour: 'colour',
            letter: 'letter',
            image: 'image',
          },
          url: 'url',
          phone: 'phone',
          email: 'email',
          location: 'location',
          locationAccuracy: 1,
          hours: 'hours',
          rating: 1,
          category: EntityCategories.ADVERTISING,
          subcategory: 'subcategory',
          dataSource: MerchantDetailsSource.ZELLER,
          locationOf: 'locationOf',
          businessName: 'businessName',
          updatedTime: new Date().getTime() / 1000,
        },
        note: 'noteUpdated',
        tags: ['tags2'],
        category: EntityCategories.ADVERTISING,
        attachments: ['attachments2'],
        reference: 'reference',
        debitCardId: 'debitCardId',
        debitCardName: 'debitCardName',
        debitCardMaskedPan: 'debitCardMaskedPan',
        referencePayee: 'referencePayee',
        declineReason: DebitCardTransactionDeclinedReason.INCORRECT_PIN,
        payeeDetailsUuid: 'payeeDetailsUuid',
        payeeDetails: {
          nppDetails: {
            payId: 'payId',
          },
          bpayDetails: {
            billerName: 'billerName',
            billerCode: 'billerCode',
            crn: 'crn',
            nickname: 'nickname',
          },
          accountDetails: {
            bsb: 'bsb',
            account: 'account',
            name: 'name',
          },
          debitCardAccountUuid: uuidv4(),
        },
        payerDetails: {
          nppDetails: {
            payId: 'payId',
          },
          bpayDetails: {
            billerName: 'billerName',
            billerCode: 'billerCode',
            crn: 'crn',
            nickname: 'nickname',
          },
          accountDetails: {
            bsb: 'bsb',
            account: 'account',
            name: 'name',
          },
          debitCardAccountUuid: uuidv4(),
        },
        scheduledTransferUuid: 'scheduledTransferUuid',
        subcategory: 'subCategory',
        accountingCategory: 'accountingCategory',
        depositId: 'depositId',
        updatedTime: new Date().getTime() / 1000,
        timestamp,
      });
      const sqsMessage = {
        'detail-type': 'crms.ProjectionDebitCardAccountTransaction.update',
        detail: dcaTxnUpdateDto,
      };

      console.log('txn update being sent is -> ', sqsMessage);
      await sendHighPriorityQueueSqsMessage(dcaTxnUpdateDto.id, sqsMessage);
      await sleep(500);

      await retry(async () => {
        const txnRes = await queryDb(aggregateId, DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION);
        console.log('txn update is -> ', txnRes);
        expect(txnRes.Items?.length).not.toEqual(0);
      }, 10);
    });
  });

  describe('Merchant Details test suite', () => {
    const aggregateId = uuidv4();
    it('should be able materialise the Merchant Details Create Projection event', async () => {
      const merchantDto = new MerchantCreatedEventDto({
        id: aggregateId,
        category: EntityCategories.ADVERTISING,
        subcategory: 'subcategory',
        abn: 'abn',
        name: 'name',
        appearAs: 'appearAs',
        address: {
          street1: 'street1',
          street2: 'street2',
          suburb: 'suburb',
          state: 'state',
          postcode: 'postcode',
          country: 'country',
        },
        icon: {
          colour: 'colour',
          letter: 'letter',
          image: 'image',
        },
        url: 'url',
        phone: 'phone',
        email: 'email',
        location: 'location',
        locationAccuracy: 1,
        hours: {
          is_always_open: true,
          sunday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
          monday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
          tuesday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
          wednesday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
          thursday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
          friday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
          saturday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
        },
        rating: 100,
        dataSource: MerchantDetailsSource.ZELLER,
        businessName: 'businessName',
        locationOf: 'locationOf',
        updatedTime: new Date().getTime(),
      });
      const sqsMessage = {
        'detail-type': 'crms.ProjectionMerchant.create',
        detail: merchantDto,
      };

      console.log('merchant create being sent is -> ', sqsMessage);
      await sendHighPriorityQueueSqsMessage(merchantDto.id, sqsMessage);

      await sleep(500);

      await retry(async () => {
        const merchantRes = await queryMerchantDb(aggregateId, DbRecordType.MERCHANT);
        console.log('merchantRes is -> ', merchantRes);
        expect(merchantRes.Items?.length).toEqual(1);
      }, 10);
    });

    it('should be able materialise the Merchant Details Update Projection event', async () => {
      const merchantDto = new MerchantUpdatedEventDto({
        id: aggregateId,
        category: EntityCategories.ADVERTISING,
        subcategory: 'subcategory',
        abn: 'abn',
        name: 'merchantUpdatedName',
        appearAs: 'appearAs',
        address: {
          street1: 'street1',
          street2: 'street2',
          suburb: 'suburb',
          state: 'state',
          postcode: 'postcode',
          country: 'country',
        },
        icon: {
          colour: 'colour',
          letter: 'letter',
          image: 'image',
        },
        url: 'url',
        phone: 'phone',
        email: 'email',
        location: 'location',
        locationAccuracy: 1,
        hours: {
          is_always_open: true,
          sunday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
          monday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
          tuesday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
          wednesday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
          thursday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
          friday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
          saturday: {
            status: 'status',
            times: [
              {
                open: 'open',
                close: 'close',
              },
            ],
          },
        },
        rating: 100,
        dataSource: MerchantDetailsSource.ZELLER,
        businessName: 'businessName',
        locationOf: 'locationOf',
        updatedTime: new Date().getTime(),
      });
      const sqsMessage = {
        'detail-type': 'crms.ProjectionMerchant.update',
        detail: merchantDto,
      };

      console.log('merchant update being sent is -> ', sqsMessage);
      await sendHighPriorityQueueSqsMessage(merchantDto.id, sqsMessage);
      await sleep(500);

      await retry(async () => {
        const merchantRes = await queryMerchantDb(aggregateId, DbRecordType.MERCHANT);
        console.log('merchantRes is -> ', merchantRes);
        expect(merchantRes.Items?.length).toEqual(1);
      }, 10);
    });
  });

  it('should return sqs batch response containing batch item failures with failed events from custom object handler', async () => {
    const payload = {
      Records: [
        {
          messageId: 'failed-message-id',
          body: JSON.stringify({ uri: 'not.exist.uri' }),
        },
      ],
    };

    const lambdaName = getLambdaNameByConvention('customObjectSqsHandler', 'projection');
    const batchResponse = await invokeSyncLambdaParsed(lambdaName, payload);
    expect(batchResponse.Payload).toEqual({ batchItemFailures: [{ itemIdentifier: 'failed-message-id' }] });
  });

  it('should return sqs batch response containing batch item failures with failed events from hubspot default handler', async () => {
    const payload = {
      Records: [
        {
          messageId: 'failed-message-id',
          body: JSON.stringify({ 'detail-type': 'not.exist.uri' }),
        },
      ],
    };

    const lambdaName = getLambdaNameByConvention('hubspotDefaultSqsHandler', 'projection');
    const batchResponse = await invokeSyncLambdaParsed(lambdaName, payload);
    expect(batchResponse.Payload).toEqual({ batchItemFailures: [{ itemIdentifier: 'failed-message-id' }] });
  });
});
