import { DynamodbClient, retry } from '@npco/bff-systemtest-utils';
import { DbRecordType, ISO4217 } from '@npco/component-dto-core';
import type { CustomerCreatedEventDto } from '@npco/component-dto-customer';
import { DebitCardProductType, VelocityWindowEnum } from '@npco/component-dto-issuing-card';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';
import { bootstrapHubspotAppOauthToken } from 'crms-engine-system-test/src/utils/hubspot/bootstrapHubspotOauthToken';
import {
  getAssociations,
  getDebitCard,
  searchObjectByDomainId,
} from 'crms-engine-system-test/src/utils/hubspot/hubspotUtil';
import { createCompany } from 'crms-engine-system-test/src/utils/simplified-services/createCompany';
import { createContact } from 'crms-engine-system-test/src/utils/simplified-services/createContact';
import { createContactEvent } from 'crms-engine-system-test/src/utils/simplified-services/createContactEvent';
import { sendHighPriorityQueueSqsMessage } from 'crms-engine-system-test/src/utils/utils';

import { v4 as uuidv4 } from 'uuid';

import { createDebitCardAccountTransactionDto, createDebitCardDto, getDebitCardAccountCard } from './utils/cms';
import { region } from './utils/globalVariables';
import { ObjectType } from './utils/hubspot/hubspotProperties';
import { HubspotPublicApp } from './utils/hubspot/hubspotTypes';

beforeAll(async () => {
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.DEFAULT_EXTENSION);
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.CUSTOM_OBJECT);
});

const db = new DynamodbClient({ region });

describe('debit cards test suite', () => {
  let dcacCreatedEvent: any;

  const stage = process.env.STAGE || 'dev';

  const debitCardUuid = uuidv4();
  const entityUuid = uuidv4();
  let contactEvent: CustomerCreatedEventDto;
  const mockDebitCardAccountNumber = '*********';

  const dbPut = async (item: any) => db.putItem(`${stage}-crms-engine-Entities`, item);

  beforeAll(async () => {
    contactEvent = createContactEvent();

    await createCompany(entityUuid);
    await createContact({
      ...contactEvent,
      entityUuid,
    });
  });

  it('should be able to create debit card', async () => {
    let hubspotObjectId: string;

    const event = createDebitCardDto(uuidv4(), entityUuid, {
      id: debitCardUuid,
      customerUuid: contactEvent.customerUuid,
      productType: DebitCardProductType.DEBIT,
    });

    dcacCreatedEvent = event;

    await dbPut({
      id: event.debitCardAccountUuid,
      type: `${DbRecordType.DEBIT_CARD_ACCOUNT}#${uuidv4()}`,
      entityUuid: event.entityUuid,
      accountDetails: { account: mockDebitCardAccountNumber },
    });

    await sendHighPriorityQueueSqsMessage(event.id, {
      'detail-type': 'crms.ProjectionDebitCardAccountCard.create',
      detail: event,
    });

    await retry(async () => {
      const dbItem = await getDebitCardAccountCard(event.id);
      expect(dbItem.Items!.length).toBe(1);
      expect(dbItem.Items![0].name).toBe(event.name);
      expect(dbItem.Items![0].id).toBe(debitCardUuid);
      expect(dbItem.Items![0].hubspotObjectId).toEqual(expect.any(String));
      expect(dbItem.Items![0].hubspotProperties).toEqual(expect.any(Array));
      expect(dbItem.Items![0].hubspotProperties.length).toBeGreaterThan(0);
      expect(dbItem.Items![0].hubspotAssociations).toBeDefined();
      hubspotObjectId = dbItem.Items![0].hubspotObjectId;
    });

    await retry(
      async () => {
        const debitCard = (await getDebitCard(hubspotObjectId)).data;
        expect(debitCard.properties.name).toBe(event.name);
        expect(debitCard.properties.debit_card_uuid).toBe(event.id);
      },
      2,
      10000,
    );

    await retry(
      async () => {
        const associatedCompanies = (await getAssociations(ObjectType.DEBIT_CARD, hubspotObjectId, ObjectType.COMPANY))
          .data;
        const associatedContacts = (await getAssociations(ObjectType.DEBIT_CARD, hubspotObjectId, ObjectType.CONTACT))
          .data;

        expect(associatedCompanies.results[0].type).toBe('debit_card_to_company');
        expect(associatedContacts.results[0].type).toBe('debit_card_to_contact');
      },
      2,
      10000,
    );
  });

  it('should be able to create corporate card', async () => {
    let hubspotObjectId: string;

    const event = createDebitCardDto(uuidv4(), entityUuid, {
      id: debitCardUuid,
      customerUuid: contactEvent.customerUuid,
      productType: DebitCardProductType.EXPENSE,
      velocityControl: {
        velocityControlUuid: uuidv4(),
        totalSpentAmount: {
          value: '100',
          currency: ISO4217.AUD,
        },
        amountLimit: {
          value: '100',
          currency: ISO4217.AUD,
        },
        maxTransactionValue: {
          value: '100',
          currency: ISO4217.AUD,
        },
        availableAmount: {
          value: '100',
          currency: ISO4217.AUD,
        },
        resetsAt: new Date().toISOString(),
        velocityWindow: VelocityWindowEnum.DAY,
        modifiedBy: {
          actingCustomerUuid: uuidv4(),
          updatedAt: new Date().toISOString(),
        },
      },
    });

    dcacCreatedEvent = event;

    await dbPut({
      id: event.debitCardAccountUuid,
      type: `${DbRecordType.DEBIT_CARD_ACCOUNT}#CORP${uuidv4()}`,
      entityUuid: event.entityUuid,
      accountDetails: { account: mockDebitCardAccountNumber },
    });

    await sendHighPriorityQueueSqsMessage(event.id, {
      'detail-type': 'crms.ProjectionDebitCardAccountCard.create',
      detail: event,
    });

    await retry(async () => {
      const dbItem = await getDebitCardAccountCard(event.id);
      expect(dbItem.Items!.length).toBe(1);
      expect(dbItem.Items![0].name).toBe(event.name);
      expect(dbItem.Items![0].id).toBe(debitCardUuid);
      expect(dbItem.Items![0].hubspotObjectId).toEqual(expect.any(String));
      expect(dbItem.Items![0].hubspotProperties).toEqual(expect.any(Array));
      expect(dbItem.Items![0].hubspotProperties.length).toBeGreaterThan(0);
      expect(dbItem.Items![0].hubspotAssociations).toBeDefined();
      hubspotObjectId = dbItem.Items![0].hubspotObjectId;
    });

    await retry(
      async () => {
        const debitCard = (await getDebitCard(hubspotObjectId)).data;
        expect(debitCard.properties.name).toBe(event.name);
        expect(debitCard.properties.debit_card_uuid).toBe(event.id);
        expect(debitCard.properties).toEqual({
          debit_card_uuid: event.id,
          name: event.name,
          status: event.status,
          type: event.type,
          product_type: event.productType,
          format: event.format,
          accessible: `${event.accessibleProfile}`,
          account_ending_in: mockDebitCardAccountNumber.slice(-4),
          masked_pan: event.maskedPan.slice(-4),
          latest_transaction_status: null,
          latest_transaction_amount: null,
          latest_transaction_merchant: null,
          last_used_time: `${event.lastUseTime}`,
          updated_time: `${event.updatedTime}`,
          hs_createdate: expect.any(String),
          hs_lastmodifieddate: expect.any(String),
          hs_object_id: expect.any(String),
          velocity_control_amount_limit: JSON.stringify(event.velocityControl?.amountLimit),
          velocity_control_velocity_window: event.velocityControl?.velocityWindow,
          velocity_control_max_transaction_value: JSON.stringify(event.velocityControl?.maxTransactionValue),
          velocity_control_modified_time: event.velocityControl?.modifiedBy?.updatedAt,
          velocity_control_modified_by: event.velocityControl?.modifiedBy?.actingCustomerUuid,
        });
      },
      2,
      10000,
    );

    await retry(
      async () => {
        const associatedCompanies = (await getAssociations(ObjectType.DEBIT_CARD, hubspotObjectId, ObjectType.COMPANY))
          .data;
        const associatedContacts = (await getAssociations(ObjectType.DEBIT_CARD, hubspotObjectId, ObjectType.CONTACT))
          .data;

        expect(associatedCompanies.results[0].type).toBe('debit_card_to_company');
        expect(associatedContacts.results[0].type).toBe('debit_card_to_contact');
      },
      2,
      10000,
    );
  });

  it('should emit PURCHASE transaction event and trigger update to HS debitCard object', async () => {
    const event = createDebitCardAccountTransactionDto(
      dcacCreatedEvent.entityUuid,
      uuidv4(),
      dcacCreatedEvent.debitCardAccountUuid,
      {
        type: DebitCardTransactionTypeV2.PURCHASE,
        debitCardId: debitCardUuid,
        source: 'banking',
      },
    );

    await sendHighPriorityQueueSqsMessage(event.id, {
      'detail-type': 'crms.ProjectionDebitCardAccountTransaction.create',
      detail: event,
    });

    await retry(
      async () => {
        const debitCards = (
          await searchObjectByDomainId(ObjectType.DEBIT_CARD, 'debit_card_uuid', event.debitCardId as string)
        ).data;

        expect(debitCards.results.length).toBe(1);
        const debitCard = debitCards.results[0];
        expect(debitCard.properties.debit_card_uuid).toBe(event.debitCardId);
        expect(debitCard.properties.name).toBe(dcacCreatedEvent.name);
        expect(debitCard.properties.latest_transaction_status).toEqual(event.status);
        expect(debitCard.properties.latest_transaction_amount).toEqual(JSON.stringify(event.amount));
        expect(debitCard.properties.latest_transaction_merchant).toEqual(event.merchant?.name);
      },
      2,
      10000,
    );
  });
});
