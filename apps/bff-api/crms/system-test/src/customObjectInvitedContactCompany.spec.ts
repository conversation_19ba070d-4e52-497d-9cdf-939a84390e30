import { DynamodbClient, describeIf, retry, sleep } from '@npco/bff-systemtest-utils';
import { CustomerRole, DbRecordType } from '@npco/component-dto-core';
import { CustomerEntityInvitationDeletedEventDto, CustomerEntityInvitedEventDto } from '@npco/component-dto-customer';
import { bootstrapHubspotAppOauthToken } from 'crms-engine-system-test/src/utils/hubspot/bootstrapHubspotOauthToken';
import { getAssociations, searchObjectByDomainIdFilters } from 'crms-engine-system-test/src/utils/hubspot/hubspotUtil';
import { createCompany } from 'crms-engine-system-test/src/utils/simplified-services/createCompany';
import { createContact } from 'crms-engine-system-test/src/utils/simplified-services/createContact';
import { createContactEvent } from 'crms-engine-system-test/src/utils/simplified-services/createContactEvent';

import { v4 as uuidv4 } from 'uuid';

import { getDynamoDbTableName } from './utils/hubspot/getDynamoDbTable';
import { ObjectType } from './utils/hubspot/hubspotProperties';
import { HubspotPublicApp } from './utils/hubspot/hubspotTypes';
import { queryContact } from './utils/simplified-services/queryContact';
import { region, sendHighPriorityQueueSqsMessage } from './utils/utils';

beforeAll(async () => {
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.DEFAULT_EXTENSION);
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.CUSTOM_OBJECT);
});

describeIf(process.env.MULTI_ENTITY_ENABLED === 'true', 'Contact-Companies Custom Object', () => {
  const entityUuid = uuidv4();
  const customerUuid = uuidv4();
  const invitedByCustomerUuid = uuidv4();

  let contact: any;
  let company: any;
  let associatedCompanies: any;
  let associatedContacts: any;
  let hubspotObjectId: string;
  const db = new DynamodbClient({ region });

  const queryCustomerEntity = async (id: string, entityId: string) => {
    console.log(
      `query customer entity id ${id} of type ${DbRecordType.CUSTOMER_ENTITY} in table ${getDynamoDbTableName()}`,
    );
    return db.query({
      TableName: getDynamoDbTableName(),
      KeyConditionExpression: 'id = :id AND #type = :type',
      ExpressionAttributeNames: {
        '#type': 'type',
      },
      ExpressionAttributeValues: {
        ':id': id,
        ':type': `${DbRecordType.CUSTOMER_ENTITY}${entityId}`,
      },
    });
  };

  beforeAll(async () => {
    company = await createCompany(entityUuid);

    contact = await createContact({
      ...createContactEvent(),
      entityUuid,
      customerUuid,
      firstname: `system-test-${customerUuid}`,
    });

    await createContact({
      entityUuid,
      customerUuid: invitedByCustomerUuid,
      registeringIndividual: false,
    });

    await sleep(10000);
  });

  describe('CustomerEntity.Invited', () => {
    const customerEntityInvitedEventDto = new CustomerEntityInvitedEventDto({
      customerUuid,
      entityUuid,
      role: CustomerRole.MANAGER,
      email: '',
      isInvitationPending: true,
      invitedBy: {
        customerUuid: invitedByCustomerUuid,
      },
    });
    beforeAll(async () => {
      const sqsMessage = {
        'detail-type': 'crms.CustomerEntity.Invited',
        detail: customerEntityInvitedEventDto,
      };
      await sendHighPriorityQueueSqsMessage(customerUuid, sqsMessage);

      await sleep(5000);
    });
    it('should be able to create Hubspot contactCompany custom object', async () => {
      await retry(
        async () => {
          const hubspotObject = (
            await searchObjectByDomainIdFilters(ObjectType.CONTACT_COMPANY, [
              {
                propertyName: 'contact_uuid',
                value: customerUuid,
                operator: 'EQ',
              },
              {
                propertyName: 'company_uuid',
                value: entityUuid,
                operator: 'EQ',
              },
            ])
          ).data;
          console.log('search contactCompany object result -> ', JSON.stringify(hubspotObject));

          expect(hubspotObject.results.length).toBeGreaterThanOrEqual(1);
          const contactCompanyObject = hubspotObject.results[0];
          expect(contactCompanyObject.properties.contact_uuid).toBe(customerUuid);
          expect(contactCompanyObject.properties.company_uuid).toBe(entityUuid);

          expect(contactCompanyObject.properties.role).toBe('role:manager');

          hubspotObjectId = contactCompanyObject.id;

          expect(hubspotObjectId).toBeDefined();
        },
        2,
        10000,
      );
    });

    it('should associate contact with company', async () => {
      await retry(
        async () => {
          console.log('load from hubspot:', contact);
          const associations = (await getAssociations(ObjectType.CONTACT, contact.id, ObjectType.COMPANY, true)).data;

          expect(associations.results.length).toBe(1);
          expect(associations.results[0].toObjectId).toBe(Number(company.id));
          expect(associations.results[0].associationTypes.some((item: any) => item.label === 'role_manager')).toBe(
            true,
          );
        },
        2,
        10000,
      );

      await retry(
        async () => {
          const dbItem = await queryContact(customerUuid);
          expect(dbItem.Items?.[0].hubspotAssociations.companiesAssociated).toContain(company.id);
        },
        2,
        10000,
      );
    });

    it('should be able to associate custom object with companies, contacts and labels', async () => {
      await retry(
        async () => {
          associatedCompanies = (await getAssociations(ObjectType.CONTACT_COMPANY, hubspotObjectId, ObjectType.COMPANY))
            .data;
          associatedContacts = (await getAssociations(ObjectType.CONTACT_COMPANY, hubspotObjectId, ObjectType.CONTACT))
            .data;

          console.log('associated companies ->', JSON.stringify(associatedCompanies));
          console.log('associated contacts ->', JSON.stringify(associatedContacts));

          expect(associatedContacts.results[0].type).toBe('contact_to_contactcompanies');
          expect(associatedCompanies.results).toEqual(
            expect.arrayContaining([{ id: expect.any(String), type: 'company_to_contactcompanies' }]),
          );
        },
        2,
        10000,
      );
    });

    it('should be able to materialise custom object item in materialised view', async () => {
      await retry(
        async () => {
          const dbItem = await queryCustomerEntity(customerUuid, entityUuid);
          console.log('customer entity db item is -> ', JSON.stringify(dbItem));
          expect(dbItem.Items?.length).toBe(1);
          expect(dbItem.Items?.[0].type).toEqual(`${DbRecordType.CUSTOMER_ENTITY}${entityUuid}`);
          expect(dbItem.Items?.[0].role).toEqual(customerEntityInvitedEventDto.role);
          expect(dbItem.Items?.[0].hubspotObjectId).toEqual(hubspotObjectId);
          expect(dbItem.Items?.[0].hubspotProperties.length).toBeGreaterThan(0);
          expect(dbItem.Items?.[0].hubspotAssociations.companyAssociated).toBe(associatedCompanies.results[0].id);
          expect(dbItem.Items?.[0].hubspotAssociations.contactAssociated).toBe(associatedContacts.results[0].id);
        },
        2,
        10000,
      );
    });
  });

  describe('CustomerEntity.InvitationDeleted', () => {
    const customerEntityInviteDeletedEventDto = new CustomerEntityInvitationDeletedEventDto({
      entityUuid,
      customerUuid,
    });
    beforeAll(async () => {
      const sqsMessage = {
        'detail-type': 'crms.CustomerEntity.InvitationDeleted',
        detail: customerEntityInviteDeletedEventDto,
      };
      await sendHighPriorityQueueSqsMessage(customerUuid, sqsMessage);

      await sleep(5000);
    });

    it('should removed hubspot custom object', async () => {
      await retry(
        async () => {
          const hubspotObject = (
            await searchObjectByDomainIdFilters(ObjectType.CONTACT_COMPANY, [
              {
                propertyName: 'contact_uuid',
                value: customerUuid,
                operator: 'EQ',
              },
              {
                propertyName: 'company_uuid',
                value: entityUuid,
                operator: 'EQ',
              },
            ])
          ).data;

          expect(hubspotObject.results.length).toBe(0);
        },
        2,
        10000,
      );
    });

    it('should be able to remove association with companies, contacts and labels', async () => {
      await retry(
        async () => {
          associatedCompanies = (await getAssociations(ObjectType.CONTACT_COMPANY, hubspotObjectId, ObjectType.COMPANY))
            .data;
          associatedContacts = (await getAssociations(ObjectType.CONTACT_COMPANY, hubspotObjectId, ObjectType.CONTACT))
            .data;

          console.log('associated companies ->', JSON.stringify(associatedCompanies));
          console.log('associated contacts ->', JSON.stringify(associatedContacts));

          expect(associatedCompanies.results).toEqual([]);
          expect(associatedContacts.results).toEqual([]);
        },
        2,
        10000,
      );
    });

    it('should be able to remove contact-company association', async () => {
      await retry(async () => {
        const associations = (await getAssociations(ObjectType.CONTACT, contact.id, ObjectType.COMPANY)).data;

        expect(associations.results.length).toBe(0);
      });

      await retry(
        async () => {
          const dbItem = await queryContact(customerUuid);
          expect(dbItem.Items?.[0].hubspotAssociations.companiesAssociated.length).toBe(0);
        },
        2,
        10000,
      );
    });

    it('should be able to remove materialised custom object item in materialised view', async () => {
      await retry(
        async () => {
          const dbItem = await queryCustomerEntity(customerUuid, entityUuid);
          console.log('customer entity db item is -> ', JSON.stringify(dbItem));
          expect(dbItem.Items?.length).toBe(0);
        },
        2,
        10000,
      );
    });
  });
});
