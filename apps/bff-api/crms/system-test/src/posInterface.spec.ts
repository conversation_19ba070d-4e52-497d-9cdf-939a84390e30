import { DynamodbClient } from '@npco/bff-systemtest-utils';
import { PosInterfacePairStatus } from '@npco/component-dbs-mp-common/dist/types';
import { ConnectionType, DbRecordType } from '@npco/component-dto-core';
import type {
  PosInterfaceDevicePairedEventDto,
  PosInterfaceDeviceUnpairedEventDto,
  PosInterfaceSiteUnpairedEventDto,
} from '@npco/component-dto-pos-interface';
import { PosInterfaceSitePairedEventDto } from '@npco/component-dto-pos-interface';

import { v4 as uuidv4 } from 'uuid';

import { retry } from './utils/retry';
import { getEngineStackName } from './utils/stacks/getEngineStackName';
import { region, sendHighPriorityQueueSqsMessage } from './utils/utils';

const documentClient = new DynamodbClient({ region });

const queryPosInterfaceFromDB = async (id: string, type?: string) => {
  const output = await documentClient.query({
    TableName: getEngineStackName('Entities'),
    KeyConditionExpression: type ? '#id = :id AND begins_with(#type, :type)' : '#id = :id',
    ExpressionAttributeValues: {
      ':id': id,
      ...(type ? { ':type': type } : {}),
    },
    ExpressionAttributeNames: {
      '#id': 'id',
      ...(type ? { '#type': 'type' } : {}),
    },
  });
  return output.Items;
};

describe('Merchant Portal - PosInterface system test suite', () => {
  const createSitePairedEventDto = () =>
    new PosInterfaceSitePairedEventDto({
      pairingUuid: uuidv4(),
      siteUuid: uuidv4(),
      entityUuid: uuidv4(),
      venueId: uuidv4(),
      stationId: uuidv4(),
      venueName: uuidv4(),
      posProvider: ConnectionType.HL_POS,
      timestamp: new Date().toISOString(),
    });

  describe('site pairing events', () => {
    const sitePairedDto = createSitePairedEventDto();
    const expectedSiteFields = {
      id: sitePairedDto.pairingUuid,
      type: `${DbRecordType.POSINTERFACE_PAIR_SITE}${sitePairedDto.siteUuid}`,
      entityUuid: sitePairedDto.entityUuid,
      siteUuid: sitePairedDto.siteUuid,
      venueId: sitePairedDto.venueId,
      stationId: sitePairedDto.stationId,
      provider: sitePairedDto.posProvider,
      deviceUuid: undefined,
      locations: undefined,
      timestamp: sitePairedDto.timestamp,
    };

    it('should project the site paired event', async () => {
      await sendHighPriorityQueueSqsMessage(sitePairedDto.pairingUuid, {
        'detail-type': 'crms.PosInterface.SitePaired',
        detail: sitePairedDto,
      });

      await retry(async () => {
        const queryResponse = await queryPosInterfaceFromDB(sitePairedDto.pairingUuid);
        const item = queryResponse![0];

        expect(item).toEqual({
          ...expectedSiteFields,
          status: PosInterfacePairStatus.ACTIVE,
          version: 0,
        });
      });
    });

    it('should project the site unpaired event', async () => {
      const siteUnpairedDto: PosInterfaceSiteUnpairedEventDto = {
        pairingUuid: sitePairedDto.pairingUuid,
        timestamp: new Date().toISOString(),
        posProvider: ConnectionType.HL_POS,
        siteUuid: sitePairedDto.siteUuid,
      };
      await sendHighPriorityQueueSqsMessage(siteUnpairedDto.pairingUuid, {
        'detail-type': 'crms.PosInterface.SiteUnpaired',
        detail: siteUnpairedDto,
      });

      await retry(async () => {
        const queryResponse = await queryPosInterfaceFromDB(sitePairedDto.pairingUuid);

        expect(queryResponse?.length).toEqual(0);
      });
    });
  });

  describe('device pairing events', () => {
    describe(ConnectionType.HL_POS, () => {
      const devicePairedDto: PosInterfaceDevicePairedEventDto = {
        ...createSitePairedEventDto(),
        deviceUuid: uuidv4(),
        locations: [
          {
            locationId: uuidv4(),
            locationName: uuidv4(),
          },
          {
            locationId: uuidv4(),
            locationName: uuidv4(),
          },
          {
            locationId: uuidv4(),
            locationName: uuidv4(),
          },
        ],
      };
      const expectedDeviceFields = {
        id: devicePairedDto.pairingUuid,
        type: `${DbRecordType.POSINTERFACE_PAIR_DEVICE}${devicePairedDto.deviceUuid}`,
        entityUuid: devicePairedDto.entityUuid,
        siteUuid: devicePairedDto.siteUuid,
        venueId: devicePairedDto.venueId,
        stationId: devicePairedDto.stationId,
        provider: devicePairedDto.posProvider,
        deviceType: expect.any(String),
        deviceUuid: devicePairedDto.deviceUuid,
        locations: devicePairedDto.locations?.map((location) => location.locationId),
        timestamp: devicePairedDto.timestamp,
      };

      it('should project the device paired event', async () => {
        await sendHighPriorityQueueSqsMessage(devicePairedDto.pairingUuid, {
          'detail-type': 'crms.PosInterface.DevicePaired',
          detail: devicePairedDto,
        });

        await retry(async () => {
          const queryResponse = await queryPosInterfaceFromDB(devicePairedDto.pairingUuid);
          const item = queryResponse![0];

          expect(item).toEqual({
            ...expectedDeviceFields,
            status: PosInterfacePairStatus.ACTIVE,
            version: 0,
          });
        });
      });

      it('should project the device unpaired event', async () => {
        const deviceUnpairedDto: PosInterfaceDeviceUnpairedEventDto = {
          pairingUuid: devicePairedDto.pairingUuid,
          deviceUuid: devicePairedDto.deviceUuid,
          timestamp: new Date().toISOString(),
          posProvider: ConnectionType.HL_POS,
        };
        await sendHighPriorityQueueSqsMessage(deviceUnpairedDto.pairingUuid, {
          'detail-type': 'crms.PosInterface.DeviceUnpaired',
          detail: deviceUnpairedDto,
        });

        await retry(async () => {
          const devicePairingType = `${DbRecordType.POSINTERFACE_PAIR_DEVICE}${deviceUnpairedDto.deviceUuid}`;
          const queryResponse = await queryPosInterfaceFromDB(devicePairedDto.pairingUuid, devicePairingType);

          expect(queryResponse?.length).toEqual(0);
        });
      });
    });

    describe(ConnectionType.ORACLE_POS, () => {
      const deviceUuid = uuidv4();
      const devicePairedDto: PosInterfaceDevicePairedEventDto = {
        pairingUuid: deviceUuid,
        siteUuid: uuidv4(),
        entityUuid: uuidv4(),
        stationId: uuidv4(),
        posProvider: ConnectionType.ORACLE_POS,
        timestamp: new Date().toISOString(),
        deviceUuid,
      };

      const expectedDeviceFields = {
        id: devicePairedDto.deviceUuid,
        type: `${DbRecordType.POSINTERFACE_PAIR_DEVICE}${devicePairedDto.deviceUuid}`,
        entityUuid: devicePairedDto.entityUuid,
        siteUuid: devicePairedDto.siteUuid,
        clientId: devicePairedDto.stationId,
        provider: devicePairedDto.posProvider,
        deviceType: expect.any(String),
        deviceUuid: devicePairedDto.deviceUuid,
        timestamp: devicePairedDto.timestamp,
      };

      it('should project the device paired event', async () => {
        await sendHighPriorityQueueSqsMessage(devicePairedDto.pairingUuid, {
          'detail-type': 'crms.PosInterface.DevicePaired',
          detail: devicePairedDto,
        });

        await retry(async () => {
          const queryResponse = await queryPosInterfaceFromDB(devicePairedDto.pairingUuid);
          const item = queryResponse![0];

          expect(item).toEqual({
            ...expectedDeviceFields,
            status: PosInterfacePairStatus.ACTIVE,
            version: 0,
          });
        });
      });

      it('should project the device unpaired event', async () => {
        const deviceUnpairedDto: PosInterfaceDeviceUnpairedEventDto = {
          pairingUuid: devicePairedDto.pairingUuid,
          deviceUuid: devicePairedDto.deviceUuid,
          timestamp: new Date().toISOString(),
          posProvider: ConnectionType.ORACLE_POS,
        };
        await sendHighPriorityQueueSqsMessage(deviceUnpairedDto.pairingUuid, {
          'detail-type': 'crms.PosInterface.DeviceUnpaired',
          detail: deviceUnpairedDto,
        });

        await retry(async () => {
          const devicePairingType = `${DbRecordType.POSINTERFACE_PAIR_DEVICE}${deviceUnpairedDto.deviceUuid}`;
          const queryResponse = await queryPosInterfaceFromDB(devicePairedDto.pairingUuid, devicePairingType);

          expect(queryResponse?.length).toEqual(0);
        });
      });
    });
  });
});
