import { DynamodbClient } from '@npco/bff-systemtest-utils';
import { DbRecordType } from '@npco/component-dto-core';
import {
  DocumentUploadedType,
  TicketDocumentUploadedEventDto,
  TicketSecureDocumentUploadedEventDto,
} from '@npco/component-dto-ticket';

import { v4 as uuidv4 } from 'uuid';

import { bootstrapHubspotAppOauthToken } from './utils/hubspot/bootstrapHubspotOauthToken';
import { getDynamoDbTableName } from './utils/hubspot/getDynamoDbTable';
import { ObjectType } from './utils/hubspot/hubspotProperties';
import { HubspotPublicApp } from './utils/hubspot/hubspotTypes';
import { searchObjectByDomainId, searchRecentEngagements } from './utils/hubspot/hubspotUtil';
import { createCompany } from './utils/simplified-services/createCompany';
import { createContact } from './utils/simplified-services/createContact';
import { createContactEvent } from './utils/simplified-services/createContactEvent';
import { sleep } from './utils/sleep';
import { sendHubspotNotificationSqsMessage } from './utils/sqs/hubspotNotificationSqs';
import { region, sendDocumentScanningSqsMessage, sendHighPriorityQueueSqsMessage } from './utils/utils';

jest.setTimeout(360000);

const testTimelineCustomerUuid = '171a5065-7ed3-48af-8a5f-37f1d54f4531';

beforeAll(async () => {
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.DEFAULT_EXTENSION);
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.DOC_SCANNING);
});

const putItem = async (uuid: string, ticketId: string, fileName: string) => {
  const db = new DynamodbClient({ region });

  const item = {
    id: uuid,
    hubspotTicketId: ticketId,
    fileName,
    type: DbRecordType.TICKET,
    key: uuidv4(),
  };
  await db.put({
    TableName: getDynamoDbTableName(),
    Item: item,
  });
};

const queryTicket = async (uniqueFileUploadReference: string) => {
  const db = new DynamodbClient({ region });
  return db.query({
    TableName: getDynamoDbTableName(),
    KeyConditionExpression: 'id = :uniqueFileUploadReference',
    ExpressionAttributeValues: {
      ':uniqueFileUploadReference': uniqueFileUploadReference,
    },
  });
};

// const insertCustomer = async () => {
//   const db = new DynamodbClient();
//   return db.put({
//     TableName: getDynamodbTableName(),
//     Item: {
//       id: testTimelineCustomerUuid,
//       type: DbRecordType.CUSTOMER,
//       customerUuid: testTimelineCustomerUuid,
//       hubspotContactId: '26633651',
//     },
//   });
// };
// const insertNotificationToken = async () => {
//   const db = new DynamodbClient();
//   const data = {
//     id: 'hubspot:notifications:oauth',
//     type: `hubspot.notifications.token.${new Date().getTime()}`,
//     accessToken: 'accessToken',
//     createdTime: '2023-09-17T21:23:30.031Z',
//     expiresIn: 1800,
//     refreshToken: 'xxx', // go to dynamoDB to get it and fill in manually
//     tokenExpiresIn: 1800,
//     ttlExpiresAt: 1695007410,
//   };
//   return db.put({
//     TableName: getDynamodbTableName(),
//     Item: data,
//   });
// };

describe('hubspot ticket creation test suite', () => {
  it('should be able update a ticket after getting a message from the sqs pipeline', async () => {
    const entityUuid = uuidv4();
    await createCompany(entityUuid);
    const contactEvent = createContactEvent();
    await createContact(contactEvent);

    const event = new TicketDocumentUploadedEventDto({
      ticketUuid: uuidv4(),
      entityUuid,
      fileName: `${uuidv4()}.png`,
      customerUuid: contactEvent.customerUuid,
      message: `crms system test doc - ${uuidv4()}`,
      uniqueFileUploadReference: uuidv4(),
      documentType: DocumentUploadedType.ASSOCIATION_DOCUMENT,
    });
    console.log('doc uploaded event is -> ', event);
    const sqsMessage = {
      'detail-type': 'crms.Ticket.DocumentUploaded',
      detail: event,
    };
    console.log('ticket being sent is -> ', sqsMessage);
    await sendHighPriorityQueueSqsMessage(event.customerUuid as string, sqsMessage);
    await sleep(25000);
    const initialTicket = await searchObjectByDomainId(
      ObjectType.TICKET,
      'z_ticket_group_reference',
      event.uniqueFileUploadReference || '',
    );
    console.log('initialTicket data is -> ', initialTicket.data);
    console.log('initialTicket data props is -> ', initialTicket.data.results[0]?.properties);
    const engagementCreatedTimeFrame = Date.now();
    const ticketId = initialTicket.data.results[0].id;
    const scanGuid = `crms-engine-system-test-${uuidv4()}`;
    const mockFileName = uuidv4();
    await putItem(scanGuid, ticketId || '', mockFileName);
    const documentScanningSqsMessage = {
      Message: JSON.stringify({
        guid: scanGuid,
        scanStatus: 'FINISHED',
        customDisplayStatus: 'Clear',
      }),
    };
    console.log('documentScanningSqsMessage is -> ', documentScanningSqsMessage);
    await sendDocumentScanningSqsMessage(JSON.stringify(documentScanningSqsMessage));
    await sleep(20000);
    const response = await searchObjectByDomainId(
      ObjectType.TICKET,
      'z_ticket_group_reference',
      event.uniqueFileUploadReference || '',
    );
    console.log('new ticket update response data is -> ', response.data);
    console.log('new ticket update response data props is -> ', response.data.results[0]?.properties);
    console.log('engagementCreatedTimeFrame is -> ', engagementCreatedTimeFrame);
    const engagements = await searchRecentEngagements(engagementCreatedTimeFrame);
    console.log('ticketId is -> ', ticketId, typeof ticketId);
    console.log('engagements are -> ', JSON.stringify(engagements.data));
    const createdEngagement = engagements.data.results.find((r: any) =>
      r.associations.ticketIds.includes(parseInt(ticketId, 10)),
    );
    console.log('a is -> ', createdEngagement);
    expect(createdEngagement?.engagement.bodyPreview).toEqual(
      `Fortiro Results: Clear https://zeller.app.protect-staging.fortiro.com/scan-results/${scanGuid} File Name: ${mockFileName}`,
    );
  });

  it('should be able create secure document uploaded ticket and associate it with a company and a pipeline', async () => {
    const entityUuid = uuidv4();
    await createCompany(entityUuid);
    const contactEvent = createContactEvent();
    await createContact(contactEvent);

    const event = new TicketSecureDocumentUploadedEventDto({
      ticketUuid: uuidv4(),
      entityUuid,
      fileName: `${uuidv4()}.png`,
      customerUuid: contactEvent.customerUuid,
      message: `crms system test secure - ${uuidv4()}`,
      uniqueFileUploadReference: uuidv4(),
      documentType: DocumentUploadedType.ID_DOCUMENT,
      consentToViewDocument: true,
    });
    console.log('secure doc uploaded event is -> ', event);
    const sqsMessage = {
      'detail-type': 'crms.Ticket.SecureDocumentUploaded',
      detail: event,
    };
    console.log('secure document being sent is -> ', sqsMessage);
    await sendHighPriorityQueueSqsMessage(event.customerUuid as string, sqsMessage);
    await sleep(25000);
    const response = await searchObjectByDomainId(
      ObjectType.TICKET,
      'z_ticket_group_reference',
      event.uniqueFileUploadReference || '',
    );
    console.log('secure document response is -> ', response);
    const filtedTicket = response.data.results;
    const dbItem = await queryTicket(`${event.uniqueFileUploadReference}-${event.documentType}`);
    console.log('secure document dbItem is -> ', dbItem);
    expect(filtedTicket.length).toBeGreaterThanOrEqual(1);
    expect(dbItem.Items!.length).toBe(1);
  });

  it('should be able create document uploaded ticket and associate it with a company and a pipeline', async () => {
    const entityUuid = uuidv4();
    await createCompany(entityUuid);
    const contactEvent = createContactEvent();
    await createContact(contactEvent);

    const event = new TicketDocumentUploadedEventDto({
      ticketUuid: uuidv4(),
      entityUuid,
      fileName: `${uuidv4()}.png`,
      customerUuid: contactEvent.customerUuid,
      message: `crms system test doc - ${uuidv4()}`,
      uniqueFileUploadReference: uuidv4(),
      documentType: DocumentUploadedType.ASSOCIATION_DOCUMENT,
    });
    console.log('doc uploaded event is -> ', event);
    const sqsMessage = {
      'detail-type': 'crms.Ticket.DocumentUploaded',
      detail: event,
    };
    console.log('ticket being sent is -> ', sqsMessage);
    await sendHighPriorityQueueSqsMessage(event.customerUuid as string, sqsMessage);
    await sleep(25000);
    const response = await searchObjectByDomainId(
      ObjectType.TICKET,
      'z_ticket_group_reference',
      event.uniqueFileUploadReference || '',
    );
    console.log('response is -> ', response);
    const filtedTicket = response.data.results;
    const dbItem = await queryTicket(`${event.uniqueFileUploadReference}-${event.documentType}`);
    console.log('dbItem is -> ', dbItem);
    expect(filtedTicket.length).toBeGreaterThanOrEqual(1);
    expect(dbItem.Items!.length).toBe(1);
  });

  it.skip('should be able to create timeline events', async () => {
    // can only verify the event manually in dev environment as the credentials do not exist in system test account
    // we find the zeller customerUuid and hubspot customer id, update the variable and run this test
    // wait for 10 seconds and go to hubspot contact page to see if the notifications are created successfully
    // await insertNotificationToken();
    // await insertCustomer();
    const sqsMessage = [
      {
        customerUuid: testTimelineCustomerUuid,
        sentTime: new Date().toISOString(),
        customerChannels: ['SMS', 'Email'],
        payload: {
          header: 'dev system test header',
          body: `body system test`,
        },
      },
    ];
    await sendHubspotNotificationSqsMessage(JSON.stringify(sqsMessage));
    expect(true).toBeTruthy();
  });
});
