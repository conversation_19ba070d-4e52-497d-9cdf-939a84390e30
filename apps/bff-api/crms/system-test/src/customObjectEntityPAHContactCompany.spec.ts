import { DynamodbClient, describeIf, retry, sleep } from '@npco/bff-systemtest-utils';
import { CustomerRole, DbRecordType } from '@npco/component-dto-core';
import { EntityPrimaryAccountHolderChangedEventDto } from '@npco/component-dto-entity';
import { bootstrapHubspotAppOauthToken } from 'crms-engine-system-test/src/utils/hubspot/bootstrapHubspotOauthToken';
import { getAssociations, searchObjectByDomainIdFilters } from 'crms-engine-system-test/src/utils/hubspot/hubspotUtil';
import { createCompany } from 'crms-engine-system-test/src/utils/simplified-services/createCompany';
import { createContact } from 'crms-engine-system-test/src/utils/simplified-services/createContact';
import { createContactEvent } from 'crms-engine-system-test/src/utils/simplified-services/createContactEvent';

import { v4 as uuidv4 } from 'uuid';

import { getDynamoDbTableName } from './utils/hubspot/getDynamoDbTable';
import { ObjectType } from './utils/hubspot/hubspotProperties';
import { HubspotPublicApp } from './utils/hubspot/hubspotTypes';
import { region, sendHighPriorityQueueSqsMessage } from './utils/utils';

beforeAll(async () => {
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.DEFAULT_EXTENSION);
  await bootstrapHubspotAppOauthToken(HubspotPublicApp.CUSTOM_OBJECT);
});

describeIf(process.env.MULTI_ENTITY_ENABLED === 'true', 'Contact-Companies Custom Object', () => {
  const entityUuid = uuidv4();
  const currentPrimaryAccountHolderCustomerUuid = uuidv4();
  const NewPrimaryAccountHolderCustomerUuid = uuidv4();
  let currentAccountHolderHubspotObjectId: string;
  let newAccountHolderHubspotObjectId: string;
  const db = new DynamodbClient({ region });

  const currentPrimaryAccountHolderCreatedContactDto = {
    ...createContactEvent(),
    customerUuid: currentPrimaryAccountHolderCustomerUuid,
    entityUuid,
    firstname: `system-test-current-account-holder-${currentPrimaryAccountHolderCustomerUuid}`,
    ceo: true,
    secretary: true,
    beneficialOwner: true,
    beneficialOwnerAlt: true,
    beneficiary: true,
    partner: true,
    trustee: true,
    settlor: true,
    generalContact: true,
    financialContact: true,
    chair: true,
    treasurer: true,
    role: CustomerRole.ADMIN,
    governmentRole: 'governmentRole',
    primaryAccountHolder: true,
    isInvitationPending: false,
    registeringIndividual: false,
  };

  const newAccountHolderCreatedContactDto = {
    ...createContactEvent(),
    customerUuid: NewPrimaryAccountHolderCustomerUuid,
    entityUuid,
    firstname: `system-test-new-account-holder-${NewPrimaryAccountHolderCustomerUuid}`,
    ceo: true,
    secretary: true,
    beneficialOwner: true,
    beneficialOwnerAlt: true,
    beneficiary: true,
    partner: true,
    trustee: true,
    settlor: true,
    generalContact: true,
    financialContact: true,
    chair: true,
    treasurer: true,
    role: CustomerRole.ADMIN,
    governmentRole: 'governmentRole',
    primaryAccountHolder: false,
    isInvitationPending: false,
    registeringIndividual: false,
  };

  const entityPrimaryAccountHolderChangedEventDto = new EntityPrimaryAccountHolderChangedEventDto({
    entityUuid,
    primaryAccountHolder: NewPrimaryAccountHolderCustomerUuid,
    previousPrimaryAccountHolder: currentPrimaryAccountHolderCustomerUuid,
  });

  const queryCustomerEntity = async (id: string, entityId: string) => {
    console.log(
      `query customer entity id ${id} of type ${DbRecordType.CUSTOMER_ENTITY} in table ${getDynamoDbTableName()}`,
    );
    return db.query({
      TableName: getDynamoDbTableName(),
      KeyConditionExpression: 'id = :id AND begins_with(#type, :type)',
      ExpressionAttributeNames: {
        '#type': 'type',
      },
      ExpressionAttributeValues: {
        ':id': id,
        ':type': `${DbRecordType.CUSTOMER_ENTITY}${entityId}`,
      },
    });
  };

  const queryEntity = async (id: string) => {
    console.log(`query entity id ${id} of type ${DbRecordType.ENTITY} in table ${getDynamoDbTableName()}`);
    return db.query({
      TableName: getDynamoDbTableName(),
      KeyConditionExpression: 'id = :id AND #type = :type',
      ExpressionAttributeNames: {
        '#type': 'type',
      },
      ExpressionAttributeValues: {
        ':id': id,
        ':type': DbRecordType.ENTITY,
      },
    });
  };

  describe('Entity.PrimarAccountHolderChange', () => {
    let associatedCompanies: any;
    let associatedContacts: any;

    beforeAll(async () => {
      await createCompany(entityUuid);

      await Promise.all([
        await createContact(currentPrimaryAccountHolderCreatedContactDto),
        await createContact(newAccountHolderCreatedContactDto),
      ]);

      await sleep(5000);

      const sqsMessage = {
        'detail-type': 'crms.Entity.PrimaryAccountHolderChanged',
        detail: entityPrimaryAccountHolderChangedEventDto,
      };
      await sendHighPriorityQueueSqsMessage(entityPrimaryAccountHolderChangedEventDto.entityUuid, sqsMessage);

      await sleep(5000);
    });

    describe('Current Primary Account Holder', () => {
      it('should be able to update Hubspot contactCompanies custom object and remove primary account holder properties', async () => {
        await retry(
          async () => {
            const hubspotObject = (
              await searchObjectByDomainIdFilters(ObjectType.CONTACT_COMPANY, [
                {
                  propertyName: 'contact_uuid',
                  value: currentPrimaryAccountHolderCustomerUuid,
                  operator: 'EQ',
                },
                {
                  propertyName: 'company_uuid',
                  value: entityUuid,
                  operator: 'EQ',
                },
              ])
            ).data;
            console.log('search contactCompany object result -> ', JSON.stringify(hubspotObject));

            expect(hubspotObject.results.length).toBeGreaterThanOrEqual(1);
            const contactCompanyObject = hubspotObject.results[0];
            expect(contactCompanyObject.properties.contact_uuid).toBe(currentPrimaryAccountHolderCustomerUuid);
            expect(contactCompanyObject.properties.company_uuid).toBe(entityUuid);
            expect(contactCompanyObject.properties.government_role).toBe(
              currentPrimaryAccountHolderCreatedContactDto.governmentRole,
            );
            expect(contactCompanyObject.properties.invitation_status).toBe('Accepted');
            expect(contactCompanyObject.properties.primary_account_holder).toBe(`false`);
            expect(contactCompanyObject.properties.role).toBe('role:admin');
            expect(contactCompanyObject.properties.kyc_idv_relationship_to_company).toBeDefined();

            currentAccountHolderHubspotObjectId = contactCompanyObject.id;

            expect(currentAccountHolderHubspotObjectId).toBeDefined();
          },
          2,
          10000,
        );
      });

      it('should be able to associate custom object with companies, contacts and labels WHITHOUT primary_account_holder', async () => {
        await retry(
          async () => {
            associatedCompanies = (
              await getAssociations(ObjectType.CONTACT_COMPANY, currentAccountHolderHubspotObjectId, ObjectType.COMPANY)
            ).data;
            associatedContacts = (
              await getAssociations(ObjectType.CONTACT_COMPANY, currentAccountHolderHubspotObjectId, ObjectType.CONTACT)
            ).data;

            console.log('associated companies ->', JSON.stringify(associatedCompanies));
            console.log('associated contacts ->', JSON.stringify(associatedContacts));

            expect(associatedContacts.results[0].type).toBe('contact_to_contactcompanies');
            expect(associatedCompanies.results).toEqual(
              expect.arrayContaining([{ id: expect.any(String), type: 'company_to_contactcompanies' }]),
            );
            expect(associatedCompanies.results).toEqual(
              expect.not.arrayContaining([{ id: expect.any(String), type: 'primary_account_holder' }]),
            );
          },
          2,
          10000,
        );
      });

      it('should be able to materialise custom object item in materialised view', async () => {
        await retry(
          async () => {
            const dbItem = await queryCustomerEntity(currentPrimaryAccountHolderCustomerUuid, entityUuid);
            console.log('customer entity db item is -> ', JSON.stringify(dbItem));
            expect(dbItem.Items?.length).toBe(1);
            expect(dbItem.Items?.[0].type).toEqual(`${DbRecordType.CUSTOMER_ENTITY}${entityUuid}`);
            expect(dbItem.Items?.[0].hubspotObjectId).toEqual(currentAccountHolderHubspotObjectId);
            expect(dbItem.Items?.[0].hubspotProperties.length).toBeGreaterThan(0);
            expect(dbItem.Items?.[0].hubspotAssociations.companyAssociated).toBe(associatedCompanies.results[0].id);
            expect(dbItem.Items?.[0].hubspotAssociations.contactAssociated).toBe(associatedContacts.results[0].id);
          },
          2,
          10000,
        );
      });
    });

    describe('New Primary Account Holder', () => {
      it('should be able to update Hubspot contactCompanies custom object for previous Primary Account Hoder', async () => {
        await retry(
          async () => {
            const hubspotObject = (
              await searchObjectByDomainIdFilters(ObjectType.CONTACT_COMPANY, [
                {
                  propertyName: 'contact_uuid',
                  value: NewPrimaryAccountHolderCustomerUuid,
                  operator: 'EQ',
                },
                {
                  propertyName: 'company_uuid',
                  value: entityUuid,
                  operator: 'EQ',
                },
              ])
            ).data;
            console.log('search contactCompany object result -> ', JSON.stringify(hubspotObject));

            expect(hubspotObject.results.length).toBeGreaterThanOrEqual(1);
            const contactCompanyObject = hubspotObject.results[0];
            expect(contactCompanyObject.properties.contact_uuid).toBe(NewPrimaryAccountHolderCustomerUuid);
            expect(contactCompanyObject.properties.company_uuid).toBe(entityUuid);
            expect(contactCompanyObject.properties.government_role).toBe(
              newAccountHolderCreatedContactDto.governmentRole,
            );
            expect(contactCompanyObject.properties.invitation_status).toBe('Accepted');
            expect(contactCompanyObject.properties.primary_account_holder).toBe('true');
            expect(contactCompanyObject.properties.role).toBe('role:admin');
            expect(contactCompanyObject.properties.kyc_idv_relationship_to_company).toBeDefined();

            newAccountHolderHubspotObjectId = contactCompanyObject.id;

            expect(newAccountHolderHubspotObjectId).toBeDefined();
          },
          2,
          10000,
        );
      });

      it('should be able to associate custom object with companies, contacts and labels WITH primary_account_holder', async () => {
        await retry(
          async () => {
            associatedCompanies = (
              await getAssociations(ObjectType.CONTACT_COMPANY, newAccountHolderHubspotObjectId, ObjectType.COMPANY)
            ).data;
            associatedContacts = (
              await getAssociations(ObjectType.CONTACT_COMPANY, newAccountHolderHubspotObjectId, ObjectType.CONTACT)
            ).data;

            console.log('associated companies ->', JSON.stringify(associatedCompanies));
            console.log('associated contacts ->', JSON.stringify(associatedContacts));

            expect(associatedContacts.results[0].type).toBe('contact_to_contactcompanies');
            expect(associatedCompanies.results).toEqual(
              expect.arrayContaining([{ id: expect.any(String), type: 'company_to_contactcompanies' }]),
            );
          },
          2,
          10000,
        );
      });

      it('should be able to materialise custom object item in materialised view', async () => {
        await retry(
          async () => {
            const dbItem = await queryCustomerEntity(NewPrimaryAccountHolderCustomerUuid, entityUuid);
            console.log('customer entity db item is -> ', JSON.stringify(dbItem));
            expect(dbItem.Items?.length).toBe(1);
            expect(dbItem.Items?.[0].type).toEqual(`${DbRecordType.CUSTOMER_ENTITY}${entityUuid}`);
            expect(dbItem.Items?.[0].hubspotObjectId).toEqual(newAccountHolderHubspotObjectId);
            expect(dbItem.Items?.[0].hubspotProperties.length).toBeGreaterThan(0);
            expect(dbItem.Items?.[0].hubspotAssociations.companyAssociated).toBe(associatedCompanies.results[0].id);
            expect(dbItem.Items?.[0].hubspotAssociations.contactAssociated).toBe(associatedContacts.results[0].id);
          },
          2,
          10000,
        );
      });

      it('should be able to materialise entity.core with new primary account holder', async () => {
        await retry(
          async () => {
            const dbItem = await queryEntity(entityUuid);
            console.log('entity db item is -> ', JSON.stringify(dbItem));
            expect(dbItem.Items?.length).toBe(1);
            expect(dbItem.Items?.[0].type).toEqual(DbRecordType.ENTITY);
            expect(dbItem.Items?.[0].primaryAccountHolder).toEqual(NewPrimaryAccountHolderCustomerUuid);
          },
          2,
          10000,
        );
      });
    });
  });
});
