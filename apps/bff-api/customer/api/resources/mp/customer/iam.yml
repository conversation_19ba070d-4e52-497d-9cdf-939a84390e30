Resources:
  getCustomersRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getCustomersHandlerDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}/index/${self:provider.siteGsi}'
        - PolicyName: ${self:provider.stackName}-getCustomersHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getCustomersHandler:*'

  updateCustomerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-updateCustomerHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-updateCustomerHandler:*'

  sendInviteEmailRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-sendInviteEmailHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-sendInviteEmailHandler:*'

  requestEmailChangeRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-firebaseAdminParameterPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-requestEmailChangeHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-requestEmailChangeHandler:*'

  completeEmailChangeRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-completeEmailChangeHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-completeEmailChangeHandler:*'

  getCustomerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getCustomerHandlerDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}/index/${self:provider.siteGsi}'
        - PolicyName: ${self:provider.stackName}-getCustomerHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getCustomerHandler:*'

  getMyPersonalInfoRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getMyPersonalInfoLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getMyPersonalInfoHandler:*'

  getCustomerSitesRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getCustomerSitesHandlerDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}/index/${self:provider.siteGsi}'
              - Effect: Allow
                Action:
                  - dynamodb:BatchGetItem
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}'
        - PolicyName: ${self:provider.stackName}-getCustomerSitesHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getCustomerSitesHandler:*'

  getReferralCodeRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getReferralCodeHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getReferralCodeHandler:*'

  createCustomerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-createCustomerHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-createCustomerHandler:*'

  deleteCustomerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-deleteCustomerHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-deleteCustomerHandler:*'

  customerProjectionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-customerProjectionLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:*:log-group:/aws/lambda/${self:provider.stackName}-customerProjectionHandler:*'
        - PolicyName: ${self:provider.stackName}-customerProjectionSqsPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 'sqs:SendMessage'
                Resource: ${self:custom.mpCqrsProjectionDLQArn}
        - PolicyName: ${self:provider.stackName}-projectionDBPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}/index/${self:provider.siteGsi}'
              - Effect: Allow
                Action:
                  - dynamodb:BatchGetItem
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}'
              - Effect: Allow
                Action:
                  - dynamodb:DeleteItem
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}'

  responseCustomerIDVRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-responseCustomerIDVHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-responseCustomerIDVHandler:*'

  verifyCustomerDocumentsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-customerIDVSubLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:*:log-group:/aws/lambda/${self:provider.stackName}-verifyCustomerDocumentsHandler:*'

  requestCustomerDocumentVerificationRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-requestCustomerIDVInvokeLambdaPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - lambda:InvokeFunction
                Resource:
                  - 'arn:aws:lambda:${self:provider.region}:*:function:${self:custom.mpCqrsCommandHandler}*'
        - PolicyName: ${self:provider.stackName}-requestCustomerIDVHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:*:log-group:/aws/lambda/${self:provider.stackName}-requestCustomerIDVHandler:*'

  getDocumentUploadUrlRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getDocumentUploadUrlS3Policy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 's3:PutObject'
                Resource: 'arn:aws:s3:::${self:custom.documentUploadsBucket}/*'
        - PolicyName: ${self:provider.stackName}-getDocumentUploadUrlLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${opt:region}:*:log-group:/aws/lambda/${self:provider.stackName}-getDocumentUploadUrlHandler:*'

  onIconUploadsBucketPermission:
    Type: AWS::Lambda::Permission
    DependsOn:
      - OnIconUploadLambdaFunction
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: ${self:provider.stackName}-onIconUpload
      Principal: s3.amazonaws.com
      SourceAccount: ${self:provider.accountId}
      SourceArn: !Sub 'arn:aws:s3:::${self:custom.customerIconTemporaryBucket}'

  onIconUploadRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-onIconUploadTempS3Policy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 's3:GetObject'
                  - 's3:DeleteObject'
                Resource:
                  - 'arn:aws:s3:::${self:custom.customerIconTemporaryBucket}'
                  - 'arn:aws:s3:::${self:custom.customerIconTemporaryBucket}/*'
        - PolicyName: ${self:provider.stackName}-onIconUploadProcessedS3Policy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 's3:PutObject'
                  - 's3:DeleteObject'
                Resource:
                  - 'arn:aws:s3:::${self:custom.customerIconProcessedBucket}'
                  - 'arn:aws:s3:::${self:custom.customerIconProcessedBucket}/*'
        - PolicyName: ${self:provider.stackName}-onIconUploadLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${opt:region}:*:log-group:/aws/lambda/${self:provider.stackName}-onIconUpload:*'

  removeIconRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-removeIconS3Policy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 's3:DeleteObject'
                Resource:
                  - 'arn:aws:s3:::${self:custom.customerIconProcessedBucket}/*'
        - PolicyName: ${self:provider.stackName}-removeIconLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${opt:region}:*:log-group:/aws/lambda/${self:provider.stackName}-removeIconHandler:*'

  getIconUploadFormRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getIconUploadFormS3Policy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 's3:PutObject'
                Resource:
                  - 'arn:aws:s3:::${self:custom.customerIconTemporaryBucket}/*'
        - PolicyName: ${self:provider.stackName}-getIconUploadFormPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${opt:region}:*:log-group:/aws/lambda/${self:provider.stackName}-getIconUploadFormHandler:*'

  finaliseCustomerKYCRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-finaliseCustomerKYCHandlerDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}/index/${self:provider.siteGsi}'
        - PolicyName: ${self:provider.stackName}-finaliseCustomerKYCLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-finaliseCustomerKYCHandler:*'

  generateBVCheckTokenRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-generateBVCheckTokenHandlerDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}/index/${self:provider.siteGsi}'
        - PolicyName: ${self:provider.stackName}-generateBVCheckTokenLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-generateBVCheckTokenHandler:*'

  requireBVCheckRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-requireBVCheckHandlerDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}/index/${self:provider.siteGsi}'
        - PolicyName: ${self:provider.stackName}-requireBVCheckLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-requireBVCheckHandler:*'

  createBVCheckRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-createBVCheckHandlerDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}/index/${self:provider.siteGsi}'
        - PolicyName: ${self:provider.stackName}-createBVCheckLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-createBVCheckHandler:*'

  updateKycCheckpointRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-updateKycCheckpointHandlerDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}/index/${self:provider.siteGsi}'
        - PolicyName: ${self:provider.stackName}-updateKycCheckpointLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-updateKycCheckpointHandler:*'

  getCustomerEntityMappingHandlerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getCustomersHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getCustomerEntityMapping:*'

  updateCustomerEntityMappingHandlerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getCustomersHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-updateCustomerEntityMapping:*'

  updateVisibleTabsHandlerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getCustomersHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-updateVisibleTabs:*'

  createRegisteringIndividualRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-createRegisteringIndividualHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-createRegisteringIndividualHandler:*'
