import { ServerlessFunctions, Arn, Action, ManagedPolicy } from '@npco/component-bff-serverless';
import { lambdaCommon } from '../../common';

export const lambdas: ServerlessFunctions = {
  getMyPersonalInfoHandler: {
    handler: 'src/lambda/crms/lambdas.getMyPersonalInfoHandler',
    name: 'getMyPersonalInfoHandler',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      MULTI_ENTITY_ENABLED: '${self:custom.multiEntityEnabled}',
    },
    appsync: {
      fieldName: 'getMyPersonalInfo',
      typeName: 'Query',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
      inline: {
        getCustomerHandlerDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.siteGsi}')],
          },
        ],
      },
    },
  },
  getCustomerHandler: {
    handler: 'src/lambda/crms/lambdas.getCustomerHandler',
    name: 'getCustomer',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      MULTI_ENTITY_ENABLED: '${self:custom.multiEntityEnabled}',
    },
    appsync: {
      fieldName: 'getCustomer',
      typeName: 'Query',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
      inline: {
        getCustomerHandlerDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.siteGsi}')],
          },
        ],
      },
    },
  },
  getCustomersHandler: {
    handler: 'src/lambda/crms/lambdas.getCustomersHandler',
    name: 'getCustomers',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      MULTI_ENTITY_ENABLED: '${self:custom.multiEntityEnabled}',
    },
    appsync: {
      fieldName: 'getCustomers',
      typeName: 'Query',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
      inline: {
        getCustomersHandlerDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.siteGsi}')],
          },
        ],
      },
    },
  },
  getCustomerSitesHandler: {
    handler: 'src/lambda/crms/lambdas.getCustomerSitesHandler',
    name: 'getCustomerSites',
    ...lambdaCommon,
    environment: { COMPONENT_TABLE: '${self:custom.componentTableName}' },
    appsync: {
      fieldName: 'sites',
      typeName: 'Customer',
      maxBatchSize: 25,
      kind: 'UNIT',
      templateCodePath: 'dist/templates/getCustomerSitesResolver.mjs',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
      inline: {
        getCustomerSitesHandlerDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.siteGsi}')],
          },
          {
            actions: [Action.dynamodb.BatchGetItem],
            resources: [Arn.dynamodb.table('${self:custom.entityTableName}')],
          },
        ],
      },
    },
  },
  createCustomerHandler: {
    handler: 'src/lambda/crms/lambdas.createCustomerHandler',
    name: 'createCustomer',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
      AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
      OPENID_ISSUER_URL: '${self:custom.auth0IssuerUrl}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      GROWSURF_API_ENDPOINT: '${self:custom.growSurfApiEndpoint}',
      GROWSURF_API_KEY: '${self:custom.growSurfApiKey}',
      GROWSURF_CAMPAIGN_ID: '${self:custom.growSurfCampaignId}',
    },
    appsync: {
      fieldName: 'createCustomer',
      typeName: 'Mutation',
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  updateCustomerHandler: {
    handler: 'src/lambda/crms/lambdas.updateCustomerHandler',
    name: 'updateCustomer',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'updateCustomer',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  sendInviteEmailHandler: {
    handler: 'src/lambda/crms/lambdas.sendInviteEmailHandler',
    name: 'sendInviteEmail',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'sendInviteEmail',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  updateCustomerPasswordHandler: {
    handler: 'src/lambda/crms/lambdas.updateCustomerPasswordHandler',
    name: 'updateCustomerPassword',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      LOG_LEVEL: 'warn',
    },
    appsync: {
      fieldName: 'updateCustomerPassword',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  createResetPasswordLinkHandler: {
    handler: 'src/lambda/crms/lambdas.createResetPasswordLinkHandler',
    name: 'createResetPasswordLink',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'createResetPasswordLink',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  getCustomerDocumentUrlsHandler: {
    handler: 'src/lambda/crms/crmsLambdas.getCustomerDocumentUrlsHandler',
    name: 'getCustomerDocumentUrls',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      S3_ENTITY_DOCUMENT_UPLOADS: '${self:custom.documentUploadsBucket}',
    },
    appsync: {
      fieldName: 'getCustomerDocumentUrls',
      typeName: 'Query',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
      inline: {
        customerListBucketS3Policy: [
          {
            actions: [Action.s3.ListBucket],
            resources: [Arn.s3('${self:custom.documentUploadsBucket}')],
          },
        ],
        customerGetDocumentUrlS3Policy: [
          {
            actions: [Action.s3.GetObject, 's3:HeadObject'],
            resources: [Arn.s3('${self:custom.documentUploadsBucket}/*')],
          },
        ],
      },
    },
  },
  getDecryptedCustomerDocsHandler: {
    handler: 'src/lambda/crms/crmsLambdas.getDecryptedCustomerDocsHandler',
    name: 'getDecryptedCustomerDocs',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      DOCUMENT_ENCRYPTION_KEY_ARN: '${self:custom.documentEncryptionKeyArn}',
    },
    appsync: {
      fieldName: 'getDecryptedCustomerDocs',
      typeName: 'Query',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
      inline: {
        customerKmsDecryptPolicy: [
          {
            actions: [Action.kms.Decrypt],
            resources: ['${self:custom.documentEncryptionKeyArn}'],
          },
        ],
      },
    },
  },
  deleteCustomerHandler: {
    handler: 'src/lambda/crms/lambdas.deleteCustomerHandler',
    name: 'deleteCustomer',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'deleteCustomer',
      typeName: 'Mutation',
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  getDocumentUploadUrlHandler: {
    handler: 'src/lambda/crms/lambdas.getCustomerDocumentUploadUrlHandler',
    name: 'getDocumentUploadUrl',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      S3_ENTITY_DOCUMENT_UPLOADS: '${self:custom.documentUploadsBucket}',
    },
    appsync: {
      fieldName: 'getCustomerDocumentUploadUrl',
      typeName: 'Query',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
      inline: {
        getDocumentUploadUrlS3Policy: [
          {
            actions: [Action.s3.PutObject],
            resources: [Arn.s3('${self:custom.documentUploadsBucket}/*')],
          },
        ],
      },
    },
  },
  updateCustomerPhoneHandler: {
    handler: 'src/lambda/crms/lambdas.updateCustomerPhoneHandler',
    name: 'updateCustomerPhone',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      LOG_LEVEL: 'warn',
    },
    appsync: {
      fieldName: 'updateCustomerPhone',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  verifyCustomerPhoneHandler: {
    handler: 'src/lambda/crms/lambdas.verifyCustomerPhoneHandler',
    name: 'verifyCustomerPhone',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      LOG_LEVEL: 'warn',
    },
    appsync: {
      fieldName: 'verifyCustomerPhone',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  getCustomerEntityMappingHandler: {
    handler: 'src/lambda/crms/lambdas.getCustomerEntityMappingHandler',
    name: 'getCustomerEntityMapping',
    ...lambdaCommon,
    environment: { COMPONENT_TABLE: '${self:custom.componentTableName}' },
    appsync: {
      fieldName: 'getCustomerEntityMapping',
      typeName: 'Query',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
    },
  },
  getCustomerRelatedEntitiesHandler: {
    handler: 'src/lambda/crms/lambdas.getCustomerRelatedEntitiesHandler',
    name: 'getCustomerRelatedEntities',
    ...lambdaCommon,
    environment: { COMPONENT_TABLE: '${self:custom.componentTableName}' },
    appsync: {
      fieldName: 'relatedEntities',
      typeName: 'Customer',
      templateCodePath: 'dist/templates/getCustomerRelatedEntitiesResolver.mjs',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
      inline: {
        getCustomerRelatedEntitiesHandlerDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.siteGsi}')],
          },
          {
            actions: [Action.dynamodb.BatchGetItem],
            resources: [Arn.dynamodb.table('${self:custom.entityTableName}')],
          },
        ],
      },
    },
  },
  getCustomerPahDetailsHandler: {
    handler: 'src/lambda/crms/lambdas.getCustomerHandler',
    name: 'getCustomerPahDetails',
    ...lambdaCommon,
    environment: { COMPONENT_TABLE: '${self:custom.componentTableName}' },
    appsync: {
      fieldName: 'pahCustomer',
      typeName: 'Entity',
      templateCodePath: 'dist/templates/getCustomerPahDetailsResolver.mjs',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
      inline: {
        getCustomerPahDetailsHandlerHandlerDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.siteGsi}')],
          },
          {
            actions: [Action.dynamodb.BatchGetItem],
            resources: [Arn.dynamodb.table('${self:custom.entityTableName}')],
          },
        ],
      },
    },
  },
};
