Resources:
  getMyPersonalInfoRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getMyPersonalInfoLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getMyPersonalInfo:*'

  updateCustomerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-customerUpdatedInvokeLambdaPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - lambda:InvokeFunction
                Resource:
                  - 'arn:aws:lambda:${self:provider.region}:*:function:${self:provider.dbsCqrsCommandHandler}*'
        - PolicyName: ${self:provider.stackName}-updateCustomerHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-updateCustomerHandler:*'

  getCustomerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getCustomerHandlerDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.deviceTableName}/index/${self:provider.siteGsi}'
        - PolicyName: ${self:provider.stackName}-getCustomerHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getCustomerHandler:*'

  getCustomerSitesRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getCustomerSitesHandlerDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.deviceTableName}/index/${self:provider.siteGsi}'
              - Effect: Allow
                Action:
                  - dynamodb:BatchGetItem
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.deviceTableName}'
        - PolicyName: ${self:provider.stackName}-getCustomerSitesHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getCustomerSitesHandler:*'

  getCustomersRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getCustomersHandlerDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.deviceTableName}/index/${self:provider.siteGsi}'
        - PolicyName: ${self:provider.stackName}-getCustomersHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getCustomersHandler:*'

  createCustomerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-customerCreateInvokeLambdaPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - lambda:InvokeFunction
                Resource:
                  - 'arn:aws:lambda:${self:provider.region}:*:function:${self:provider.dbsCqrsCommandHandler}*'
        - PolicyName: ${self:provider.stackName}-createCustomerHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-createCustomerHandler:*'

  deleteCustomerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-deleteCustomerHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-deleteCustomerHandler:*'

  customerProjectionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-customerProjectionLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${opt:region}:*:log-group:/aws/lambda/${self:provider.stackName}-customerProjectionHandler:*'
        - PolicyName: ${self:provider.stackName}-eventBridgeDomainProjectionHandlerDLQPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 'sqs:SendMessage'
                Resource: ${self:custom.dbsCqrsProjectionDLQArn}
        - PolicyName: ${self:provider.stackName}-projectionDBPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.deviceTableName}/index/${self:provider.siteGsi}'
              - Effect: Allow
                Action:
                  - dynamodb:BatchGetItem
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.deviceTableName}'
              - Effect: Allow
                Action:
                  - dynamodb:DeleteItem
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.deviceTableName}'

  getCustomerEntityMappingHandlerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-crossAccountInvocationPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getCustomersHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getCustomerEntityMapping:*'
