service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-customer

plugins:
  - serverless-esbuild
  - serverless-dotenv-plugin
  - serverless-plugin-tracing
  - serverless-plugin-resource-tagging
  - serverless-pseudo-parameters
  - serverless-plugin-lambda-dead-letter
  - serverless-prune-plugin

useDotenv: true
variablesResolutionMode: ********
frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region}
  accountId: '#{AWS::AccountId}'
  vpc:
    securityGroupIds:
      - 'Fn::ImportValue': '${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg'
    subnetIds:
      - 'Fn::ImportValue': '${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01'
      - 'Fn::ImportValue': '${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02'
      - 'Fn::ImportValue': '${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03'
  stackName: ${self:service}
  serviceName: ${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}
  service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  layers:
    - arn:aws:lambda:${opt:region}:${self:custom.lambdaPararameterExtensionAccountId}:layer:AWS-Parameters-and-Secrets-Lambda-Extension:11
  timeout: ${env:LAMBDA_TIMEOUT_IN_SECONDS}
  dynamodbStackName: ${self:provider.serviceName}-dynamodb
  appsyncStackName: ${self:provider.service}-appsync
  entityTableName: ${self:provider.dynamodbStackName}-${env:COMPONENT_TABLE}
  mpCqrsStackName: ${opt:stage}-mp-cqrs
  sessionCacheTableName: ${self:provider.dynamodbStackName}-${env:SESSION_CACHE_TABLE}
  auth0Tenant: ${env:IDENTITY_AUTH0_TENANT}
  auth0IssuerUrl: ${env:OPENID_ISSUER_URL}
  entityGsi: ${env:ENTITY_GSI}
  entityCacheGsi: ${env:ENTITY_CACHE_GSI}
  siteGsi: ${env:SITE_GSI}
  amsApiEndpoint: ${ssm:${opt:stage}-ams-engine-api-endpoint, 'http://localhost:3000'}
  amsApiEndpointVersion: ${env:AMS_API_ENDPOINT_VERSION}
  crmsApiEndpoint: ${env:CRMS_API_ENDPOINT}
  crmsApiEndpointVersion: ${env:CRMS_API_ENDPOINT_VERSION}
  appSyncApiId:
    Fn::ImportValue: !Sub '${self:provider.appsyncStackName}-graphQlApiId'
  iamUserKey: ${ssm:/aws/reference/secretsmanager/${self:provider.service}/IAM_USER_KEY}
  iamUserSecret: ${ssm:/aws/reference/secretsmanager/${self:provider.service}/IAM_USER_SECRET}
  bucketStackName: ${env:COMPONENT_NAME}-${env:PART_NAME}-iac-s3
  deploymentBucket:
    name: ${cf:${self:provider.bucketStackName}.deploymentBucket}
    maxPreviousDeploymentArtifacts: 100
    blockPublicAccess: true
  auth0ClientId: ${ssm:/${self:provider.serviceName}/AUTH0_CLIENT_ID}
  auth0ClientSecret: ${ssm:/${self:provider.serviceName}/AUTH0_CLIENT_SECRET}
  selfieVerificationApiEndpoint: ${ssm:${opt:stage}-os-engine-private-api-endpoint, ''}
  selfieVerificationApiEndpointVersion: ${env:SELFIE_CHECK_VERIFICATION_ENDPOINT_VERSION}
  selfieVerificationEnabled: ${env:SELFIE_CHECK_VERIFICATION_ENABLED}
  multiEntityEnabled: ${env:MULTI_ENTITY_ENABLED}
  enableFinaliseKycUplift: '${env:ENABLE_FINALISE_KYC_UPLIFT}'

  environment:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    AUTH0_CLIENT_ID: ${self:provider.auth0ClientId}
    AUTH0_CLIENT_SECRET: ${self:provider.auth0ClientSecret}
    CQRS_COMMAND_HANDLER: ${self:custom.mpCqrsCommandHandler}
    IS_ZELLER_SESSION_ID_ENABLED: ${env:IS_ZELLER_SESSION_ID_ENABLED}
    SESSION_CACHE_TABLE: ${self:provider.sessionCacheTableName}
    COMPONENT_TABLE: ${self:provider.entityTableName}
    ZELLER_APP_FIREBASE_ADMIN_PRIVATE_KEY_SSM_NAME: ${self:custom.firebaseAdminPrivateKeySsmName}
    ZELLER_APP_FIREBASE_ADMIN_EMAIL_SSM_NAME: ${self:custom.firebaseAdminEmailSsmName}
    IS_RBAC_ENFORCED: ${env:IS_RBAC_ENFORCED}
    IS_RBAC_ENFORCE_ROLE: ${env:IS_RBAC_ENFORCE_ROLE}
    GLOBAL_ACCOUNT_ID: '${env:SYDNEY_ACCOUNT_ID}'
  tags:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    service: ${env:COMPONENT_NAME}-${env:PART_NAME}
    env: ${opt:stage}
  stackTags: ${self:provider.tags}

package:
  individually: true
  patterns:
    - '!node_modules/**'

custom:
  esbuild:
    bundle: true
    keepNames: true
    exclude:
      - 'cache-manager'
      - 'class-transformer'
      - 'class-validator'
      - '@nestjs/microservices'
      - '@nestjs/websockets/socket-module'
      - '@nestjs/platform-express'
    external:
      - 'sharp'
      - 'heic-convert'
      - 'pngjs'
    plugins: esbuild_plugin.js
    packagerOptions:
      scripts:
        - rm -rf node_modules/sharp
        - SHARP_IGNORE_GLOBAL_LIBVIPS=1 npm_config_arch=x64 npm_config_platform=linux npm install --cpu=x64 --os=linux sharp@0.33.4
  domicileLookupTableReadRolePolicyArn:
    Ref: AWS::NoValue

  prune:
    automatic: true
    includeLayers: true
    number: 5
  dotenv:
    exclude:
      - BANKING_WRAPPER_ENABLED
      - BILLING_API_ENDPOINT_VERSION
      - BUSINESS_IDENTIFIER_SEARCH_API_ENDPOINT_VERSION
      - CARDHOLDER_GSI
      - CONDITIONAL_FUND_TRANSFER_FEATURE_FLAG
      - CONTACT_UUID_GSI
      - DEBIT_CARD_ID_GSI
      - DEPOSITS_PENDING_GSI
      - DEPOSITS_PENDING_SCHEDULER
      - ECOMMERCE_CHANNEL_ID
      - ENTITY_MODEL_SERIAL_GSI
      - ENTITY_MODELSERIAL_GSI
      - ENTITY_SORT_KEY_GSI
      - ENTITY_TRANSACTION_TOTAL_GSI
      - MERCHANT_TABLE
      - ORIGINAL_TRANSACTION_GSI
      - PGS_CPOC_ENDPOINT
      - PGS_CNP_ENDPOINT
      - PRINTED_RECEIPT_LOGO_SIZE
      - SECONDARY_GSI_V1
      - SHORT_ID_GSI
      - SITE_GSI
      - SORT_KEY_GSI
      - TRANSACTION_DATE_GSI
  # S3 does not allow duplicate prefix triggers (which can occur when deploy stXXXX pipelines). workaround by adding
  # the stage in prefix to prevent duplicates.
  customerIconOnUploadS3Prefix:
    dev: customer/icon/
    prod: customer/icon/
    staging: customer/icon/
    systemTest: ${opt:stage}/customer/icon/
  appSyncEndpoint:
    Fn::ImportValue: !Sub '${self:provider.appsyncStackName}-graphQlApiUrl'
  mpCqrsCommandHandler: ${self:provider.mpCqrsStackName}-commandHandlers-handler
  componentStack:
    dev: '${opt:stage}-mp'
    staging: '${opt:stage}-mp'
    prod: '${opt:stage}-mp'
    st: '${opt:stage}-mp-customer'
  cqrsStackName: ${self:custom.componentStack.${opt:stage}, "${self:custom.componentStack.st}"}-cqrs
  mpCqrsEventBus: '${cf:${self:custom.cqrsStackName}-iac-eventBridge.EventBusProjectionArn}'
  mpCqrsProjectionDLQArn: '${cf:${self:custom.cqrsStackName}-iac-eventBridge.EventBusProjectionDLQArn}'
  apiStackName: ${self:custom.componentStack.${opt:stage}, "${self:custom.componentStack.st}"}-api
  documentUploadsBucket: ${ssm:${self:provider.serviceName}-assets-document-uploads}
  customerIconBaseUrl: '${ssm:/${self:provider.serviceName}/DASHBOARD_URL}/customer/icon/assets/'
  customerIconTemporaryBucket: ${self:provider.serviceName}-addrbook-contact-${self:provider.region}-${self:provider.accountId}
  customerIconProcessedBucket: ${ssm:${env:STATIC_ENV_NAME}-s3-mp-logo-download}
  appsyncDataSourceRoleArn: ${cf:${self:provider.appsyncStackName}.DataSourceLambdaRole}
  firebaseAdminPrivateKeySsmName: /${self:provider.serviceName}/ZELLER_APP_FIREBASE_ADMIN_PRIVATE_KEY
  firebaseAdminEmailSsmName: /${self:provider.serviceName}/ZELLER_APP_FIREBASE_ADMIN_EMAIL
  zellerAppAuth0ClientId: ${ssm:/${self:provider.serviceName}/ZELLER_APP_AUTH0_CLIENT_ID, 'none'}
  selfieVerificationApiEndpoint: ${ssm:${opt:stage}-os-engine-private-api-endpoint, ''}
  customerChangeEmailRedirectUrl: '${ssm:/${self:provider.serviceName}/DASHBOARD_URL}/change-email'
  riskRuleApiEndpoint: ${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule5-endpoint}
  onboardingRiskRuleApiEndpoint: ${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule22-endpoint}
  onboardingRiskRuleEndpointCategoryPath: ${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule22-endpoint-path}
  riskRuleEndpointProhibitedMCCPath: ${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule5a-endpoint-path}
  lambdaPararameterExtensionAccountId: '${ssm:Parameters-and-Secrets-Lambda-Extension}'
  removeAccountId:
    ap-southeast-2: ${env:LONDON_ACCOUNT_ID}
    eu-west-2: ${env:SYDNEY_ACCOUNT_ID}

functions:
  - ${file(resources/mp/customer/lambdas.yml)}

resources:
  - ${file(resources/mp/customer/resolvers.yml)}
  - ${file(resources/mp/customer/iam.yml)}
  - ${file(resources/mp/customer/eventBridgeRule.yml)}
  - ${file(resources/mp/customer/crossAccountPolicy.yml)}
