import { ISO4217 } from '@npco/component-dto-core/dist/types';
import { DebitCardAccountType, SavingsAccountProductType } from '@npco/component-dto-issuing-account/dist/types';
import {
  DebitCardColour,
  DebitCardFormat,
  DebitCardProductType,
  DebitCardType,
  VelocityWindowEnum,
} from '@npco/component-dto-issuing-card/dist/types';

import { v4 } from 'uuid';

import type { CoreBankingGetBpayBillerDetailResponse } from '../bpay/types';
import type {
  DebitCardAccountResponse,
  CoreBankingAdjustmentResponse,
  DebitCardResponse,
  VelocityControlResult,
  SavingsAccountBaseResponse,
} from '../type';
import {
  CoreBankingAdjustmentResponseStatus,
  CoreBankingDebitCardAccountStatus,
  CoreBankingDebitCardStatus,
} from '../type';

export const createMockDebitCardResponse = (input?: Partial<DebitCardResponse>) => {
  const base: DebitCardResponse = {
    id: v4(),
    accountUuid: v4(),
    colour: DebitCardColour.WHITE,
    productType: DebitCardProductType.DEBIT,
    displayName: v4(),
    cardHolder: v4(),
    customerUuid: v4(),
    status: CoreBankingDebitCardStatus.ACTIVE,
    accessibleProfile: false,
    format: DebitCardFormat.PHYSICAL,
    maskedPan: v4(),
    type: DebitCardType.MASTERCARD_DEBIT,
    entityUuid: v4(),
    customText: v4(),
    previewUrl: v4(),
  };

  return { ...base, ...input };
};

export const createMockDebitCardAccountResponse = (input?: Partial<DebitCardAccountResponse>) => {
  const base: DebitCardAccountResponse = {
    id: v4(),
    accountName: v4(),
    accountNumber: '**********',
    bsb: '123456',
    balance: {
      value: '100',
      currency: ISO4217.AUD,
    },
    status: CoreBankingDebitCardAccountStatus.ACTIVE,
    entityUuid: v4(),
    displayName: v4(),
    icon: {},
    createdAt: new Date().toISOString(),
    type: DebitCardAccountType.ZLR_DEBIT,
  };

  return { ...base, ...input };
};

export const createMockSavingsAccountBaseResponse = (input?: Partial<SavingsAccountBaseResponse>) => {
  const base: SavingsAccountBaseResponse = {
    id: v4(),
    balance: {
      value: '100',
      currency: ISO4217.AUD,
    },
    status: CoreBankingDebitCardAccountStatus.ACTIVE,
    savingsAccountType: SavingsAccountProductType.SZSA001,
  };

  return { ...base, ...input };
};

export const createMockDebitCardTransactionResponse = (input?: Partial<CoreBankingAdjustmentResponse>) => {
  const base: CoreBankingAdjustmentResponse = {
    id: v4(),
    accountUuid: v4(),
    amount: {
      value: '200',
      currency: ISO4217.AUD,
    },
    zellerReference: v4(),
    merchantReference: v4(),
    zellerUserId: v4(),
    adjustmentCategory: v4(),
    status: CoreBankingAdjustmentResponseStatus.COMPLETED,
    type: v4(),
    idempotencyKey: null,
    rejectReason: null,
  };

  return { ...base, ...input };
};

export const createMockBpayBillerDetail = (input?: Partial<CoreBankingGetBpayBillerDetailResponse>) => {
  const base: CoreBankingGetBpayBillerDetailResponse = {
    billerCode: '1102020',
    longName: 'test',
    shortName: 't',
    validCrnLengths: [1],
    variableCrnIndicator: 'N',
    industryANZSICCode: 'test',
    acceptedPaymentMethods: ['110'],
  };
  return { ...base, ...input };
};

export const createMockVelocityControlResponse = (input?: Partial<VelocityControlResult>) => {
  const base: VelocityControlResult = {
    velocityControlUuid: v4(),
    customerUuid: v4(),
    idempotencyKey: v4(),
    entityUuid: v4(),
    amountLimit: {
      value: v4(),
      currency: ISO4217.AED,
    },
    maxTransactionValue: {
      value: v4(),
      currency: ISO4217.AED,
    },
    velocityWindow: VelocityWindowEnum.WEEK,
    timeZone: 'Australia/Sydney',
  };

  return { ...base, ...input };
};

export function createMockSavingAccountResponse(
  entityUuid: string,
  savingsAccountType: SavingsAccountProductType,
  displayName?: string,
) {
  return {
    id: v4(),
    savingsAccountType,
    displayName: displayName ?? savingsAccountType,
    balance: {
      value: '3000',
      currency: ISO4217.AUD,
    },
    status: CoreBankingDebitCardAccountStatus.ACTIVE,
    entityUuid,
    createdAt: new Date().toISOString(),
    tfnSupplied: true,
    baseInterestRate: 1.35,
    bonusInterestRate: 0,
    effectiveInterestRate: 1.35,
  };
}

export function createMockPersonaliseSavingAccountResponse(entityUuid: string, displayName?: string) {
  return {
    id: v4(),
    displayName: displayName ?? SavingsAccountProductType.SZSA001,
    entityUuid,
    icon: {
      colour: 'GREEN',
    },
  };
}
