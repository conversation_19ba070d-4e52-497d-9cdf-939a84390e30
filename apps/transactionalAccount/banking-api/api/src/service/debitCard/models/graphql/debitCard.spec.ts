import { ISO4217 } from '@npco/component-dto-core/dist/types';
import { VelocityWindowEnum } from '@npco/component-dto-issuing-card/dist';

import { createMockDebitCardResponse } from '../../../coreBanking/testing/mockResponses';

import { DebitCard } from './debitCard';

describe('DebitCard', () => {
  it('should be able to convert CBS debit card response to debit card', async () => {
    const response = createMockDebitCardResponse({ maskedPan: '100000_ABCD' });
    const debitCard = new DebitCard(response);

    expect(debitCard).toEqual({
      __typename: 'DebitCardV2',
      accessibleProfile: response.accessibleProfile,
      colour: response.colour,
      customerUuid: response.customerUuid,
      entityUuid: response.entityUuid,
      debitCardAccountUuid: response.accountUuid,
      format: response.format,
      id: response.id,
      lastUsed: undefined,
      maskedPan: 'ABCD',
      name: response.displayName,
      owner: response.cardHolder,
      status: response.status,
      type: response.type,
      productType: response.productType,
      labelOnCard: response.customText,
      monochromeCardLogoPreviewUrl: response.previewUrl,
    });
  });

  it('should be able to convert CBS debit card with velocity control response to debit card', async () => {
    const velocityControl = {
      velocityControlUuid: '123',
      amountLimit: {
        value: '100',
        currency: ISO4217.AUD,
      },
      maxTransactionValue: {
        value: '100',
        currency: ISO4217.AUD,
      },
      velocityWindow: VelocityWindowEnum.DAY,
      timeZone: 'Australia/Sydney',
    };

    const response = createMockDebitCardResponse({
      maskedPan: '100000_ABCD',
      velocityControl,
    });
    const debitCard = new DebitCard(response);

    expect(debitCard).toEqual({
      __typename: 'DebitCardV2',
      accessibleProfile: response.accessibleProfile,
      colour: response.colour,
      customerUuid: response.customerUuid,
      entityUuid: response.entityUuid,
      debitCardAccountUuid: response.accountUuid,
      format: response.format,
      id: response.id,
      lastUsed: undefined,
      maskedPan: 'ABCD',
      name: response.displayName,
      owner: response.cardHolder,
      status: response.status,
      type: response.type,
      productType: response.productType,
      velocityControl: response.velocityControl,
      labelOnCard: response.customText,
      monochromeCardLogoPreviewUrl: response.previewUrl,
    });
  });
});
