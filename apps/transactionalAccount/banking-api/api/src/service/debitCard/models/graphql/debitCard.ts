import type {
  DebitCardColour,
  DebitCardFormat,
  DebitCardProductType,
  DebitCardType,
} from '@npco/component-dto-issuing-card/dist/types';
import { DebitCardStatus } from '@npco/component-dto-issuing-card/dist/types';

import type { DebitCardResponse, VelocityControlWithOmittedProperties } from '../../../coreBanking/type';
import { CoreBankingDebitCardStatus } from '../../../coreBanking/type';

import type { WithTypeName } from './commonTypes';

const mapCardStatus = (status: CoreBankingDebitCardStatus): DebitCardStatus => {
  const map = {
    [CoreBankingDebitCardStatus.ACTIVE]: DebitCardStatus.ACTIVE,
    [CoreBankingDebitCardStatus.SUSPENDED]: DebitCardStatus.SUSPENDED,
    [CoreBankingDebitCardStatus.LIMITED]: DebitCardStatus.ACTIVE,
    [CoreBankingDebitCardStatus.LOCKED]: DebitCardStatus.LOCKED,
    [CoreBankingDebitCardStatus.TERMINATED]: DebitCardStatus.CLOSED,
    [CoreBankingDebitCardStatus.UNACTIVATED]: DebitCardStatus.INACTIVE,
    [CoreBankingDebitCardStatus.UNSUPPORTED]: DebitCardStatus.CLOSED,
    [CoreBankingDebitCardStatus.CLOSED]: DebitCardStatus.CLOSED,
    [CoreBankingDebitCardStatus.LOST]: DebitCardStatus.LOST,
  };

  return map[status] ?? DebitCardStatus.CLOSED;
};

export class DebitCard implements WithTypeName<DebitCard> {
  id: string;

  entityUuid: string;

  name: string;

  owner?: string;

  customerUuid?: string;

  maskedPan: string;

  status: DebitCardStatus;

  colour: DebitCardColour;

  format?: DebitCardFormat;

  type: DebitCardType;

  /**
   * always undefined in this model to transform CBS -> GraphQL DebitCardV2 as CBS does not store lastUsed in cards
   * and needs to get it elsewhere (e.g. projections).
   */
  lastUsed: string | undefined;

  debitCardAccountUuid: string;

  accessibleProfile?: boolean;

  productType?: DebitCardProductType;

  firstActivatedTimestamp: number | undefined;

  firstActivatedAt: string | undefined;

  expiryMMYY: string | undefined;

  velocityControl: VelocityControlWithOmittedProperties | undefined;

  monochromeCardLogoPreviewUrl?: string;

  labelOnCard?: string;

  __typename = 'DebitCardV2'; // V2 is due to migration from EML.

  constructor(data: DebitCardResponse) {
    this.id = data.id;
    this.entityUuid = data.entityUuid;
    this.name = data.displayName;
    this.owner = data.cardHolder;
    this.customerUuid = data.customerUuid;
    this.maskedPan = data.maskedPan.slice(-4); // CBS returns something like 111111______6681 so need to get last 4
    this.status = mapCardStatus(data.status);
    this.colour = data.colour;
    this.format = data.format;
    this.type = data.type;
    this.lastUsed = undefined;
    this.debitCardAccountUuid = data.accountUuid;
    this.accessibleProfile = data.accessibleProfile;
    this.productType = data.productType;
    this.firstActivatedTimestamp = data.firstActivatedTimestamp;
    this.firstActivatedAt = data.firstActivatedAt;
    this.expiryMMYY = data.expiryMMYY;
    this.velocityControl = data.velocityControl;
    this.monochromeCardLogoPreviewUrl = data.previewUrl;
    this.labelOnCard = data.customText;
  }
}
