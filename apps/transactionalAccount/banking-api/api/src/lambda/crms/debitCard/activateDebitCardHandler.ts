import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware/xrayAggregateMiddleware';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';

import type { ActivateDebitCardError } from '../../../service/debitCard/models/graphql/activateDebitCardError';
import type { DebitCard } from '../../../service/debitCard/models/graphql/debitCard';
import { debitCardCommandService } from '../../dependencies/debitCardDependencies';
import type { ResolverHandler } from '../../types';

const getAggregateId = (e: Parameters<typeof lambdaHandler>[0]) => e.args.input.debitCardAccountUuid;

const lambdaHandler: ResolverHandler<
  {
    input: {
      debitCardAccountUuid: string;
      entityUuid: string;
      customerUuid: string;
      reference: string;
    };
  },
  ActivateDebitCardError | DebitCard | undefined
> = async (e) => {
  const { entityUuid, debitCardAccountUuid, customerUuid, reference } = e.args.input;
  return debitCardCommandService.activateDebitCard({
    entityUuid,
    customerUuid,
    accountUuid: debitCardAccountUuid,
    shortCode: reference,
  });
};

export const handler = withMiddlewaresV2(
  { component: ZellerComponent.CRMS, eventType: LambdaEventSource.APPSYNC },
  lambdaHandler,
  [xrayAggregateMiddleware(getAggregateId)],
);
