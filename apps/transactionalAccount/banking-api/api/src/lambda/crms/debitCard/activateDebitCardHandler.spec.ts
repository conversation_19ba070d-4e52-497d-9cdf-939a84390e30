import '../../testing/middlewareMock';

import { v4 } from 'uuid';

import { DebitCardCommandService } from '../../../service/debitCard/debitCardCommandService';
import { getBaseAppSyncResolverEvent } from '../../testing/baseEvent';

import { handler } from './activateDebitCardHandler';

jest.mock('../../../service/debitCard/debitCardCommandService');

const mockContext = {} as any;
const mockRequest = {} as any;

const MockDebitCardService = <jest.Mock<DebitCardCommandService>>DebitCardCommandService;
const mockDebitCardService = <jest.Mocked<DebitCardCommandService>>MockDebitCardService.mock.instances[0];

it('should be able to handle activateDebitCard', async () => {
  const event = getBaseAppSyncResolverEvent({
    args: {
      input: {
        debitCardAccountUuid: v4(),
        entityUuid: v4(),
        customerUuid: v4(),
        reference: v4(),
      },
    },
    request: mockRequest,
  });
  await handler(event, mockContext, () => {});

  expect(mockDebitCardService.activateDebitCard).toHaveBeenCalledTimes(1);
});
