import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware/xrayAggregateMiddleware';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';

import { debitCardAccountCommandService } from '../../dependencies/debitCardAccountDependencies';
import type { ResolverHandler } from '../../types';

const getAggregateId = (e: Parameters<typeof lambdaHandler>[0]) => e.args.input.debitCardAccountUuid;

const lambdaHandler: ResolverHandler<{ input: { debitCardAccountUuid: string; reason: string } }, boolean> = async (
  e,
) => {
  const { debitCardAccountUuid, reason } = e.args.input;
  return debitCardAccountCommandService.unsuspendAccount({ accountUuid: debitCardAccountUuid, reason });
};

export const handler = withMiddlewaresV2(
  { component: ZellerComponent.CRMS, eventType: LambdaEventSource.APPSYNC },
  lambdaHandler,
  [xrayAggregateMiddleware(getAggregateId)],
);
