import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware/xrayAggregateMiddleware';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import type { Icon } from '@npco/component-dto-core/dist/types';

import type { DebitCardAccountResponse } from '../../../service/coreBanking/type';
import { coreBankingService } from '../../dependencies/debitCardDependencies';
import type { ResolverHandler } from '../../types';

const getAggregateId = (e: Parameters<typeof lambdaHandler>[0]) => e.args.input.debitCardAccountUuid;

const lambdaHandler: ResolverHandler<
  {
    input: {
      debitCardAccountUuid: string;
      displayName: string;
      icon: Icon;
    };
    entityUuid?: string;
  },
  DebitCardAccountResponse
> = async (e, c) => {
  const { debitCardAccountUuid, displayName, icon } = e.args.input;
  const entityUuid = e.args.entityUuid ?? c.entityUuid;
  return coreBankingService.personaliseDebitCardAccount({
    entityUuid,
    accountUuid: debitCardAccountUuid,
    displayName,
    icon,
  });
};

export const handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  lambdaHandler,
  [xrayAggregateMiddleware(getAggregateId)],
);
