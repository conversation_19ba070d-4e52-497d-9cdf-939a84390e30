import '../../testing/middlewareMock';

import { v4 } from 'uuid';

import { CoreBankingService } from '../../../service/coreBanking/coreBankingService';
import { getBaseAppSyncResolverEvent } from '../../testing/baseEvent';

import { handler } from './personaliseDebitCardAccountHandler';

jest.mock('../../../service/debitCard/debitCardCommandService');

const mockContext = {
  entityUuid: v4(),
} as any;
const mockRequest = {} as any;

jest.mock('../../../service/debitCard/debitCardQueryService');
jest.mock('../../../service/coreBanking/coreBankingService');

const MockCoreBankingService = <jest.Mock<CoreBankingService>>CoreBankingService;
const mockCoreBankingService = <jest.Mocked<CoreBankingService>>MockCoreBankingService.mock.instances[0];
it('should be able to handle personaliseDCA - entityUuid from context', async () => {
  const event = getBaseAppSyncResolverEvent({
    args: {
      input: {
        debitCardAccountUuid: v4(),
        reference: v4(),
        entityUuid: v4(),
        displayName: 'test',
        icon: {} as any,
      },
    },
    request: mockRequest,
  });
  await handler(event, mockContext, () => {});

  expect(mockCoreBankingService.personaliseDebitCardAccount).toHaveBeenCalledTimes(1);
});

it('should be able to handle personaliseDCA - entityUuid from request', async () => {
  const event = getBaseAppSyncResolverEvent({
    args: {
      input: {
        debitCardAccountUuid: v4(),
        reference: v4(),
        entityUuid: v4(),
        displayName: 'test',
        icon: {} as any,
      },
    },
    request: mockRequest,
  });
  await handler(event, {} as any, () => {});

  expect(mockCoreBankingService.personaliseDebitCardAccount).toHaveBeenCalledTimes(2);
});
