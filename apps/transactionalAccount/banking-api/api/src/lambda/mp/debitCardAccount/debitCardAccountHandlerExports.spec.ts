import '../../testing/middlewareMock';

import { CustomerRole } from '@npco/component-dto-core/dist';

import { v4 } from 'uuid';

import { dcaMpCommonService } from '../../dependencies/debitCardAccountDependencies';
import { getBaseAppSyncResolverEvent } from '../../testing/baseEvent';

import {
  getDebitCardV2Handler,
  getDebitCardsV2Handler,
  getDebitCardAccountV2Handler,
  getDebitCardAccountsV2Handler,
  getDebitCardAccountBalancesHandler,
  getDebitCardsByAccountIdV2Handler,
  getDebitCardAccountCardsCountV2Handler,
  getDebitCardAccountTxnHandler,
  getDebitCardAccountTxnsHandler,
} from './debitCardAccountHandlerExports';

jest.mock('../../dependencies/debitCardAccountDependencies', () => ({
  dcaMpCommonService: {
    getDebitCardAccountCardV2: jest.fn(),
    getDebitCardAccountCardsV2: jest.fn(),
    getIssuingAccount: jest.fn(),
    getIssuingAccounts: jest.fn(),
    getDebitCardAccountBalanceHistory: jest.fn(),
    getDebitCardsByAccountId: jest.fn(),
    getDebitCardAccountCardsCountV2: jest.fn(),
    getDebitCardAccountTransactionV2: jest.fn(),
    getDebitCardAccountTransactionsV2: jest.fn(),
  },
}));

const mockContext = {} as any;

it('should handle getDebitCardV2Handler', async () => {
  const c = { customerUuid: v4(), role: CustomerRole.ADMIN, entityUuid: v4() };
  const event = getBaseAppSyncResolverEvent({
    args: { cardId: v4() },
  });
  await getDebitCardV2Handler(event, c as any, () => {});
  expect(dcaMpCommonService.getDebitCardAccountCardV2).toHaveBeenCalledTimes(1);
  expect(dcaMpCommonService.getDebitCardAccountCardV2).toHaveBeenCalledWith(c.entityUuid, event.args.cardId, {
    customerUuid: c.customerUuid,
    role: c.role,
  });
});

it('should handle getDebitCardsV2Handler', async () => {
  const c = { customerUuid: v4(), role: CustomerRole.ADMIN, entityUuid: v4() };
  const event2 = getBaseAppSyncResolverEvent({
    args: { limit: 5, nextToken: 'token', filter: {} },
  });
  await getDebitCardsV2Handler(event2, c as any, () => {});
  expect(dcaMpCommonService.getDebitCardAccountCardsV2).toHaveBeenCalledTimes(1);
  expect(dcaMpCommonService.getDebitCardAccountCardsV2).toHaveBeenCalledWith(
    c.entityUuid,
    event2.args.limit,
    event2.args.nextToken,
    {
      customerUuid: c.customerUuid,
      role: c.role,
    },
    {},
  );
});

it('should handle getDebitCardAccountV2Handler', async () => {
  const event = getBaseAppSyncResolverEvent({ args: { entityUuid: v4(), debitCardAccountUuid: v4() } });
  await getDebitCardAccountV2Handler(event, mockContext, () => {});
  expect(dcaMpCommonService.getIssuingAccount).toHaveBeenCalledTimes(1);
});

it('should handle getDebitCardAccountV2Handler - entityuuid from context', async () => {
  const event = getBaseAppSyncResolverEvent({ args: { debitCardAccountUuid: v4() } });
  await getDebitCardAccountV2Handler(event, { entityUuid: v4() } as any, () => {});
  expect(dcaMpCommonService.getIssuingAccount).toHaveBeenCalledTimes(2);
});

it('should handle getDebitCardAccountsV2Handler', async () => {
  const event = getBaseAppSyncResolverEvent({ args: { entityUuid: v4(), limit: 5, nextToken: 'token' } });
  await getDebitCardAccountsV2Handler(event, mockContext, () => {});
  expect(dcaMpCommonService.getIssuingAccounts).toHaveBeenCalledTimes(1);
});

it('should handle getDebitCardAccountsV2Handler', async () => {
  const event = getBaseAppSyncResolverEvent({ args: { limit: 5, nextToken: 'token' } });
  await getDebitCardAccountsV2Handler(event, { entityUuid: v4() } as any, () => {});
  expect(dcaMpCommonService.getIssuingAccounts).toHaveBeenCalledTimes(2);
});

it('should handle getDebitCardAccountBalancesHandler', async () => {
  const entityUuid = v4();
  const debitCardAccountUuid = v4();
  const timeZone = 'UTC';
  const range = { from: '2025-01-01', to: '2025-01-31' };

  const event = getBaseAppSyncResolverEvent({
    args: { timeZone, range, debitCardAccountUuid },
  });

  await getDebitCardAccountBalancesHandler(event, { entityUuid } as any, () => {});

  expect(dcaMpCommonService.getDebitCardAccountBalanceHistory).toHaveBeenCalledTimes(1);
  expect(dcaMpCommonService.getDebitCardAccountBalanceHistory).toHaveBeenCalledWith(
    range,
    timeZone,
    entityUuid,
    debitCardAccountUuid,
  );
});

it('should handle getDebitCardsByAccountIdV2Handler', async () => {
  const event = getBaseAppSyncResolverEvent({ args: { entityUuid: v4(), debitCardAccountUuid: v4() } });
  await getDebitCardsByAccountIdV2Handler(event, mockContext, () => {});
  expect(dcaMpCommonService.getDebitCardsByAccountId).toHaveBeenCalledTimes(1);
});

it('should handle getDebitCardsByAccountIdV2Handler - entityuuid from context', async () => {
  const event = getBaseAppSyncResolverEvent({ args: { debitCardAccountUuid: v4() } });
  await getDebitCardsByAccountIdV2Handler(event, { entityUuid: v4() } as any, () => {});
  expect(dcaMpCommonService.getDebitCardsByAccountId).toHaveBeenCalledTimes(2);
});

it('should handle getDebitCardAccountCardsCountV2Handler', async () => {
  const event = getBaseAppSyncResolverEvent({ args: { entityUuid: v4(), debitCardAccountUuid: v4() } });
  await getDebitCardAccountCardsCountV2Handler(event, mockContext, () => {});
  expect(dcaMpCommonService.getDebitCardAccountCardsCountV2).toHaveBeenCalledTimes(1);
});

it('should handle getDebitCardAccountTxnHandler', async () => {
  const event = getBaseAppSyncResolverEvent({ args: { entityUuid: v4(), debitCardTransactionUuid: v4() } });
  await getDebitCardAccountTxnHandler(event, mockContext, () => {});
  expect(dcaMpCommonService.getDebitCardAccountTransactionV2).toHaveBeenCalledTimes(1);
});

it('should handle getDebitCardAccountTxnHandler - entityUuid from context', async () => {
  const event = getBaseAppSyncResolverEvent({ args: { debitCardTransactionUuid: v4() } });
  await getDebitCardAccountTxnHandler(event, { entityUuid: v4() } as any, () => {});
  expect(dcaMpCommonService.getDebitCardAccountTransactionV2).toHaveBeenCalledTimes(2);
});

it('should handle getDebitCardAccountTxnsHandler', async () => {
  const event = getBaseAppSyncResolverEvent({ args: { entityUuid: v4(), limit: 5, nextToken: 'token' } });
  await getDebitCardAccountTxnsHandler(event, mockContext, () => {});
  expect(dcaMpCommonService.getDebitCardAccountTransactionsV2).toHaveBeenCalledTimes(1);
});

it('should handle getDebitCardAccountTxnsHandler - entityUuid from context', async () => {
  const event = getBaseAppSyncResolverEvent({ args: { limit: 5, nextToken: 'token' } });
  await getDebitCardAccountTxnsHandler(event, { entityUuid: v4() } as any, () => {});
  expect(dcaMpCommonService.getDebitCardAccountTransactionsV2).toHaveBeenCalledTimes(2);
});
