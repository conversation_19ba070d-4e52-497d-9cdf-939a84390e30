import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware/xrayAggregateMiddleware';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import type { Icon } from '@npco/component-dto-core/dist/types';

import type { DebitCardAccount } from '../../../service/debitCardAccount/models/graphql/debitCardAccount';
import { debitCardAccountCommandService } from '../../dependencies/debitCardAccountDependencies';
import type { ResolverHandler } from '../../types';

const getAggregateId = (e: Parameters<typeof lambdaHandler>[0]) => e.args.entityUuid;

const lambdaHandler: ResolverHandler<
  { input: { name: string; icon: Icon }; entityUuid: string },
  DebitCardAccount
> = async (e) => {
  const { name, icon } = e.args.input;
  const { entityUuid } = e.args;
  return debitCardAccountCommandService.createAccount({ entityUuid, displayName: name, icon });
};

export const handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  lambdaHandler,
  [xrayAggregateMiddleware(getAggregateId)],
);
