import '../../testing/middlewareMock';
import * as Logger from '@npco/component-bff-core/dist/utils/logger';

import { v4 } from 'uuid';

import { getBaseAppSyncResolverEvent } from '../../testing/baseEvent';

import {
  onDebitCardTransactionUpdateHandler,
  onDebitCardAccountCardUpdateHandler,
  onDebitCardAccountUpdateHandler,
} from './index';

jest.mock('@npco/component-bff-core/dist/utils/logger', () => {
  const originalModule = jest.requireActual('@npco/component-bff-core/dist/utils/logger');

  return {
    __esModule: true,
    ...originalModule,
  };
});

jest.mock('@npco/component-bff-core/dist/middleware/entityUuidCheckMiddleware', () => ({
  entityUuidCheckMiddleware: jest.requireActual('@npco/component-bff-core/dist/middleware/entityUuidCheckMiddleware')
    .entityUuidCheckMiddleware,
}));

const mockEntityUuid = v4();
const mockContext = { entityUuid: mockEntityUuid } as any;
const mockRequest = {} as any;

describe('Publisher Lambda Handlers', () => {
  it('should handle onDebitCardTransactionUpdateHandler', async () => {
    jest.spyOn(Logger, 'info').mockReset();
    const spy = jest.spyOn(Logger, 'info');

    const event = getBaseAppSyncResolverEvent({ args: { entityUuid: mockEntityUuid }, request: mockRequest });
    await onDebitCardTransactionUpdateHandler(event, mockContext, () => {});

    expect(spy).toHaveBeenCalledWith('Subscribe to Debit Card Transaction Update');
  });

  it('should handle onDebitCardAccountCardUpdateHandler', async () => {
    jest.spyOn(Logger, 'info').mockReset();
    const spy = jest.spyOn(Logger, 'info');

    const event = getBaseAppSyncResolverEvent({ args: { entityUuid: mockEntityUuid }, request: mockRequest });
    await onDebitCardAccountCardUpdateHandler(event, mockContext, () => {});

    expect(spy).toHaveBeenCalledWith('Subscribe to Debit Card Account Card Update');
  });

  it('should handle onDebitCardAccountUpdateHandler', async () => {
    jest.spyOn(Logger, 'info').mockReset();
    const spy = jest.spyOn(Logger, 'info');

    const event = getBaseAppSyncResolverEvent({ args: { entityUuid: mockEntityUuid }, request: mockRequest });
    await onDebitCardAccountUpdateHandler(event, mockContext, () => {});

    expect(spy).toHaveBeenCalledWith('Subscribe to Debit Card Account Update');
  });
});
