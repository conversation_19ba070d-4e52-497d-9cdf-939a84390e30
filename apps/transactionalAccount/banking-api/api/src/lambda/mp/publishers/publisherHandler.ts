import { appIdentityMiddleware, entityUuidCheckMiddleware } from '@npco/component-bff-core/dist/middleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import { info } from '@npco/component-bff-core/dist/utils/logger';

import type { Handler } from 'aws-lambda';

const onDebitCardTransactionUpdate: Handler = async () => {
  info('Subscribe to Debit Card Transaction Update');
  // no return needed
};

export const onDebitCardTransactionUpdateHandler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  onDebitCardTransactionUpdate,
  [appIdentityMiddleware(true), entityUuidCheckMiddleware],
);

const onDebitCardAccountCardUpdate: Handler = async () => {
  info('Subscribe to Debit Card Account Card Update');
  // no return needed
};

export const onDebitCardAccountCardUpdateHandler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  onDebitCardAccountCardUpdate,
  [appIdentityMiddleware(true), entityUuidCheckMiddleware],
);

const onDebitCardAccountUpdate: Handler = async () => {
  info('Subscribe to Debit Card Account Update');
  // no return needed
};

export const onDebitCardAccountUpdateHandler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  onDebitCardAccountUpdate,
  [appIdentityMiddleware(true), entityUuidCheckMiddleware],
);
