import { ServerlessPlugin } from '@npco/component-bff-serverless/dist/serverless/common/plugins';
import { ApiAppServerlessStack } from '@npco/component-bff-serverless/dist/serverless/stacks/apiAppServerlessStack';

import { mpEnvConfig } from './iac/mp/env';
import { getAbusiveDescriptionResolvers } from './iac/mp/resolvers/abusiveDescriptionResolvers';
import { bffResolvers } from './iac/mp/resolvers/additionalResolvers';
import { getCardLogoResolvers } from './iac/mp/resolvers/cardLogoResolvers';
import { getCardProvisionResolvers } from './iac/mp/resolvers/cardProvisionResolvers';
import { getInterestSummaryResolvers } from './iac/mp/resolvers/interestSummaryResolvers';
import { publisherResolvers } from './iac/mp/resolvers/publisherResolvers';
import { getScheduledTransfersResolvers } from './iac/mp/resolvers/scheduledTransfersResolvers';

const region = '${opt:region}';
const accountId = '${aws:accountId}';
const staticServiceName = '${env:STATIC_ENV_NAME}-mp-api';
const appsyncStackName = `${staticServiceName}-appsync`;

const interestSummaryStackName = `${staticServiceName}-banking-interestSummary`;
const bankingStackName = `${staticServiceName}-banking`;

const scheduledTransfersStackName = `${staticServiceName}-banking-sart`;

const cardlogoStackName = `${staticServiceName}-banking-card-logo`;

const custom = {
  appSyncApiId: { 'Fn::ImportValue': `${appsyncStackName}-graphQlApiId` },
  appsyncDataSourceRoleArn: `\${cf:${appsyncStackName}.DataSourceLambdaRole}`,

  getDebitCardLambda: `${bankingStackName}-getDebitCard`,

  getInterestSummariesLambda: `${interestSummaryStackName}-getInterestSummaries`,

  getDigitalWalletTokenLambda: `${bankingStackName}-getDigitalWalletToken`,
  getDigitalWalletTokensByDebitCardUuidLambda: `${bankingStackName}-getDigitalWalletTokensByDcacUuid`,
  getDigitalWalletTokensByDeviceLambda: `${bankingStackName}-getDigitalWalletTokensByDevice`,

  requestProvisioningOfCardAndroidLambda: `${bankingStackName}-requestProvisioningOfCardAndroid`,
  requestProvisioningOfCardIosLambda: `${bankingStackName}-requestProvisioningOfCardIos`,
  confirmProvisionedCardOnDeviceLambda: `${bankingStackName}-confirmProvisionedCardOnDevice`,
  removeProvisionedCardLambda: `${bankingStackName}-removeProvisionedCard`,

  checkForAbusiveDescriptionLambda: `${bankingStackName}-checkForAbusiveDescription`,

  createScheduledTransfersLambda: `${scheduledTransfersStackName}-createScheduledTransfersHandler`,
  updateScheduledTransfersLambda: `${scheduledTransfersStackName}-updateScheduledTransfersHandler`,
  skipScheduledTransfersLambda: `${scheduledTransfersStackName}-skipScheduledTransfersHandler`,
  unSkipScheduledTransfersLambda: `${scheduledTransfersStackName}-unSkipScheduledTransfersHandler`,
  retryScheduledTransfersLambda: `${scheduledTransfersStackName}-retryScheduledTransfersHandler`,
  cancelScheduledTransfersLambda: `${scheduledTransfersStackName}-cancelScheduledTransfersHandler`,
  getScheduledTransfersLambda: `${scheduledTransfersStackName}-getScheduledTransfersHandler`,
  getTransferScheduleLambda: `${scheduledTransfersStackName}-getTransferScheduleHandler`,
  getScheduledTransferSkippedExecutionsLambda: `${scheduledTransfersStackName}-getSkippedExecutionsHandler`,
  getScheduledTransferContactLambda: `${scheduledTransfersStackName}-getScheduledTransferContactHandler`,

  getCardLogoUploadUrlLambda: `${cardlogoStackName}-getCardLogoUploadUrl`,
  getCardLogosLambda: `${cardlogoStackName}-getCardLogos`,
  getCardLogoLambda: `${cardlogoStackName}-getCardLogo`,
  deleteCardLogoLambda: `${cardlogoStackName}-deleteCardLogo`,
  onCardLogoProcessedLambda: `${cardlogoStackName}-onCardLogoProcessed`,

  getSavingsAccountProductByAccountLambda: `${bankingStackName}-getSavingsAccountProductByAccount`,
  getDebitCardsInDebitCardAccountLambda: `${bankingStackName}-dca-getDebitCardsByAccountIdV2`,
  getDebitCardAccountCardsCountLambda: `${bankingStackName}-dca-getDebitCardAccountCardsCountV2`,
  getDebitCardAccountV2Lambda: `${bankingStackName}-dca-getDebitCardAccountV2`,
  getOutstandingTransactionsLambda: `${bankingStackName}-getOutstandingTransactions`,
};

const sls = new ApiAppServerlessStack('banking-resolvers', mpEnvConfig, {
  plugins: [
    ServerlessPlugin.Dotenv,
    ServerlessPlugin.ResourceTagging,
    ServerlessPlugin.Tracing,
    'serverless-pseudo-parameters',
  ],
  custom: {
    ...custom,
    richDataStackName: '${self:custom.service}-richdata',
    getMerchantDetailsLambda: '${self:custom.richDataStackName}-getMerchantDetails',
    ...mpEnvConfig.getAppsync(),
  },
  resources: {
    ...bffResolvers,
    ...publisherResolvers(custom).Resources,
    ...getInterestSummaryResolvers(region, accountId, custom).Resources,
    ...getCardProvisionResolvers(region, accountId, custom).Resources,
    ...getAbusiveDescriptionResolvers(region, accountId, custom).Resources,
    ...getScheduledTransfersResolvers(region, accountId, custom).Resources,
    ...getCardLogoResolvers(region, accountId, custom).Resources,
  },
});

module.exports = sls.build();
