import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon } from '../../common/env';
import { responseDefault } from '../../common/resolverTemplates';
import { dbsCommonPolicies } from '../env';

export const lambdas: ServerlessFunctions = {
  getDebitCardAccountsV3: {
    handler: 'src/lambda/dbs/debitCardAccount/index.getDebitCardAccountsV3Handler',
    name: 'getDebitCardAccountsV3',
    ...lambdaCommon,
    policy: {
      managed: [...dbsCommonPolicies, ManagedPolicy.deviceTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardAccountsV3',
      typeName: 'Query',
      template: {
        request: `
          #set($maxLimit = 50)
          #set($limit = $context.args.limit)
          #if ($limit > $maxLimit) {
          $utils.error("Limit exceeds maximum (50)")
          }
          #end
          {
            "version" : "2018-05-29",
            "operation": "Invoke",
            "payload": {
              "args": $util.toJson($context.args),
              "identity": $util.toJson($context.identity),
              "request": $util.toJson($context.request),
              "authType": $util.toJson($util.authType()),
              "source": $util.toJson($context.source), 
              "info": $util.toJson($context.info)
            }
          }
        `,
        response: responseDefault,
      },
    },
  },
};
