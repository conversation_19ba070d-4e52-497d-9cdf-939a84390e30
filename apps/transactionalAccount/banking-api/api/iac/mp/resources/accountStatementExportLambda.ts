import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon } from '../../common/env';
import { mpCommonPolicies } from '../env';

export const lambdas: ServerlessFunctions = {
  triggerAccountStatementExportJobs: {
    handler: 'src/lambda/mp/accountStatementExport/index.triggerAccountStatementExportJobsHandler',
    name: 'triggerAccountStatementExportJobs',
    ...lambdaCommon,
    timeout: 900,
    memorySize: 2048,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        publishToAccountStatementExportSqsPolicy: [
          {
            effect: 'Allow',
            actions: [
              'sqs:SendMessage',
              'sqs:DeleteMessage',
              'sqs:ChangeMessageVisibility',
              'sqs:GetQueueAttributes',
              'sqs:GetQueueUrl',
            ],
            resources: [{ 'Fn::GetAtt': ['accountStatementExportQueue', 'Arn'] }],
          },
        ],
        typeGsiV2QueryPolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:Query', 'dynamodb:BatchGetItem'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.typeGsi}',
            ],
          },
        ],
      },
    },
    events: [
      {
        schedule: {
          name: '${self:provider.stackName}-quarterlyAccountStatementExportTarget',
          description: 'Account statements exports that runs quarterly',
          rate: 'cron(0 16 1 1,4,7,10 ? *)',
          enabled: '${env:ENABLE_ACCOUNT_STATEMENT_EXPORTS_CRON}' as unknown as boolean,
          input: { type: 'QUARTERLY' },
        },
      },
      {
        schedule: {
          name: '${self:provider.stackName}-monthlyAccountStatementExportTarget',
          description: 'Account statements exports that runs monthly',
          rate: 'cron(0 14 1 * ? *)',
          enabled: '${env:ENABLE_ACCOUNT_STATEMENT_EXPORTS_CRON}' as unknown as boolean,
          input: { type: 'MONTHLY' },
        },
      },
    ],
    environment: {
      ACCOUNT_STATEMENT_EXPORTS_SQS_URL: { Ref: 'accountStatementExportQueue' },
    },
  },
  exportAccountStatementWorker: {
    handler: 'src/lambda/mp/accountStatementExport/index.exportAccountStatementWorkerHandler',
    name: 'exportAccountStatementWorker',
    ...lambdaCommon,
    timeout: 45,
    memorySize: 1024,
    maximumRetryAttempts: 0,
    policy: {
      managed: [
        ...mpCommonPolicies,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.entityTableWriteItemRolePolicy,
      ],
      inline: {
        exportToS3Policy: [
          {
            effect: 'Allow',
            actions: ['s3:PutObject'],
            resources: ['arn:aws:s3:::${self:custom.accountStatementExportBucket}/debitCardAccountStatements/*'],
          },
        ],
        sqsPolicy: [
          {
            effect: 'Allow',
            actions: ['sqs:ReceiveMessage', 'sqs:DeleteMessage', 'sqs:GetQueueAttributes'],
            resources: [{ 'Fn::GetAtt': ['accountStatementExportQueue', 'Arn'] }],
          },
        ],
        secondaryGsiV1QueryPolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:Query', 'dynamodb:BatchGetItem'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.secondaryGsiV1}',
            ],
          },
        ],
        commandHandlerPolicy: [
          {
            effect: 'Allow',
            actions: ['lambda:InvokeFunction'],
            resources: ['arn:aws:lambda:${self:provider.region}:*:function:${self:custom.cqrsCommandHandler}*'],
          },
        ],
      },
    },
    events: [
      {
        sqs: {
          arn: { 'Fn::GetAtt': ['accountStatementExportQueue', 'Arn'] },
          maximumConcurrency: 10,
          enabled: true,
          batchSize: 1,
        } as any,
      },
    ],
    environment: {
      ACCOUNT_STATEMENT_EXPORT_BUCKET: '${self:custom.accountStatementExportBucket}',
      CQRS_COMMAND_HANDLER: '${self:custom.cqrsCommandHandler}',
    },
  } as any,
  getAccountStatements: {
    handler: 'src/lambda/mp/accountStatementExport/index.getAccountStatementsHandler',
    name: 'getAccountStatements',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        getAccountStatementFromS3Policy: [
          {
            effect: 'Allow',
            actions: ['s3:GetObject'],
            resources: ['arn:aws:s3:::${self:custom.accountStatementExportBucket}/debitCardAccountStatements/*'],
          },
        ],
      },
    },
    environment: {
      ACCOUNT_STATEMENT_EXPORT_BUCKET: '${self:custom.accountStatementExportBucket}',
    },
    appsync: {
      fieldName: 'getBankingAccountStatements',
      typeName: 'Query',
    },
  },
  exportInterimAccountStatement: {
    handler: 'src/lambda/mp/accountStatementExport/index.exportInterimAccountStatementHandler',
    name: 'exportInterimAccountStatement',
    timeout: 45,
    memorySize: 1024,
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        getAccountStatementFromS3Policy: [
          {
            effect: 'Allow',
            actions: ['s3:GetObject', 's3:PutObject'],
            resources: ['arn:aws:s3:::${self:custom.accountStatementExportBucket}/debitCardAccountStatements/*'],
          },
        ],
        secondaryGsiV1QueryPolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:Query', 'dynamodb:BatchGetItem'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.secondaryGsiV1}',
            ],
          },
        ],
      },
    },
    environment: {
      ACCOUNT_STATEMENT_EXPORT_BUCKET: '${self:custom.accountStatementExportBucket}',
    },
    appsync: {
      fieldName: 'createInterimBankingAccountStatement',
      typeName: 'Mutation',
    },
  },
};
