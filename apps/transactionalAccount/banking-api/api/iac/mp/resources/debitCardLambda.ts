import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon } from '../../common/env';
import { mpCommonPolicies } from '../env';

export const lambdas: ServerlessFunctions = {
  getDebitCard: {
    handler: 'src/lambda/mp/debitCard/index.getDebitCardHandler',
    name: 'getDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  getDebitCards: {
    handler: 'src/lambda/mp/debitCard/index.getDebitCardsHandler',
    name: 'getDebitCards',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  getDebitCardsByAccountId: {
    handler: 'src/lambda/mp/debitCard/index.getDebitCardsByAccountIdHandler',
    name: 'getDebitCardsByAccountId',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  createNewDebitCard: {
    handler: 'src/lambda/mp/debitCard/index.createNewDebitCardHandler',
    name: 'createNewDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'requestNewDebitCard',
      typeName: 'Mutation',
    },
  },
  updateDebitCard: {
    handler: 'src/lambda/mp/debitCard/index.updateDebitCardHandler',
    name: 'updateDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'updateDebitCard',
      typeName: 'Mutation',
    },
  },
  closeAllDebitCards: {
    handler: 'src/lambda/mp/debitCard/index.closeAllDebitCardsHandler',
    name: 'closeAllDebitCards',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  reportLostDebitCard: {
    handler: 'src/lambda/mp/debitCard/index.reportLostDebitCardHandler',
    name: 'reportLostDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'reportLostDebitCard',
      typeName: 'Mutation',
    },
  },
  closeDebitCard: {
    handler: 'src/lambda/mp/debitCard/index.closeDebitCardHandler',
    name: 'closeDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'cancelDebitCard',
      typeName: 'Mutation',
    },
  },
  updateCorporateCard: {
    handler: 'src/lambda/mp/debitCard/index.updateCorporateCardHandler',
    name: 'updateCorporateCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'updateCorporateCardVelocityControl',
      typeName: 'Mutation',
    },
  },
  lockDebitCard: {
    handler: 'src/lambda/mp/debitCard/index.lockDebitCardHandler',
    name: 'lockDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'lockDebitCard',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
  },
  unlockDebitCard: {
    handler: 'src/lambda/mp/debitCard/index.unlockDebitCardHandler',
    name: 'unlockDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'unlockDebitCard',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
  },
  getDebitCardV2: {
    handler: 'src/lambda/mp/debitCardAccount/index.getDebitCardV2Handler',
    name: 'getDebitCardV2',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardV2',
      typeName: 'Query',
    },
  },
  getDebitCardsV2: {
    handler: 'src/lambda/mp/debitCardAccount/index.getDebitCardsV2Handler',
    name: 'getDebitCardsV2',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardsV2',
      typeName: 'Query',
      dataResolverName: { namePrefix: 'getDebitCardsV2' },
      template: {
        request: `
          #set($maxLimit = 50)
          #set($limit = $context.args.limit)
          #if ($limit > $maxLimit)
            $utils.error("Limit exceeds maximum (50)")
          #end
          #set($filter = $util.defaultIfNull($context.args.filter, {}))
          #set($filterExpression = "")
          #set($expressionNames = {})
          #set($expressionValues = {})
          #foreach($field in $filter.keySet())
            #if (!$util.isNullOrEmpty($filter[$field]))
              #set($fieldFilter = $util.transform.toDynamoDBFilterExpression({ "$field": $filter[$field] }))
              #set($fieldFilter = $util.parseJson($fieldFilter))
              #if(!$util.isNullOrEmpty($fieldFilter.expression))
                #if($filterExpression != "")
                  #set($filterExpression = "$filterExpression AND $fieldFilter.expression")
                #else
                  #set($filterExpression = "$fieldFilter.expression")
                #end
                #if(!$util.isNullOrEmpty($fieldFilter.expressionNames))
                  $util.qr($expressionNames.putAll($fieldFilter.expressionNames))
                #else
                  $util.qr($expressionNames.put("#$field", "$field"))
                #end
                #if(!$util.isNullOrEmpty($fieldFilter.expressionValues))
                  $util.qr($expressionValues.putAll($fieldFilter.expressionValues))
                #end
              #end
            #end
          #end
          #foreach($key in $expressionNames.keySet())
            #if (!$filterExpression.contains($key))
              $util.qr($expressionNames.remove($key))
            #end
          #end
          #if (!$util.isNullOrEmpty($filterExpression))
            #set($context.args.filter = $util.toJson({
              "expression": $filterExpression,
              "expressionNames": $expressionNames,
              "expressionValues": $expressionValues
            }))
          #else
            #set($context.args.filter = $util.toJson({}))
          #end
          {
            "version" : "2018-05-29",
            "operation": "Invoke",
            "payload": {
              "args": $util.toJson($context.args),
              "identity": $util.toJson($context.identity),
              "request": $util.toJson($context.request),
              "authType": $util.toJson($util.authType()),
              "source": $util.toJson($context.source),
              "info": $util.toJson($context.info)
            }
          }
        `,
      },
    },
  },
  getDebitCardsByAccountIdV2: {
    handler: 'src/lambda/mp/debitCardAccount/index.getDebitCardsByAccountIdV2Handler',
    name: 'getDebitCardsByAccountIdV2',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  getDebitCardAccountCardsCountV2: {
    handler: 'src/lambda/mp/debitCardAccount/index.getDebitCardAccountCardsCountV2Handler',
    name: 'getDebitCardAccountCardsCountV2',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  onDebitCardAccountCardUpdateLambda: {
    handler: 'src/lambda/mp/publishers/publisherHandler.onDebitCardAccountCardUpdateHandler',
    name: 'onDebitCardUpdate',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  activateDebitCard: {
    handler: 'src/lambda/mp/debitCard/index.activateDebitCardHandler',
    name: 'activateDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'activateDebitCard',
      typeName: 'Mutation',
    },
  },
};
