import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon } from '../../common/env';
import { mpCommonPolicies } from '../env';

export const lambdas: ServerlessFunctions = {
  notification: {
    handler: 'src/lambda/mp/transferRemittance/index.transferRemittanceNotificationHandler',
    name: 'notification',
    ...lambdaCommon,
    environment: {
      TRANSFER_REMITTANCE_SUCCESS_QUEUE_URL: { Ref: 'notificationSuccessFifoQueue' },
      TRANSFER_REMITTANCE_FAILURE_QUEUE_URL: { Ref: 'transferRemittanceNotificationFailureQueue' },
      SOURCE_EMAIL_ADDRESS: '${self:custom.sourceEmailAddress}',
    },
    policy: {
      managed: [
        ...mpCommonPolicies,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.entityTableWriteItemRolePolicy,
      ],
      inline: {
        publishToTransferRemittanceQueue: [
          {
            effect: 'Allow',
            actions: [
              'sqs:SendMessage',
              'sqs:ReceiveMessage',
              'sqs:DeleteMessage',
              'sqs:GetQueueAttributes',
              'logs:CreateLogGroup',
              'logs:CreateLogStream',
              'logs:PutLogEvents',
            ],
            resources: [
              { 'Fn::GetAtt': ['notificationSuccessFifoQueue', 'Arn'] },
              { 'Fn::GetAtt': ['transferRemittanceNotificationFailureQueue', 'Arn'] },
            ],
          },
        ],
      },
    },
    appsync: {
      fieldName: 'transferRemittanceNotification',
      typeName: 'Mutation',
    },
  },
  sendSmsOrEmailNotification: {
    handler: 'src/lambda/mp/transferRemittance/index.sendSmsOrEmailNotificationHandler',
    name: 'sendSmsOrEmailNotification',
    ...lambdaCommon,
    environment: {
      SOURCE_EMAIL_ADDRESS: '${self:custom.sourceEmailAddress}',
      MESSAGE_MEDIA_API_KEY_SSM_NAME: '${env:STATIC_ENV_NAME}-messagemedia-api-key',
      MESSAGE_MEDIA_API_SECRET_SSM_NAME: '${env:STATIC_ENV_NAME}-messagemedia-api-secret',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        smsNotificationSqsPolicy: [
          {
            effect: 'Allow',
            actions: [
              'sqs:ReceiveMessage',
              'sqs:DeleteMessage',
              'sqs:GetQueueAttributes',
              'logs:CreateLogGroup',
              'logs:CreateLogStream',
              'logs:PutLogEvents',
            ],
            resources: [{ 'Fn::GetAtt': ['notificationSuccessFifoQueue', 'Arn'] }],
          },
        ],
        smsSsmPolicy: [
          {
            actions: ['ssm:GetParameter'],
            resources: ['*'],
          },
        ],
        sendEmailPolicy: [{ effect: 'Allow', actions: ['ses:SendEmail', 'ses:SendRawEmail'], resources: ['*'] }],
      },
    },
  },
  failureDlqLog: {
    handler: 'src/lambda/mp/transferRemittance/index.transferRemittanceFailureDlqLogHandler',
    name: 'failureDlqLog',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies],
      inline: {
        logMessagePolicy: [
          {
            effect: 'Allow',
            actions: [
              'sqs:ReceiveMessage',
              'sqs:DeleteMessage',
              'sqs:GetQueueAttributes',
              'logs:CreateLogGroup',
              'logs:CreateLogStream',
              'logs:PutLogEvents',
            ],
            resources: [{ 'Fn::GetAtt': ['transferRemittanceNotificationFailureDlq', 'Arn'] }],
          },
        ],
      },
    },
  },
  transferRemittanceFailure: {
    handler: 'src/lambda/mp/transferRemittance/index.transferRemittanceFailureHandler',
    name: 'transferRemittanceFailure',
    ...lambdaCommon,
    environment: {
      SOURCE_EMAIL_ADDRESS: '${self:custom.sourceEmailAddress}',
      MESSAGE_MEDIA_API_KEY_SSM_NAME: '${env:STATIC_ENV_NAME}-messagemedia-api-key',
      MESSAGE_MEDIA_API_SECRET_SSM_NAME: '${env:STATIC_ENV_NAME}-messagemedia-api-secret',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        sqsPolicy: [
          {
            effect: 'Allow',
            actions: [
              'sqs:SendMessage',
              'sqs:ReceiveMessage',
              'sqs:DeleteMessage',
              'sqs:GetQueueAttributes',
              'logs:CreateLogGroup',
              'logs:CreateLogStream',
              'logs:PutLogEvents',
            ],
            resources: [{ 'Fn::GetAtt': ['transferRemittanceNotificationFailureQueue', 'Arn'] }],
          },
        ],
        sendEmailPolicy: [{ effect: 'Allow', actions: ['ses:SendEmail', 'ses:SendRawEmail'], resources: ['*'] }],
      },
    },
  },
  getTransferRemittancePdf: {
    handler: 'src/lambda/mp/transferRemittance/index.getTransferRemittancePdfHandler',
    name: 'getTransferRemittancePdf',
    ...lambdaCommon,
    environment: {
      SOURCE_EMAIL_ADDRESS: '${self:custom.sourceEmailAddress}',
      TRANSFER_REMITTANCE_PDF_BUCKET_NAME: '${self:custom.transferRemittanceExportBucket}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        exportToS3Policy: [
          {
            effect: 'Allow',
            actions: ['s3:GetObject', 's3:PutObject'],
            resources: ['arn:aws:s3:::${self:custom.transferRemittanceExportBucket}/transferRemittance/*'],
          },
        ],
      },
    },
    appsync: {
      fieldName: 'getTransferRemittancePdf',
      typeName: 'Query',
    },
  },
};
