import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon, commonBankingProductTablePolicy } from '../../common/env';
import { responseDefault } from '../../common/resolverTemplates';
import { mpCommonPolicies } from '../env';
import { getDebitCardAccountSavingsAccountProductRequest } from '../resolvers/resolverTemplates/getDebitCardAccountSavingsAccountProductRequest';

export const lambdas: ServerlessFunctions = {
  createSavingsAccount: {
    handler: 'src/lambda/mp/debitCardAccount/index.createSavingsAccountHandler',
    name: 'createSavingsAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'createSavingsAccount',
      typeName: 'Mutation',
    },
  },
  createSavingsAccountV2: {
    handler: 'src/lambda/mp/debitCardAccount/index.createSavingsAccountV2Handler',
    name: 'createSavingsAccountV2',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'createSavingsAccountV2',
      typeName: 'Mutation',
    },
  },
  personaliseSavingsAccount: {
    handler: 'src/lambda/mp/debitCardAccount/index.personaliseSavingsAccountHandler',
    name: 'personaliseSavingsAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'personaliseSavingsAccount',
      typeName: 'Mutation',
    },
  },
  personaliseSavingsAccountV2: {
    handler: 'src/lambda/mp/debitCardAccount/index.personaliseSavingsAccountV2Handler',
    name: 'personaliseSavingsAccountV2',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'personaliseSavingsAccountV2',
      typeName: 'Mutation',
    },
  },
  onSavingsAccountUpdateV2: {
    handler: 'src/lambda/mp/debitCardAccount/index.onSavingsAccountUpdateV2Handler',
    name: 'onSavingsAccountUpdateV2',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'onSavingsAccountUpdateV2',
      typeName: 'Subscription',
    },
  },
  getSavingsAccounts: {
    handler: 'src/lambda/mp/debitCardAccount/index.getSavingsAccountsHandler',
    name: 'getSavingsAccounts',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getSavingsAccounts',
      typeName: 'Query',
    },
  },
  getSavingsAccountsV2: {
    handler: 'src/lambda/mp/debitCardAccount/index.getSavingsAccountsV2Handler',
    name: 'getSavingsAccountsV2',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        sortKeyGsiQueryPolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:Query', 'dynamodb:BatchGetItem'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.entitySortKeyGsi}',
            ],
          },
        ],
      },
    },
    appsync: {
      fieldName: 'getSavingsAccountsV2',
      typeName: 'Query',
      template: {
        request: `
          #set($maxLimit = 50)
          #set($limit = $context.args.limit)
          #if ($limit > $maxLimit) {
          $utils.error("Limit exceeds maximum (50)")
          }
          #end
          {
            "version" : "2018-05-29",
            "operation": "Invoke",
            "payload": {
              "args": $util.toJson($context.args),
              "identity": $util.toJson($context.identity),
              "request": $util.toJson($context.request),
              "authType": $util.toJson($util.authType()),
              "source": $util.toJson($context.source), 
              "info": $util.toJson($context.info)
            }
          }
        `,
        response: responseDefault,
      },
    },
  },
  getSavingsAccountV2: {
    handler: 'src/lambda/mp/debitCardAccount/index.getSavingsAccountV2Handler',
    name: 'getSavingsAccountV2',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getSavingsAccountV2',
      typeName: 'Query',
    },
  },
  getSavingsAccountProductByAccount: {
    handler: 'src/lambda/mp/debitCardAccount/index.getSavingsAccountProductByAccountHandler',
    name: 'getSavingsAccountProductByAccount',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: { bankingProductTablePolicy: commonBankingProductTablePolicy },
    },
    appsync: {
      fieldName: 'savingsAccountProduct',
      typeName: 'DebitCardAccountV2',
      template: {
        request: getDebitCardAccountSavingsAccountProductRequest,
        response: responseDefault,
      },
    },
  },
  getSavingsAccountProductByEntity: {
    handler: 'src/lambda/mp/debitCardAccount/index.getSavingsAccountProductByEntityHandler',
    name: 'getSavingsAccountProductByEntity',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: { bankingProductTablePolicy: commonBankingProductTablePolicy },
    },
  },
};

export const lambdasForNewStack: ServerlessFunctions = {
  initiateSavingsAccountSecureSession: {
    handler: 'src/lambda/mp/savingsAccount/index.initiateSavingsAccountSecureSession',
    name: 'initiateSavingsAccountSecureSession',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'initiateSavingsAccountSecureSession',
      typeName: 'Mutation',
    },
  },
  createSavingsAccountV3: {
    handler: 'src/lambda/mp/debitCardAccount/index.createSavingsAccountV3Handler',
    name: 'createSavingsAccountV3',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'createSavingsAccountV3',
      typeName: 'Mutation',
    },
  },
};
