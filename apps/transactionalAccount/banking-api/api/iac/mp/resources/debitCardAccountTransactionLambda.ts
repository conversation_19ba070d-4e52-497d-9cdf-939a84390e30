import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon } from '../../common/env';
import { requestDefault, responseDefault } from '../../common/resolverTemplates';
import { mpCommonPolicies } from '../env';
import { getDebitCardTransactionsRequest } from '../resolvers/resolverTemplates/getDebitCardTransactionsRequest';
import { getDebitCardTxnsRequest } from '../resolvers/resolverTemplates/getDebitCardTxnsRequest';

export const lambdas: ServerlessFunctions = {
  searchBpayBillers: {
    handler: 'src/lambda/mp/debitCardAccountTransaction/index.searchBpayBillersHandler',
    name: 'searchBpayBillers',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'searchBpayBillers',
      typeName: 'Query',
      template: {
        request: requestDefault,
        response: responseDefault,
      },
    },
  },
  getBpayBillerDetail: {
    handler: 'src/lambda/mp/debitCardAccountTransaction/index.getBpayBillerDetailHandler',
    name: 'getBpayBillerDetail',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getBpayBillerDetail',
      typeName: 'Query',
      template: {
        request: requestDefault,
        response: responseDefault,
      },
    },
  },
  getBpayRestrictedBillers: {
    handler: 'src/lambda/mp/debitCardAccountTransaction/index.getBpayRestrictedBillersHandler',
    name: 'getBpayRestrictedBillers',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getBpayRestrictedBillers',
      typeName: 'Query',
      template: {
        request: requestDefault,
        response: responseDefault,
      },
    },
  },
  validateBpayPayment: {
    handler: 'src/lambda/mp/debitCardAccountTransaction/index.validateBpayPaymentHandler',
    name: 'validateBpayPayment',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    appsync: {
      fieldName: 'validateBpayPayment',
      typeName: 'Query',
      template: {
        request: requestDefault,
        response: responseDefault,
      },
    },
  },
  submitStaticCrnBpayPayment: {
    handler: 'src/lambda/mp/debitCardAccountTransaction/index.submitStaticCrnBpayPaymentHandler',
    name: 'submitStaticCrnBpayPayment',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'submitStaticCrnBpayPayment',
      typeName: 'Mutation',
      template: {
        request: requestDefault,
        response: responseDefault,
      },
    },
  },
  submitDynamicCrnBpayPayment: {
    handler: 'src/lambda/mp/debitCardAccountTransaction/index.submitDynamicCrnBpayPaymentHandler',
    name: 'submitDynamicCrnBpayPayment',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'submitDynamicCrnBpayPayment',
      typeName: 'Mutation',
      template: {
        request: requestDefault,
        response: responseDefault,
      },
    },
  },
  getOutstandingTransactions: {
    handler: 'src/lambda/mp/debitCardAccountTransaction/index.getOutstandingTransactionsHandler',
    name: 'getOutstandingTransactions',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        debitCardIdGsiV1QueryPolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:Query', 'dynamodb:BatchGetItem'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.debitCardIdGsi}',
            ],
          },
        ],
      },
    },
    appsync: {
      fieldName: 'outstandingTransactions',
      typeName: 'DebitCardV2',
      template: {
        request: `
          {
            "version" : "2018-05-29",
            "operation": "Invoke",
            "payload": {
            "args": {
                "input": {
                  "debitCardId": $util.toJson($context.source.id),
                  "entityUuid": $util.toJson($context.source.entityUuid)
                }
              },
              "identity": $util.toJson($context.identity),
              "request": $util.toJson($context.request),
              "authType": $util.toJson($util.authType()),
              "source": $util.toJson($context.source),
              "info": $util.toJson($context.info)
            } 
          }
        `,
        response: responseDefault,
      },
    },
  },
  transferFunds: {
    handler: 'src/lambda/mp/debitCardAccountTransaction/index.transferFundsHandler',
    name: 'transferFunds',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
      ZELLER_APP_FIREBASE_ADMIN_PRIVATE_KEY_SSM_NAME: '${self:custom.firebaseAdminPrivateKeySsmName}',
      ZELLER_APP_FIREBASE_ADMIN_EMAIL_SSM_NAME: '${self:custom.firebaseAdminEmailSsmName}',
      ZELLER_APP_AUTH0_CLIENT_ID: '${self:custom.zellerAppAuth0ClientId}',
      BEDROCK_MODEL_NAME: '${self:custom.bedrockModelName}',
      BEDROCK_REGION: '${self:custom.bedrockRegion}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        abusiveDescriptionCachePolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:PutItem', 'dynamodb:GetItem', 'dynamodb:BatchGetItem', 'dynamodb:Query'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${env:STATIC_ENV_NAME}-mp-api-dynamodb-AbusiveDescriptionCache',
            ],
          },
        ],
        bedrockInvokeModelPolicy: [
          {
            effect: 'Allow',
            actions: ['bedrock:InvokeModel'],
            resources: [
              'arn:aws:bedrock:${self:custom.bedrockRegion}::foundation-model/${self:custom.bedrockModelName}',
            ],
          },
        ],
        firebaseAdminParameterPolicy: [
          {
            effect: 'Allow',
            actions: ['ssm:GetParameter'],
            resources: [
              'arn:aws:ssm:${self:provider.region}:${self:custom.accountId}:parameter${self:custom.firebaseAdminEmailSsmName}',
              'arn:aws:ssm:${self:provider.region}:${self:custom.accountId}:parameter${self:custom.firebaseAdminPrivateKeySsmName}',
            ],
          },
        ],
      },
    },
    appsync: {
      fieldName: 'transferFundsDebitCardAccount',
      typeName: 'Mutation',
    },
  },
  getDebitCardTransactions: {
    handler: 'src/lambda/mp/debitCardAccountTransaction/index.getDebitCardTransactionsHandler',
    name: 'getDebitCardTransactions',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        debitCardIdGsiV1QueryPolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:Query', 'dynamodb:BatchGetItem'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.debitCardIdGsi}',
            ],
          },
        ],
      },
    },
    appsync: {
      fieldName: 'getDebitCardTransactions',
      typeName: 'Query',
      template: {
        request: getDebitCardTransactionsRequest,
        response: responseDefault,
      },
    },
  },
  getDebitCardAccountTxn: {
    handler: 'src/lambda/mp/debitCardAccount/index.getDebitCardAccountTxnHandler',
    name: 'getDebitCardAccountTxn',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardTransactionV2',
      typeName: 'Query',
      template: {
        request: requestDefault,
        response: responseDefault,
      },
    },
  },
  getDebitCardAccountTxns: {
    handler: 'src/lambda/mp/debitCardAccount/index.getDebitCardAccountTxnsHandler',
    name: 'getDebitCardAccountTxns',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardTransactionsV2',
      typeName: 'Query',
      template: {
        request: getDebitCardTxnsRequest,
        response: responseDefault,
      },
    },
  },
  onDebitCardTransactionUpdateLambda: {
    handler: 'src/lambda/mp/publishers/publisherHandler.onDebitCardTransactionUpdateHandler',
    name: 'onDebitCardTransactionUpdate',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'onDebitCardTransactionUpdate',
      typeName: 'Subscription',
    },
  },
};
