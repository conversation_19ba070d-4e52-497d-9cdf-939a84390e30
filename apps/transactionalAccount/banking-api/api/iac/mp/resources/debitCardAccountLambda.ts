import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import { Action } from '@npco/component-bff-serverless/dist/param/actions';
import { Arn } from '@npco/component-bff-serverless/dist/param/arn';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon } from '../../common/env';
import { requestDefault, responseDefault } from '../../common/resolverTemplates';
import { mpCommonPolicies } from '../env';

export const lambdas: ServerlessFunctions = {
  getDebitCardAccountV3: {
    handler: 'src/lambda/mp/debitCardAccount/index.getDebitCardAccountV3Handler',
    name: 'getDebitCardAccountV3',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardAccountV3',
      typeName: 'Query',
    },
  },
  getDebitCardAccountsV3: {
    handler: 'src/lambda/mp/debitCardAccount/index.getDebitCardAccountsV3Handler',
    name: 'getDebitCardAccountsV3',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardAccountsV3',
      typeName: 'Query',
      template: {
        request: `
          #set($maxLimit = 50)
          #set($limit = $context.args.limit)
          #if ($limit > $maxLimit) {
          $utils.error("Limit exceeds maximum (50)")
          }
          #end
          {
            "version" : "2018-05-29",
            "operation": "Invoke",
            "payload": {
              "args": $util.toJson($context.args),
              "identity": $util.toJson($context.identity),
              "request": $util.toJson($context.request),
              "authType": $util.toJson($util.authType()),
              "source": $util.toJson($context.source), 
              "info": $util.toJson($context.info)
            }
          }
        `,
        response: responseDefault,
      },
    },
  },
  getIssuingAccount: {
    handler: 'src/lambda/mp/debitCardAccount/index.getIssuingAccountHandler',
    name: 'getIssuingAccount',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        sortKeyGsiQueryPolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:Query', 'dynamodb:BatchGetItem'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.sortKeyGsi}',
            ],
          },
        ],
      },
    },
    appsync: {
      fieldName: 'getIssuingAccount',
      typeName: 'Query',
      template: {
        request: requestDefault,
        response: responseDefault,
      },
    },
  },
  getIssuingAccountBalances: {
    handler: 'src/lambda/mp/debitCardAccount/index.getIssuingAccountBalancesHandler',
    name: 'getIssuingAccountBalances',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        gsiQueryPolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:Query', 'dynamodb:BatchGetItem'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.sortKeyGsi}',
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.entitySortKeyGsi}',
            ],
          },
        ],
      },
    },
    appsync: {
      fieldName: 'getIssuingAccountBalances',
      typeName: 'Query',
      template: {
        request: requestDefault,
        response: responseDefault,
      },
    },
  },
  closeDebitCardAccount: {
    handler: 'src/lambda/mp/debitCardAccount/index.closeDebitCardAccountHandler',
    name: 'closeDebitCardAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
      CLOSE_ALL_DEBIT_CARDS_HANDLER_NAME: '${self:provider.stackName}-closeAllDebitCards',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        invokeCloseAllDebitCardsLambdaPolicy: [
          {
            actions: [Action.lambda.InvokeFunction],
            resources: [Arn.lambda.function('${self:provider.stackName}-closeAllDebitCards*')],
          },
        ],
      },
    },
    appsync: {
      fieldName: 'closeDebitCardAccount',
      typeName: 'Mutation',
    },
  },
  onDebitCardAccountUpdateV3: {
    handler: 'src/lambda/mp/debitCardAccount/index.onDebitCardAccountUpdateV3Handler',
    name: 'onDebitCardAccountUpdateV3',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'onDebitCardAccountUpdateV3',
      typeName: 'Subscription',
    },
  },
  getDebitCardAccountV2: {
    handler: 'src/lambda/mp/debitCardAccount/index.getDebitCardAccountV2Handler',
    name: 'getDebitCardAccountV2',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardAccountV2',
      typeName: 'Query',
      template: {
        request: requestDefault,
        response: responseDefault,
      },
    },
  },
  getDebitCardAccountsV2: {
    handler: 'src/lambda/mp/debitCardAccount/index.getDebitCardAccountsV2Handler',
    name: 'getDebitCardAccountsV2',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardAccountsV2',
      typeName: 'Query',
      template: {
        request: `
          #set($maxLimit = 50)
          #set($limit = $context.args.limit)
          #if ($limit > $maxLimit) {
          $utils.error("Limit exceeds maximum (50)")
          }
          #end
          {
            "version" : "2018-05-29",
            "operation": "Invoke",
            "payload": {
              "args": $util.toJson($context.args),
              "identity": $util.toJson($context.identity),
              "request": $util.toJson($context.request),
              "authType": $util.toJson($util.authType()),
              "source": $util.toJson($context.source), 
              "info": $util.toJson($context.info)
            }
          }
      `,
        response: responseDefault,
      },
    },
  },
  getDebitCardAccountBalances: {
    handler: 'src/lambda/mp/debitCardAccount/index.getDebitCardAccountBalancesHandler',
    name: 'getDebitCardAccountBalances',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardAccountBalances',
      typeName: 'Query',
      template: {
        request: requestDefault,
        response: responseDefault,
      },
    },
  },
  onDebitCardAccountUpdateLambda: {
    handler: 'src/lambda/mp/publishers/publisherHandler.onDebitCardAccountUpdateHandler',
    name: 'onDebitCardAccountUpdate',
    ...lambdaCommon,
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  personaliseDebitCardAccount: {
    handler: 'src/lambda/mp/debitCardAccount/index.personaliseDebitCardAccountHandler',
    name: 'personaliseDebitCardAccount',
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    ...lambdaCommon,
    appsync: {
      fieldName: 'personaliseDebitCardAccount',
      typeName: 'Mutation',
    },
    policy: { managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy] },
  },
  createDebitCardAccount: {
    handler: 'src/lambda/mp/debitCardAccount/index.createDebitCardAccountHandler',
    name: 'createDebitCardAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'createDebitCardAccount',
      typeName: 'Mutation',
    },
  },
};
