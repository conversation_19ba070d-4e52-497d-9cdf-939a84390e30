import { createLambdaDataResolver } from '@npco/component-bff-serverless/dist';

import { responseDefault } from '../../common/resolverTemplates';

import { getDebitCardAccountCardsCountV3Request } from './resolverTemplates/getDebitCardAccountCardsCountV3Request';
import { getDebitCardTxnMerchantDetailsRequest } from './resolverTemplates/getDebitCardTxnMerchantDetailsRequest';
import { getDebitCardsInDebitCardAccountV3Request } from './resolverTemplates/getDebitCardsInDebitCardAccountV3Request';
import { getSavingsAccountV2SavingsAccountProductRequest } from './resolverTemplates/getSavingsAccountV2SavingsAccountProductRequest';

export const bffResolvers = [
  {
    lambda: '${self:custom.getMerchantDetailsLambda}',
    name: 'getDebitCardTxnMerchantDetails',
    appsync: {
      fieldName: 'merchant',
      typeName: 'DebitCardTransactionV2',
      template: {
        request: getDebitCardTxnMerchantDetailsRequest,
        response: responseDefault,
      },
    },
  },
  {
    lambda: '${self:custom.getMerchantDetailsLambda}',
    name: 'getDcaTransactionMerchantDetails',
    appsync: {
      fieldName: 'merchant',
      typeName: 'DebitCardAccountTransaction',
      template: {
        request: getDebitCardTxnMerchantDetailsRequest,
        response: responseDefault,
      },
    },
  },
  {
    lambda: '${self:custom.getSavingsAccountProductByAccountLambda}',
    name: 'getSavingsAccountV2SavingsAccountProduct',
    appsync: {
      fieldName: 'savingsAccountProduct',
      typeName: 'SavingsAccountV2',
      template: {
        request: getSavingsAccountV2SavingsAccountProductRequest,
        response: responseDefault,
      },
    },
  },
  {
    lambda: '${self:custom.getDebitCardsInDebitCardAccountLambda}',
    name: 'getDebitCardsInDebitCardAccountV3',
    appsync: {
      fieldName: 'cards',
      typeName: 'DebitCardAccountV3',
      template: {
        request: getDebitCardsInDebitCardAccountV3Request,
        response: responseDefault,
      },
    },
  },
  {
    lambda: '${self:custom.getDebitCardsInDebitCardAccountLambda}',
    name: 'getDebitCardsInDebitCardAccountV2',
    appsync: {
      fieldName: 'cards',
      typeName: 'DebitCardAccountV2',
      template: {
        request: getDebitCardsInDebitCardAccountV3Request,
        response: responseDefault,
      },
    },
  },
  {
    lambda: '${self:custom.getDebitCardAccountCardsCountLambda}',
    name: 'getDebitCardAccountCardsCountV3',
    appsync: {
      fieldName: 'cardsCount',
      typeName: 'DebitCardAccountV3',
      template: {
        request: getDebitCardAccountCardsCountV3Request,
        response: responseDefault,
      },
    },
  },
  {
    lambda: '${self:custom.getDebitCardAccountCardsCountLambda}',
    name: 'getDebitCardAccountCardsCountV2',
    appsync: {
      fieldName: 'cardsCount',
      typeName: 'DebitCardAccountV2',
      template: {
        request: `
        {
          "version" : "2018-05-29",
          "operation": "Invoke",
          "payload": {
            "args": { "debitCardAccountUuid": $util.toJson($context.source.id) },
            "identity": $util.toJson($context.identity),
            "request": $util.toJson($context.request),
            "authType": $util.toJson($util.authType()),
            "source": $util.toJson($context.source),
            "info": $util.toJson($context.info)
          } 
        } `,
        response: responseDefault,
      },
    },
  },
  {
    lambda: '${self:custom.getOutstandingTransactionsLambda}',
    name: 'getOutstandingTransactionsLambdaForV2Update',
    appsync: {
      fieldName: 'outstandingTransactions',
      typeName: 'DebitCardV2Update',
      template: {
        request: `
          {
            "version" : "2018-05-29",
            "operation": "Invoke",
            "payload": {
            "args": {
                "input": {
                  "debitCardId": $util.toJson($context.source.id),
                  "entityUuid": $util.toJson($context.source.entityUuid)
                }
              },
              "identity": $util.toJson($context.identity),
              "request": $util.toJson($context.request),
              "authType": $util.toJson($util.authType()),
              "source": $util.toJson($context.source),
              "info": $util.toJson($context.info)
            } 
          }
        `,
        response: responseDefault,
      },
    },
  },
  {
    lambda: '${self:custom.getDebitCardAccountV2Lambda}',
    name: '${self:custom.getDebitCardAccountV2Lambda}',
    appsync: {
      fieldName: 'debitCardAccount',
      typeName: 'DebitCardV2',
      dataResolverName: {
        customDataSourceName: 'getDCAV2DataSource',
        customResolverName: 'getDCAV2Resolver',
      },
      template: {
        request: `{
          "version":"2018-05-29",
          "operation":"Invoke",
          "payload":{
            "args":{"debitCardAccountUuid": $util.toJson($context.source.debitCardAccountUuid) ,"entityUuid": $util.toJson($context.source.entityUuid) },
            "identity": $util.toJson($context.identity),
            "request": $util.toJson($context.request),
            "authType": $util.toJson($util.authType()),
            "source": $util.toJson($context.source),
            "info": $util.toJson($context.info),
            }
          }`,
      },
    },
  },
  {
    lambda: '${self:custom.getDebitCardAccountV2Lambda}',
    name: '${self:custom.getDebitCardAccountV2Lambda}',
    appsync: {
      fieldName: 'debitCardAccount',
      typeName: 'DebitCardTransactionCounterparty',
      dataResolverName: {
        customDataSourceName: 'getTransactionCounterpartyDataSource',
        customResolverName: 'getTransactionCounterpartyDebitCardAccountResolver',
      },
      template: {
        request: `
        #if ($util.isNullOrEmpty($context.source.debitCardAccountUuid))
          #return
          #end
          {
            "version" : "2018-05-29",
            "operation": "Invoke",
            "payload": {
              "args": { "debitCardAccountUuid": $util.toJson($context.source.debitCardAccountUuid), "entityUuid": $util.toJson($context.info.variables.item.entityUuid) },
              "identity": $util.toJson($context.identity),
              "request": $util.toJson($context.request),
              "authType": $util.toJson($util.authType()),
              "source": $util.toJson($context.source),
              "info": $util.toJson($context.info)
            } 
          }
        `,
        response: responseDefault,
      },
    },
  },
  {
    lambda: '${self:custom.getDebitCardAccountV2Lambda}',
    name: '${self:custom.getDebitCardAccountV2Lambda}',
    appsync: {
      fieldName: 'debitCardAccount',
      typeName: 'DebitCardTransactionCounterpartyEvent',
      dataResolverName: {
        customDataSourceName: 'getTransactionCounterpartyEventDataSource',
        customResolverName: 'getTransactionCounterpartyEventDebitCardAccountResolver',
      },
      template: {
        request: `
        #if ($util.isNullOrEmpty($context.source.debitCardAccountUuid))
          #return
          #end
          {
            "version" : "2018-05-29",
            "operation": "Invoke",
            "payload": {
              "args": { "debitCardAccountUuid": $util.toJson($context.source.debitCardAccountUuid), "entityUuid": $util.toJson($context.info.variables.item.entityUuid) },
              "identity": $util.toJson($context.identity),
              "request": $util.toJson($context.request),
              "authType": $util.toJson($util.authType()),
              "source": $util.toJson($context.source),
              "info": $util.toJson($context.info)
            } 
          }
        `,
        response: responseDefault,
      },
    },
  },
].reduce(
  (res, resolver) => ({
    ...res,
    ...createLambdaDataResolver(resolver.name, resolver.lambda, resolver.appsync, false),
  }),
  {},
);
