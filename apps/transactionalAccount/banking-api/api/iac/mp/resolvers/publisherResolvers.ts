import { responseDefault } from '../../common/resolverTemplates';

export const publisherResolvers = (custom: Record<string, any>) => {
  return {
    Resources: {
      // add this after the txn export service is moved - cannot create without the lambda and gql types
      // // publishDebitCardTransactionsExport
      // publishDebitCardTransactionsExportDataSource: {
      //   Type: 'AWS::AppSync::DataSource',
      //   Properties: {
      //     Name: 'publishDebitCardTransactionsExportDataSource',
      //     ApiId: custom.appSyncApiId,
      //     Type: 'NONE',
      //   },
      // },
      // publishDebitCardTransactionsExportResolver: {
      //   Type: 'AWS::AppSync::Resolver',
      //   Properties: {
      //     ApiId: custom.appSyncApiId,
      //     /// / need to be uncommented when the txn export service is moved
      //     // FieldName: 'publishDebitCardTransactionsExport',
      //     // TypeName: 'Mutation',
      //     RequestMappingTemplate: `{
      //       "version": "2018-05-29",
      //       "payload": $util.toJson($context.arguments.input)
      //       }`,
      //     ResponseMappingTemplate: responseDefault,
      //     DataSourceName: { 'Fn::GetAtt': ['publishDebitCardTransactionsExportDataSource', 'Name'] },
      //   },
      // },

      // publishSavingsAccountUpdateEventV2
      publishSavingsAccountUpdateEventV2DataSource: {
        Type: 'AWS::AppSync::DataSource',
        Properties: {
          Name: 'publishSavingsAccountUpdateEventV2DataSource',
          ApiId: custom.appSyncApiId,
          Type: 'NONE',
        },
      },
      publishSavingsAccountUpdateEventV2Resolver: {
        Type: 'AWS::AppSync::Resolver',
        Properties: {
          ApiId: custom.appSyncApiId,
          FieldName: 'publishSavingsAccountUpdateEventV2',
          TypeName: 'Mutation',
          RequestMappingTemplate: `{
            "version": "2018-05-29",
            "payload": $util.toJson($context.arguments.input)
            }`,
          ResponseMappingTemplate: responseDefault,
          DataSourceName: { 'Fn::GetAtt': ['publishSavingsAccountUpdateEventV2DataSource', 'Name'] },
        },
      },

      // publishDebitCardAccountUpdateEventV3
      publishDebitCardAccountUpdateEventV3DataSource: {
        Type: 'AWS::AppSync::DataSource',
        Properties: {
          Name: 'publishDebitCardAccountUpdateEventV3DataSource',
          ApiId: custom.appSyncApiId,
          Type: 'NONE',
        },
      },
      publishDebitCardAccountUpdateEventV3Resolver: {
        Type: 'AWS::AppSync::Resolver',
        Properties: {
          ApiId: custom.appSyncApiId,
          FieldName: 'publishDebitCardAccountUpdateEventV3',
          TypeName: 'Mutation',
          RequestMappingTemplate: `{
            "version": "2018-05-29",
            "payload": $util.toJson($context.arguments.input)
            }`,
          ResponseMappingTemplate: responseDefault,
          DataSourceName: { 'Fn::GetAtt': ['publishDebitCardAccountUpdateEventV3DataSource', 'Name'] },
        },
      },

      // publishDebitCardTransactionUpdateEvent
      publishDebitCardTransactionUpdateEventDataSource: {
        Type: 'AWS::AppSync::DataSource',
        Properties: {
          Name: 'publishDebitCardTransactionUpdateEventDataSource',
          ApiId: custom.appSyncApiId,
          Type: 'NONE',
        },
      },
      publishDebitCardTransactionUpdateEventResolver: {
        Type: 'AWS::AppSync::Resolver',
        Properties: {
          ApiId: custom.appSyncApiId,
          FieldName: 'publishDebitCardTransactionUpdateEvent',
          TypeName: 'Mutation',
          RequestMappingTemplate: `{
            "version": "2018-05-29",
            "payload": $util.toJson($context.arguments.transaction)
            }`,
          ResponseMappingTemplate: responseDefault,
          DataSourceName: { 'Fn::GetAtt': ['publishDebitCardTransactionUpdateEventDataSource', 'Name'] },
        },
      },

      // publishDebitCardAccountCardUpdateEvent
      publishDebitCardAccountCardUpdateEventDataSource: {
        Type: 'AWS::AppSync::DataSource',
        Properties: {
          Name: 'publishDebitCardAccountCardUpdateEventDataSource',
          ApiId: custom.appSyncApiId,
          Type: 'NONE',
        },
      },
      publishDebitCardAccountCardUpdateEventResolver: {
        Type: 'AWS::AppSync::Resolver',
        Properties: {
          ApiId: custom.appSyncApiId,
          FieldName: 'publishDebitCardUpdateEvent',
          TypeName: 'Mutation',
          RequestMappingTemplate: `{
            "version": "2018-05-29",
            "payload": $util.toJson($context.arguments.debitCard)
            }`,
          ResponseMappingTemplate: responseDefault,
          DataSourceName: { 'Fn::GetAtt': ['publishDebitCardAccountCardUpdateEventDataSource', 'Name'] },
        },
      },

      // publishDebitCardAccountUpdateEvent
      publishDebitCardAccountUpdateEventDataSource: {
        Type: 'AWS::AppSync::DataSource',
        Properties: {
          Name: 'publishDebitCardAccountUpdateEventDataSource',
          ApiId: custom.appSyncApiId,
          Type: 'NONE',
        },
      },
      publishDebitCardAccountUpdateEventResolver: {
        Type: 'AWS::AppSync::Resolver',
        Properties: {
          ApiId: custom.appSyncApiId,
          FieldName: 'publishDebitCardAccountUpdateEvent',
          TypeName: 'Mutation',
          RequestMappingTemplate: `{
            "version": "2018-05-29",
            "payload": $util.toJson($context.arguments.debitCardAccount)
            }`,
          ResponseMappingTemplate: responseDefault,
          DataSourceName: { 'Fn::GetAtt': ['publishDebitCardAccountUpdateEventDataSource', 'Name'] },
        },
      },
    },
  };
};
