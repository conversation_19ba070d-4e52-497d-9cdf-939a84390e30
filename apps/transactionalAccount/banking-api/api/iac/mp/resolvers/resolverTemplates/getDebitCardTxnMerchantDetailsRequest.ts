export const getDebitCardTxnMerchantDetailsRequest = `
#if ($util.isNullOrEmpty($context.source.merchantId))
  #return
#end
{
    "version" : "2018-05-29",
    "operation": "Invoke",
    "payload": {
        "args": { "merchantUuid": $util.toJson($context.source.merchantId) },
        "identity": $util.toJson($context.identity),
        "request": $util.toJson($context.request),
        "authType": $util.toJson($util.authType()),
        "source": $util.toJson($context.source),
        "info": $util.toJson($context.info)
    }
}
`;
