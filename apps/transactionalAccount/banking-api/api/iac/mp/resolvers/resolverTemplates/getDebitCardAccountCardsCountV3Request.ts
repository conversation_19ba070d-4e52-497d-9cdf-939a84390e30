export const getDebitCardAccountCardsCountV3Request = `
#if($context.info.variables.item.entityUuid)
  {
    "version": "2018-05-29",
    "operation": "Invoke",
    "payload": {
      "args": { 
        "debitCardAccountUuid": $util.toJson($context.source.id),
        "entityUuid": $util.toJson($context.info.variables.item.entityUuid)
      },
      "identity": $util.toJson($context.identity),
      "request": $util.toJson($context.request),
      "authType": $util.toJson($util.authType())
    }
  }
  #else
  {
    "version": "2018-05-29",
    "operation": "Invoke",
    "payload": {
      "args": {
        "debitCardAccountUuid": $util.toJson($context.source.id)
      },
      "identity": $util.toJson($context.identity),
      "request": $util.toJson($context.request)
    }
  }
  #end
`;
