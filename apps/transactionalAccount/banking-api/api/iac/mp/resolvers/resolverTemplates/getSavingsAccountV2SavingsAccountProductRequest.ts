export const getSavingsAccountV2SavingsAccountProductRequest = `
#if ($util.isNullOrEmpty($context.source.id))
  #return
#end
{
    "version" : "2018-05-29",
    "operation": "Invoke",
    "payload": {
        "args": { "savingsAccountUuid": $util.toJson($context.source.id) },
        "identity": $util.toJson($context.identity),
        "request": $util.toJson($context.request),
        "authType": $util.toJson($util.authType()),
        "source": $util.toJson($context.source),
        "info": $util.toJson($context.info)
    }
}
`;
