export const getDebitCardTxnsRequest = `
#set($maxLimit = 50)
#set($limit = $context.args.limit)
#if (!$util.isNullOrEmpty($context.args.limit) and $limit > $maxLimit) {
    $utils.error("Limit exceeds maximum (50)")
    }
#end

#set ( $context.args.originalFilter = $context.args.filter)
#set( $context.args.filter = $util.parseJson($context.args.filter))

#set ( $advancedFilters = [] )

#if (!$util.isNullOrEmpty($context.args.filter.timestamp))
  #set( $context.args.timestampFilter = $util.transform.toDynamoDBFilterExpression({"timestampFilter": $context.args.filter.timestamp}) )
  $util.qr($context.args.filter.remove("timestamp"))
#end

#if (!$util.isNullOrEmpty($context.args.filter.type))
    #set($context.args.filter.transactionType = $context.args.filter.type)
#end
$util.qr($context.args.filter.remove("type"))

#if ( !$util.isNullOrEmpty($context.args.filter.reference) )
    #if ( !$util.isNullOrEmpty($context.args.filter.reference.eq) )
        #set($referenceFilter = {"expression":"(#referenceLowerCase = :or_0_referenceLowerCase_eq) OR (#referencePayeeLowerCase = :or_1_referencePayeeLowerCase_eq) OR (#descriptionLowerCase = :or_2_descriptionLowerCase_eq)","expressionNames":{"#referenceLowerCase":"referenceLowerCase","#referencePayeeLowerCase":"referencePayeeLowerCase","#descriptionLowerCase":"descriptionLowerCase"},"expressionValues":{":or_2_descriptionLowerCase_eq":{"S":$util.str.toLower($context.args.filter.reference.eq)}, ":or_1_referencePayeeLowerCase_eq":{"S":$util.str.toLower($context.args.filter.reference.eq)},":or_0_referenceLowerCase_eq":{"S":$util.str.toLower($context.args.filter.reference.eq)}}})
    #end
    #if ( !$util.isNullOrEmpty($context.args.filter.reference.contains) )
        #set($referenceFilter = {"expression":"(contains(#referenceLowerCase,:or_0_referenceLowerCase_contains)) OR (contains(#referencePayeeLowerCase,:or_1_referencePayeeLowerCase_contains)) OR (contains(#descriptionLowerCase,:or_2_descriptionLowerCase_contains))","expressionNames":{"#referenceLowerCase":"referenceLowerCase","#referencePayeeLowerCase":"referencePayeeLowerCase","#descriptionLowerCase":"descriptionLowerCase"},"expressionValues":{":or_2_descriptionLowerCase_contains":{"S":$util.str.toLower($context.args.filter.reference.contains)},":or_1_referencePayeeLowerCase_contains":{"S":$util.str.toLower($context.args.filter.reference.contains)},":or_0_referenceLowerCase_contains":{"S":$util.str.toLower($context.args.filter.reference.contains)}}})
    #end
    #if ( !$util.isNullOrEmpty($context.args.filter.reference.beginsWith) )
        #set($referenceFilter = {"expression":"(begins_with(#referenceLowerCase,:or_0_referenceLowerCase_beginsWith)) OR (begins_with(#referencePayeeLowerCase,:or_1_referencePayeeLowerCase_beginsWith)) OR (begins_with(#descriptionLowerCase,:or_2_descriptionLowerCase_beginsWith))","expressionNames":{"#referenceLowerCase":"referenceLowerCase","#referencePayeeLowerCase":"referencePayeeLowerCase","#descriptionLowerCase":"descriptionLowerCase"},"expressionValues":{":or_0_referenceLowerCase_beginsWith":{"S":$util.str.toLower($context.args.filter.reference.beginsWith)},":or_1_referencePayeeLowerCase_beginsWith":{"S":$util.str.toLower($context.args.filter.reference.beginsWith)},":or_2_descriptionLowerCase_beginsWith":{"S":$util.str.toLower($context.args.filter.reference.beginsWith)}}})
    #end
    $util.qr($advancedFilters.add($referenceFilter))
#end
$util.qr($context.args.filter.remove("reference"))

#if ( !$util.isNullOrEmpty($context.args.filter.attachment) )
    #if ( $context.args.filter.attachment )
        #set($attachmentsFilter = {"expression":"size(#attachments) > :attachmentSize","expressionNames":{"#attachments":"attachments"},"expressionValues":{":attachmentSize":{"N":"0"}}})
    #else 
        #set($attachmentsFilter = {"expression":"size(#attachments) = :attachmentSize OR attribute_not_exists(#attachments)","expressionNames":{"#attachments":"attachments"},"expressionValues":{":attachmentSize":{"N":"0"}}})
    #end
    $util.qr($advancedFilters.add($attachmentsFilter))
#end
$util.qr($context.args.filter.remove("attachment"))

#if ( !$util.isNullOrEmpty($context.args.filter.contactUuid) )
    #if ( !$util.isNullOrEmpty($context.args.filter.category) )
        #if ( !$util.isNullOrEmpty($context.args.filter.subcategory) )
            #set($context.args.contact = $util.toJson({ "contactUuid": $context.args.filter.contactUuid, "category": $context.args.filter.category, "subcategoryUuid": $context.args.filter.subcategory }))
        #else
            #set($context.args.contact = $util.toJson({ "contactUuid": $context.args.filter.contactUuid, "category": $context.args.filter.category }))
        #end
    #else
        #set($context.args.contact = $util.toJson({ "contactUuid": $context.args.filter.contactUuid }))
    #end
#elseif ( !$util.isNullOrEmpty($context.args.filter.category) )
    #if ( !$util.isNullOrEmpty($context.args.filter.subcategory) )
        #set($context.args.contact = $util.toJson({ "category": $context.args.filter.category, "subcategoryUuid": $context.args.filter.subcategory }))
    #else
        #set($context.args.contact = $util.toJson({ "category": $context.args.filter.category }))
    #end
#end
$util.qr($context.args.filter.remove("subcategory"))
$util.qr($context.args.filter.remove("category"))
$util.qr($context.args.filter.remove("contactUuid"))

#set ( $nonNullFilter = {} )
#foreach ( $f in $context.args.filter.entrySet() )
    #if ( !$util.isNullOrEmpty($f.value) )
        $util.qr( $nonNullFilter.put($f.key, $f.value) )
    #end
#end

#set( $filter = $util.transform.toDynamoDBFilterExpression($nonNullFilter))
#set( $filter = $util.parseJson($filter))
#if(!$util.isNullOrEmpty($filter.expression))
    #set( $filterExpression = $filter.expression )
#end

#set ( $finalExpression = $filterExpression )
#set ( $finalExpressionNames = $filter.expressionNames )
#set ( $finalExpressionValues = $filter.expressionValues )
#foreach ( $f in $advancedFilters )
    #if ( !$util.isNullOrEmpty($finalExpression) )
        #set( $finalExpression = "$finalExpression AND ($f.expression)" )
    #else
        #set( $finalExpression = $f.expression )
    #end
    
    #foreach ($expressionName in $f.expressionNames.entrySet())
        $util.qr($finalExpressionNames.put($expressionName.key, $expressionName.value))
    #end

    #foreach ($expressionValue in $f.expressionValues.entrySet())
    $util.qr($finalExpressionValues.put($expressionValue.key, $expressionValue.value))
    #end
#end

#if(!$util.isNullOrEmpty($finalExpression))
    #set( $context.args.filter = $util.toJson({"expression": $finalExpression, "expressionNames": $finalExpressionNames, "expressionValues": $finalExpressionValues}) )
#else
    $util.qr($context.args.remove("filter"))
#end
{
    "version" : "2018-05-29",
    "operation": "Invoke",
    "payload": { 
        "args": $util.toJson($context.args),
        "identity": $util.toJson($context.identity),
        "request": $util.toJson($context.request),
        "authType": $util.toJson($util.authType()),
        "source": $util.toJson($context.source),
        "info": $util.toJson($context.info)
    }
}

`;
