import { ApiAppEnvConfig, Fn, esbuild } from '@npco/component-bff-serverless/dist';

class CrmsApiAppEnvConfig extends ApiAppEnvConfig {
  // eslint-disable-next-line no-underscore-dangle
  override componentTableName = `${this._serviceName}-${this.componentTable}` as const;

  // eslint-disable-next-line no-underscore-dangle
  bankingProductTableName = `${this._serviceName}-${this.bankingProductTable}` as const;

  // eslint-disable-next-line no-underscore-dangle
  merchantTableName = `${this._serviceName}-${this.merchantTable}` as const; // not sure why this is different from "mp/env.ts'

  merchantTableQueryRolePolicyArn = { [Fn.ImportValue]: '${self:custom.serviceName}-merchantTableQueryRolePolicyArn' };

  override appsyncStackName = `${this.service}-appsync` as const;

  getDefaults = () => ({
    entityTableName: this.componentTableName,
    esbuild,
  });
}

export const crmsEnvConfig = new CrmsApiAppEnvConfig('iac/crms/config', true);
