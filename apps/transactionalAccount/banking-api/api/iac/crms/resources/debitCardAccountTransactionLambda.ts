import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import { Action, Arn } from '@npco/component-bff-serverless/dist/param';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon } from '../../common/env';
import { responseDefault } from '../../common/resolverTemplates';
import { getDebitCardTransactionsRequest } from '../resolvers/resolverTemplates/getDebitCardTransactionsRequest';
import { getDebitCardTxnsRequest } from '../resolvers/resolverTemplates/getDebitCardTxnsRequest';

export const lambdas: ServerlessFunctions = {
  addAdjustment: {
    handler: 'src/lambda/crms/debitCardAccountTransaction/index.addAdjustmentHandler',
    name: 'addAdjustment',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'addAdjustment',
      typeName: 'Mutation',
    },
  },
  getDebitCardTransactions: {
    handler: 'src/lambda/crms/debitCardAccountTransaction/index.getDebitCardTransactionsHandler',
    name: 'getDebitCardTransactions',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        debitCardIdGsiV1QueryPolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:Query', 'dynamodb:BatchGetItem'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.debitCardIdGsi}',
            ],
          },
        ],
      },
    },
    appsync: {
      fieldName: 'getDebitCardTransactions',
      typeName: 'Query',
      template: {
        request: getDebitCardTransactionsRequest,
        response: responseDefault,
      },
    },
  },
  getDebitCardAccountTxn: {
    handler: 'src/lambda/crms/debitCardAccount/index.getDebitCardAccountTxnHandler',
    name: 'getDebitCardAccountTxn',
    ...lambdaCommon,
    appsync: {
      fieldName: 'getDebitCardTransactionV2',
      typeName: 'Query',
      dataResolverName: { namePrefix: 'getDebitCardAccountTxn' },
      template: {
        request: `$util.qr($context.request.headers.put("entity-uuid", $context.args.entityUuid))
      {
        "version" : "2018-05-29",
        "operation": "Invoke",
        "payload": { "args": $util.toJson($context.args), "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.toJson($context.source), "info": $util.toJson($context.info)}
      }`,
      },
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        getDebitCardAccountTxnDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.table('${self:custom.merchantTableName}')],
          },
        ],
      },
    },
  },
  getDebitCardAccountTxns: {
    handler: 'src/lambda/crms/debitCardAccount/index.getDebitCardAccountTxnsHandler',
    name: 'getDebitCardAccountTxns',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    useLogicalArnName: true,
    tracing: true,
    appsync: {
      fieldName: 'getDebitCardTransactionsV2',
      typeName: 'Query',
      dataResolverName: { namePrefix: 'getDebitCardAccountTxns' },
      template: { request: getDebitCardTxnsRequest },
    },
  },
  // exportDebitCardTransactionsHandler: {
  //   handler: 'src/lambda/crms/debitCardTransactionsExportLambda.exportDebitCardTransactionsHandler',
  //   name: 'exportDcaTxns',
  //   ...lambdaCommon,
  //   environment: {
  //     COMPONENT_TABLE: '${self:custom.entityTableName}',
  //     PUBLISH_HANDLER_NAME: '${self:provider.stackName}-publishDcaTxnsExport',
  //   },
  //   /// / Uncomment the following lines - dca txn service should be migrated
  //   // appsync: {
  //   //   fieldName: 'exportDebitCardTransactions',
  //   //   typeName: 'Subscription',
  //   //   template: { request: getDebitCardTxnsRequest },
  //   //   dataResolverName: {
  //   //     namePrefix: 'exportDebitCardTransactions',
  //   //   },
  //   // },
  //   policy: {
  //     inline: {
  //       invokePublishLambdaPolicy: [
  //         {
  //           actions: [Action.lambda.InvokeFunction],
  //           resources: [Arn.lambda.function('${self:provider.stackName}-publishDcaTxnsExport')],
  //         },
  //       ],
  //     },
  //   },
  // },
  // /// / dca txn service should be migrated - works with above subscription

  // publishDebitCardTransactionsExportHandler: {
  //   handler: 'src/lambda/crms/debitCardTransactionsExportLambda.publishDebitCardTransactionsExportHandler',
  //   name: 'publishDcaTxnsExport',
  //   ...lambdaCommon,
  //   environment: {
  //     COMPONENT_TABLE: '${self:custom.entityTableName}',
  //     MERCHANT_TABLE: '${self:custom.merchantTableName}',
  //     TRANSACTION_EXPORT_BUCKET: '${self:custom.exportTransactionsBucket}',
  //     IAM_USER_KEY: '${self:custom.iamUserKey}',
  //     IAM_USER_SECRET: '${self:custom.iamUserSecret}',
  //     APP_SYNC_ENDPOINT: '${self:custom.appSyncEndpoint}',
  //   },
  //   policy: {
  //     managed: [ManagedPolicy.entityTableQueryRolePolicy, '${self:custom.merchantTableQueryRolePolicyArn}'],
  //     inline: {
  //       exportS3Policy: [
  //         {
  //           actions: [Action.s3.PutObject, Action.s3.GetObject],
  //           resources: [Arn.s3('${self:custom.exportTransactionsBucket}/*')],
  //         },
  //       ],
  //     },
  //   },
  //   timeout: 300,
  //   memorySize: 1024,
  // },
};
