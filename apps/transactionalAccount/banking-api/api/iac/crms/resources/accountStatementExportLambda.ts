import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon } from '../../common/env';

export const lambdas: ServerlessFunctions = {
  getAccountStatements: {
    handler: 'src/lambda/crms/accountStatementExport/index.getAccountStatementsHandler',
    name: 'getAccountStatements',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        getAccountStatementFromS3Policy: [
          {
            effect: 'Allow',
            actions: ['s3:GetObject'],
            resources: ['arn:aws:s3:::${self:custom.accountStatementExportBucket}/debitCardAccountStatements/*'],
          },
        ],
      },
    },
    environment: {
      ACCOUNT_STATEMENT_EXPORT_BUCKET: '${self:custom.accountStatementExportBucket}',
    },
    appsync: {
      fieldName: 'getBankingAccountStatements',
      typeName: 'Query',
    },
  },
  exportInterimAccountStatement: {
    handler: 'src/lambda/crms/accountStatementExport/index.exportInterimAccountStatementHandler',
    name: 'exportInterimAccountStatement',
    timeout: 45,
    memorySize: 1024,
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        getAccountStatementFromS3Policy: [
          {
            effect: 'Allow',
            actions: ['s3:GetObject', 's3:PutObject'],
            resources: ['arn:aws:s3:::${self:custom.accountStatementExportBucket}/debitCardAccountStatements/*'],
          },
        ],
        secondaryGsiV1QueryPolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:Query', 'dynamodb:BatchGetItem'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.secondaryGsiV1}',
            ],
          },
        ],
      },
    },
    environment: {
      ACCOUNT_STATEMENT_EXPORT_BUCKET: '${self:custom.accountStatementExportBucket}',
    },
    appsync: {
      fieldName: 'createInterimBankingAccountStatement',
      typeName: 'Mutation',
    },
  },
};
