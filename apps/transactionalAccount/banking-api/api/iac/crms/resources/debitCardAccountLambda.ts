import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon } from '../../common/env';

export const lambdas: ServerlessFunctions = {
  getDebitCardAccount: {
    handler: 'src/lambda/crms/debitCardAccount/index.getDebitCardAccountHandler',
    name: 'getDebitCardAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  getDebitCardAccounts: {
    handler: 'src/lambda/crms/debitCardAccount/index.getDebitCardAccountsHandler',
    name: 'getDebitCardAccounts',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  getDebitCardAccountV3: {
    handler: 'src/lambda/crms/debitCardAccount/index.getDebitCardAccountV3Handler',
    name: 'getDebitCardAccountV3',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardAccountV3',
      typeName: 'Query',
    },
  },
  getDebitCardAccountsV3: {
    handler: 'src/lambda/crms/debitCardAccount/index.getDebitCardAccountsV3Handler',
    name: 'getDebitCardAccountsV3',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardAccountsV3',
      typeName: 'Query',
    },
  },
  getIssuingAccount: {
    handler: 'src/lambda/crms/debitCardAccount/index.getIssuingAccountHandler',
    name: 'getIssuingAccount',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        sortKeyGsiQueryPolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:Query', 'dynamodb:BatchGetItem'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.sortKeyGsi}',
            ],
          },
        ],
      },
    },
    appsync: {
      fieldName: 'getIssuingAccount',
      typeName: 'Query',
    },
  },
  createDebitCardAccount: {
    handler: 'src/lambda/crms/debitCardAccount/index.createDebitCardAccountHandler',
    name: 'createDebitCardAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'createDebitCardAccount',
      typeName: 'Mutation',
    },
  },
  closeDebitCardAccount: {
    handler: 'src/lambda/crms/debitCardAccount/index.closeDebitCardAccountHandler',
    name: 'closeDebitCardAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'closeDebitCardAccount',
      typeName: 'Mutation',
    },
  },
  suspendDebitCardAccount: {
    handler: 'src/lambda/crms/debitCardAccount/index.suspendDebitCardAccountHandler',
    name: 'suspendDebitCardAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'suspendDebitCardAccount',
      typeName: 'Mutation',
    },
  },
  unsuspendDebitCardAccount: {
    handler: 'src/lambda/crms/debitCardAccount/index.unsuspendDebitCardAccountHandler',
    name: 'unsuspendDebitCardAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'unsuspendDebitCardAccount',
      typeName: 'Mutation',
    },
  },
  getDebitCardAccountV2: {
    handler: 'src/lambda/crms/debitCardAccount/index.getDebitCardAccountV2Handler',
    name: 'getDebitCardAccountV2',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardAccountV2',
      typeName: 'Query',
    },
  },
  getDebitCardAccountsV2: {
    handler: 'src/lambda/crms/debitCardAccount/index.getDebitCardAccountsV2Handler',
    name: 'getDebitCardAccountsV2',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardAccountsV2',
      typeName: 'Query',
    },
  },
  personaliseDebitCardAccount: {
    handler: 'src/lambda/crms/debitCardAccount/index.personaliseDebitCardAccountHandler',
    name: 'personaliseDebitCardAccount',
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    ...lambdaCommon,
    appsync: {
      fieldName: 'personaliseDebitCardAccount',
      typeName: 'Mutation',
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy] },
  },
};
