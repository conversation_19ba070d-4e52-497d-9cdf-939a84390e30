import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon } from '../../common/env';

export const lambdas: ServerlessFunctions = {
  getDebitCard: {
    handler: 'src/lambda/crms/debitCard/index.getDebitCardHandler',
    name: 'getDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  getDebitCards: {
    handler: 'src/lambda/crms/debitCard/index.getDebitCardsHandler',
    name: 'getDebitCards',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  getDebitCardsByAccountId: {
    handler: 'src/lambda/crms/debitCard/index.getDebitCardsByAccountIdHandler',
    name: 'getDebitCardsByAccountId',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  updateDebitCardDisplayName: {
    handler: 'src/lambda/crms/debitCard/index.updateDebitCardDisplayNameHandler',
    name: 'updateDebitCardDisplayName',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  activateDebitCard: {
    handler: 'src/lambda/crms/debitCard/index.activateDebitCardHandler',
    name: 'activateDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'activateDebitCard',
      typeName: 'Mutation',
    },
  },
  lockDebitCard: {
    handler: 'src/lambda/crms/debitCard/index.lockDebitCardHandler',
    name: 'lockDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'lockDebitCard',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
  },
  unlockDebitCard: {
    handler: 'src/lambda/crms/debitCard/index.unlockDebitCardHandler',
    name: 'unlockDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'unlockDebitCard',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
  },
  closeDebitCard: {
    handler: 'src/lambda/crms/debitCard/index.closeDebitCardHandler',
    name: 'closeDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'cancelDebitCard',
      typeName: 'Mutation',
    },
  },
  reportLostDebitCard: {
    handler: 'src/lambda/crms/debitCard/index.reportLostDebitCardHandler',
    name: 'reportLostDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'reportLostDebitCard',
      typeName: 'Mutation',
    },
  },
  suspendDebitCard: {
    handler: 'src/lambda/crms/debitCard/index.suspendDebitCardHandler',
    name: 'suspendDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'suspendDebitCard',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
  },
  unsuspendDebitCard: {
    handler: 'src/lambda/crms/debitCard/index.unsuspendDebitCardHandler',
    name: 'unsuspendDebitCard',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'unsuspendDebitCard',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
  },
  getDebitCardV2: {
    handler: 'src/lambda/crms/debitCardAccount/index.getDebitCardV2Handler',
    name: 'getDebitCardV2',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardV2',
      typeName: 'Query',
    },
  },
  getDebitCardsV2: {
    handler: 'src/lambda/crms/debitCardAccount/index.getDebitCardsV2Handler',
    name: 'getDebitCardsV2',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getDebitCardsV2',
      typeName: 'Query',
      dataResolverName: { namePrefix: 'getDebitCardsV2' },
      template: {
        request: `
          #if (!$util.isNullOrEmpty($context.args.filter.productType))
            #set( $filter = $util.transform.toDynamoDBFilterExpression({ "productType": $context.args.filter.productType }))
            #set( $filter = $util.parseJson($filter))
          #end

          #if(!$util.isNullOrEmpty($filter.expression))
            #set( $filterExpression = $filter.expression )
          #end

          $util.qr($context.args.filter.remove("productType"))

          #set( $context.args.filter = $util.toJson({"expression": $filterExpression, "expressionNames": $filter.expressionNames, "expressionValues": $filter.expressionValues}))
          {
            "version" : "2018-05-29",
            "operation": "Invoke",
            "payload": {
              "args": $util.toJson($context.args),
              "identity": $util.toJson($context.identity),
              "request": $util.toJson($context.request),
              "authType": $util.toJson($util.authType()),
              "source": $util.toJson($context.source), 
              "info": $util.toJson($context.info)
            }
          }
        `,
      },
    },
  },
  getDebitCardsByAccountIdV2: {
    handler: 'src/lambda/crms/debitCardAccount/index.getDebitCardsByAccountIdv2Handler',
    name: 'getDebitCardsByAccountIdV2',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  getDebitCardsInDebitCardAccountV3Handler: {
    handler: 'src/lambda/crms/debitCardAccount/index.getDebitCardsByAccountIdv2Handler',
    name: 'getDebitCardsInDebitCardAccountV3',
    ...lambdaCommon,
    appsync: {
      fieldName: 'cards',
      typeName: 'DebitCardAccountV3',
      template: {
        request: `{
          "version" : "2018-05-29",
          "operation": "Invoke",
          "payload": {
            "args": { "debitCardAccountUuid": $util.toJson($context.source.id), "entityUuid": $util.toJson($context.source.entityUuid) },
            "identity": $util.toJson($context.identity),
            "request": $util.toJson($context.request),
            "authType": $util.toJson($util.authType()),
            "source": $util.toJson($context.source),
            "info": $util.toJson($context.info)
          } 
        }`,
      },
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy] },
  },
};
