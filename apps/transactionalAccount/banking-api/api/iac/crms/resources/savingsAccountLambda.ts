import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon, commonBankingProductTablePolicy } from '../../common/env';
import { responseDefault } from '../../common/resolverTemplates';
import { getDebitCardAccountSavingsAccountProductRequest } from '../resolvers/resolverTemplates/getDebitCardAccountSavingsAccountProductRequest';

export const lambdas: ServerlessFunctions = {
  personaliseSavingsAccount: {
    handler: 'src/lambda/crms/debitCardAccount/index.personaliseSavingsAccountHandler',
    name: 'personaliseSavingsAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    // doesnt exist in CRMS appsync?
    // appsync: {
    //   fieldName: 'personaliseSavingsAccount',
    //   typeName: 'Mutation',
    // },
  },
  personaliseSavingsAccountV2: {
    handler: 'src/lambda/crms/debitCardAccount/index.personaliseSavingsAccountV2Handler',
    name: 'personaliseSavingsAccountV2',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
  },
  closeSavingsAccount: {
    handler: 'src/lambda/crms/debitCardAccount/index.closeSavingsAccountHandler',
    name: 'closeSavingsAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'closeSavingsAccount',
      typeName: 'Mutation',
    },
  },
  suspendSavingsAccount: {
    handler: 'src/lambda/crms/debitCardAccount/index.suspendSavingsAccountHandler',
    name: 'suspendSavingsAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'suspendSavingsAccount',
      typeName: 'Mutation',
    },
  },
  unsuspendSavingsAccount: {
    handler: 'src/lambda/crms/debitCardAccount/index.unsuspendSavingsAccountHandler',
    name: 'unsuspendSavingsAccount',
    ...lambdaCommon,
    environment: {
      CBS_API_ENDPOINT: '${self:custom.cbsEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'unsuspendSavingsAccount',
      typeName: 'Mutation',
    },
  },
  getSavingsAccounts: {
    handler: 'src/lambda/crms/debitCardAccount/index.getSavingsAccountsHandler',
    name: 'getSavingsAccounts',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getSavingsAccounts',
      typeName: 'Query',
    },
  },
  getSavingsAccountsV2: {
    handler: 'src/lambda/crms/debitCardAccount/index.getSavingsAccountsV2Handler',
    name: 'getSavingsAccountsV2',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        sortKeyGsiQueryPolicy: [
          {
            effect: 'Allow',
            actions: ['dynamodb:Query', 'dynamodb:BatchGetItem'],
            resources: [
              'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/${self:custom.componentTableName}/index/${self:custom.entitySortKeyGsi}',
            ],
          },
        ],
      },
    },
    appsync: {
      fieldName: 'getSavingsAccountsV2',
      typeName: 'Query',
    },
  },
  getSavingsAccountV2: {
    handler: 'src/lambda/crms/debitCardAccount/index.getSavingsAccountV2Handler',
    name: 'getSavingsAccountV2',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
    },
    appsync: {
      fieldName: 'getSavingsAccountV2',
      typeName: 'Query',
    },
  },
  getSavingsAccountProductByAccount: {
    handler: 'src/lambda/crms/debitCardAccount/index.getSavingsAccountProductByAccountHandler',
    name: 'getSavingsAccountProductByAccount',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
      inline: { bankingProductTablePolicy: commonBankingProductTablePolicy },
    },
    appsync: {
      fieldName: 'savingsAccountProduct',
      typeName: 'DebitCardAccountV2',
      template: {
        request: getDebitCardAccountSavingsAccountProductRequest,
        response: responseDefault,
      },
    },
  },
  getSavingsAccountProductByEntity: {
    handler: 'src/lambda/crms/debitCardAccount/index.getSavingsAccountProductByEntityHandler',
    name: 'getSavingsAccountProductByEntity',
    ...lambdaCommon,
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy],
      inline: { bankingProductTablePolicy: commonBankingProductTablePolicy },
    },
  },
};
