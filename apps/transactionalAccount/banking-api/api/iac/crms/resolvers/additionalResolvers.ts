import { createLambdaDataResolver } from '@npco/component-bff-serverless/dist';

import { responseDefault } from '../../common/resolverTemplates';

import { getDebitCardTxnMerchantDetailsRequest } from './resolverTemplates/getDebitCardTxnMerchantDetailsRequest';
import { getSavingsAccountV2SavingsAccountProductRequest } from './resolverTemplates/getSavingsAccountV2SavingsAccountProductRequest';

export const bffResolvers = [
  {
    lambda: '${self:custom.getMerchantDetailsLambda}',
    name: 'getDebitCardTxnMerchantDetails',
    appsync: {
      fieldName: 'merchant',
      typeName: 'DebitCardTransactionV2',
      template: {
        request: getDebitCardTxnMerchantDetailsRequest,
        response: responseDefault,
      },
    },
  },
  {
    lambda: '${self:custom.getMerchantDetailsLambda}',
    name: 'getDcaTransactionMerchantDetails',
    appsync: {
      fieldName: 'merchant',
      typeName: 'DebitCardAccountTransaction',
      template: {
        request: getDebitCardTxnMerchantDetailsRequest,
        response: responseDefault,
      },
    },
  },
  {
    lambda: '${self:custom.getSavingsAccountProductByAccountLambda}',
    name: 'getSavingsAccountV2SavingsAccountProduct',
    appsync: {
      fieldName: 'savingsAccountProduct',
      typeName: 'SavingsAccountV2',
      template: {
        request: getSavingsAccountV2SavingsAccountProductRequest,
        response: responseDefault,
      },
    },
  },
  {
    lambda: '${self:custom.getDebitCardAccountV2Lambda}',
    name: '${self:custom.getDebitCardAccountV2Lambda}',
    appsync: {
      fieldName: 'debitCardAccount',
      typeName: 'DebitCardV2',
      dataResolverName: {
        customDataSourceName: 'getDCAV2DataSource',
        customResolverName: 'getDCAV2Resolver',
      },
      template: {
        request: `{
          "version":"2018-05-29",
          "operation":"Invoke",
          "payload":{
            "args":{"debitCardAccountUuid": $util.toJson($context.source.debitCardAccountUuid) ,"entityUuid": $util.toJson($context.source.entityUuid) },
            "identity": $util.toJson($context.identity),
            "request": $util.toJson($context.request),
            "authType": $util.toJson($util.authType()),
            "source": $util.toJson($context.source),
            "info": $util.toJson($context.info),
            }
          }`,
      },
    },
  },
  {
    lambda: '${self:custom.getDebitCardAccountV2Lambda}',
    name: '${self:custom.getDebitCardAccountV2Lambda}',
    appsync: {
      fieldName: 'debitCardAccount',
      typeName: 'DebitCardTransactionCounterparty',
      dataResolverName: {
        customDataSourceName: 'getTransactionCounterpartyDebitCardAccountDataSource',
        customResolverName: 'getTransactionCounterpartyDebitCardAccountResolver',
      },
      template: {
        request: `
        #if ($util.isNullOrEmpty($context.source.debitCardAccountUuid))
        #return
        #end
        {
          "version" : "2018-05-29",
          "operation": "Invoke",
          "payload": {
            "args": { "debitCardAccountUuid": $util.toJson($context.source.debitCardAccountUuid), "entityUuid": $util.toJson($context.request.headers['entity-uuid']) },
            "identity": $util.toJson($context.identity),
            "request": $util.toJson($context.request),
            "authType": $util.toJson($util.authType()),
            "source": $util.toJson($context.source),
            "info": $util.toJson($context.info)
          } 
        }`,
        response: responseDefault,
      },
    },
  },
].reduce(
  (res, resolver) => ({
    ...res,
    ...createLambdaDataResolver(resolver.name, resolver.lambda, resolver.appsync, false),
  }),
  {},
);
