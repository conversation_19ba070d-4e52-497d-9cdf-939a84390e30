import { ServerlessPlugin } from '@npco/component-bff-serverless/dist/serverless/common/plugins';
import { ssmSharedVpcImport as vpcImport } from '@npco/component-bff-serverless/dist/serverless/common/vpc';
import { ApiAppServerlessStack } from '@npco/component-bff-serverless/dist/serverless/stacks/apiAppServerlessStack';

import { crmsEnvConfig } from './iac/crms/env';
import { bffResolvers } from './iac/crms/resolvers/additionalResolvers';
import { getCardProvisionResolvers } from './iac/crms/resolvers/cardProvisionResolvers';
import { getInterestSummaryResolvers } from './iac/crms/resolvers/interestSummaryResolvers';
import { getTransferRemittanceResolvers } from './iac/crms/resolvers/transferRemittanceResolvers';

const region = '${opt:region}';
const accountId = '${aws:accountId}';
const staticServiceName = '${env:STATIC_ENV_NAME}-crms-engine';
const appsyncStackName = `${staticServiceName}-appsync`;

const bankingStackName = `${staticServiceName}-banking`;
const interestSummaryStackName = `${staticServiceName}-banking-interestSummary`;
const transferRemittanceStackName = `${staticServiceName}-banking-remittance`;

const custom = {
  vpcImport,
  appSyncApiId: { 'Fn::ImportValue': `${appsyncStackName}-graphQlApiId` },
  appsyncDataSourceRoleArn: `\${cf:${appsyncStackName}.DataSourceLambdaRole}`,
  getDebitCardLambda: `${bankingStackName}-getDebitCard`,

  getInterestSummariesLambda: `${interestSummaryStackName}-getInterestSummaries`,
  getRemittancePdfLambda: `${transferRemittanceStackName}-getTransferRemittancePdf`,

  getDigitalWalletTokenLambda: `${bankingStackName}-getDigitalWalletToken`,
  getDigitalWalletTokensByDebitCardUuidLambda: `${bankingStackName}-getDigitalWalletTokensByDcacUuid`,
  getDigitalWalletTokensByEntityLambda: `${bankingStackName}-getDigitalWalletTokensByEntity`,
  getDigitalWalletTokenHistoriesLambda: `${bankingStackName}-getDigitalWalletTokenHistories`,

  terminateDigitalWalletTokenLambda: `${bankingStackName}-terminateDigitalWalletToken`,
  suspendDigitalWalletTokenLambda: `${bankingStackName}-suspendDigitalWalletToken`,
  unsuspendDigitalWalletTokenLambda: `${bankingStackName}-unsuspendDigitalWalletToken`,

  getSavingsAccountProductByAccountLambda: `${bankingStackName}-getSavingsAccountProductByAccount`,

  getDebitCardAccountV2Lambda: `${bankingStackName}-getDebitCardAccountV2`,
};

const sls = new ApiAppServerlessStack('banking-resolvers', crmsEnvConfig, {
  plugins: [
    ServerlessPlugin.Dotenv,
    ServerlessPlugin.ResourceTagging,
    ServerlessPlugin.Tracing,
    'serverless-pseudo-parameters',
  ],
  custom: {
    ...custom,
    richDataStackName: '${self:custom.service}-richdata',
    getMerchantDetailsLambda: '${self:custom.richDataStackName}-getMerchantDetails',
    ...crmsEnvConfig.getAppsync(),
  },
  resources: {
    ...bffResolvers,
    ...getCardProvisionResolvers(region, accountId, custom).Resources,
    ...getInterestSummaryResolvers(region, accountId, custom).Resources,
    ...getTransferRemittanceResolvers(region, accountId, custom).Resources,
  },
});

module.exports = sls.build();
