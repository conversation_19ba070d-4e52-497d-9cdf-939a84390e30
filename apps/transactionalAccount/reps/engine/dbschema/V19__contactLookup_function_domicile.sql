DROP FUNCTION ContactLookUp(entityUuid uuid) CASCADE;

CREATE OR REPLACE FUNCTION ContactLookUp(
    entityUuid uuid,
    dom varchar(3)
)
RETURNS TABLE (
    "typeId" uuid,
    type text,
    "contactUuid" uuid,
    "entityUuid" uuid,
    category text,
    subcategory text
)
LANGUAGE plpgsql AS
$func$
BEGIN
    RETURN QUERY

    -- MERCHANT
    SELECT 
        id AS "typeId",
        'MERCHANT'::text AS type,
        c."contactUuid",
        c."entityUuid",
        c.category,
        c.subcategory
    FROM ${flyway:defaultSchema}."Contact" c
    JOIN ${flyway:defaultSchema}.ContactMerchantRelationship(entityUuid, dom) cm
        ON c."contactUuid" = cm."contactUuid" AND c."entityUuid" = cm."entityUuid"
    WHERE c."entityUuid" = entityUuid AND c.domicile = dom

    UNION ALL

    -- SENDER
    SELECT 
        s."senderUuid" AS "typeId",
        'SENDER'::text AS type,
        c."contactUuid",
        c."entityUuid",
        c.category,
        c.subcategory
    FROM ${flyway:defaultSchema}."Contact" c
    JOIN ${flyway:defaultSchema}."Sender" s
        ON c."contactUuid" = s."contactUuid" AND s."entityUuid" = entityUuid
    WHERE c."entityUuid" = entityUuid AND c.domicile = dom AND s.domicile = dom

    UNION ALL

    -- PAYMENT_INSTRUMENT
    SELECT 
        pi."paymentInstrumentUuid" AS "typeId",
        'PAYMENT_INSTRUMENT' AS type,
        c."contactUuid",
        c."entityUuid",
        c.category,
        c.subcategory
    FROM ${flyway:defaultSchema}."Contact" c
    JOIN ${flyway:defaultSchema}."PaymentInstrument" pi
        ON c."contactUuid" = pi."contactUuid" AND pi."entityUuid" = entityUuid
    WHERE c."entityUuid" = entityUuid AND c.domicile = dom AND pi.domicile = dom;

END;
$func$;
