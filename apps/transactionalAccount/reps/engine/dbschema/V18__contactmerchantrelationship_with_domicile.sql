DROP FUNCTION contactmerchantrelationship(entityuuid uuid) CASCADE;

CREATE OR REPLACE FUNCTION contactmerchantrelationship(entityuuid uuid, dom varchar(3))
    returns TABLE(id uuid, "contactUuid" uuid, "entityUuid" uuid)
    language plpgsql
as
$$
BEGIN
    RETURN QUERY
        SELECT DISTINCT ON (m.id, cml."contactUuid")
            m.id,
            cml."contactUuid",
            cml."entityUuid"
        FROM ${flyway:defaultSchema}."ContactMerchantLink" cml
        JOIN ${flyway:defaultSchema}."Merchant" m ON
            (cml."merchantUuid" = m.id OR cml."merchantUuid" = m."locationOf")
        WHERE
            cml."entityUuid" = entityUuid
            AND cml.domicile = dom
        ORDER BY m.id, cml."contactUuid";
END;
$$;