import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { EntityCategories } from '@npco/component-dto-core';

import { v4 as uuidv4 } from 'uuid';

import { ReportType, SortBy, SortOrder, TransactionDirection } from '../../common/types/index.js';
import type { FunctionTypes } from '../../common/types/index.js';
import { EnvironmentService } from '../../config/environmentService.js';
import { DEFAULT_TIMEZONE } from '../../const/timezone.js';
import { getCurrentFormattedDate } from '../../utils/testing/date.js';

import type * as ServiceModule from './issuingTransactionFullDetailsService.js';

type ServiceModuleFunctionTypes = FunctionTypes<typeof ServiceModule>;

jest.mock('../../config/environmentService');

const getMonthlyNetAmountTransactionRecords = jest.fn();
const getTTMNetAmountTransactionRecords = jest.fn();
const addCurrenciesToTxnResponse = jest.fn();
const getTTMReportDateRange = jest.fn();
const getMonthlyReportDateRange = jest.fn();

jest.mock('../issuingTransactionFullDetails/db/netAmountReport/monthly/issuingTransactionFullDetailsDb', () => ({
  __esModule: true,
  getMonthlyNetAmountTransactionRecords,
}));

jest.mock('../issuingTransactionFullDetails/db/netAmountReport/ttm/issuingTransactionFullDetailsDb', () => ({
  __esModule: true,
  getTTMNetAmountTransactionRecords,
}));

jest.mock('../issuingTransactionFullDetails/utils/transactionResponse', () => ({
  __esModule: true,
  addCurrenciesToTxnResponse,
  getTTMReportDateRange,
  getMonthlyReportDateRange,
  getReportDateRangeByReportType: jest.fn(),
}));

describe('Issuing transactions full details service', () => {
  const domicile = Domicile.AU;
  let env: EnvironmentService;
  let service: any;

  beforeAll(async () => {
    env = new EnvironmentService();
    service = await import('./issuingTransactionFullDetailsService.js');
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe.each([ReportType.MONTHLY, ReportType.TTM])('by report type %s', (reportType) => {
    describe('getCategoryNetAmount', () => {
      let getCategoryNetAmount: ServiceModuleFunctionTypes['getCategoryNetAmount'];
      const entityUuid = uuidv4();
      const date = getCurrentFormattedDate();

      beforeAll(async () => {
        getCategoryNetAmount = service.getCategoryNetAmount;
      });

      it('should throw an error if report type is invalid', async () => {
        await expect(
          getCategoryNetAmount(
            {
              reportType: 'invalid' as ReportType,
              entityUuid,
              timeZone: DEFAULT_TIMEZONE,
              date,
              domicile,
            },
            env,
          ),
        ).rejects.toThrow('Invalid report type');
      });

      it('should call get transaction records from db', async () => {
        await getCategoryNetAmount(
          {
            reportType,
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            domicile,
          },
          env,
        );

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'category',
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'category',
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }

        expect(addCurrenciesToTxnResponse).toHaveBeenCalled();
      });

      it('should call get transaction records from db with sorting and limit', async () => {
        await getCategoryNetAmount(
          {
            reportType,
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            sortBy: SortBy.AZ,
            sortOrder: SortOrder.DESC,
            limit: 1,
            domicile,
          },
          env,
        );

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'category',
            transactionDirection: TransactionDirection.EXPENSE,
            sortBy: SortBy.AZ,
            sortOrder: SortOrder.DESC,
            limit: 1,
            domicile,
          });
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'category',
            transactionDirection: TransactionDirection.EXPENSE,
            sortBy: SortBy.AZ,
            sortOrder: SortOrder.DESC,
            limit: 1,
            domicile,
          });
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }

        expect(addCurrenciesToTxnResponse).toHaveBeenCalled();
      });

      it('should call get transaction records from db for single account', async () => {
        const accountUuid = uuidv4();
        await getCategoryNetAmount(
          {
            reportType,
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            accountUuid,
            domicile,
          },
          env,
        );

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'category',
            accountUuid,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'category',
            accountUuid,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }

        expect(addCurrenciesToTxnResponse).toHaveBeenCalled();
      });

      it('should call get transaction records from db with fallback to default timezone is not set', async () => {
        await getCategoryNetAmount(
          {
            reportType,
            entityUuid,
            date,
            domicile,
          },
          env,
        );

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'category',
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getMonthlyReportDateRange).toHaveBeenCalled();
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'category',
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }

        expect(addCurrenciesToTxnResponse).toHaveBeenCalled();
      });

      it('should return correct payload', async () => {
        const accountUuid = uuidv4();
        const sortBy = SortBy.AZ;
        const sortOrder = SortOrder.DESC;
        const limit = 1;

        const response = await getCategoryNetAmount(
          {
            accountUuid,
            reportType,
            entityUuid,
            date,
            sortBy,
            sortOrder,
            limit,
            domicile,
          },
          env,
        );

        expect(response).toEqual(
          expect.objectContaining({
            accountUuid,
            sortBy,
            sortOrder,
            limit,
            reportType,
            timeZone: DEFAULT_TIMEZONE,
          }),
        );
      });
    });

    describe('getContactNetAmount', () => {
      let getContactNetAmount: ServiceModuleFunctionTypes['getContactNetAmount'];
      const entityUuid = uuidv4();
      const date = getCurrentFormattedDate();

      beforeAll(async () => {
        getContactNetAmount = service.getContactNetAmount;
      });

      it('should throw an error if report type is invalid', async () => {
        await expect(
          getContactNetAmount(
            {
              reportType: 'invalid' as ReportType,
              entityUuid,
              timeZone: DEFAULT_TIMEZONE,
              date,
              transactionDirection: TransactionDirection.EXPENSE,
              domicile,
            },
            env,
          ),
        ).rejects.toThrow('Invalid report type');
      });

      it('should call get transaction records from db', async () => {
        await getContactNetAmount(
          {
            reportType,
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          },
          env,
        );

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'contactUuid',
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getMonthlyReportDateRange).toHaveBeenCalled();
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'contactUuid',
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }

        expect(addCurrenciesToTxnResponse).toHaveBeenCalled();
      });

      it('should call get transaction records from db for income transactions', async () => {
        await getContactNetAmount(
          {
            reportType,
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            transactionDirection: TransactionDirection.INCOME,
            domicile,
          },
          env,
        );

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'contactUuid',
            transactionDirection: TransactionDirection.INCOME,
            domicile,
          });
          expect(getMonthlyReportDateRange).toHaveBeenCalled();
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'contactUuid',
            transactionDirection: TransactionDirection.INCOME,
            domicile,
          });
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }
        expect(addCurrenciesToTxnResponse).toHaveBeenCalled();
      });

      it('should call get transaction records from db for single account', async () => {
        const accountUuid = uuidv4();
        await getContactNetAmount(
          {
            reportType,
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            accountUuid,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          },
          env,
        );

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'contactUuid',
            accountUuid,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getMonthlyReportDateRange).toHaveBeenCalled();
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'contactUuid',
            accountUuid,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }

        expect(addCurrenciesToTxnResponse).toHaveBeenCalled();
      });

      it('should call get transaction records from db with fallback to default timezone is not set', async () => {
        await getContactNetAmount(
          {
            reportType,
            entityUuid,
            date,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          },
          env,
        );

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'contactUuid',
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getMonthlyReportDateRange).toHaveBeenCalled();
        }

        expect(addCurrenciesToTxnResponse).toHaveBeenCalled();
      });
    });

    describe('getSubcategoriesNetAmountByCategory', () => {
      const category = EntityCategories.ADVERTISING;

      let getSubcategoriesNetAmountByCategory: ServiceModuleFunctionTypes['getSubcategoriesNetAmountByCategory'];
      const entityUuid = uuidv4();
      const date = getCurrentFormattedDate();

      beforeAll(async () => {
        getSubcategoriesNetAmountByCategory = service.getSubcategoriesNetAmountByCategory;
      });

      it('should throw an error if report type is invalid', async () => {
        await expect(
          getSubcategoriesNetAmountByCategory(
            {
              reportType: 'invalid' as ReportType,
              entityUuid,
              timeZone: DEFAULT_TIMEZONE,
              date,
              category,
              domicile,
            },
            env,
          ),
        ).rejects.toThrow('Invalid report type');
      });

      it('should call getMonthlyNetAmountTransactionRecords records from db', async () => {
        await getSubcategoriesNetAmountByCategory(
          {
            reportType,
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            category,
            domicile,
          },
          env,
        );

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'subcategory',
            category,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getMonthlyReportDateRange).toHaveBeenCalled();
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'subcategory',
            category,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }

        expect(addCurrenciesToTxnResponse).toHaveBeenCalled();
      });

      it('should call get transaction records from db for single account', async () => {
        const accountUuid = uuidv4();
        await getSubcategoriesNetAmountByCategory(
          {
            reportType,
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            category,
            accountUuid,
            domicile,
          },
          env,
        );

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'subcategory',
            category,
            accountUuid,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getMonthlyReportDateRange).toHaveBeenCalled();
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'subcategory',
            category,
            accountUuid,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }
      });

      it('should call get transaction records from db with fallback to default timezone is not set', async () => {
        await getSubcategoriesNetAmountByCategory(
          {
            reportType,
            entityUuid,
            date,
            category,
            domicile,
          },
          env,
        );

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'subcategory',
            category,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getMonthlyReportDateRange).toHaveBeenCalled();
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledTimes(1);
          expect(getTTMNetAmountTransactionRecords).toHaveBeenCalledWith(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date,
            groupBy: 'subcategory',
            category,
            transactionDirection: TransactionDirection.EXPENSE,
            domicile,
          });
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }

        expect(addCurrenciesToTxnResponse).toHaveBeenCalled();
      });
    });
  });
});
