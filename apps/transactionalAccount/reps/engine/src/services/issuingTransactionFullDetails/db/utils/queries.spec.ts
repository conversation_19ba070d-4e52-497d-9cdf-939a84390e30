import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';

import { TransactionDirection } from '../../../../common/types/index.js';
import { DEFAULT_TIMEZONE } from '../../../../const/timezone.js';

import {
  getIssuingFullDetailsFunction,
  getPreviousMonthTransactionSelectQuery,
  getTransactionDirectionFilterWhereClause,
} from './queries.js';

describe('getSelectTransactionQuery', () => {
  it('should return the correct select query with default timezone', () => {
    expect(getPreviousMonthTransactionSelectQuery()[1]).toContain(`'${DEFAULT_TIMEZONE}'`);
  });

  it('should return the correct select query with custom timezone', () => {
    const timezone = 'Australia/Sydney';
    expect(getPreviousMonthTransactionSelectQuery(timezone)[1]).toContain(`'${timezone}'`);
  });
});

describe('getSelectTransactionQuery', () => {
  it('should return the correct select query with default timezone', () => {
    expect(getPreviousMonthTransactionSelectQuery()[1]).toContain(`'${DEFAULT_TIMEZONE}'`);
  });

  it('should return the correct select query with custom timezone', () => {
    const timezone = 'Australia/Sydney';
    expect(getPreviousMonthTransactionSelectQuery(timezone)[1]).toContain(`'${timezone}'`);
  });
});

describe('getTransactionDirectionFilterWhereClause', () => {
  it.each(Object.values(TransactionDirection))(
    'should return the correct filter clause for expense transaction direction',
    (transactionDirection) => {
      expect(getTransactionDirectionFilterWhereClause(transactionDirection)).toBe(
        `cashflowType = '${transactionDirection}'`,
      );
    },
  );
});

describe('getIssuingFullDetailsFunction', () => {
  const domicile = Domicile.AU;
  const stage = 'test';
  const entityUuid = '123';
  const accountUuid = '456';
  const transactionDirection = TransactionDirection.EXPENSE;

  const baseInput = {
    entityUuid,
    date: 'date',
    domicile,
  };

  it('should return the correct function name and params when accountUuid and transactionDirection are provided', () => {
    const input = {
      ...baseInput,
      accountUuid,
      transactionDirection,
    };

    const result = getIssuingFullDetailsFunction(stage, input);

    expect(result.functionName).toBe(`${stage}.issuingtransactionfulldetails($1::uuid, $2::uuid, $3, $4)`);
    expect(result.params).toEqual([entityUuid, accountUuid, transactionDirection, domicile]);
  });

  it('should return the correct function name and params when only accountUuid is provided', () => {
    const input = {
      ...baseInput,
      entityUuid,
      accountUuid,
    };

    const result = getIssuingFullDetailsFunction(stage, input);

    expect(result.functionName).toBe(`${stage}.issuingtransactionfulldetails($1::uuid, $2::uuid, $3)`);
    expect(result.params).toEqual([entityUuid, accountUuid, domicile]);
  });

  it('should return the correct function name and params when only transactionDirection is provided', () => {
    const input = {
      ...baseInput,
      entityUuid,
      transactionDirection,
    };

    const result = getIssuingFullDetailsFunction(stage, input);

    expect(result.functionName).toBe(`${stage}.issuingtransactionfulldetails($1::uuid, $2, $3)`);
    expect(result.params).toEqual([entityUuid, transactionDirection, domicile]);
  });

  it('should return the correct function name and params when neither accountUuid nor transactionDirection are provided', () => {
    const input = {
      ...baseInput,
      entityUuid,
    };

    const result = getIssuingFullDetailsFunction(stage, input);

    expect(result.functionName).toBe(`${stage}.issuingtransactionfulldetails($1::uuid, $2)`);
    expect(result.params).toEqual([entityUuid, domicile]);
  });
});
