import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { EntityCategories, ISO4217 } from '@npco/component-dto-core';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';
import type { DebitCardTransactionV2 } from '@npco/component-dto-issuing-transaction';

import { v4 as uuid } from 'uuid';

import { SortBy, SortOrder, TransactionDirection } from '../../../../../common/types/index.js';
import { EnvironmentService } from '../../../../../config/environmentService.js';
import { DEFAULT_TIMEZONE } from '../../../../../const/timezone.js';
import { getDebitCardTransactionDto } from '../../../../debitCardAccountTransaction/mocks/getDebitCardTransactionMock.js';

import { getMonthlyNetAmountTransactionRecords } from './issuingTransactionFullDetailsDb.js';

jest.mock('../../../../../config/environmentService');

const currentDate: any = new Date().toISOString().split('T')[0];

describe('issuingTransactionFullDetailsDb', () => {
  const domicile = Domicile.AU;
  let saveDebitCardTransaction: (
    data: DebitCardTransactionV2,
    env: EnvironmentService,
    domicile: Domicile,
  ) => Promise<void>;

  let env: EnvironmentService;

  beforeAll(async () => {
    env = new EnvironmentService();
    const dbService = await import('../../../../debitCardAccountTransaction/db/debitCardAccountTransactionDb.js');
    saveDebitCardTransaction = dbService.saveDebitCardTransaction;
  });

  describe('categories net amount', () => {
    const entityUuid = uuid();
    const merchantEntityUuid = uuid();

    const transactions: Partial<DebitCardTransactionV2>[] = [
      {
        category: EntityCategories.ADVERTISING,
        amount: {
          value: '100',
          currency: ISO4217.AUD,
        },
      },
      {
        category: EntityCategories.BANK_FEES,
        amount: {
          value: '200',
          currency: ISO4217.AUD,
        },
      },
      {
        category: EntityCategories.BANK_FEES,
        amount: {
          value: '300',
          currency: ISO4217.AUD,
        },
      },
    ];

    const dtos = transactions.map((values, index) =>
      getDebitCardTransactionDto({
        entityUuid,
        type: DebitCardTransactionTypeV2.BPAY_OUT,
        merchant: {
          id: merchantEntityUuid,
          name: uuid(),
          updatedTime: Date.now(),
        },
        updatedTime: index,
        ...values,
      }),
    );

    beforeEach(async () => {
      await Promise.all(dtos.map((dto) => saveDebitCardTransaction(dto, env, domicile)));
    });

    describe('sorting', () => {
      it.each`
        sortBy                      | sortOrder         | expectedFirstCategory
        ${SortBy.AZ}                | ${SortOrder.ASC}  | ${EntityCategories.ADVERTISING}
        ${SortBy.AZ}                | ${SortOrder.DESC} | ${EntityCategories.BANK_FEES}
        ${SortBy.AVERAGE}           | ${SortOrder.ASC}  | ${EntityCategories.BANK_FEES}
        ${SortBy.AVERAGE}           | ${SortOrder.DESC} | ${EntityCategories.ADVERTISING}
        ${SortBy.NO_OF_TRANSACTION} | ${SortOrder.DESC} | ${EntityCategories.BANK_FEES}
        ${SortBy.NO_OF_TRANSACTION} | ${SortOrder.ASC}  | ${EntityCategories.ADVERTISING}
        ${SortBy.TOTAL}             | ${SortOrder.ASC}  | ${EntityCategories.ADVERTISING}
        ${SortBy.TOTAL}             | ${SortOrder.DESC} | ${EntityCategories.BANK_FEES}
        ${undefined}                | ${SortOrder.ASC}  | ${EntityCategories.ADVERTISING}
        ${undefined}                | ${SortOrder.DESC} | ${EntityCategories.BANK_FEES}
        ${undefined}                | ${undefined}      | ${EntityCategories.BANK_FEES}
      `(
        'should return records and ORDER BY $sortBy $sortOrder',
        async ({ sortBy, sortOrder, expectedFirstCategory }) => {
          const result = await getMonthlyNetAmountTransactionRecords(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            transactionDirection: TransactionDirection.EXPENSE,
            groupBy: 'category',
            sortBy,
            sortOrder,
            domicile,
          });

          expect(result.length).toBe(2);
          expect(result[0].category).toBe(expectedFirstCategory);
        },
      );
    });

    describe('limit', () => {
      it.each([
        {
          limit: undefined,
          expectedRecordLength: 2,
        },
        {
          limit: 1,
          expectedRecordLength: 1,
        },
      ])('should return records and LIMIT 1', async ({ limit, expectedRecordLength }) => {
        const result = await getMonthlyNetAmountTransactionRecords(env, {
          entityUuid,
          timeZone: DEFAULT_TIMEZONE,
          date: currentDate,
          transactionDirection: TransactionDirection.EXPENSE,
          groupBy: 'category',
          limit,
          domicile,
        });

        expect(result.length).toBe(expectedRecordLength);
      });
    });
  });
});
