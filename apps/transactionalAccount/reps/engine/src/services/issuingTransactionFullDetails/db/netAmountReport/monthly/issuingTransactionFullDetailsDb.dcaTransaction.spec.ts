import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { EntityCategories } from '@npco/component-dto-core';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';
import type { DebitCardTransactionV2 } from '@npco/component-dto-issuing-transaction';

import { v4 as uuid } from 'uuid';

import { TransactionDirection } from '../../../../../common/types/index.js';
import { EnvironmentService } from '../../../../../config/environmentService.js';
import { DEFAULT_TIMEZONE } from '../../../../../const/timezone.js';
import type { DebitCardAccountTransaction } from '../../../../../database/entities/index.js';
import { convertAmountWithSign } from '../../../../../utils/covertAmountWithSign.js';
import { saveContact } from '../../../../contact/db/contactDb.js';
import { getContactCreatedEventDto } from '../../../../contact/mocks/contactDto.js';
import {
  getAllDebitCardAccountTransactionRecords,
  getAllIssuingTransactionRecordsById,
} from '../../../../debitCardAccountTransaction/db/debitCardAccountTransactionDb.js';
import { getDebitCardTransactionDto } from '../../../../debitCardAccountTransaction/mocks/getDebitCardTransactionMock.js';
import { savePaymentInstrumentToDb } from '../../../../paymentInstrument/db/paymentInstrumentDb.js';
import { getPaymentInstrumentCreatedDto } from '../../../../paymentInstrument/mocks/getPaymentInstrumentDto.js';
import { expenseTransactions, incomeTransactions } from '../../const.js';

import { getMonthlyNetAmountTransactionRecords } from './issuingTransactionFullDetailsDb.js';

jest.mock('../../../../../config/environmentService');

const expectedIssuingTransactionRecord = async (
  env: EnvironmentService,
  id: string,
  domicile: Domicile,
): Promise<DebitCardAccountTransaction> => {
  const issuingTxnRecord = (await getAllIssuingTransactionRecordsById(id, env, domicile))[0];

  return {
    id: issuingTxnRecord.id,
    amount: issuingTxnRecord.amount,
    attachments: issuingTxnRecord.attachments ?? 0,
    category: issuingTxnRecord.category,
    accountUuid: issuingTxnRecord.accountUuid,
    debitCardId: issuingTxnRecord.debitCardId,
    entityUuid: issuingTxnRecord.entityUuid,
    merchantId: issuingTxnRecord.merchantId,
    note: !!issuingTxnRecord.note,
    paymentInstrumentUuid: issuingTxnRecord.paymentInstrumentUuid,
    senderUuid: issuingTxnRecord.senderUuid,
    status: issuingTxnRecord.status,
    subcategory: issuingTxnRecord.subcategory,
    tags: issuingTxnRecord.tags,
    timestamp: issuingTxnRecord.timestamp,
    type: issuingTxnRecord.type,
    updatedTime: issuingTxnRecord.updatedTime,
    domicile,
    currency: issuingTxnRecord.currency,
  };
};

const currentDate: any = new Date().toISOString().split('T')[0];

describe('issuingTransactionFullDetailsDb', () => {
  const domicile = Domicile.AU;
  let saveDebitCardTransaction: (
    data: DebitCardTransactionV2,
    env: EnvironmentService,
    domicile: Domicile,
  ) => Promise<void>;

  let env: EnvironmentService;

  beforeAll(async () => {
    env = new EnvironmentService();
    const dbService = await import('../../../../debitCardAccountTransaction/db/debitCardAccountTransactionDb.js');
    saveDebitCardTransaction = dbService.saveDebitCardTransaction;
  });

  describe('Issuing transaction full details view by entity', () => {
    const entityUuid = uuid();
    const merchantEntityUuid = uuid();
    const dtos = Array(5)
      .fill(0)
      .map((_, index) =>
        getDebitCardTransactionDto({
          entityUuid,
          merchant: {
            id: merchantEntityUuid,
            name: uuid(),
            updatedTime: Date.now(),
          },
          updatedTime: index,
        }),
      );

    beforeEach(async () => {
      await Promise.all(dtos.map((dto) => saveDebitCardTransaction(dto, env, domicile)));
    });

    it('should be able to return transaction full details records', async () => {
      const result = await getAllDebitCardAccountTransactionRecords(env, {
        where: {
          merchantId: merchantEntityUuid,
          domicile,
        },
        order: {
          updatedTime: 'ASC',
        },
      });

      expect(result.length).toBe(dtos.length);
      expect(result[result.length - 1].updatedTime).toBe(dtos[dtos.length - 1].updatedTime.toString()); // check ASC option
      expect(result[0]).toMatchObject(await expectedIssuingTransactionRecord(env, result[0].id, domicile));
    });
  });

  describe('Get records by category', () => {
    const entityUuid = uuid();
    const merchantEntityUuid = uuid();

    const allTransactions = [...expenseTransactions, ...incomeTransactions];
    const dtos = allTransactions.map((type, index) =>
      getDebitCardTransactionDto({
        entityUuid,
        type,
        merchant: {
          id: merchantEntityUuid,
          name: uuid(),
          updatedTime: Date.now(),
        },
        updatedTime: index,
      }),
    );

    beforeEach(async () => {
      await Promise.all(dtos.map((dto) => saveDebitCardTransaction(dto, env, domicile)));
    });

    describe('for all accounts', () => {
      describe('getMonthlyNetAmountTransactionRecords', () => {
        it('should return all expense records', async () => {
          const result = await getMonthlyNetAmountTransactionRecords(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            transactionDirection: TransactionDirection.EXPENSE,
            groupBy: 'category',
            domicile,
          });

          expect(result.length).toBeGreaterThan(0);
          expect(result[0].entityUuid).toBe(entityUuid);
          expect(new Date(result[0].dateRangeStart).getMonth()).toBe(new Date(currentDate).getMonth());
          expect(new Date(result[0].dateRangeEnd).getMonth()).toBe(new Date(currentDate).getMonth());

          expect(result[0].category).toBe(dtos[0].category);
          expect(result[0].subcategoryCount).toBeDefined();
          expect(result[0].total).toBeDefined();
          expect(result[0].average).toBeDefined();
          expect(result[0].change).toBeDefined();

          expect(result[0].noOfTransaction).toBe(expenseTransactions.length.toString());
        });

        it('should return all records if TransactionDirection field not set', async () => {
          const result = await getMonthlyNetAmountTransactionRecords(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            groupBy: 'category',
            domicile,
          });

          expect(result[0].noOfTransaction).toBe(dtos.length.toString());
        });
      });
    });

    describe('for single account', () => {
      describe('getMonthlyNetAmountTransactionRecords', () => {
        it.each(dtos)('should return record for a debit account record id', async (dto) => {
          const results = await getMonthlyNetAmountTransactionRecords(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            accountUuid: dto.debitCardAccountUuid,
            groupBy: 'category',
            domicile,
          });

          expect(results.length).toBe(1);

          const [result] = results;
          expect(result.entityUuid).toBe(entityUuid);
          expect(new Date(result.dateRangeStart).getMonth()).toBe(new Date(currentDate).getMonth());
          expect(new Date(result.dateRangeEnd).getMonth()).toBe(new Date(currentDate).getMonth());

          expect(result.category).toBe(dto.category);
          expect(result.subcategoryCount).toBe('1');
          expect(result.total).toBe(convertAmountWithSign(Number(dto.amount.value), dto.type).toString());
          expect(result.average).toBeDefined();
          expect(result.change).toBeDefined();

          expect(result.noOfTransaction).toBe('1');
        });

        it('should not return record if debit account does not exist', async () => {
          const results = await getMonthlyNetAmountTransactionRecords(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            accountUuid: uuid(),
            groupBy: 'category',
            domicile,
          });

          expect(results.length).toBe(0);
        });
      });
    });
  });

  describe('Get records by category without subcategories', () => {
    const entityUuid = uuid();

    const dtos = [
      getDebitCardTransactionDto({
        entityUuid,
        subcategory: undefined,
      }),
    ];

    beforeEach(async () => {
      await Promise.all(dtos.map((dto) => saveDebitCardTransaction(dto, env, domicile)));
    });

    it('should return zero value on subcategory count', async () => {
      const result = await getMonthlyNetAmountTransactionRecords(env, {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date: currentDate,
        groupBy: 'category',
        domicile,
      });

      expect(Number(result[0].subcategoryCount)).toEqual(0);
    });
  });

  describe('Get all account records for a contact by contactUuid', () => {
    const entityUuid = uuid();
    const contactUuid = uuid();
    const dtoContact = getContactCreatedEventDto({
      entityUuid,
      contactUuid,
    });
    const paymentInstrumentDto = getPaymentInstrumentCreatedDto({
      entityUuid,
      contactUuid: dtoContact.contactUuid,
    });

    const allTransactions = [...expenseTransactions, ...incomeTransactions];

    const dtos = allTransactions.map((type, index) =>
      getDebitCardTransactionDto({
        entityUuid,
        type,
        contactUuid: paymentInstrumentDto.contactUuid,
        updatedTime: index,
        payeeDetails: {
          recipientUuid: paymentInstrumentDto.paymentInstrumentUuid,
        },
      }),
    );

    beforeEach(async () => {
      await saveContact(dtoContact, env, domicile);
      await savePaymentInstrumentToDb(paymentInstrumentDto, env, domicile);
      await Promise.all(dtos.map((dto) => saveDebitCardTransaction(dto, env, domicile)));
    });

    it('should return all expense records', async () => {
      const result = await getMonthlyNetAmountTransactionRecords(env, {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date: currentDate,
        transactionDirection: TransactionDirection.EXPENSE,
        groupBy: 'contactUuid',
        domicile,
      });
      expect(result[0]).not.toHaveProperty(['category', 'subcategoryCount']);

      expect(result.length).toBeGreaterThan(0);
      expect(result[0].entityUuid).toBe(entityUuid);
      expect(result[0].contactUuid).toBe(contactUuid);

      expect(result[0].total).toBeDefined();
      expect(result[0].average).toBeDefined();
      expect(result[0].change).toBeDefined();

      expect(result[0].noOfTransaction).toBe(expenseTransactions.length.toString());
    });

    it('should return all income records', async () => {
      const result = await getMonthlyNetAmountTransactionRecords(env, {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date: currentDate,
        transactionDirection: TransactionDirection.INCOME,
        groupBy: 'contactUuid',
        domicile,
      });
      expect(result[0]).not.toHaveProperty(['category', 'subcategoryCount']);

      expect(result.length).toBeGreaterThan(0);
      expect(result[0].entityUuid).toBe(entityUuid);
      expect(result[0].contactUuid).toBe(contactUuid);

      expect(result[0].total).toBeDefined();
      expect(result[0].average).toBeDefined();
      expect(result[0].change).toBeDefined();

      expect(result[0].noOfTransaction).toBe(incomeTransactions.length.toString());
    });

    it('should return all records if transactionDirection field not set', async () => {
      const result = await getMonthlyNetAmountTransactionRecords(env, {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date: currentDate,
        groupBy: 'contactUuid',
        domicile,
      });

      expect(result[0].noOfTransaction).toBe(dtos.length.toString());
    });
  });

  describe('Get all records from single account for a contact by contactUuid', () => {
    const entityUuid = uuid();
    const contactUuid = uuid();
    const debitCardAccountUuid = uuid();
    const dtoContact = getContactCreatedEventDto({
      entityUuid,
      contactUuid,
    });
    const paymentInstrumentDto = getPaymentInstrumentCreatedDto({
      entityUuid,
      contactUuid: dtoContact.contactUuid,
    });

    const allTransactions = [...expenseTransactions, ...incomeTransactions];

    const dtos = allTransactions.map((type, index) =>
      getDebitCardTransactionDto({
        entityUuid,
        type,
        debitCardAccountUuid,
        contactUuid: paymentInstrumentDto.contactUuid,
        updatedTime: index,
        payeeDetails: {
          recipientUuid: paymentInstrumentDto.paymentInstrumentUuid,
        },
      }),
    );

    beforeEach(async () => {
      await saveContact(dtoContact, env, domicile);
      await savePaymentInstrumentToDb(paymentInstrumentDto, env, domicile);
      await Promise.all(dtos.map((dto) => saveDebitCardTransaction(dto, env, domicile)));
    });

    it('should return all expense records', async () => {
      const result = await getMonthlyNetAmountTransactionRecords(env, {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date: currentDate,
        transactionDirection: TransactionDirection.EXPENSE,
        accountUuid: debitCardAccountUuid,
        groupBy: 'contactUuid',
        domicile,
      });
      expect(result[0]).not.toHaveProperty(['category', 'subcategoryCount']);

      expect(result.length).toBeGreaterThan(0);
      expect(result[0].entityUuid).toBe(entityUuid);
      expect(result[0].contactUuid).toBe(contactUuid);

      expect(result[0].total).toBeDefined();
      expect(result[0].average).toBeDefined();
      expect(result[0].change).toBeDefined();

      expect(result[0].noOfTransaction).toBe(expenseTransactions.length.toString());
    });

    it('should return all income records', async () => {
      const result = await getMonthlyNetAmountTransactionRecords(env, {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date: currentDate,
        groupBy: 'contactUuid',
        accountUuid: debitCardAccountUuid,
        transactionDirection: TransactionDirection.INCOME,
        category: undefined,
        domicile,
      });
      expect(result[0]).not.toHaveProperty(['category', 'subcategoryCount']);

      expect(result.length).toBeGreaterThan(0);
      expect(result[0].entityUuid).toBe(entityUuid);
      expect(result[0].contactUuid).toBe(contactUuid);

      expect(result[0].total).toBeDefined();
      expect(result[0].average).toBeDefined();
      expect(result[0].change).toBeDefined();

      expect(result[0].noOfTransaction).toBe(incomeTransactions.length.toString());
    });

    it('should return all records if transactionDirection field not set', async () => {
      const result = await getMonthlyNetAmountTransactionRecords(env, {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date: currentDate,
        groupBy: 'contactUuid',
        accountUuid: debitCardAccountUuid,
        domicile,
      });

      expect(result[0].noOfTransaction).toBe(dtos.length.toString());
    });
  });

  describe('Get all subcategories net amount records', () => {
    const entityUuid = uuid();
    const merchantEntityUuid = uuid();

    const expenseCategoryAndSubCategory: Partial<DebitCardTransactionV2>[] = [
      {
        category: EntityCategories.ADVERTISING,
        subcategory: 'subCategory',
      },
      {
        category: EntityCategories.ADVERTISING,
        subcategory: 'subCategory',
      },
      {
        category: EntityCategories.ADVERTISING,
        subcategory: 'subCategory1',
      },
      {
        category: EntityCategories.BANK_FEES,
        subcategory: 'subCategory',
      },
      {
        category: EntityCategories.CLEANING,
        subcategory: 'subCategory',
      },
    ];

    const expenseTransactionDtos = expenseCategoryAndSubCategory.map((category, index) =>
      getDebitCardTransactionDto({
        entityUuid,
        category: category.category,
        subcategory: category.subcategory,
        merchant: {
          id: merchantEntityUuid,
          name: uuid(),
          updatedTime: Date.now(),
        },
        updatedTime: index,
      }),
    );

    const incomeTransactionDto = getDebitCardTransactionDto({
      entityUuid,
      type: DebitCardTransactionTypeV2.DE_IN,
      category: EntityCategories.CONSULTING_ACCOUNTING,
      subcategory: 'subCategory',
      merchant: {
        id: merchantEntityUuid,
        name: uuid(),
        updatedTime: Date.now(),
      },
      updatedTime: expenseTransactionDtos.length + 1,
    });

    const allTransactions = expenseTransactionDtos.concat(incomeTransactionDto);

    beforeEach(async () => {
      await Promise.all(allTransactions.map((dto) => saveDebitCardTransaction(dto, env, domicile)));
    });

    it.each(expenseTransactionDtos)('should be able to return expense records by category name', async (dto) => {
      const results = await getMonthlyNetAmountTransactionRecords(env, {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date: currentDate,
        accountUuid: dto.debitCardAccountUuid,
        groupBy: 'subcategory',
        category: dto.category!,
        transactionDirection: TransactionDirection.EXPENSE,
        domicile,
      });

      results.forEach((result: any) => {
        expect(result.entityUuid).toBe(dto.entityUuid);
        expect(result.category).toBe(dto.category);
        expect(result.subcategory).toEqual(expect.any(String));
        expect(result.total).toBeDefined();
        expect(result.average).toBeDefined();
      });
    });

    it('should not be able to return income records by category name', async () => {
      const results = await getMonthlyNetAmountTransactionRecords(env, {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date: currentDate,
        groupBy: 'subcategory',
        category: incomeTransactionDto.category!,
        transactionDirection: TransactionDirection.EXPENSE,
        domicile,
      });

      expect(results.length).toBe(0);
    });
  });
});
