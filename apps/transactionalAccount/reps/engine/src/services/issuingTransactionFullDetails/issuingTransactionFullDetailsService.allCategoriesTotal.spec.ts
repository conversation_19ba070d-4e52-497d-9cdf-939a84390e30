import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';

import { v4 as uuidv4 } from 'uuid';

import { ReportType, TransactionDirection } from '../../common/types/index.js';
import type { FunctionTypes } from '../../common/types/index.js';
import { EnvironmentService } from '../../config/environmentService.js';
import { DEFAULT_TIMEZONE } from '../../const/timezone.js';
import { getCurrentFormattedDate } from '../../utils/testing/date.js';

import type * as ServiceModule from './issuingTransactionFullDetailsService.js';

type ServiceModuleFunctionTypes = FunctionTypes<typeof ServiceModule>;

jest.mock('../../config/environmentService');

const getMonthlyCategoriesTotalRecords = jest.fn();
const getTTMCategoriesTotalRecords = jest.fn();
const addCurrenciesToTxnResponse = jest.fn();
const getTTMReportDateRange = jest.fn();
const getMonthlyReportDateRange = jest.fn();

jest.mock(
  '../issuingTransactionFullDetails/db/allCategoriesTotalReport/monthly/issuingTransactionFullDetailsDb',
  () => ({
    __esModule: true,
    getMonthlyCategoriesTotalRecords,
  }),
);

jest.mock('../issuingTransactionFullDetails/db/allCategoriesTotalReport/ttm/issuingTransactionFullDetailsDb', () => ({
  __esModule: true,
  getTTMCategoriesTotalRecords,
}));

jest.mock('../issuingTransactionFullDetails/utils/transactionResponse', () => ({
  __esModule: true,
  addCurrenciesToTxnResponse,
  getTTMReportDateRange,
  getMonthlyReportDateRange,
}));

describe('Issuing transactions full details service', () => {
  const domicile = Domicile.AU;
  let env: EnvironmentService;
  let service: any;

  beforeAll(async () => {
    env = new EnvironmentService();
    service = await import('./issuingTransactionFullDetailsService.js');

    // getTransactionRecordsByReportType.mockReturnValue([]);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe.each([ReportType.MONTHLY, ReportType.TTM])('by report type %s', (reportType) => {
    describe('getAllCategoriesTotal', () => {
      let getAllCategoriesTotal: ServiceModuleFunctionTypes['getAllCategoriesTotal'];
      const entityUuid = uuidv4();
      const date = getCurrentFormattedDate();

      beforeAll(async () => {
        getAllCategoriesTotal = service.getAllCategoriesTotal;
      });

      it('should throw an error if report type is invalid', async () => {
        await expect(
          getAllCategoriesTotal(
            {
              reportType: 'invalid' as ReportType,
              entityUuid,
              timeZone: DEFAULT_TIMEZONE,
              date,
              transactionDirection: TransactionDirection.EXPENSE,
              domicile,
            },
            env,
          ),
        ).rejects.toThrow('Invalid report type');
      });

      it('should call get transaction records from db', async () => {
        const input = {
          reportType,
          entityUuid,
          timeZone: DEFAULT_TIMEZONE,
          date,
          transactionDirection: TransactionDirection.EXPENSE,
          domicile,
        };
        await getAllCategoriesTotal(input, env);

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyCategoriesTotalRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyCategoriesTotalRecords).toHaveBeenCalledWith(env, input);
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMCategoriesTotalRecords).toHaveBeenCalledTimes(1);
          expect(getTTMCategoriesTotalRecords).toHaveBeenCalledWith(env, input);
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }
      });

      it('should call get transaction records from db for single account', async () => {
        const accountUuid = uuidv4();
        const input = {
          reportType,
          entityUuid,
          timeZone: DEFAULT_TIMEZONE,
          date,
          accountUuid,
          transactionDirection: TransactionDirection.EXPENSE,
          domicile,
        };
        await getAllCategoriesTotal(input, env);

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyCategoriesTotalRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyCategoriesTotalRecords).toHaveBeenCalledWith(env, input);
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMCategoriesTotalRecords).toHaveBeenCalledTimes(1);
          expect(getTTMCategoriesTotalRecords).toHaveBeenCalledWith(env, input);
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }
      });

      it('should call get transaction records from db with fallback to default timezone is not set', async () => {
        const input = {
          reportType,
          entityUuid,
          date,
          transactionDirection: TransactionDirection.EXPENSE,
          domicile,
        };
        await getAllCategoriesTotal(input, env);

        if (reportType === ReportType.MONTHLY) {
          expect(getMonthlyCategoriesTotalRecords).toHaveBeenCalledTimes(1);
          expect(getMonthlyCategoriesTotalRecords).toHaveBeenCalledWith(env, {
            ...input,
            timeZone: DEFAULT_TIMEZONE,
          });
          expect(getMonthlyReportDateRange).toHaveBeenCalled();
        }

        if (reportType === ReportType.TTM) {
          expect(getTTMCategoriesTotalRecords).toHaveBeenCalledTimes(1);
          expect(getTTMCategoriesTotalRecords).toHaveBeenCalledWith(env, {
            ...input,
            timeZone: DEFAULT_TIMEZONE,
          });
          expect(getTTMReportDateRange).toHaveBeenCalled();
        }
      });
    });
  });
});
