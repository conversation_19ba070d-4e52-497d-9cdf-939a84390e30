import {
  ApiAppServerlessStack,
  ssmSharedVpcImport as vpcImport,
  ServerlessPlugin,
} from '@npco/component-bff-serverless';

import { CrmsApiAppEnvConfig, esbuildWithSharpAndPdfkit } from './resources/crms/config';
import { iam } from './resources/crms/iam';
import { lambdas } from './resources/crms/lambdas';
import {
  getDepositForDcaTransactionAdditionalDataSource,
  getDepositForDcaTransactionAdditionalResolver,
  getDcaTransactionsDepositResolver,
} from './resources/crms/resolvers';

export const envConfig = new CrmsApiAppEnvConfig('resources/crms/config', true);

const sls = new ApiAppServerlessStack('deposit', envConfig, {
  plugins: [
    ServerlessPlugin.Tracing,
    ServerlessPlugin.ResourceTagging,
    ServerlessPlugin.EsBuild,
    ServerlessPlugin.Dotenv,
    ServerlessPlugin.CanaryDeployments,
    ServerlessPlugin.Dependson,
  ],
  package: {
    individually: true,
    patterns: ['!node_modules/aws-sdk', 'src/fonts/*'],
  },
  custom: {
    ...envConfig.getDefaults(),
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAmsApi(),
    ...envConfig.getComponentCqrs(),
    vpcImport,
    stlmtsV2V1MappingTableName: '${env:STATIC_ENV_NAME}-stlmts-v2-v1-settlementUuid-map',
    depositGsi: envConfig.depositGsi,
    depositsPendingGsi: envConfig.depositsPendingGsi,
    siteGsi: envConfig.siteGsi,
    entityGsi: envConfig.entityGsi,
    assetsStackName: `${envConfig.service}-assets`,
    exportBucketName: '${cf:${self:custom.assetsStackName}.ExportBucketName, "none"}',
    esbuild: esbuildWithSharpAndPdfkit,
    multiEntityEnabled: '${env:MULTI_ENTITY_ENABLED}',
    dependsOn: {
      // Optional. Defaults to true, set to false to disable the plugin
      enabled: true,
      // Optional. Sets amount of lambda deployment parallelization plugin will attempt to create. Defaults to 1
      chains: 3,
    },
  },
  functions: lambdas,
  resources: {
    ...iam,
    getDepositForDcaTransactionAdditionalResolver,
    getDepositForDcaTransactionAdditionalDataSource,
    getDcaTransactionsDepositResolver,
  },
  environment: {
    ...envConfig.dotenvConfig,
    COMPONENT_TABLE: envConfig.componentTableName,
    DOMICILE_LOOKUP_ENABLED: '${env:DOMICILE_LOOKUP_ENABLED}',
  },
});

module.exports = sls.build();
