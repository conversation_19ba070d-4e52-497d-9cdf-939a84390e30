import {
  ApiAppServerlessStack,
  esbuildNestWithPdfKit,
  pluginsAppDefault,
  vpcImport,
  MpApiAppEnvConfig,
} from '@npco/component-bff-serverless';
import { lambdas } from './resources/mp/lambdas';
import { eventBridgeRule } from './resources/mp/eventBridgeRule';
import { sqsEvents } from './resources/mp/sqsEvents';
import { resolvers } from './resources/mp/resolvers';

const envConfig = new MpApiAppEnvConfig('resources/mp/config', true);

const sls = new ApiAppServerlessStack('deposit', envConfig, {
  plugins: pluginsAppDefault,
  package: {
    individually: true,
    patterns: ['!node_modules/**', 'src/fonts/*'],
  },
  custom: {
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAmsApi(),
    ...envConfig.getComponentCqrs(),
    ...envConfig.getAuth0(),
    vpcImport,
    esbuild: esbuildNestWithPdfKit,
    entityTableName: '${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
    sessionCacheTableName: '${self:custom.dynamodbStackName}-${env:SESSION_CACHE_TABLE}',
    auth0Tenant: '${env:IDENTITY_AUTH0_TENANT}',
    modelSerialGsi: '${env:ENTITY_MODELSERIAL_GSI}',
    entityGsi: '${env:ENTITY_GSI}',
    entityCacheGsi: '${env:ENTITY_CACHE_GSI}',
    siteGsi: '${env:SITE_GSI}',
    depositGsi: 'depositGsi',
    depositsPendingGsi: '${env:DEPOSITS_PENDING_GSI}',
    streamArn: { 'Fn::ImportValue': '${self:custom.dynamodbStackName}-entityTableStreamArn' },
    multiEntityEnabled: '${env:MULTI_ENTITY_ENABLED}',
    stlmtsV2V1MappingTableName: '${env:STATIC_ENV_NAME}-stlmts-v2-v1-settlementUuid-map',
    exportDepositBucket: '${ssm:${env:STATIC_ENV_NAME}-mp-api-assets-txn-de-bucket}',
    depositBatchBucket:
      '${env:STATIC_ENV_NAME}-ds-engine-deposit-batch-${self:provider.region}-${self:custom.accountId}',
    settlementBatchBucket: '${env:STATIC_ENV_NAME}-stlmts-batch-files-${self:provider.region}-${self:custom.accountId}',
    primaryRegion: '${env:PRIMARY_REGION}',
    mpCqrsStackName: {
      dev: '${opt:stage}-mp-cqrs',
      staging: '${opt:stage}-mp-cqrs',
      prod: '${opt:stage}-mp-cqrs',
      st: '${opt:stage}-mp-stl-mock-cqrs',
    },
    cqrsSqsArn:
      '${cf:${self:custom.mpCqrsStackName.${opt:stage}, "${self:custom.mpCqrsStackName.st}"}-iac-sqs.QueueARN}',
    cqrsSqsUrl:
      '${cf:${self:custom.mpCqrsStackName.${opt:stage}, "${self:custom.mpCqrsStackName.st}"}-iac-sqs.QueueURL}',
    mpCqrsEventBus:
      '${cf:${self:custom.mpCqrsStackName.${opt:stage}, "${self:custom.mpCqrsStackName.st}"}-iac-eventBridge.EventBusProjectionArn}',
    mpCqrsProjectionDLQArn:
      '${cf:${self:custom.mpCqrsStackName.${opt:stage}, "${self:custom.mpCqrsStackName.st}"}-iac-eventBridge.EventBusProjectionDLQArn}',
    mpCqrsCommandHandler: '${opt:stage}-mp-cqrs-commandHandlers-handler',
    dependsOn: { enabled: false, chains: 3 },
    domicileLookupTableReadRolePolicyArn: {
      'Fn::ImportValue': '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn',
    },
  },
  functions: lambdas,
  resources: {
    ...sqsEvents.Resources,
    ...eventBridgeRule.Resources,
    ...resolvers,
  },
  environment: {
    ...envConfig.dotenvConfig,
    CQRS_COMMAND_HANDLER: '${self:custom.mpCqrsCommandHandler}',
    IS_ZELLER_SESSION_ID_ENABLED: '${env:IS_ZELLER_SESSION_ID_ENABLED}',
    STLMTS_V2_V1_MAPPING_TABLE_NAME: '${opt:stage}-stlmts-v2-v1-settlementUuid-map',
    SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    IS_RBAC_ENFORCED: '${env:IS_RBAC_ENFORCED}',
    IS_RBAC_ENFORCE_ROLE: '${env:IS_RBAC_ENFORCE_ROLE}',
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
    DOMICILE_LOOKUP_ENABLED: '${env:DOMICILE_LOOKUP_ENABLED}',
  },
});
module.exports = sls.build();
