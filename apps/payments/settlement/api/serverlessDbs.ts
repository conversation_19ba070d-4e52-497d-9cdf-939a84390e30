import {
  ApiAppServerlessStack,
  esbuildNestCommon,
  pluginsAppDefault,
  vpcImport,
  DbsApiAppEnvConfig,
} from '@npco/component-bff-serverless';
import { lambdas } from './resources/dbs/lambdas';
import { eventBridgeRule } from './resources/dbs/eventBridgeRule';
import { sqsEvents } from './resources/dbs/sqsEvents';
import { resolvers } from './resources/dbs/resolvers';

const envConfig = new DbsApiAppEnvConfig('resources/dbs/config', true);

const sls = new ApiAppServerlessStack('deposit', envConfig, {
  plugins: pluginsAppDefault,
  custom: {
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAmsApi(),
    ...envConfig.getComponentCqrs(),
    ...envConfig.getAuth0(),
    vpcImport,
    deviceTableName: '${self:custom.dynamodbStackName}-${env:COMPONENT_TABLE}',
    modelSerialGsi: '${env:CACHE_MODELSERIAL_GSI}',
    cacheTableName: '${self:custom.dynamodbStackName}-${env:SESSION_CACHE_TABLE}',
    accessTokenGsi: '${env:ACCESS_TOKEN_GSI}',
    entityCacheGsi: '${env:ENTITY_CACHE_GSI}',
    depositsPendingGsi: '${env:DEPOSITS_PENDING_GSI}',
    auth0Tenant: '${env:IDENTITY_AUTH0_TENANT}',
    entityGsi: '${env:ENTITY_GSI}',
    siteGsi: '${env:SITE_GSI}',
    multiEntityEnabled: '${env:MULTI_ENTITY_ENABLED}',
    depositGsi: 'depositGsi',
    dbsCqrsEventBus:
      '${cf:${self:custom.cqrsStackName.${opt:stage}, "${self:custom.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionArn}',
    streamArn: { 'Fn::ImportValue': '${self:custom.dynamodbStackName}-deviceTableStreamArn' },
    esbuild: esbuildNestCommon,
    stlmtsV2V1MappingTableName: '${env:STATIC_ENV_NAME}-stlmts-v2-v1-settlementUuid-map',
    depositBatchBucket:
      '${env:STATIC_ENV_NAME}-ds-engine-deposit-batch-${self:provider.region}-${self:custom.accountId}',
    settlementBatchBucket: '${env:STATIC_ENV_NAME}-stlmts-batch-files-${self:provider.region}-${self:custom.accountId}',
    primaryRegion: '${env:PRIMARY_REGION}',
    cqrsStackName: {
      dev: '${opt:stage}-dbs-cqrs',
      staging: '${opt:stage}-dbs-cqrs',
      prod: '${opt:stage}-dbs-cqrs',
      st: '${opt:stage}-dbs-stl-mock-cqrs',
    },
    dbsCqrsProjectionDLQArn:
      '${cf:${self:custom.cqrsStackName.${opt:stage}, "${self:custom.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionDLQArn}',
    cqrsSqsArn: '${cf:${self:custom.cqrsStackName.${opt:stage}, "${self:custom.cqrsStackName.st}"}-iac-sqs.QueueARN}',
    cqrsSqsUrl: '${cf:${self:custom.cqrsStackName.${opt:stage}, "${self:custom.cqrsStackName.st}"}-iac-sqs.QueueURL}',
    dependsOn: { enabled: false, chains: 3 },
    domicileLookupTableReadRolePolicyArn: {
      'Fn::ImportValue': '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn',
    },
  },
  functions: lambdas,
  resources: {
    ...sqsEvents.Resources,
    ...eventBridgeRule.Resources,
    ...resolvers,
  },
  environment: {
    ...envConfig.dotenvConfig,
    AWS_NODEJS_CONNECTION_REUSE_ENABLED: 1,
    IS_RBAC_ENFORCED: '${env:IS_RBAC_ENFORCED}',
    IS_RBAC_ENFORCE_ROLE: '${env:IS_RBAC_ENFORCE_ROLE}',
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
    DOMICILE_LOOKUP_ENABLED: '${env:DOMICILE_LOOKUP_ENABLED}',
  },
});
module.exports = sls.build();
