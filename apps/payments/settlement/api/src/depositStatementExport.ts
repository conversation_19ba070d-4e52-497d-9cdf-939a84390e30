import { S3Client } from '@npco/component-bff-core/dist/aws/s3Client';
import { GraphQLError, ServerError } from '@npco/component-bff-core/dist/error';
import { error } from '@npco/component-bff-core/dist/utils/logger';
import { DynamodbService } from '@npco/component-dbs-mp-common';
import { convertDbItemToEntity, getEntityDbItem } from '@npco/component-dbs-mp-common/dist/entity';
import { convertDbItemToSite } from '@npco/component-dbs-mp-common/dist/site/convertDbItemToSite';
import { getSiteDbItemOrThrow } from '@npco/component-dbs-mp-common/dist/site/getSiteDbItem';
import type { Entity } from '@npco/component-dbs-mp-common/dist/types';
import { ExportType } from '@npco/component-dbs-mp-common/dist/types';
import { generatePresignedURL, getSiteUuidsFromFilter, uploadToS3 } from '@npco/component-dbs-mp-common/dist/utils';
import {
  dateTimeFormatForOffset,
  findTimeoffsetFromRequest,
  getStartAndEndFromFilter,
} from '@npco/component-dbs-mp-common/dist/utils/pdfExportUtils';
import type { Site } from '@npco/component-dto-site';

import { Injectable } from '@nestjs/common';

import { DepositService } from './depositService';
import { EnvService } from './envService';
import { DepositStatementPdfService } from './pdf/depositStatementPdfService';
import type { DepositQueryInput, DepositTotals } from './types';

@Injectable()
export class DepositStatementExportService {
  private static readonly EXPIRES_IN = 365 * 24 * 60 * 60 * 1000;

  private static readonly PRE_SIGNED_EXPIRES_IN = 60 * 60;

  constructor(
    private readonly envService: EnvService,
    private readonly depositService: DepositService,
    private readonly pdfService: DepositStatementPdfService,
    readonly dynamodbService: DynamodbService,
  ) {}

  exportDepositStatement = async (
    event: DepositQueryInput,
    entityUuid: string,
    exportType: ExportType,
    managerCustomerUuid?: string,
  ) => {
    const region = this.envService.awsRegion || 'ap-southeast-2';
    const s3 = new S3Client(region);
    const fileName = `${entityUuid}_${new Date().getTime()}.${exportType.toLowerCase()}`;
    const filePath = 'depositStatement';
    const key = `${filePath}/${fileName}`;

    try {
      const item = await getEntityDbItem(this.dynamodbService, entityUuid);
      if (!item) {
        throw new ServerError(`Invalid entity ${entityUuid}`);
      }
      const entity = convertDbItemToEntity(item);
      const parsedFilter = JSON.parse(event.filter?.length ? event.filter : '{}');
      const siteUuids = getSiteUuidsFromFilter(parsedFilter);
      const sites =
        siteUuids.length === 1
          ? [convertDbItemToSite(await getSiteDbItemOrThrow(this.dynamodbService, entityUuid, siteUuids[0]))]
          : siteUuids.map((id) => ({ id } as Site));
      const depositTotals = await this.depositService.getDepositTotals(event, entityUuid, managerCustomerUuid);
      const body = await this.generateStatementExport(depositTotals, exportType, event, entity, sites);

      const expire = await uploadToS3(
        s3,
        this.envService.transactionDepositExportBucket,
        key,
        body,
        DepositStatementExportService.EXPIRES_IN,
      );
      const presigned = await generatePresignedURL(
        s3,
        this.envService.transactionDepositExportBucket,
        key,
        DepositStatementExportService.PRE_SIGNED_EXPIRES_IN,
      );
      return { downloadLink: presigned, expire };
    } catch (err: any) {
      error(err.message);
      if (err instanceof GraphQLError) {
        return Promise.reject(err); // NOSONAR
      }
      return Promise.reject(new ServerError('Failed to upload deposit statement.')); // NOSONAR
    }
  };

  private readonly generateStatementExport = async (
    depositTotals: DepositTotals,
    type: ExportType,
    event: DepositQueryInput,
    entity: Entity,
    sites: Site[],
  ) => {
    if (type !== ExportType.PDF) {
      throw new ServerError(`Unsupported export type ${type}.`);
    }
    return this.generateStatementPdf(depositTotals, event, entity, sites);
  };

  private readonly generateStatementPdf = async (
    depositTotals: DepositTotals,
    event: DepositQueryInput,
    entity: Entity,
    sites: Site[],
  ) => {
    const [start, end] = getStartAndEndFromFilter(event.timestampFilter);
    const offset = findTimeoffsetFromRequest(event);
    return this.pdfService.createDocument({
      title: `Statement Period: ${dateTimeFormatForOffset(start, offset)} – ${dateTimeFormatForOffset(end, offset)}`,
      generatedAt: dateTimeFormatForOffset(new Date(), offset),
      sites,
      entity,
      depositTotals,
      items: [],
      headers: [],
    });
  };
}
