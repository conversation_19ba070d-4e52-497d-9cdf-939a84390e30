import { S3Client } from '@npco/component-bff-core/dist/aws/s3Client';
import { ServerError } from '@npco/component-bff-core/dist/error';
import { error } from '@npco/component-bff-core/dist/utils/logger';
import { ExcelWriter } from '@npco/component-dbs-mp-common';
import { ExportType } from '@npco/component-dbs-mp-common/dist/types';
import { generatePresignedURL, uploadToS3 } from '@npco/component-dbs-mp-common/dist/utils';
import { adjustToOffset } from '@npco/component-dbs-mp-common/dist/utils/dateUtil';
import {
  dateTimeFormatForOffset,
  findTimeoffsetFromRequest,
  getStartAndEndFromFilter,
} from '@npco/component-dbs-mp-common/dist/utils/pdfExportUtils';
import type { DepositModel } from '@npco/component-dto-deposit';
import { DepositStatus } from '@npco/component-dto-deposit';

import { Injectable } from '@nestjs/common';

import { DepositService } from './depositService';
import { EnvService } from './envService';
import { DepositInvoicePdfService } from './pdf/depositInvoicePdfService';
import type { DepositExportRow, DepositHeaderColumn, DepositQueryInput } from './types';

const COLUMN_WIDTH = 12;

export const DEPOSIT_HEADER_COLUMNS: DepositHeaderColumn[] = [
  { key: 'settlement', header: 'Settlement', width: COLUMN_WIDTH },
  { key: 'siteName', header: 'Site Name', width: COLUMN_WIDTH },
  { key: 'dateSettled', header: 'Date Settled', width: COLUMN_WIDTH },
  { key: 'timeAEDT', header: 'Time (AEDT)', width: COLUMN_WIDTH },
  { key: 'dateHeld', header: 'Date Held', optional: true, width: COLUMN_WIDTH },
  { key: 'processed', header: 'Processed', width: COLUMN_WIDTH },
  { key: 'feeAmount', header: 'Processing Fees (excl GST)', width: COLUMN_WIDTH },
  { key: 'feeAmountGst', header: 'GST portion of Processing Fees', width: COLUMN_WIDTH },
  { key: 'refunds', header: 'Refunds', width: COLUMN_WIDTH },
  { key: 'pending', header: 'Pending', width: COLUMN_WIDTH },
  {
    key: 'rolledOverProcessed',
    header: 'Processed (Rolled over)',
    optional: true,
    width: COLUMN_WIDTH,
  },
  {
    key: 'rolledOverFeeAmount',
    header: 'Processing Fees (excl GST) (Rolled over)',
    optional: true,
    width: COLUMN_WIDTH,
  },
  {
    key: 'rolledOverGstFeeAmount',
    header: 'GST portion of Processing Fees (Rolled over)',
    optional: true,
    width: COLUMN_WIDTH,
  },
  { key: 'amountSettled', header: 'Amount Settled', width: COLUMN_WIDTH },
  { key: 'accountName', header: 'Account settled to', width: COLUMN_WIDTH },
];

@Injectable()
export class DepositExportService {
  private static readonly EXPIRES_IN = 365 * 24 * 60 * 60 * 1000;

  private static readonly PRE_SIGNED_EXPIRES_IN = 60 * 60; // in seconds

  constructor(
    private readonly envService: EnvService,
    private readonly depositService: DepositService,
    private readonly pdfService: DepositInvoicePdfService,
  ) {}

  exportDeposit = async (
    event: DepositQueryInput,
    entityUuid: string,
    exportType: ExportType,
    managerCustomerUuid?: string,
  ) => {
    const region = this.envService.awsRegion || 'ap-southeast-2';
    const s3 = new S3Client(region);
    const fileName = `${entityUuid}_${new Date().getTime()}.${exportType.toLowerCase()}`;
    const filePath = 'deposit';
    const key = `${filePath}/${fileName}`;
    const offset = findTimeoffsetFromRequest(event);
    try {
      const deposits = await this.depositService.getAllDeposits(event, entityUuid, managerCustomerUuid);
      const depositRows = this.mapDepositsToDepositRows(deposits, offset);
      const headerData = this.removeOptionalEmptyColumns(depositRows);
      const body = await this.generateExport(headerData, depositRows, exportType, event);

      const expire = await uploadToS3(
        s3,
        this.envService.transactionDepositExportBucket,
        key,
        body,
        DepositExportService.EXPIRES_IN,
      );
      const presigned = await generatePresignedURL(
        s3,
        this.envService.transactionDepositExportBucket,
        key,
        DepositExportService.PRE_SIGNED_EXPIRES_IN,
      );
      return { downloadLink: presigned, expire };
    } catch (err: any) {
      error(err.message);
      return Promise.reject(new ServerError('Failed to upload deposit.')); // NOSONAR
    }
  };

  formatAmount = (amount?: number): string => {
    if (amount || amount === 0) {
      return `$${(amount / 100).toFixed(2)}`;
    }
    return '';
  };

  formatNonZeroAmount = (amount?: number): string | undefined => {
    if (amount !== 0) {
      return this.formatAmount(amount);
    }
    return undefined;
  };

  getGstAmount = (amount: number): number => {
    if (amount) {
      return Math.trunc(amount / 11);
    }
    return 0;
  };

  private readonly removeOptionalEmptyColumns = (deposits: DepositExportRow[]): DepositHeaderColumn[] => {
    const headers = JSON.parse(JSON.stringify(DEPOSIT_HEADER_COLUMNS));
    DEPOSIT_HEADER_COLUMNS.forEach(({ key, optional }, i) => {
      if (optional) {
        deposits.forEach((row) => {
          if (row[key]) {
            headers[i].optional = false;
          }
        });
      }
    });
    return headers.reduce((headerData: DepositHeaderColumn[], header: DepositHeaderColumn) => {
      if (!header.optional) {
        headerData.push(header);
      }
      return headerData;
    }, []);
  };

  private readonly mapDepositsToDepositRows = (deposits: DepositModel[], offset: string): DepositExportRow[] =>
    deposits.map((deposit) => {
      let dateSettled = '';
      let timeAEDT = '';
      let dateHeld = '';
      try {
        const processedDateOffset = adjustToOffset(new Date(deposit.timestamp).toISOString(), offset);
        dateSettled = processedDateOffset.substring(0, 10);
        timeAEDT = processedDateOffset.substring(11, 19);
        if (deposit.skippedTimestamp) {
          const dateOffset = adjustToOffset(new Date(deposit.skippedTimestamp).toISOString(), offset);
          dateHeld = dateOffset.substring(0, 10);
        }
      } catch (err: any) {
        error(`${err.message} ${JSON.stringify(deposit)}`);
      }

      const rolledOverFeeGst = this.getGstAmount(deposit.rolledOverFeeAmount);
      const rolledOverProcessed = this.formatNonZeroAmount(deposit.rolledOverProcessedAmount);
      const rolledOverFeeAmountExclGst = this.formatNonZeroAmount(deposit.rolledOverFeeAmount - rolledOverFeeGst);
      const rolledOverGstFeeAmount = this.formatNonZeroAmount(rolledOverFeeGst);

      // FE fix - remove after migration
      const processedFeeAmount = deposit.feeAmount - deposit.rolledOverFeeAmount;
      const feeGstAmount = this.getGstAmount(processedFeeAmount);
      const feeAmountExclGst = processedFeeAmount - feeGstAmount;
      return {
        settlement: deposit.shortId ?? '',
        siteName: deposit.siteName,
        dateSettled,
        timeAEDT,
        ...(dateHeld ? { dateHeld } : {}),
        status: deposit.status === DepositStatus.COMPLETED ? 'SETTLED' : deposit.status,
        processed: this.formatAmount(deposit.processed),
        feeAmount: this.formatAmount(feeAmountExclGst),
        feeAmountGst: this.formatAmount(feeGstAmount),
        ...(rolledOverProcessed ? { rolledOverProcessed } : {}),
        ...(rolledOverFeeAmountExclGst ? { rolledOverFeeAmount: rolledOverFeeAmountExclGst } : {}),
        ...(rolledOverGstFeeAmount ? { rolledOverGstFeeAmount } : {}),
        refunds: this.formatAmount(deposit.refundsAmount),
        pending: this.formatAmount(deposit.withHeld),
        amountSettled: this.formatAmount(deposit.amountSettled),
        accountName: deposit.accountName,
      };
    });

  private readonly generateExport = async (
    headers: DepositHeaderColumn[],
    rows: DepositExportRow[],
    type: ExportType,
    event: DepositQueryInput,
  ) => {
    switch (type) {
      case ExportType.CSV:
        return this.generateCsvExport(headers, rows);
      case ExportType.XLSX:
        return this.generateXlsxExport(headers, rows);
      case ExportType.PDF:
        return this.generatePdf(headers, rows, event);
      default:
        throw new ServerError('Failed to generate settlement.');
    }
  };

  private readonly generatePdf = async (
    headers: DepositHeaderColumn[],
    rows: DepositExportRow[],
    event: DepositQueryInput,
  ) => {
    const [start, end] = getStartAndEndFromFilter(event.timestampFilter);
    const offset = findTimeoffsetFromRequest(event);
    return this.pdfService.createDocument({
      title: `Settlements Period: ${dateTimeFormatForOffset(start, offset)} – ${dateTimeFormatForOffset(end, offset)}`,
      items: rows,
      headers,
    });
  };

  private readonly generateCsvExport = (headers: DepositHeaderColumn[], rows: DepositExportRow[]) => {
    const writer = new ExcelWriter();
    writer.addSheet(rows, headers);
    return writer.getCsvOutput();
  };

  private readonly generateXlsxExport = async (headers: DepositHeaderColumn[], rows: DepositExportRow[]) => {
    const writer = new ExcelWriter();
    writer.addSheet(rows, headers, 'Settlements');
    return writer.getXlsxOutput();
  };
}
