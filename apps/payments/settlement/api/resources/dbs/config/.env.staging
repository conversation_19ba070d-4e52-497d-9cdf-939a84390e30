IDENTITY_AUTH0_AUDIENCE=https://devices.myzeller.com
IDENTITY_AUTH0_TENANT=https://zeller-staging.au.auth0.com
OPENID_ISSUER_URL=https://zeller-staging.au.auth0.com/

LOG_LEVEL=info

COMPONENT_NAME=dbs
PART_NAME=api

NODE_JS_RUNTIME=nodejs18.x
NODE_RUNTIME=18

COMPONENT_TABLE=Devices
SESSION_CACHE_TABLE=SessionCache
SMSCODE_TABLE=Smscodes
SMSCODE_TTL=600
SMS_VERIFICATION_RATE_LIMIT=3600000
CACHE_MODELSERIAL_GSI=modelSerialGsi
ENTITY_GSI=entityGsi
SHORT_ID_GSI=shortIdGsi
ACCESS_TOKEN_GSI=accessTokenGsiv2
SITE_GSI=siteGsi
TYPE_GSI=typeGsiV2
SITE_NAME_GSI=siteNameGsi
CARDHOLDER_GSI=carholderGsi
DEVICE_GSI=deviceGsi
ENTITY_CACHE_GSI=entityCacheGsi
DEPOSITS_PENDING_GSI=depositsPendingGsi
ENTITY_TRANSACTION_TOTAL_GSI=entityTransactionTotal200GsiV4
TRANSACTION_DATE_GSI=transactionDateGsi
ORIGINAL_TRANSACTION_GSI=originalTransactionGsi
CONTRIBUTOR_INSIGHTS=true

DEPOSITS_PENDING_SCHEDULER=cron(0 15 ? * 1 *)
BACKUP_SCHEDULER=cron(0 16 ? * * *)

LAMBDA_TIMEOUT_IN_SECONDS=30

PGS_API_COMPONENT_NAME=pgs-api
PGS_INITIATE_TXN_HANDLER=initiateTransactionHandler
IDENTITY_AUTH0_JWT_MAX_AGE=86400

PGS_API_INITIATE_TXN_PATH=/v1/transactions

IAM_APPSYNC_PUBLISHER=appsync-publisher

GLOBAL_EVENT_BUS_NAME=staging-eventBus-global

# AMS
AMS_API_ENDPOINT_VERSION=v2
AMS_API_ENDPOINT_DEVICE_PATH=/device
AMS_API_ENDPOINT_CUSTOMER_PATH=/customer

#Keep warm
KEEP_WARM_SCHEDULER=cron(5/5 * ? * * *)
KEEP_WARM_SOURCE=keep.warm
LAMBDA_KEEP_WARM_STATE=DISABLED

# VPC
VPC_ENV_NAME=staging
STATIC_ENV_NAME=staging

# PC
REQUEST_TXN_PROVISIONED_CONCURRENCY=1
PROVISION_ALIAS=:provisioned

# PGS
USE_PGS_RKI=false

# CMS 
CMS_API_ENDPOINT_VERSION=v1

# Billing
BILLING_API_ENDPOINT_VERSION=v1

# Multiple region
PRIMARY_REGION=ap-southeast-2

# MP-API
MP_API_STAGE_NAME=staging

# MFA enrolment
MFA_ENROLMENT_ENABLED=true

# Payement Gateway Service
PGS_API_ENDPOINT_SSM=staging-pgs-api-apiGatewayEndpoint

# RBAC
IS_RBAC_ENFORCED=false
IS_RBAC_ENFORCE_ROLE=false

# MultiEntity
MULTI_ENTITY_ENABLED=true

DOMICILE_LOOKUP_ENABLED=false
