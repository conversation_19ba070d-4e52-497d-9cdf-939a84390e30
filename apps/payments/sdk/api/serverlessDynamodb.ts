import {
  ApiAppServerlessStack,
  Env,
  ManagedPolicy,
  ServerlessPlugin,
  awsfunc,
  createManagedPolicies,
  vpcImport,
} from '@npco/component-bff-serverless';

import { envConfig, esbuild, pluginsApp } from './iac/resources/common';
import { outputs, resources } from './iac/resources/dynamodb/dynamodb';
import { streamLambdas } from './iac/resources/dynamodb/lambdas';

const policies = createManagedPolicies([
  ManagedPolicy.entityTableWriteItemRolePolicy,
  ManagedPolicy.entityTableDeleteRolePolicy,
  ManagedPolicy.sessionCacheTableDBPolicy,
  ManagedPolicy.xrayPolicy,
  ManagedPolicy.lambdaVpcPolicy,
  ManagedPolicy.entityTableQueryRolePolicy,
  ManagedPolicy.crossAccountInvocationPolicyArn,
]);

const sls = new ApiAppServerlessStack('dynamodb', envConfig, {
  plugins: [...pluginsApp, ServerlessPlugin.CreateGlobalDynamoDbTableTags],
  custom: {
    ...envConfig.getDynamoDb(),
    globalTables: {
      version: 'v2',
      regions: ['ap-southeast-1'],
      createStack: true,
      enable: '${env:DEPLOY_MULTI_REGION}',
    },
    vpcImport,
    componentName: Env.COMPONENT_NAME,
    entityGsi: Env.ENTITY_GSI,
    partName: Env.PART_NAME,
    primaryRegion: '${env:PRIMARY_REGION}',
    sessionCacheTableName: envConfig.sessionCacheTableName,
    accessTokenGsi: envConfig.accessTokenGsi,
    entityCacheGsi: envConfig.entityCacheGsi,
    deviceGsi: envConfig.deviceGsi,
    modelSerialGsi: '${env:CACHE_MODEL_SERIAL_GSI}',
    siteNameGsi: envConfig.siteNameGsi,
    siteGsi: envConfig.siteGsi,
    backupNamePrefix: envConfig.dynamodbStackName,
    tableDeletionProtection: {
      dev: true,
      staging: true,
      prod: true,
      st: false,
    },
    esbuild,
    service: envConfig.service,
    entityTableName: envConfig.entityTableName,
    tableStreamArn: awsfunc.getAtt(['EntityTable', 'StreamArn']),
  },
  resources: {
    ...resources,
    ...policies.resources?.Resources,
  },
  provider: {
    name: 'aws',
    vpc: {
      securityGroupIds: ['${ssm:${env:VPC_ENV_NAME}-vpc-shared-01-lambda-sg}'],
      subnetIds: [
        '${ssm:${env:VPC_ENV_NAME}-vpc-shared-01-lambda-subnet01}',
        '${ssm:${env:VPC_ENV_NAME}-vpc-shared-01-lambda-subnet02}',
        '${ssm:${env:VPC_ENV_NAME}-vpc-shared-01-lambda-subnet03}',
      ],
    },
  },
  functions: streamLambdas,
  outputs: {
    ...outputs,
    ...policies.resources?.Outputs,
  },
  environment: {
    ...envConfig.dotenvConfig,
    COMPONENT_TABLE: envConfig.componentTableName,
  },
});

console.log('xxxxxx', JSON.stringify(sls.build(), null, 2));
module.exports = sls.build();
