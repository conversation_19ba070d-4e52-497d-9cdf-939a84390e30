# IAC
COMPONENT_NAME=sdk
PART_NAME=api
STATIC_ENV_NAME=staging
VPC_ENV_NAME=staging

# Dynamodb
COMPONENT_TABLE=Entities
SESSION_CACHE_TABLE=SessionCache
ACCESS_TOKEN_GSI=accessTokenGsi
ENTITY_CACHE_GSI=entityCacheGsi
CACHE_MODEL_SERIAL_GSI=modelSerialGsi
ENTITY_GSI=entityGsi
SHORT_ID_GSI=shortIdGsi
TYPE_GSI=typeGsiV2
SITE_NAME_GSI=siteNameGsi
SITE_GSI=siteGsi
DEVICE_GSI=deviceGsi

# Multiple region
DEPLOY_MULTI_REGION=false
PRIMARY_REGION=ap-southeast-2

# Lambdas
LAMBDA_TIMEOUT_IN_SECONDS=30
LAMBDA_MEMORY_DEFAULT=512
NODE_OPTIONS=--enable-source-maps

#Keep warm
KEEP_WARM_SCHEDULER=cron(6/5 * ? * * *)
KEEP_WARM_SOURCE=keep.warm
LAMBDA_KEEP_WARM_STATE=DISABLED

LOG_LEVEL=info

NODE_JS_RUNTIME=nodejs18.x

# auth
IDENTITY_AUTH0_TENANT=https://zeller-staging.au.auth0.com
OPENID_ISSUER_URL=https://auth.myzeller.show/
IDENTITY_AUTH0_AUDIENCE=https://sdk.myzeller.com

# External API
AMS_API_ENDPOINT_VERSION=v1
