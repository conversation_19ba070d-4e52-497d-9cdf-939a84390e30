import type { ServerlessFunctions } from '@npco/component-bff-serverless';
import { Action, ManagedPolicy } from '@npco/component-bff-serverless';
import { DbRecordType } from '@npco/component-dto-core';

export const streamLambdas: ServerlessFunctions = {
  onEntitiesTableStreamHandler: {
    handler: 'src/lambda/stream/entitiesTableStreamHandler.onEntitiesTableStreamHandler',
    name: 'onEntitiesTableStreamHandler',
    tracing: true,
    useLogicalArnName: true,
    timeout: 30,
    dependsOn: ['EntityTable'],
    events: [
      {
        stream: {
          type: 'dynamodb',
          arn: '${self:custom.tableStreamArn}',
          startingPosition: 'LATEST',
          batchSize: 100,
          filterPatterns: [
            {
              eventName: ['INSERT', 'MODIFY'],
              dynamodb: {
                NewImage: {
                  type: {
                    S: [
                      {
                        prefix: DbRecordType.CUSTOMER,
                      },
                    ],
                  },
                },
              },
            },
          ],
        },
      },
    ],
    policy: {
      useVpc: false,
      useXray: false,
      managed: [
        { Ref: 'lambdaVpcPolicy' }, 
        { Ref: 'xrayPolicy' }, 
        { Ref: 'entityTableQueryRolePolicy' }, 
        { Ref: 'entityTableWriteItemRolePolicy' },
        { Ref: 'crossAccountInvocationPolicy' },
      ],
      inline: {
        merchantTableDbStreamPolicy: [
          {
            actions: [
              Action.dynamodb.Query,
              Action.dynamodb.ListStreams,
              Action.dynamodb.DescribeStream,
              Action.dynamodb.DescribeTable,
              Action.dynamodb.GetItem,
              Action.dynamodb.GetRecords,
              Action.dynamodb.GetShardIterator,
            ],
            resources: ['${self:custom.tableStreamArn}'],
          },
        ],
      },
    },
  },
};
