import type { Config } from 'jest';

const config: Config = {
  roots: ['<rootDir>/tests'],
  testTimeout: 30000,
  collectCoverage: false,
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
  reporters: [
    'summary',
    ['github-actions', { silent: false }],
    [
      'jest-html-reporter',
      {
        pageTitle: 'Test Report',
        outputPath: 'dist/test-report.html',
      },
    ],
  ],
};

export default config;
