import { getAuthenticatedClient, retry, sleep } from '@npco/bff-systemtest-utils';
import { SdkApiGraphql } from '@npco/bff-systemtest-utils/dist/graphql/sdkApiGraphql';
import { ConnectionType } from '@npco/component-dto-core';

import { randomUUID as v4 } from 'crypto';
import gql from 'graphql-tag';

import { TestHelper } from './testHelper';
import { testLog } from './utils/loggerTest';
import { SdkApis, type TestSequenceStore } from './utils/testSequenceStore';

describe('IMPOS API Component Integration Test Suite', () => {
  let deviceUuid: string;
  let terminalDeviceUuid: string;
  let terminalDeviceUuid2: string;
  const pairingLocationUuid = v4();
  let entityUuid: string;
  let siteUuid: string;
  let siteName: string;
  let sdkTestHelper: TestHelper;
  let testSequenceStore: TestSequenceStore;
  let api: SdkApiGraphql;

  beforeAll(async () => {
    // entityUuid = 'cfb66790-d95c-4fc3-8411-6436a05f5aca';
    entityUuid = '2c2494a1-0919-4de1-83b6-86cbd3228e7d';

    sdkTestHelper = new TestHelper(ConnectionType.IMPOS, entityUuid);

    // before sets, auth0, and appsync
    await sdkTestHelper.before();
    await sleep(2000);

    api = new SdkApiGraphql(await sdkTestHelper.getOpenIdClient());

    testSequenceStore = sdkTestHelper.testSequenceStore;

    console.info(
      'IMPOS Base test data has been created',
      JSON.stringify(
        {
          entityUuid,
          customerUuid: sdkTestHelper.getCustomerUuid(),
          accessToken: sdkTestHelper.testData.accessToken,
          terminalDeviceUuid,
          terminalDeviceUuid2,
        },
        null,
        2,
      ),
    );
  });

  afterAll(async () => {
    console.info('IMPOS Test Suite Completed. Here are the result...');
    console.info(JSON.stringify(testSequenceStore.getTestResults(), null, 2));

    await sdkTestHelper.cleanupEntity(api, deviceUuid);
  });

  // skipping this test will fail to set the deviceUuid that other tests depends on
  testLog(`${SdkApis.getDeviceUuid} should be able to call get device uuid`, async () => {
    let getDeviceUuidResponse;
    try {
      getDeviceUuidResponse = (await sdkTestHelper.getImposDeviceUuid('POS_INTERFACE', v4())) as any;
      testSequenceStore.setTestResult(SdkApis.getDeviceUuid, getDeviceUuidResponse.data.getDeviceUuid);
    } catch (error) {
      console.error(`${SdkApis.getDeviceUuid} failed`, error);
      throw error;
    }

    // sets the deviceUuid for the subsequent dependent test cases
    deviceUuid = getDeviceUuidResponse?.data?.getDeviceUuid?.id;
    expect(deviceUuid).toBeDefined();
  });

  testLog(`${SdkApis.getCustomerEntityMapping} should be able to get customer entity mapping`, async () => {
    let customerEntityMapping;
    try {
      customerEntityMapping = await api.getCustomerEntityMapping();
      testSequenceStore.setTestResult(
        SdkApis.getCustomerEntityMapping,
        customerEntityMapping.data.getCustomerEntityMapping,
      );
    } catch (error) {
      console.error(`${SdkApis.getCustomerEntityMapping} failed`, error);
      throw error;
    }

    const customerEntityRelations = customerEntityMapping.data.getCustomerEntityMapping.entityRelations;

    // validate if the required fields are projected
    expect(customerEntityMapping.data.getCustomerEntityMapping.defaultEntityUuid).toBeDefined();
    expect(customerEntityRelations.length).toBeGreaterThan(0);
    expect(customerEntityRelations[0].entityUuid).toBeDefined();
  });

  testLog(`${SdkApis.getSites} should throw error on queries if not assigned to entity`, async () => {
    await expect(api.getSites(deviceUuid, 2)).rejects.toThrow('GraphQL error: Device not assigned to entity');
  });

  // uses the deviceUuid from getDeviceUuid test
  testLog(`${SdkApis.assignDeviceToEntity} should be able to assign device to entity`, async () => {
    let assignDeviceToEntityResponse;
    try {
      assignDeviceToEntityResponse = await api.assignDeviceToEntity(deviceUuid, entityUuid);
      testSequenceStore.setTestResult(
        SdkApis.assignDeviceToEntity,
        assignDeviceToEntityResponse.data.assignDeviceToEntity,
      );
    } catch (error: any) {
      console.error(`${SdkApis.assignDeviceToEntity} failed`, error);
      throw error;
    }
    expect(assignDeviceToEntityResponse.data.assignDeviceToEntity).toBe(true);
  });

  testLog(`[CLEANUP] should remove all devices assigned to entity aside from current device`, async () => {
    await sdkTestHelper.cleanupEntity(api, deviceUuid, true);
  });

  testLog(`${SdkApis.updateDeviceInformation} should be able to update device information`, async () => {
    const input = {
      id: deviceUuid,
      timestamp: new Date().toISOString(),
      timestampUtc: new Date().toISOString(),
      os: 'ubuntu',
      kernel: 'kernel1',
      uiSoftwareVersion: '1',
      model: 'POS_INTERFACE',
    };

    await sdkTestHelper.updateDeviceInformation(input);

    await sleep(3000);
    await retry(async () => {
      const result = await (
        await getAuthenticatedClient()
      ).query({
        query: gql`
          query GetDeviceInformation($id: ID!) {
            getDeviceInformation(deviceUuid: $id) {
              id
              hardware {
                model
                os
                kernel
                uiSoftwareVersion
              }
            }
          }
        `,
        variables: {
          id: deviceUuid,
        },
      });

      expect(result.data.getDeviceInformation.hardware).toEqual({
        kernel: 'kernel1',
        model: 'POS_INTERFACE',
        os: 'ubuntu',
        uiSoftwareVersion: '1',
      });

      return result.data.getDeviceInformation;
    });
  });

  testLog(
    `${SdkApis.getDeviceSettings} should be able to get updated device name on the second call of assignDeviceToEntity`,
    async () => {
      // make a call to assignDevice with a different name
      await api.assignDeviceToEntity(deviceUuid, entityUuid, `updated_name_impos_${v4().split('-')[0]}`);

      await sleep(3000); // allow time for materialisation

      // verify the name change from getDeviceSettings
      await retry(async () => {
        const { data: getDeviceSettingsResponse } = await sdkTestHelper.getDeviceSettingsWithPosConfig(deviceUuid);
        expect(getDeviceSettingsResponse.getDeviceSettings).toMatchObject({
          id: deviceUuid,
          entityUuid,
          model: 'POS_INTERFACE',
          name: expect.stringContaining('updated_name_impos_'),
        });
      });
    },
  );

  testLog(`${SdkApis.getSites} should be able to get all the sites to which the device is assigned`, async () => {
    // create multiple test sites
    const site1 = await sdkTestHelper.createSiteGql();
    siteUuid = site1.id;
    siteName = site1.name;
    // Allow site 1 to materialise
    await sleep(1000);

    [terminalDeviceUuid, terminalDeviceUuid2] = await Promise.all([
      sdkTestHelper.createTerminalDeviceGql({
        siteUuid,
      }),
      sdkTestHelper.createTerminalDeviceGql({
        siteUuid,
      }),
    ]).catch((err) => {
      console.log(err);
      throw err;
    });

    // allow time for materialisation of above updates
    await sleep(3000);

    await retry(async () => {
      const { data: getSitesResponse } = await sdkTestHelper.getSitesWithDevices(deviceUuid, 2, undefined, {
        siteName: { eq: siteName },
      });
      testSequenceStore.setTestResult(SdkApis.getSites, getSitesResponse.getSites);

      expect(getSitesResponse.getSites.sites.length).toBe(1);
      expect(getSitesResponse.getSites.nextToken).toBeNull();

      // testing the get devices by site resolver
      expect(getSitesResponse.getSites.sites[0]).toMatchObject({
        id: siteUuid,
        devices: expect.arrayContaining([
          {
            id: terminalDeviceUuid,
          },
          {
            id: terminalDeviceUuid2,
          },
        ]),
      });
    }, 5);
  });

  // skipping this will break getImposConfiguration test
  testLog(`${SdkApis.configureImpos} should be able to configure current device using deviceUuid`, async () => {
    let configureImposResponse;
    const imposConfiguration = {
      clientApiKey: v4(),
      hostIp: 'https://***********',
      venues: [
        {
          id: deviceUuid,
          name: 'Venus Constructions',
          locations: [{ id: pairingLocationUuid, name: 'Einstein Apothecary', number: v4() }],
        },
        {
          id: '', // Not assigned to a device
          name: 'Zeller Store',
          locations: [{ id: v4(), name: 'Zeller Inventory', number: v4() }],
        },
      ],
    } as any;
    try {
      configureImposResponse = (await api.configureImpos(deviceUuid, imposConfiguration)).data;
      testSequenceStore.setTestResult(SdkApis.configureImpos, configureImposResponse.configureImpos);
    } catch (error: any) {
      console.error(`${SdkApis.configureImpos} failed`, error);
      throw error;
    }
    expect(configureImposResponse.configureImpos).toStrictEqual(imposConfiguration);
  });

  testLog(
    `${SdkApis.getImposConfiguration} should be able to get impos configuration of the current device using deviceUuid`,
    async () => {
      await retry(async () => {
        const { data: getImposConfigResponse } = await api.getImposConfiguration(deviceUuid);
        console.log(getImposConfigResponse);
        testSequenceStore.setTestResult(SdkApis.getImposConfiguration, getImposConfigResponse.getImposConfiguration);

        // checks for the already configured values from the previous test 👆
        expect(getImposConfigResponse.getImposConfiguration).toMatchObject({
          hostIp: expect.any(String),
          clientApiKey: expect.any(String),
          venues: expect.arrayContaining([
            expect.objectContaining({
              id: deviceUuid,
              name: 'Venus Constructions',
            }),
          ]),
        });
      });
    },
  );

  testLog(
    `${SdkApis.getPairedDevices} should be able to get all paired devices of the current device using deviceUuid`,
    async () => {
      // pair impos to venue
      await sdkTestHelper.pairVenueIdToSite(deviceUuid, siteUuid, pairingLocationUuid).catch((err) => {
        console.log(err);
        throw err;
      });

      // allow time for above update to materialise
      await sleep(3000);

      await retry(async () => {
        const { data: getPairedDevicesResponse } = await api.getPairedDevices(deviceUuid, {}, 100);
        testSequenceStore.setTestResult(SdkApis.getPairedDevices, getPairedDevicesResponse.getPairedDevices);
        expect(getPairedDevicesResponse.getPairedDevices.items.length).toBeGreaterThanOrEqual(2);
        expect(getPairedDevicesResponse.getPairedDevices.items).toMatchObject(
          expect.arrayContaining([
            expect.objectContaining({
              deviceUuid: terminalDeviceUuid,
              provider: ConnectionType.IMPOS,
            }),
            expect.objectContaining({
              deviceUuid: terminalDeviceUuid2,
              provider: ConnectionType.IMPOS,
            }),
          ]),
        );
      });
    },
  );

  /* 
    There is a bug currently which fetches site pairings: posinterface.pair.site. that is mapped to not return id
    due to which the query is not returning the expected response
    remove skip once the issue is resolved
   */
  test.skip(`${SdkApis.getPosPairConfiguration} should be able to get pos pair configuration of the current entity`, async () => {
    await sleep(5000);
    let getPosPairConfigResponse;
    try {
      getPosPairConfigResponse = (await api.getPosPairConfiguration(deviceUuid, ConnectionType.IMPOS)).data;
      testSequenceStore.setTestResult(
        SdkApis.getPosPairConfiguration,
        getPosPairConfigResponse.getPosPairConfiguration,
      );
      // todo: add expect statement
    } catch (error: any) {
      console.error(`${SdkApis.getPosPairConfiguration} failed`, error);
      throw error;
    }
  });

  testLog(
    `${SdkApis.getDeviceSettings} should be able to get current device settings with pos config using deviceUuid `,
    async () => {
      await retry(async () => {
        const { data: getDeviceSettingsResponse } = await sdkTestHelper.getDeviceSettingsWithPosConfig(deviceUuid);
        testSequenceStore.setTestResult(SdkApis.getDeviceSettings, getDeviceSettingsResponse.getDeviceSettings);
        // checks for the already configured values from the previous test 👆
        expect(getDeviceSettingsResponse.getDeviceSettings).toMatchObject({
          id: deviceUuid,
          entityUuid,
          model: 'POS_INTERFACE',
          posPairConfiguration: {
            id: expect.anything(),
            devices: expect.arrayContaining([
              expect.objectContaining({
                id: terminalDeviceUuid,
              }),
              expect.objectContaining({
                id: terminalDeviceUuid2,
              }),
            ]),
          },
        });
      });
    },
  );

  // uses the deviceUuid from getDeviceUuid test
  testLog(`${SdkApis.removeDeviceFromEntity} should be able to remove existing device from entity`, async () => {
    let removeDeviceFromEntityResponse;
    try {
      removeDeviceFromEntityResponse = (await api.removeDeviceFromEntity(deviceUuid, entityUuid)).data;
      testSequenceStore.setTestResult(
        SdkApis.removeDeviceFromEntity,
        removeDeviceFromEntityResponse.removeDeviceFromEntity,
      );
    } catch (error: any) {
      console.error(`${SdkApis.removeDeviceFromEntity} failed`, error);
      throw error;
    }
    expect(removeDeviceFromEntityResponse.removeDeviceFromEntity).toBe(true);
  });

  testLog(`[CLEANUP CHECK] should throw error on queries if device is removed from entity`, async () => {
    await retry(async () => {
      await expect(sdkTestHelper.getDeviceSettingsWithPosConfig(deviceUuid)).rejects.toThrow(
        `GraphQL error: Device not found ${deviceUuid}`,
      );
    });
  });
});
