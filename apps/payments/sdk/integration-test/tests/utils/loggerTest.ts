export const testLog = (name: string, fn: () => Promise<any>, timeout?: number) => {
  test(
    name,
    async () => {
      console.log(`Running test ${name}`);
      let result;
      try {
        result = await fn();
      } catch (error) {
        console.error(`Error in test ${name}:`, error);
        throw error; // Re-throw the error to fail the test
      }
      console.log(`Finished test ${name}`);
      return result;
    },
    timeout,
  );
};
