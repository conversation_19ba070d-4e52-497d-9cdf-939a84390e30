import { retry, sleep } from '@npco/bff-systemtest-utils';
import { SdkApiGraphql } from '@npco/bff-systemtest-utils/dist/graphql/sdkApiGraphql';
import { ConnectionType } from '@npco/component-dto-core';

import { randomUUID as v4 } from 'crypto';

import { TestHelper } from './testHelper';
import { testLog } from './utils/loggerTest';
import { SdkApis, type TestSequenceStore } from './utils/testSequenceStore';

describe('SDK API Component Integration Test Suite', () => {
  let deviceUuid: string;
  let entityUuid: string;
  let siteUuid: string;
  let siteName: string;
  let sdkTestHelper: TestHelper;
  let testSequenceStore: TestSequenceStore;
  let api: SdkApiGraphql;
  let terminalDeviceUuid: string;
  let terminalDeviceUuid2: string;

  beforeAll(async () => {
    entityUuid = 'cfb66790-d95c-4fc3-8411-6436a05f5aca';
    // entityUuid = '2c2494a1-0919-4de1-83b6-86cbd3228e7d';
    sdkTestHelper = new TestHelper(ConnectionType.SDK, entityUuid);

    await sdkTestHelper.before();
    await sleep(2000);

    api = new SdkApiGraphql(await sdkTestHelper.getOpenIdClient());

    testSequenceStore = sdkTestHelper.testSequenceStore;

    console.info(
      'Base test data has been created',
      JSON.stringify(
        {
          entityUuid,
          customerUuid: sdkTestHelper.getCustomerUuid(),
          accessToken: sdkTestHelper.testData.accessToken,
          terminalDeviceUuid,
          terminalDeviceUuid2,
        },
        null,
        2,
      ),
    );
  });

  afterAll(async () => {
    // remove created entities to avoid spamming table in future
    console.info('SDK Test Suite Completed. Here are the result...');
    console.info(JSON.stringify(testSequenceStore.getTestResults(), null, 2));
    await sdkTestHelper.cleanupEntity(api, deviceUuid);
  });

  // skipping this test will fail to set the deviceUuid that other tests depends on
  testLog(`${SdkApis.getDeviceUuid} should be able to call get device uuid`, async () => {
    let getDeviceUuidResponse;
    try {
      getDeviceUuidResponse = await api.getDeviceUuid('WINDOWS_SDK', v4());
      testSequenceStore.setTestResult(SdkApis.getDeviceUuid, getDeviceUuidResponse.data.getDeviceUuid);
    } catch (error) {
      console.error(`${SdkApis.getDeviceUuid} failed`, error);
      throw error;
    }

    // sets the deviceUuid for the subsequent dependent test cases
    deviceUuid = getDeviceUuidResponse?.data?.getDeviceUuid?.id;
    expect(deviceUuid).toBeDefined();
  });

  testLog(`${SdkApis.getCustomerEntityMapping} should be able to get customer entity mapping`, async () => {
    let customerEntityMapping;
    try {
      customerEntityMapping = await api.getCustomerEntityMapping();
      testSequenceStore.setTestResult(
        SdkApis.getCustomerEntityMapping,
        customerEntityMapping.data.getCustomerEntityMapping,
      );
    } catch (error) {
      console.error(`${SdkApis.getCustomerEntityMapping} failed`, error);
      throw error;
    }

    const customerEntityRelations = customerEntityMapping.data.getCustomerEntityMapping.entityRelations;

    // validate if the required fields are projected
    expect(customerEntityMapping.data.getCustomerEntityMapping.defaultEntityUuid).toBeDefined();
    expect(customerEntityRelations.length).toBeGreaterThan(0);
    expect(customerEntityRelations[0].entityUuid).toBeDefined();
  });

  testLog(`${SdkApis.getSites} should throw error on queries if not assigned to entity`, async () => {
    await expect(api.getSites(deviceUuid, 2)).rejects.toThrow('GraphQL error: Device not assigned to entity');
  });

  // uses the deviceUuid from getDeviceUuid test
  testLog(`${SdkApis.assignDeviceToEntity} should be able to assign device to entity`, async () => {
    let assignDeviceToEntityResponse;
    try {
      assignDeviceToEntityResponse = await api.assignDeviceToEntity(deviceUuid, entityUuid);
      testSequenceStore.setTestResult(
        SdkApis.assignDeviceToEntity,
        assignDeviceToEntityResponse.data.assignDeviceToEntity,
      );
    } catch (error: any) {
      console.error(`${SdkApis.assignDeviceToEntity} failed`, error);
      throw error;
    }
    expect(assignDeviceToEntityResponse.data.assignDeviceToEntity).toBe(true);

    await sleep(3000); // allow time for materialisation

    // verify assignment in materialised view using getDeviceSettings
    await retry(async () => {
      const { data: getDeviceSettingsResponse } = await sdkTestHelper.getDeviceSettingsWithPosConfig(deviceUuid);
      expect(getDeviceSettingsResponse.getDeviceSettings).toMatchObject({
        id: deviceUuid,
        entityUuid,
        model: 'WINDOWS_SDK',
      });
    });
  });

  testLog(`[CLEANUP] should remove all devices assigned to entity aside from current device`, async () => {
    await sdkTestHelper.cleanupEntity(api, deviceUuid, true);
  });

  testLog(
    `${SdkApis.getDeviceSettings} should be able to get updated device name on the second call of assignDeviceToEntity`,
    async () => {
      // make a call to assignDevice with a different name
      await api.assignDeviceToEntity(deviceUuid, entityUuid, `updated_name_sdk_${v4().split('-')[0]}`);

      await sleep(3000); // allow time for materialisation

      // verify the name change from materialised view using getDeviceSettings
      await retry(async () => {
        const { data: getDeviceSettingsResponse } = await sdkTestHelper.getDeviceSettingsWithPosConfig(deviceUuid);
        expect(getDeviceSettingsResponse.getDeviceSettings).toMatchObject({
          id: deviceUuid,
          entityUuid,
          model: 'WINDOWS_SDK',
          name: expect.stringContaining('updated_name_sdk_'),
        });
      });
    },
  );

  testLog(`${SdkApis.getSites} should be able to get all the sites to which the device is assigned`, async () => {
    // create multiple test sites
    const site1 = await sdkTestHelper.createSiteGql();
    siteUuid = site1.id;
    siteName = site1.name;

    terminalDeviceUuid = await sdkTestHelper
      .createTerminalDeviceGql({
        siteUuid,
      })
      .catch((err) => {
        console.log(err);
        throw err;
      });

    await retry(async () => {
      const getSitesResponse = (
        await sdkTestHelper.getSitesWithDevices(deviceUuid, 2, undefined, { siteName: { eq: siteName } })
      ).data;

      testSequenceStore.setTestResult(SdkApis.getSites, getSitesResponse.getSites);

      expect(getSitesResponse.getSites.sites.length).toBe(1);
      expect(getSitesResponse.getSites.nextToken).toBeNull();

      // testing the get devices by site resolver
      expect(getSitesResponse.getSites.sites[0]).toMatchObject({
        id: siteUuid,
        devices: expect.arrayContaining([
          {
            id: terminalDeviceUuid,
          },
        ]),
      });
    });
  });

  testLog(`${SdkApis.pairDevices} should not be able to pair a device to a non existing device`, async () => {
    const nonExistingDeviceUuid = v4();
    await expect(
      api.pairDevices(deviceUuid, [{ pairedDeviceUuid: nonExistingDeviceUuid, override: true }]),
    ).rejects.toThrow(Error(`GraphQL error: [404] Cant find device ${nonExistingDeviceUuid}.`));
  });

  testLog(`${SdkApis.pairDevices} should not be able to pair a non existing device to an existing device`, async () => {
    const nonExistingDeviceUuid = v4();

    await expect(
      api.pairDevices(nonExistingDeviceUuid, [{ pairedDeviceUuid: deviceUuid, override: true }]),
    ).rejects.toThrow(Error(`GraphQL error: [404] Cant find device ${nonExistingDeviceUuid}.`));
  });

  // skipping this will affect getPairedDevices and unpairDevices test cases
  testLog(`${SdkApis.pairDevices} should be able to pair a device to the current device using deviceUuid`, async () => {
    let pairDevicesResponse;

    terminalDeviceUuid2 = await sdkTestHelper
      .createTerminalDeviceGql({
        siteUuid,
      })
      .catch((err) => {
        console.log(err);
        throw err;
      });

    await sleep(3000); // allow time for materialisation

    try {
      console.log(`Pairing ${terminalDeviceUuid2} to ${deviceUuid}`);
      pairDevicesResponse = (
        await api.pairDevices(deviceUuid, [{ pairedDeviceUuid: terminalDeviceUuid2, override: true }])
      ).data;
      console.log(pairDevicesResponse);
      testSequenceStore.setTestResult(SdkApis.pairDevices, pairDevicesResponse.pairDevices);
    } catch (error: any) {
      console.error(`${SdkApis.pairDevices} failed`, error);
      throw error;
    }
    expect(pairDevicesResponse.pairDevices).toBe(true);

    await sleep(3000); // allow time for materialisation

    // verify pairing in materialised view using getPairedDevices
    await retry(async () => {
      // query records of type posinterface.pair.device.
      const { data: getPairedDevicesResponse } = await api.getPairedDevices(
        deviceUuid,
        { provider: ConnectionType.SDK, deviceUuid: terminalDeviceUuid2 },
        10,
      );
      testSequenceStore.setTestResult(SdkApis.getPairedDevices, getPairedDevicesResponse.getPairedDevices);

      // should only return SDK Provider Devices since the provider filter is SDK
      expect(getPairedDevicesResponse.getPairedDevices.items.length).toBe(1);
      expect(getPairedDevicesResponse.getPairedDevices.nextToken).toBeNull();
      expect(getPairedDevicesResponse.getPairedDevices.items).toStrictEqual([
        {
          deviceUuid: terminalDeviceUuid2,
          provider: ConnectionType.SDK,
          locationNames: null,
          venueName: null,
        },
      ]);
    });
  });

  testLog(`${SdkApis.getDeviceSettings} should be able to get current device settings using deviceUuid`, async () => {
    await retry(async () => {
      const { data: getDeviceSettingsResponse } = await sdkTestHelper.getDeviceSettingsWithPosConfig(deviceUuid);
      testSequenceStore.setTestResult(SdkApis.getDeviceSettings, getDeviceSettingsResponse.getDeviceSettings);
      // checks for the already configured values from the previous test 👆
      expect(getDeviceSettingsResponse.getDeviceSettings).toMatchObject({
        id: deviceUuid,
        entityUuid,
        model: 'WINDOWS_SDK',
        posPairConfiguration: {
          id: expect.anything(),
          devices: expect.arrayContaining([
            expect.objectContaining({
              id: terminalDeviceUuid2,
            }),
          ]),
        },
      });
    });
  });

  testLog(`${SdkApis.pairDevices} should be paired to previous device if new pairing fails`, async () => {
    let pairedDevice: { deviceUuid: string };
    await retry(async () => {
      const { data: getPairedDevicesResponse } = await api.getPairedDevices(
        deviceUuid,
        { provider: ConnectionType.SDK },
        10,
      );

      // should only return SDK Provider Devices since the provider filter is SDK
      expect(getPairedDevicesResponse.getPairedDevices.items.length).toBeGreaterThanOrEqual(1);
      pairedDevice = getPairedDevicesResponse.getPairedDevices.items[0];
    });

    const nonExistingDeviceUuid = v4();
    await expect(
      api.pairDevices(nonExistingDeviceUuid, [{ pairedDeviceUuid: deviceUuid, override: true }]),
    ).rejects.toThrow(Error(`GraphQL error: [404] Cant find device ${nonExistingDeviceUuid}.`));

    await retry(async () => {
      const { data: getPairedDevicesResponse } = await api.getPairedDevices(
        deviceUuid,
        { provider: ConnectionType.SDK },
        10,
      );

      // should only return SDK Provider Devices since the provider filter is SDK
      expect(getPairedDevicesResponse.getPairedDevices.items.length).toBeGreaterThanOrEqual(1);
      expect(getPairedDevicesResponse.getPairedDevices.items).toMatchObject(expect.arrayContaining([pairedDevice]));
    });
  });

  testLog(
    `${SdkApis.unpairDevices} should be able to unpair the previously paired device from the current device using deviceUuid`,
    async () => {
      let unpairDevicesResponse;
      try {
        unpairDevicesResponse = (await api.unpairDevices(deviceUuid, [{ deviceUuid: terminalDeviceUuid2 }])).data;
        testSequenceStore.setTestResult(SdkApis.unpairDevices, unpairDevicesResponse.unpairDevices);
      } catch (error: any) {
        console.error(`${SdkApis.unpairDevices} failed`, error);
        throw error;
      }
      expect(unpairDevicesResponse.unpairDevices).toBe(true);

      await sleep(3000); // allow time for materialisation

      // verify unpairing in materialised view using getPairedDevices
      await retry(async () => {
        // query records of type posinterface.pair.device.
        const { data: getPairedDevicesResponse } = await api.getPairedDevices(
          deviceUuid,
          { provider: ConnectionType.SDK, deviceUuid: terminalDeviceUuid2 },
          10,
        );
        testSequenceStore.setTestResult(SdkApis.getPairedDevices, getPairedDevicesResponse.getPairedDevices);

        // should only return SDK Provider Devices since the provider filter is SDK
        expect(getPairedDevicesResponse.getPairedDevices.items.length).toBe(0);
        expect(getPairedDevicesResponse.getPairedDevices.nextToken).toBeNull();
      });
    },
  );

  testLog(
    `${SdkApis.getDeviceSettings} should have empty list of devices in getDeviceSettings posPairConfiguration devices after unpair`,
    async () => {
      await retry(async () => {
        const { data: getDeviceSettingsResponse } = await sdkTestHelper.getDeviceSettingsWithPosConfig(deviceUuid);
        console.log('device settings after unpair', JSON.stringify(getDeviceSettingsResponse, null, 2));
        expect(getDeviceSettingsResponse.getDeviceSettings).toMatchObject({
          id: deviceUuid,
          entityUuid,
          model: 'WINDOWS_SDK',
          posPairConfiguration: null, // no pairing configurations should be present
        });
      });
    },
  );

  // uses the deviceUuid from getDeviceUuid test
  testLog(`${SdkApis.removeDeviceFromEntity} should be able to remove existing device from entity`, async () => {
    let removeDeviceFromEntityResponse;
    try {
      removeDeviceFromEntityResponse = (await api.removeDeviceFromEntity(deviceUuid, entityUuid)).data;
      testSequenceStore.setTestResult(
        SdkApis.removeDeviceFromEntity,
        removeDeviceFromEntityResponse.removeDeviceFromEntity,
      );
    } catch (error: any) {
      console.error(`${SdkApis.removeDeviceFromEntity} failed`, error);
      throw error;
    }
    expect(removeDeviceFromEntityResponse.removeDeviceFromEntity).toBe(true);
  });

  testLog(`[CLEANUP CHECK] should throw error on queries if device is removed from entity`, async () => {
    await retry(async () => {
      await expect(sdkTestHelper.getDeviceSettingsWithPosConfig(deviceUuid)).rejects.toThrow(
        `GraphQL error: Device not found ${deviceUuid}`,
      );
    });
  });
});
