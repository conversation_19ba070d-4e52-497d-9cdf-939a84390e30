import { BaseTestHelper, ComponentClients, sleep } from '@npco/bff-systemtest-utils';
import type { SdkApiGraphql } from '@npco/bff-systemtest-utils/dist/graphql/sdkApiGraphql';
import { ProjectionMode } from '@npco/bff-systemtest-utils/dist/helper/baseTestHelper';
import { ConnectionType, DbRecordType } from '@npco/component-dto-core';
import type { DeviceCreatedEventDto, DeviceInfoUpdateDto } from '@npco/component-dto-device';
import { SiteType } from '@npco/component-dto-site';

import type AWSAppSyncClient from 'aws-appsync';
import { randomUUID as v4 } from 'crypto';
import gql from 'graphql-tag';
import { CrmsApiClient } from 'integration-tests/src/utils/gql/crmsGqlApiClient';
import { DbsApiClient } from 'integration-tests/src/utils/gql/dbsGqlApiClient';

import { TestSequenceStore } from './utils/testSequenceStore';

export class TestHelper extends BaseTestHelper {
  /**
   * customerUuid which exists in auth0 for sdk-api
   */
  private readonly customerUuid: string;

  /**
   * entityUuid which exists in auth0 for sdk-api
   */
  private readonly entityUuid: string;

  testSequenceStore: TestSequenceStore;

  apiClient?: AWSAppSyncClient<any>;

  crmsApiClient: CrmsApiClient;

  dbsApiClient: DbsApiClient;

  constructor(private readonly connection: ConnectionType, entityUuid: string) {
    super(ComponentClients.SoftwareDevelopmentKit, undefined, ProjectionMode.HighPriorityQueue);
    this.customerUuid = '9e528a76-3ad5-4568-a93f-51f683d09f4f';
    this.entityUuid = entityUuid;
    this.testSequenceStore = new TestSequenceStore(this.connection);
    this.crmsApiClient = new CrmsApiClient();
    this.dbsApiClient = new DbsApiClient();
  }

  before = async () => {
    await this.auth0Helper.setClient();
    await this.setAccessTokenInTestData();
    await this.setAppsyncTestData();
    this.apiClient = await this.getApiKeyClient();
    const testData = { ...this.getTestData(), customerUuid: this.customerUuid, entityUuid: this.entityUuid };

    this.setTestData(testData);
    await Promise.all([this.crmsApiClient.beforeAll(), this.dbsApiClient.beforeAll()]);
    this.crmsApiClient.setTestData({ ...this.crmsApiClient.getTestData(), ...testData });
    this.dbsApiClient.setTestData({ ...this.dbsApiClient.getTestData(), ...testData });

    console.log('SDK Integration Test Setup Complete');
  };

  cleanupEntity = async (sdkApiClient: SdkApiGraphql, deviceUuid: string, keepCurrentDevice?: boolean) => {
    // eslint-disable-next-line no-constant-condition
    while (true) {
      const response = await sdkApiClient.getPairedDevices(deviceUuid, { provider: ConnectionType.SDK }, 100);
      const items = response.data.getPairedDevices.items;
      if (items.length <= 1) break;
      await Promise.all(
        items
          .filter((item: any) => item.deviceUuid !== deviceUuid)
          .map((item: any) => {
            // console.log(`Removing device ${item.deviceUuid} from ${this.entityUuid}`);
            return sdkApiClient.removeDeviceFromEntity(item.deviceUuid, this.entityUuid);
          }),
      );
    }
    if (!keepCurrentDevice) await sdkApiClient.removeDeviceFromEntity(deviceUuid, this.entityUuid);
  };

  getCustomerUuid = () => this.customerUuid;

  createSiteGql = async () => {
    const siteInput = {
      name: `test-${v4()}`,
      address: {
        state: 'state',
        street: 'street1',
        suburb: '1234',
        postcode: 'Melbourne',
      },
      pin: '1234',
      type: SiteType.FIXED,
    };

    const site = await this.crmsApiClient.createSite({
      siteInput,
      entityUuid: this.getEntityUuid(),
    });

    console.log(`creating site with entityUuid: ${this.getEntityUuid()}, siteUuid: ${site.id}`);

    return site;
  };

  createTerminalDeviceGql = async (updates = {}) => {
    const serial = v4().replaceAll('-', '').substring(20);
    const dto: DeviceCreatedEventDto = {
      entityUuid: this.getEntityUuid(),
      deviceUuid: v4(),
      name: `integration-test-terminal-${serial}`,
      model: 'T2',
      serial: `T2${serial}`,
      ...updates,
    };

    const { deviceUuid } = await this.dbsApiClient.deviceSignIn(dto.model ?? 'T2', dto.serial ?? `T2${serial}`);

    // allow time for materialisation
    await sleep(3000);

    console.log(`creating device with deviceUuid: ${deviceUuid}`);

    if (dto.siteUuid) {
      console.log(`assigning device ${deviceUuid} to site with siteUuid: ${dto.siteUuid}`);
      await this.crmsApiClient.assignDeviceToSite(deviceUuid, dto.siteUuid);
    }
    return deviceUuid;
  };

  pairSiteWithEntity = async (siteUuid: string, venueId: string, venueName: string) => {
    const sitePairedDto = {
      pairingUuid: v4(),
      entityUuid: this.getEntityUuid(),
      siteUuid,
      timeStamp: new Date().toISOString(),
      posProvider: ConnectionType.IMPOS,
      venueId,
      venueName,
      type: `${DbRecordType.POSINTERFACE_PAIR_SITE}${siteUuid}`,
      locations: [v4(), v4()],
    };

    await this.sendHighPriorityQueueEvent('sdk.PosInterface.SitePaired', sitePairedDto, this.getEntityUuid());
  };

  pairVenueIdToSite = async (
    deviceUuid: string,
    siteUuid: string,
    locationId: string,
    provider = ConnectionType.IMPOS,
  ) => {
    console.log(`pairing venue ${deviceUuid} to site with siteUuid: ${siteUuid}`);
    await this.crmsApiClient.pairPos(
      [
        {
          siteUuid,
          venueId: deviceUuid,
          locationId,
        },
      ],
      this.entityUuid,
      provider,
    );
  };

  // Helper GQL APIS
  getSitesWithDevices = async (deviceUuid: string, limit = 10, nextToken?: any, filter?: any) => {
    return (await this.getOpenIdClient(false)).query({
      query: gql`
        query getSites($deviceUuid: ID!, $limit: Int!, $nextToken: SiteNextTokenInput, $filter: SiteFilterInput) {
          getSites(deviceUuid: $deviceUuid, limit: $limit, nextToken: $nextToken, filter: $filter) {
            sites {
              id
              name
              devices {
                id
              }
            }
            nextToken {
              siteNameSortKey
              type
            }
          }
        }
      `,
      variables: {
        deviceUuid,
        limit,
        nextToken,
        filter,
      },
    });
  };

  getDeviceSettingsWithPosConfig = async (deviceUuid: string) => {
    return (await this.getOpenIdClient(false)).query({
      query: gql`
        query getDeviceSettings($deviceUuid: ID!) {
          getDeviceSettings(deviceUuid: $deviceUuid) {
            id
            entityUuid
            model
            serial
            name
            site {
              siteUuid
              name
              type
              screensaver {
                primaryColour
                primaryLogoUrl
              }
            }
            entity {
              canAcquire
              canRefund
              domicile
              currency
            }
            posPairConfiguration {
              id
              siteUuid
              devices {
                id
              }
            }
          }
        }
      `,
      variables: {
        deviceUuid,
      },
    });
  };

  getImposDeviceUuid = async (model: string, serial: string) => {
    return this.apiClient!.mutate({
      mutation: gql`
        mutation getDeviceUuid($input: DeviceUuidInput!) {
          getDeviceUuid(input: $input) {
            id
          }
        }
      `,
      variables: {
        input: { model, serial },
      },
    });
  };

  updateDeviceInformation = async (input: DeviceInfoUpdateDto) => {
    return this.apiClient!.mutate({
      mutation: gql`
        mutation updateDeviceInformation($input: DeviceStatusInput!) {
          updateDeviceInformation(input: $input)
        }
      `,
      variables: {
        input,
      },
    });
  };
}
