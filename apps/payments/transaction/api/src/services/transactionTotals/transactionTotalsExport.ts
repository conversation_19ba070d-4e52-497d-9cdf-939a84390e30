import { S3Client } from '@npco/component-bff-core/dist/aws/s3Client';
import { debug } from '@npco/component-bff-core/dist/utils/logger';
import { convertDbItemToEntity, getEntityDbItemOrThrow } from '@npco/component-dbs-mp-common/dist/entity';
import { ExcelWriter } from '@npco/component-dbs-mp-common/dist/interface/excelWriter';
import type { CommonService } from '@npco/component-dbs-mp-common/dist/module/commonService';
import { generatePresignedURL } from '@npco/component-dbs-mp-common/dist/utils';

import type { PutObjectCommandInput } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';

import type { TransactionTotalsQueryService } from './transactionTotalsQueryService';
import type {
  BetweenFilter,
  TransactionTotalsTypeV2,
  TransactionTotalsMultiEntityExport,
  TransactionTotalsMultiEntityExportFormat,
  TransactionTotalsV2ReportRow,
} from './types';
import { formatCentsToDollars } from './utils/utils';

type TransactionTotalsMultiEntityExportRow = Pick<
  TransactionTotalsV2ReportRow,
  'countPurchases' | 'tipAmount' | 'surchargeAmount' | 'purchaseAmount' | 'refundAmount' | 'feeAmount' | 'taxAmounts'
>;

export class TransactionTotalsExportService {
  private static readonly TOTALS_HEADER_WIDTH = 12;

  private static readonly PRESIGNED_URL_EXPIRES_IN = 60 * 60; // 1 hour

  private static readonly TOTALS_HEADER_COLUMNS = [
    'Business',
    'Sales',
    'Transactions',
    'Tips',
    'Surcharges',
    'Processed',
    'Refunds',
    'Subtotal',
    'Fees',
    'Total',
    'GST',
  ];

  s3: S3Client;

  constructor(
    protected readonly commonService: CommonService,
    private readonly transactionTotalsQueryService: TransactionTotalsQueryService,
  ) {
    this.s3 = new S3Client(commonService.envService.awsRegion);
  }

  async getTransactionTotalsMultiEntityExport(
    totalsType: TransactionTotalsTypeV2,
    format: TransactionTotalsMultiEntityExportFormat,
    entityUuids: string[],
    timeRange: BetweenFilter,
  ): Promise<TransactionTotalsMultiEntityExport> {
    debug(`format: ${format}`);
    debug(`entityUuids: ${entityUuids}`);
    debug(`timeRange: ${JSON.stringify(timeRange)}`);

    const { entities } = await this.transactionTotalsQueryService.getTransactionTotalsMultiEntityV2(
      totalsType,
      entityUuids,
      timeRange,
      {},
    );

    const rows = await Promise.all(entities.map(async ({ entityUuid, totals }) => this.buildRow(entityUuid, totals)));

    const body = await this.generateCsvExport(rows);

    // upload the file to S3
    const key = `multiEntityAcquiringTransactionReport/${uuidv4()}_${new Date().toISOString()}.${format.toLocaleLowerCase()}`;
    const bucket = this.commonService.envService.transactionDepositExportBucket;

    const params: PutObjectCommandInput = {
      Bucket: bucket,
      Key: key,
      Body: body,
    };
    await this.s3.upload(params);

    const presignedUrl = await generatePresignedURL(
      this.s3,
      bucket,
      key,
      TransactionTotalsExportService.PRESIGNED_URL_EXPIRES_IN,
    );

    const response: TransactionTotalsMultiEntityExport = {
      url: presignedUrl,
    };

    return response;
  }

  private readonly buildRow = async (entityUuid: string, totals: TransactionTotalsV2ReportRow[]) => {
    const entity = convertDbItemToEntity(await getEntityDbItemOrThrow(this.commonService.dynamodbService, entityUuid));

    const summedTotals = totals.reduce(
      (acc: TransactionTotalsMultiEntityExportRow, total: TransactionTotalsV2ReportRow) => {
        const taxNames = new Set([
          ...acc.taxAmounts.map((tax) => tax.name),
          ...total.taxAmounts.map((tax) => tax.name),
        ]);

        const taxAmounts = Array.from(taxNames).map((taxName) => {
          const accTaxAmount = acc.taxAmounts.find((tax) => tax.name === taxName)?.amount ?? '0';
          const totalTaxAmount = total.taxAmounts.find((tax) => tax.name === taxName)?.amount ?? '0';
          return {
            name: taxName,
            amount: (BigInt(accTaxAmount) + BigInt(totalTaxAmount)).toString(),
          };
        });

        return {
          countPurchases: (BigInt(acc.countPurchases) + BigInt(total.countPurchases)).toString(),
          purchaseAmount: (BigInt(acc.purchaseAmount) + BigInt(total.purchaseAmount)).toString(),
          refundAmount: (BigInt(acc.refundAmount) + BigInt(total.refundAmount)).toString(),
          surchargeAmount: (BigInt(acc.surchargeAmount) + BigInt(total.surchargeAmount)).toString(),
          tipAmount: (BigInt(acc.tipAmount) + BigInt(total.tipAmount)).toString(),
          feeAmount: (BigInt(acc.feeAmount) + BigInt(total.feeAmount)).toString(),
          taxAmounts,
        };
      },
      {
        countPurchases: '0',
        tipAmount: '0',
        surchargeAmount: '0',
        purchaseAmount: '0',
        refundAmount: '0',
        feeAmount: '0',
        taxAmounts: [],
      },
    );

    const saleAmount = (
      BigInt(summedTotals.purchaseAmount) -
      BigInt(summedTotals.surchargeAmount) -
      BigInt(summedTotals.tipAmount)
    ).toString();
    const totalAmount = (BigInt(summedTotals.purchaseAmount) - BigInt(summedTotals.refundAmount)).toString();
    const totalAmountMinusFees = (
      BigInt(summedTotals.purchaseAmount) -
      BigInt(summedTotals.refundAmount) -
      BigInt(summedTotals.feeAmount)
    ).toString();

    return {
      Business: entity.tradingName ?? entity.name,
      Sales: formatCentsToDollars(saleAmount),
      Transactions: summedTotals.countPurchases,
      Tips: formatCentsToDollars(summedTotals.tipAmount),
      Surcharges: formatCentsToDollars(summedTotals.surchargeAmount),
      Processed: formatCentsToDollars(summedTotals.purchaseAmount),
      Refunds: formatCentsToDollars(summedTotals.refundAmount),
      Subtotal: formatCentsToDollars(totalAmountMinusFees),
      Fees: formatCentsToDollars(summedTotals.feeAmount),
      Total: formatCentsToDollars(totalAmount),
      GST: formatCentsToDollars(summedTotals.taxAmounts.find((tax) => tax.name === 'GST')?.amount ?? '0'),
    };
  };

  private readonly generateCsvExport = (rows: any[]) => {
    const writer = new ExcelWriter();

    const columns = ExcelWriter.columnsFromColumnNames(
      TransactionTotalsExportService.TOTALS_HEADER_COLUMNS,
      TransactionTotalsExportService.TOTALS_HEADER_WIDTH,
    );
    writer.addSheet(rows, columns);

    return writer.getCsvOutput();
  };
}
