import { S3Client } from '@npco/component-bff-core/dist/aws/s3Client';
import { ForbiddenError, NotFoundError, ServerError } from '@npco/component-bff-core/dist/error';
import { MaskingType, dataMasking } from '@npco/component-bff-core/dist/utils/dataMasking';
import { debug, error, info, upsertReportData, warn } from '@npco/component-bff-core/dist/utils/logger';
import { CardholderService } from '@npco/component-dbs-mp-common/dist/cardholder';
import { queryCustomerSiteUuids, validateCustomerRoleAccess } from '@npco/component-dbs-mp-common/dist/customer';
import { getDeviceSettingsDbItemOrThrow } from '@npco/component-dbs-mp-common/dist/devices/getDeviceSettingsDbItem';
import { AmsApiService } from '@npco/component-dbs-mp-common/dist/interface/amsApiService';
import { CommonService } from '@npco/component-dbs-mp-common/dist/module/commonService';
import { SessionService } from '@npco/component-dbs-mp-common/dist/session';
import { convertDbItemToTransaction } from '@npco/component-dbs-mp-common/dist/transaction/convertDbItemToTransaction';
import type { EntityGsiKeySchema, QueryIndex } from '@npco/component-dbs-mp-common/dist/types';
import type { CnpTransactionRefundRequestedDto } from '@npco/component-dto-cnp/dist/cnpTransactionRefundRequestedDto';
import type { CnpTransactionRequestedDto } from '@npco/component-dto-cnp/dist/cnpTransactionRequestedDto';
import type { ForcedTransactionRequestedDto } from '@npco/component-dto-cnp/dist/forcedTransactionRequestedDto';
import type { BatchContent, Channel, TransactionCancelReason } from '@npco/component-dto-core/dist/types';
import {
  CustomerRole,
  ISO4217,
  Status,
  TransactionResponseCode,
  TransactionType,
} from '@npco/component-dto-core/dist/types';
import { parseDepositId } from '@npco/component-dto-deposit/dist/parseDepositId';
import type { DeviceDbItem } from '@npco/component-dto-device/dist/types';
import type { DepositGpvUpdatedEventDto } from '@npco/component-dto-stlmts/dist/depositGpvUpdatedEventDto';
import type { BatchFeeAdjustedTransactionCommandDto } from '@npco/component-dto-transaction/dist/batchFeeAdjustedTransactionCommandDto';
import type { BatchTransactionDepositedEventDto } from '@npco/component-dto-transaction/dist/batchTransactionDepositedDto';
import { CancelTransactionCommandDto } from '@npco/component-dto-transaction/dist/cancelTransactionCommandDto';
import type { FeeOwedTransactionCommandDto } from '@npco/component-dto-transaction/dist/feeOwedTransactionCommandDto';
import type { FeeRefundedTransactionCommandDto } from '@npco/component-dto-transaction/dist/feeRefundedTransactionCommandDto';
import type { FeeReversedTransactionCommandDto } from '@npco/component-dto-transaction/dist/feeReversedTransactionCommandDto';
import type { InitiateIncompleteTransactionDto } from '@npco/component-dto-transaction/dist/initiateIncompleteTransactionDto';
import type { InitiateTransactionCommandDto } from '@npco/component-dto-transaction/dist/initiateTransactionCommandDto';
import type { MockApprovedIncompleteTransactionDto } from '@npco/component-dto-transaction/dist/mockApprovedIncompleteTransactionDto';
import type { MockDeclinedIncompleteTransactionDto } from '@npco/component-dto-transaction/dist/mockDeclinedIncompleteTransactionDto';
import type { OfflineTransactionApprovedCommandDto } from '@npco/component-dto-transaction/dist/offlineTransactionApprovedCommandDto';
import type { RequestTransactionCommandDto } from '@npco/component-dto-transaction/dist/requestTransactionCommandDto';
import type { ResponseTransactionCommandDto } from '@npco/component-dto-transaction/dist/responseTransactionCommandDto';
import type { TransactionBatchLinkedToDepositEventDto } from '@npco/component-dto-transaction/dist/transactionBatchLinkedToDepositEventDto';
import type { TransactionDepositedDto } from '@npco/component-dto-transaction/dist/transactionDepositedDto';
import type { TransactionImagesUpdatedDto } from '@npco/component-dto-transaction/dist/transactionImagesUpdatedDto';
import type { TransactionNotesUpdatedDto } from '@npco/component-dto-transaction/dist/transactionNotesUpdatedDto';
import type { TransactionTimedOutEventDto } from '@npco/component-dto-transaction/dist/transactionTimedOutEventDto';
import { TransactionStatus } from '@npco/component-dto-transaction/dist/types';
import type {
  TransactionQueryFilterInput,
  TransactionQueryInput,
  Transaction,
} from '@npco/component-dto-transaction/dist/types';

import { Injectable } from '@nestjs/common';

import { PgsApi } from './pgsApi';
import { ProjectionFactory } from './projection';
import { BaseProjection } from './projection/baseProjection';
import { OfflineApprovedProjection } from './projection/offlineApprovedProjection';
import { TransactionDb } from './transactionDb';
import { TransactionImages } from './transactionImages';
import { TransactionModel } from './transactionModel';
import {
  createOfflineDtoFromDeviceInput,
  createPgsTransactionRequestDto,
  createRequestDtoFromDeviceInput,
  txnLogSafe,
} from './transactionUtils';
import type {
  BatchTransactionDeposited,
  SplitPaymentWithTransactions,
  TransactionConnection,
  TransactionRequestInput,
  TransactionResponse,
} from './types';
import { StandInAllowed } from './types';

@Injectable()
export class TransactionService {
  transactionDb: TransactionDb;

  transactionImages: TransactionImages;

  s3: S3Client;

  protected pgsApi: PgsApi;

  private readonly amsApi: AmsApiService;

  private readonly filterSettlementsV2EntityList?: string[];

  constructor(
    protected readonly commonService: CommonService,
    protected readonly cardholderService: CardholderService,
    protected readonly sessionService: SessionService,
  ) {
    this.pgsApi = new PgsApi(this.commonService.envService.pgsApiEndpoint, {
      httpTimeout: this.commonService.envService.httpTimeout,
      rootCaSsmArn: this.commonService.envService.pgsRootCaSsmArn,
      isInLambda: this.commonService.envService.isInLambda,
      enableStandIn: true,
    });
    this.s3 = new S3Client(commonService.envService.awsRegion);
    this.transactionDb = new TransactionDb(this.commonService.envService, this.commonService.dynamodbService);
    this.amsApi = new AmsApiService(
      this.commonService.envService,
      this.commonService.envService.amsTransactionEndpointPath,
      'transaction',
    );
    this.transactionImages = new TransactionImages(
      this.commonService.envService,
      this.commonService.dynamodbService,
      this.amsApi,
    );
    this.filterSettlementsV2EntityList = process.env.SETTLEMENTS_V2_FILTER_LIST?.split(',');
  }

  // Used by the Transaction subscription to get the device siteUuid and validate the customer role access
  getDeviceSettings = async (entityUuid: string, deviceUuid: string): Promise<DeviceDbItem> =>
    getDeviceSettingsDbItemOrThrow(this.commonService.dynamodbService, entityUuid, deviceUuid);

  requestTransaction = async (
    entityUuid: string,
    deviceUuid: string,
    customerUuid: string,
    customerRole: CustomerRole,
    channel: Channel,
    event: TransactionRequestInput,
    {
      standInAllowed,
    }: {
      standInAllowed?: StandInAllowed;
    } = {},
  ): Promise<TransactionResponse> => {
    upsertReportData({
      input: txnLogSafe(
        { entityUuid, deviceUuid, customerUuid, customerRole, channel, standInAllowed, ...event },
        false,
      ),
    });
    const device = await this.getDeviceAndValidateRoleAccess(entityUuid, deviceUuid, customerUuid, customerRole);

    this.validateDeviceAccessWithTransactionType(device, event.type);
    try {
      let requestDto: RequestTransactionCommandDto = createRequestDtoFromDeviceInput(
        { deviceUuid, entityUuid, siteUuid: device.siteUuid, customerUuid, channel },
        event,
      );
      if (event.originalTransactionUuid && event.type === TransactionType.REFUND) {
        const injectedData = await this.getOriginalTransactionData(entityUuid, event.originalTransactionUuid);
        requestDto = { ...requestDto, ...injectedData };
      }

      upsertReportData({ requestDto: txnLogSafe(requestDto, false) });
      await this.callCommandHandler(this.commonService.envService.cqrsCmds.Transaction.Requested, {
        ...requestDto,
        iso8583: undefined,
      });

      const pgsTransactionRequestDto = createPgsTransactionRequestDto(requestDto);
      const { data } = await this.pgsApi.post(
        this.commonService.envService.pgsApiInitiateTxnPath,
        pgsTransactionRequestDto,
        { standInAllowed },
      );
      upsertReportData({ pgsResponse: txnLogSafe(data, false) });

      const {
        transactionUuid,
        iso8583ResponseMessage,
        iso8583ResponseMessageLength,
        responseCode,
        authCode,
        ksn,
        rrn,
        cardHolderUuid,
        par,
        panToken,
        maskedPan,
        emv,
      } = data;

      const { cardholderEmail, cardholderPhone } = await this.cardholderService.getCardholderData(cardHolderUuid);

      return {
        id: transactionUuid,
        responseCode,
        iso8583: iso8583ResponseMessage,
        iso8583Length: iso8583ResponseMessageLength,
        approvalCode: authCode,
        ksn,
        rrn,
        par,
        panToken,
        panMasked: maskedPan,
        cardholderUuid: cardHolderUuid,
        cardholderEmail,
        cardholderMobile: cardholderPhone,
        emv,
      };
    } catch (err: any) {
      const logLevel = standInAllowed === StandInAllowed.ALWAYS_STAND_IN ? warn : error;
      logLevel(`get error on request transaction: ${err.message}`, event.id);
      upsertReportData({ pgsError: this.pgsErrorSafeLog(err) });
      if (err instanceof ServerError) {
        return Promise.reject(err); // NOSONAR
      }
      return Promise.reject(new ServerError('Transaction request failed.')); // NOSONAR
    }
  };

  pgsErrorSafeLog = (pgsErrorPayload: any) => {
    const maskedPayload = dataMasking.maskObject(pgsErrorPayload, [
      { field: 'config.data', strategy: MaskingType.MASK_STRING_ALL_FIXED_LEN },
    ]);

    return maskedPayload;
  };

  projectTransaction = async (model: TransactionModel) => {
    const projector = ProjectionFactory.createProjection(
      this.transactionDb,
      this.commonService.dynamodbService,
      model,
      this.commonService.envService,
    );
    return projector.projectTransaction();
  };

  requestTransactionProjection = async (dto: RequestTransactionCommandDto) => {
    info(`requestTransactionProjection: ${txnLogSafe(dto)}`, dto.transactionUuid);
    const model = TransactionModel.createModelFromRequest(dto, TransactionStatus.PROCESSING);
    await this.projectTransaction(model);
  };

  initiateTransactionProjection = async (dto: InitiateTransactionCommandDto | InitiateIncompleteTransactionDto) => {
    info(`initiateTransactionProjection: ${txnLogSafe(dto)}`, dto.transactionUuid);
    const model = TransactionModel.createModelFromRequest(dto, TransactionStatus.PROCESSING);
    if (model.transactionType === TransactionType.REVERSAL) {
      info(`don't project reversal transaction initiated event ${model.transactionUuid}`, model.transactionUuid);
      return;
    }
    model.cardholderUuid = dto.cardholderUuid;
    await this.projectTransaction(model);
  };

  responseTransactionProjection = async (
    dto: ResponseTransactionCommandDto | MockApprovedIncompleteTransactionDto | MockDeclinedIncompleteTransactionDto,
  ): Promise<TransactionModel | null> => {
    info(`responseTransactionProjection ${txnLogSafe(dto)}`, dto.transactionUuid);
    const maskedCardholderData = await this.cardholderService.getCardholderData(dto.cardholderUuid);
    debug(`masked cardholderData ${JSON.stringify(maskedCardholderData)}`);
    const model = TransactionModel.createModelFromResponse(
      dto.entityUuid,
      dto.responseType as any, // NOSONAR
      {
        ...dto,
        ...maskedCardholderData,
      },
    );

    debug(`project model ${JSON.stringify(model)}`);
    return this.projectTransaction(model);
  };

  getTransaction = async (
    entityUuid: string,
    transactionUuid: string,
    managerCustomerUuid?: string,
  ): Promise<Transaction> => {
    const output = await this.transactionDb.getTransaction(entityUuid, transactionUuid);
    if (output.Items && output.Items.length > 0) {
      const dbItem = output.Items[0];
      await validateCustomerRoleAccess(
        this.commonService.dynamodbService,
        entityUuid,
        dbItem.siteUuid,
        managerCustomerUuid,
      );
      return convertDbItemToTransaction(dbItem);
    }
    return Promise.reject(new NotFoundError('Transaction not found', transactionUuid)); // NOSONAR
  };

  getTransactions = async (
    event: TransactionQueryInput,
    entityUuid: string,
    managerCustomerUuid?: string,
  ): Promise<TransactionConnection> => {
    const [customerAllowedSiteUuids, cardholderUuids] = await Promise.all([
      queryCustomerSiteUuids(this.commonService.dynamodbService, entityUuid, managerCustomerUuid),
      this.cardholderService.getCardholderUuidsByContactUuids(entityUuid, event.contactUuids, event.cardholderUuid),
    ]);
    if (managerCustomerUuid && customerAllowedSiteUuids.length === 0) {
      info(`customer ${managerCustomerUuid} doesn't have a site.`);
      return { transactions: [] };
    }
    const newEvent: TransactionQueryFilterInput = { ...event };
    if (event.contactUuids?.length) {
      if (!cardholderUuids || cardholderUuids.length === 0) {
        info(`Contacts ${JSON.stringify(event.contactUuids)} do match any transaction/cardholders.`);
        return { transactions: [] };
      }
      newEvent.cardholderUuids = cardholderUuids;
    }
    return this.queryTransactions(newEvent, entityUuid, customerAllowedSiteUuids);
  };

  getRefundTransactions = async (originalTransactionUuid: string): Promise<Transaction[]> => {
    const output = await this.transactionDb.getRefundTransactions(originalTransactionUuid);
    if (output?.Items?.length) {
      return output.Items.map((item: any) => convertDbItemToTransaction(item));
    }
    return [];
  };

  getSplitPayment = async (
    entityUuid: string,
    transactionUuid: string,
    managerCustomerUuid?: string,
  ): Promise<SplitPaymentWithTransactions | null> => {
    const output = await this.transactionDb.getTransaction(entityUuid, transactionUuid);
    if (output?.Items?.length) {
      const transaction = convertDbItemToTransaction(output.Items[0]);

      if (transaction.splitPaymentUuid === undefined || !transaction.splitPayment) {
        return null;
      }

      const splitPaymentTransactions = await this.getSplitPaymentTransactions(
        transaction.id,
        transaction.timestamp,
        transaction.splitPaymentUuid,
        entityUuid,
        managerCustomerUuid,
      );

      const splitPayment: SplitPaymentWithTransactions = {
        ...transaction.splitPayment,
        transactions: splitPaymentTransactions,
      };

      return splitPayment;
    }
    return null;
  };

  getSplitPaymentTransactions = async (
    transactionUuid: string,
    transactionTimestamp: string,
    splitPaymentUuid: string,
    entityUuid: string,
    managerCustomerUuid?: string,
  ): Promise<Transaction[]> => {
    const customerAllowedSiteUuids = await queryCustomerSiteUuids(
      this.commonService.dynamodbService,
      entityUuid,
      managerCustomerUuid,
    );
    if (managerCustomerUuid && customerAllowedSiteUuids.length === 0) {
      info(`customer ${managerCustomerUuid} doesn't have a site.`);
      return [];
    }

    const event = this.transactionDb.getSplitPaymentsTransactionFilters(transactionTimestamp, splitPaymentUuid);
    const splitPaymentTransactionsWithinTimeRange = await this.queryTransactions(
      event,
      entityUuid,
      customerAllowedSiteUuids,
    );

    return splitPaymentTransactionsWithinTimeRange.transactions.filter(
      (splitPaymentTransaction) => splitPaymentTransaction.id !== transactionUuid,
    );
  };

  queryTransactions = async (
    event: TransactionQueryFilterInput,
    pkValue: string,
    customerAllowedSiteUuids: string[],
    queryIndex?: QueryIndex,
    mapSourceFilters = true,
  ): Promise<TransactionConnection> => {
    const input: TransactionQueryFilterInput = { ...event, managerSiteUuids: customerAllowedSiteUuids };
    const output = await this.transactionDb.getTransactions(input, pkValue, queryIndex);
    if (output?.Items?.length) {
      const transactions = output.Items.map((item: any) => convertDbItemToTransaction(item, mapSourceFilters));
      const nextToken = output.LastEvaluatedKey ? (output.LastEvaluatedKey as EntityGsiKeySchema) : undefined;
      return nextToken ? { transactions, nextToken } : { transactions };
    }
    return { transactions: [] };
  };

  cancelTransaction = async (
    fields: {
      entityUuid: string;
      deviceUuid: string;
      customerUuid: string;
      customerRole: CustomerRole;
      transactionUuid: string;
      reason: TransactionCancelReason;
      channel: Channel;
    },
    event?: TransactionRequestInput,
  ) => {
    const { entityUuid, deviceUuid, customerRole, customerUuid, reason, channel, transactionUuid } = fields;
    const { siteUuid } = await this.getDeviceAndValidateRoleAccess(entityUuid, deviceUuid, customerUuid, customerRole);
    await this.callCommandHandler(
      this.commonService.envService.cqrsCmds.Transaction.Cancelled,
      new CancelTransactionCommandDto({
        entityUuid,
        transactionUuid,
        responseCode: TransactionResponseCode[reason],
        reason,
        ...(event
          ? {
              transactionDetails: createRequestDtoFromDeviceInput(
                { deviceUuid, entityUuid, siteUuid, customerUuid, channel },
                event,
              ),
              type: event.type,
              timestampUtc: event.timestampUtc,
            }
          : {}),
      }),
      true,
    );
    return true;
  };

  cancelTransactionProjection = async (dto: CancelTransactionCommandDto) => {
    info(`cancelTransactionProjection ${txnLogSafe(dto)}`, dto.transactionUuid);
    const model = TransactionModel.createModelFromCancel(dto);
    await this.projectTransaction(model);
  };

  // This to become the default behaviour while transitioning types to the new projection
  cancelCpTransactionProjection = async (dto: CancelTransactionCommandDto) => {
    info(`cancelTransactionProjection ${txnLogSafe(dto)}`, dto.transactionUuid);
    if (!dto.transactionDetails) {
      const existing = await this.transactionDb.getTransactionById(dto.transactionUuid);
      if (!existing) {
        error(
          "Cancelled transaction doesn't contain original transaction details, nor can find an existing transaction record. A transaction will not be materialised.",
          dto.transactionUuid,
        );
        return;
      }
    }
    const model = TransactionModel.createModelFromCancel(dto);
    await this.projectTransaction(model);
  };

  timedOutTransactionProjection = async (dto: TransactionTimedOutEventDto) => {
    info(`timedOutTransactionProjection ${txnLogSafe(dto)}`, dto.transactionUuid);
    const model = TransactionModel.createModelFromTimeout(dto);
    await this.projectTransaction(model);
  };

  processTransactionAsyncBatch = async (uri: string, detail: any) => {
    info(`processTransactionAsyncBatch ${uri} ${JSON.stringify(detail)}`);
    const name = uri.split('.').pop();
    if (name === 'LinkedToDeposit') {
      await this.transactionBatchDepositedProjection(detail);
    } else if (name === 'LinkedToDepositV2') {
      await this.transactionBatchLinkedToDepositProjection(detail);
    } else if (name === 'FeeAdjusted') {
      await this.transactionBatchFeeAdjustedProjection(detail);
    }
  };

  transactionAsyncBatch = async (detail: any, uri: string) => {
    info(`handleTransactionAsyncBatch ${uri} ${JSON.stringify(detail)}`);
    await this.commonService.lambdaService.invokeAsync(this.commonService.envService.transactionAsyncBatchHandler, {
      uri,
      detail,
    });
  };

  transactionBatchLinkedToDepositProjection = async (event: TransactionBatchLinkedToDepositEventDto) => {
    info(`transactionBatchLinkedToDepositProjection ${JSON.stringify(event)}`, event.transactionBatchUuid);
    if (event.previousDepositUuid) {
      let lastEvaluatedKey: any;
      do {
        const result = await this.transactionDb.getTransactionsByDepositUuid(
          event.previousDepositUuid,
          lastEvaluatedKey,
        );
        debug(`filterSettlementsV2EntityList ${JSON.stringify(this.filterSettlementsV2EntityList)}`);
        for (const transaction of result.Items ?? []) {
          if (
            !this.filterSettlementsV2EntityList?.length ||
            this.filterSettlementsV2EntityList.includes(transaction.entityUuid)
          ) {
            await this.transactionDepositedProjection({
              depositId: event.depositUuid,
              transactionUuid: transaction.id,
            }).catch((err) => {
              error(`transactionBatchLinkedToDepositProjection ${err}`, transaction.id);
            });
          }
        }
        lastEvaluatedKey = result.LastEvaluatedKey;
      } while (lastEvaluatedKey);
    }
  };

  transactionBatchDepositedProjection = async (event: BatchTransactionDepositedEventDto) => {
    info(`transactionBatchDepositedProjection ${JSON.stringify(event)}`, event.id);
    const deposits: BatchTransactionDeposited[] = await this.downloadFromS3(event.content);
    for (const deposit of deposits) {
      for (const transactionUuid of deposit.transactions) {
        await this.transactionDepositedProjection({
          depositId: deposit.entityIdSiteIdProcessingDateTimeAEST,
          transactionUuid,
        }).catch((err) => {
          error(`transactionBatchDepositedProjection ${err}`, transactionUuid);
        });
      }
    }
  };

  depositGpvUpdatedProjection = async (dto: DepositGpvUpdatedEventDto) => {
    await this.saveTransactionDepositedDetails(dto.depositUuid, dto.transactionUuid, dto.fromDateTime);
  };

  transactionDepositedProjection = async (dto: TransactionDepositedDto) => {
    await this.saveTransactionDepositedDetails(dto.depositId, dto.transactionUuid);
  };

  transactionOfflineProjection = async (dto: OfflineTransactionApprovedCommandDto) => {
    info(`transactionOfflineProjection ${txnLogSafe(dto)}`, dto.transactionUuid);
    const model = TransactionModel.createModelFromOfflineApproved(dto);
    const proj = new OfflineApprovedProjection(
      this.transactionDb,
      this.commonService.dynamodbService,
      model,
      this.commonService.envService,
    );
    await proj.projectTransaction();
  };

  downloadFromS3 = async (content: BatchContent): Promise<any> => {
    debug(`Batch transactions ${JSON.stringify(content)}`);
    try {
      const [bucket, ...keyPath] = content.link.split('/');
      const getObjectParams = {
        Bucket: bucket,
        Key: keyPath.join('/'),
      };
      const object = await this.s3.getObjectAndTransformToString(getObjectParams);
      return JSON.parse(object ?? '[]');
    } catch (e) {
      error(e);
      error(`Failed to get batch transaction ${JSON.stringify(content)}`);
    }
    return [];
  };

  transactionBatchFeeAdjustedProjection = async (event: BatchFeeAdjustedTransactionCommandDto) => {
    info(`transactionBatchFeeAdjustedProjection ${JSON.stringify(event)}`, event.id);
    const transactions = await this.downloadFromS3(event.content);
    for (const txnJsonEvent of transactions) {
      await this.transactionFeeAffected({
        transactionUuid: txnJsonEvent.transactionId,
        fees: txnJsonEvent.fees,
      } as FeeOwedTransactionCommandDto);
    }
  };

  transactionFeeReversed = async (dto: FeeReversedTransactionCommandDto) => {
    info(`transactionFeeReversed ${JSON.stringify(dto)}`, dto.transactionUuid);
    const purchaseTxn = await this.transactionDb.getTransactionByIdOrThrow(dto.transactionUuid);
    await this.transactionDb.updateTransaction(purchaseTxn.entityUuid, dto.transactionUuid, {
      feeAmount: { currency: ISO4217.AUD, value: 0 },
      feeCharged: false,
      feeReversed: true,
    });
  };

  transactionFeeAffected = async (dto: FeeOwedTransactionCommandDto) => {
    info(`transactionFeeAffected ${JSON.stringify(dto.fees)}`, dto.transactionUuid);

    if (dto.transactionType === TransactionType.REVERSAL) {
      info(`don't project reversal transaction Fee Owed event ${dto.transactionUuid}`, dto.transactionUuid);
      return null;
    }

    const item = await this.transactionDb.getTransactionById(dto.transactionUuid, false);

    if (item?.status === Status.DELETED || item?.feeReversed) {
      return Promise.resolve(); // NOSONAR
    }

    const model = TransactionModel.createModelFromFeeOwed(dto);

    const projection = new BaseProjection(
      this.transactionDb,
      this.commonService.dynamodbService,
      {
        ...model,
      },
      this.commonService.envService,
    );

    const transaction = await projection.projectTransaction();

    // Only want to handle the refund fee adjustment for fee service v1
    // fee service v2 uses feeRefunded events contains no transactionType
    if (
      dto?.transactionType === TransactionType.REFUND &&
      transaction?.status === TransactionStatus.APPROVED &&
      transaction?.originalTransactionUuid &&
      transaction?.feeAmount
    ) {
      const original = await this.transactionDb.getTransactionById(transaction.originalTransactionUuid);
      if (original) {
        const transactionFeeAmountValue = !Number.isNaN(Number(transaction.feeAmount?.value))
          ? Number(transaction.feeAmount?.value)
          : 0;
        const newFee = Math.max(0, original.feeAmount.value - transactionFeeAmountValue);
        await this.transactionDb.updateTransaction(original.entityUuid, original.transactionUuid, {
          feeAmount: { currency: original.feeAmount.currency, value: newFee },
          feeCharged: newFee > 0,
        });
      }
    }
    return transaction;
  };

  transactionFeeRefunded = async (dto: FeeRefundedTransactionCommandDto) => {
    info(`transactionFeeRefunded ${JSON.stringify(dto)}`, dto.transactionUuid);
    const refundFee = parseInt(dto.refundedFee, 10);
    if (refundFee === 0) {
      return;
    }

    const purchaseTxn = await this.transactionDb.getTransactionByIdOrThrow(dto.transactionUuid);
    if (!purchaseTxn.feeAmount || Number(purchaseTxn.feeAmount.value) === 0) {
      throw new ServerError('Transaction fee not found or already refunded');
    }

    let newFee = Number(purchaseTxn.feeAmount.value) - refundFee;
    if (newFee < 0) {
      warn(
        `Refunded fee is greater than the original fee: purchase fee: ${purchaseTxn.feeAmount.value}, refunded fee: ${refundFee}`,
        dto.transactionUuid,
      );
      newFee = 0;
    }

    await this.transactionDb.updateTransaction(purchaseTxn.entityUuid, dto.transactionUuid, {
      feeAmount: { currency: purchaseTxn.feeAmount.currency, value: newFee },
      feeCharged: newFee > 0,
    });
  };

  offlineTransaction = async (
    entityUuid: string,
    deviceUuid: string,
    customerUuid: string,
    customerRole: CustomerRole,
    channel: Channel,
    event: TransactionRequestInput,
  ): Promise<boolean> => {
    const { siteUuid } = await this.getDeviceAndValidateRoleAccess(entityUuid, deviceUuid, customerUuid, customerRole);
    const offlineDto: OfflineTransactionApprovedCommandDto = createOfflineDtoFromDeviceInput(
      {
        siteUuid,
        entityUuid,
        deviceUuid,
        customerUuid,
        channel,
      },
      event,
    );
    try {
      const cmd = this.commonService.envService.cqrsCmds.Transaction.OfflineApproved;
      info(`call command handler: ${cmd}, ${txnLogSafe(offlineDto)}`, offlineDto.transactionUuid);
      const output = await this.commonService.lambdaService.invokeCommandSync(cmd, offlineDto);
      debug(`call command handler ${cmd} response ${txnLogSafe(output)}`, offlineDto.transactionUuid);
    } catch (err) {
      error(err, offlineDto.transactionUuid);
      throw err;
    }
    return true;
  };

  public createTransactionNotes = async (transactionUuid: string, notes: string) => {
    return this.amsApi.createTransactionNotes(transactionUuid, notes);
  };

  public updateTransactionNotes = async (transactionUuid: string, notes: string) => {
    return this.amsApi.updateTransactionNotes(transactionUuid, notes);
  };

  public deleteTransactionNotes = async (transactionUuid: string) => {
    return this.amsApi.deleteTransactionNotes(transactionUuid);
  };

  public transactionNotesProjection = async (dto: TransactionNotesUpdatedDto): Promise<boolean> =>
    this.transactionDb.transactionNotesProjection(dto);

  public transactionNotesDeleteProjection = async (dto: TransactionNotesUpdatedDto): Promise<boolean> =>
    this.transactionDb.transactionNotesProjection({ ...dto, notes: null });

  getTransactionImageUploadUrls = async (
    entityUuid: string,
    transactionUuid: string,
    fileNames: string[],
  ): Promise<any> => this.transactionImages.getTransactionImageUploadUrls(entityUuid, transactionUuid, fileNames);

  getTransactionImageDownloadUrls = async (entityUuid: string, transactionUuid: string): Promise<any> =>
    this.transactionImages.getTransactionImageDownloadUrls(entityUuid, transactionUuid);

  removeTransactionImage = async (entityUuid: string, transactionUuid: string, fileUuid: string): Promise<any> =>
    this.transactionImages.removeTransactionImage(entityUuid, transactionUuid, fileUuid);

  transactionImagesProjection = async (dto: TransactionImagesUpdatedDto): Promise<boolean> =>
    this.transactionImages.transactionImagesProjection(dto);

  protected callCommandHandler = async (
    aggregateUri: string,
    dto:
      | RequestTransactionCommandDto
      | CnpTransactionRequestedDto
      | CancelTransactionCommandDto
      | CnpTransactionRefundRequestedDto
      | ForcedTransactionRequestedDto,
    throwError = false,
  ) => {
    try {
      info(`send dto to command handler: ${aggregateUri}, ${txnLogSafe(dto)}`, dto.transactionUuid);

      const output = await this.commonService.lambdaService.invokeCommand(aggregateUri, dto);
      debug(`invoke command handler lambda response: ${JSON.stringify(output, null, 2)}`, dto.transactionUuid);
    } catch (err: any) {
      error(`Failed to call ${aggregateUri} command handler: ${err.message}`, dto.transactionUuid);
      if (throwError) {
        throw err;
      }
    }
  };

  protected validateEntityOnboarded = async (entityUuid: string) => {
    const isEntityOnboarded = await this.sessionService.checkEntityOnboardingStatus(entityUuid);
    if (!isEntityOnboarded) {
      error(`entity not onboarded ${isEntityOnboarded}`);
      return Promise.reject(new ForbiddenError('Not allowed')); // NOSONAR
    }
    return true;
  };

  protected validateDeviceAccessWithTransactionType = (device: Record<string, any>, type: TransactionType) => {
    let fields: string[] = [];
    if (type === TransactionType.PURCHASE) {
      fields = ['canAcquire'];
    }
    if (type === TransactionType.REFUND) {
      fields = ['canRefund'];
    }
    return this.validateEntityDeviceSettingsAccess(device, fields);
  };

  protected validateEntityDeviceSettingsAccess = (device: Record<string, any>, fields: string[]) => {
    const entityDeviceSettingAccess = fields.every((accessField) => {
      return device.entity?.[accessField];
    });
    if (!entityDeviceSettingAccess) {
      error(`entity device setting access validation failed ${JSON.stringify(device.entity)}`);
      throw new ForbiddenError('Not allowed');
    }
    return true;
  };

  protected getDeviceAndValidateRoleAccess = async (
    entityUuid: string,
    deviceUuid: string,
    customerUuid: string,
    customerRole: CustomerRole,
  ): Promise<DeviceDbItem & { siteUuid: string }> => {
    const managerCustomerUuid = customerRole !== CustomerRole.ADMIN ? customerUuid : undefined;
    const [device, customerAllowedSiteUuids] = await Promise.all([
      this.getDeviceSettings(entityUuid, deviceUuid),
      queryCustomerSiteUuids(this.commonService.dynamodbService, entityUuid, managerCustomerUuid),
    ]);
    if (!device.siteUuid) {
      error(`not found site for device ${deviceUuid}`);
      return Promise.reject(new NotFoundError('Site for device not found', deviceUuid)); // NOSONAR
    }
    if (managerCustomerUuid && !customerAllowedSiteUuids.includes(device.siteUuid)) {
      error(`Manager does not have access to site ${device.siteUuid}`);
      return Promise.reject(new ForbiddenError('Not allowed.')); // NOSONAR
    }
    return { ...device, siteUuid: device.siteUuid as string };
  };

  private readonly saveTransactionDepositedDetails = async (
    depositUuid: string,
    transactionUuid: string,
    fromDateTime?: string,
  ) => {
    info(`transactionDepositedProjection ${depositUuid}`, transactionUuid);
    const item = await this.transactionDb.getTransactionById(transactionUuid, false);
    if (!item) {
      return Promise.reject(new Error(`transaction id ${transactionUuid} not found.`)); // NOSONAR
    }
    if (item.status === Status.DELETED) {
      return Promise.resolve(); // NOSONAR
    }
    const { processingDateTimeAEST } = parseDepositId(depositUuid);
    debug(`get deposit timestamp ${processingDateTimeAEST}`, transactionUuid);
    const model = TransactionModel.createModelFromDbItem(item, {
      depositUuid,
      depositDate: new Date(processingDateTimeAEST ?? fromDateTime ?? new Date()).toISOString(),
    });
    debug(`start materialising deposit ${JSON.stringify(model, null, 2)}`, transactionUuid);
    const projection = new BaseProjection(
      this.transactionDb,
      this.commonService.dynamodbService,
      {
        ...model,
      },
      this.commonService.envService,
    );
    return projection.projectTransaction();
  };

  private readonly getOriginalTransactionData = async (entityUuid: string, originalTransactionUuid: string) => {
    const item = await this.transactionDb.getTransaction(entityUuid, originalTransactionUuid);
    if (!item.Items || item.Items.length === 0) {
      return Promise.reject(new ServerError(`Cant find original transaction ${originalTransactionUuid}`)); // NOSONAR
    }
    const originalTransaction = item.Items[0];
    debug(`extract original transaction ${JSON.stringify(originalTransaction, null, 2)}`);
    return {
      scheme: originalTransaction.scheme,
      bin: originalTransaction.bin,
      panMasked: originalTransaction.maskedPan,
      panToken: originalTransaction.panToken,
      par: originalTransaction.par,
    };
  };
}
