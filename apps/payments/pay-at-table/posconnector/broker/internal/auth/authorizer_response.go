package auth

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/smithy-go/ptr"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/session"
	coreTypes "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/types"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
	"gopkg.in/square/go-jose.v2/jwt"
)

type AuthorizerResponse struct {
	events.APIGatewayCustomAuthorizerResponse
	dbClient       *db.DynamoDb
	auth0          IAuth0
	sessionService session.ISessionService
}

type JwtClaims struct {
	Aud          []string
	Sub          string
	CustomerUuid *string
}

const (
	ClaimSub          = "sub"
	ClaimExpiry       = "exp"
	ClaimAud          = "aud"
	ClaimCustomerUuid = "https://identity.myzeller.com/customerUuid"
)

type Effect int

const (
	Allow Effect = iota
	Deny
)

func (e Effect) String() string {
	switch e {
	case Allow:
		return "Allow"
	case Deny:
		return "Deny"
	}
	return ""
}

const (
	TOKEN_EXPIRED_ERROR   = "Access token expired"
	TOKEN_NOT_FOUND_ERROR = "Access token not found"
)

func NewAuthorizerResponse(ctx context.Context) *AuthorizerResponse {
	db := db.NewDynamoDb(ctx)
	db.AddSensitiveTableNames(common.DbsSessionTableName(), common.SessionCacheTableName())

	resp := &AuthorizerResponse{
		APIGatewayCustomAuthorizerResponse: events.APIGatewayCustomAuthorizerResponse{
			PolicyDocument: events.APIGatewayCustomAuthorizerPolicy{
				Version: "2012-10-17",
			},
		},
		dbClient:       db,
		auth0:          NewAuth0(),
		sessionService: session.New(db),
	}

	return resp
}

func (resp *AuthorizerResponse) CheckDbsSession(ctx context.Context, event events.APIGatewayCustomAuthorizerRequestTypeRequest, jwtToken *string) (events.APIGatewayCustomAuthorizerResponse, error) {
	deviceCache, err := resp.GetSessionCache(ctx, *jwtToken, common.DbsSessionTableName(), "accessTokenGsiv2")
	if err != nil {
		logger.Error(ctx, err.Error())
		resp.AddMethod(Effect(Deny), event.MethodArn)
		return resp.APIGatewayCustomAuthorizerResponse, resp.CreateErrorResponse(err)
	}

	tokenContext := TokenContext{
		DeviceCache: *deviceCache,
		DeviceType:  ptr.String(string(types.DeviceTypeTerminal)),
	}
	err = resp.setDeviceSitePairing(ctx, types.POSINTERFACE_PAIR_DEVICE, &tokenContext)

	if err != nil {
		logger.Error(ctx, err.Error())
		resp.AddMethod(Effect(Deny), event.MethodArn)
		return resp.APIGatewayCustomAuthorizerResponse, resp.CreateErrorResponse(err)
	}
	resp.AddMethod(Effect(Allow), event.MethodArn)
	resp.Context = tokenContext.ToMap()
	resp.attachZellerTraceIdFromContext(ctx)
	return resp.APIGatewayCustomAuthorizerResponse, nil
}

func (resp *AuthorizerResponse) attachZellerTraceIdFromContext(ctx context.Context) {
	zellerTraceId, ok := ctx.Value(coreTypes.ZellerTraceId{}).(string)
	if ok {
		resp.Context["zellerTraceId"] = zellerTraceId
	}
}

func (resp *AuthorizerResponse) CheckSdkSession(ctx context.Context, event events.APIGatewayCustomAuthorizerRequestTypeRequest, jwtToken *string, jwtClaims *JwtClaims) (events.APIGatewayCustomAuthorizerResponse, error) {
	// 1. Get deviceUuid
	deviceUuid, pconnError := ExtractDeviceUuid(event)
	if pconnError != nil {
		logger.Error(ctx, pconnError.Error())
		resp.AddMethod(Effect(Deny), event.MethodArn)
		return resp.APIGatewayCustomAuthorizerResponse, resp.CreateErrorResponse(pconnError)
	}

	tokenContext := TokenContext{
		DeviceType: ptr.String(string(types.DeviceTypePOS)),
		DeviceCache: DeviceCache{
			Status: DeviceStatusActive,
		},
	}

	// 2. Use sessionService to get identitySub cache via accessTokenGsi
	// 5. Use sessionService to get deviceUuid cache via deviceUuid
	identityCache, _ := resp.sessionService.CheckIdentityCache(ctx, *jwtToken)
	deviceCache, err := resp.sessionService.CheckDeviceCache(ctx, *deviceUuid)

	// 5. Check device exists, is active, has entityUuid
	if deviceCache == nil || err != nil {
		message := "Error getting device cache"
		logger.Error(ctx, message)
		resp.AddMethod(Effect(Deny), event.MethodArn)
		return resp.APIGatewayCustomAuthorizerResponse, resp.CreateErrorResponse(&protocol.PosConnectorError{
			Type:    protocol.FORBIDDEN,
			Message: message,
		})
	}

	if *deviceCache.Status != string(DeviceStatusActive) {
		message := "Device is inactive"
		logger.Error(ctx, message)
		resp.AddMethod(Effect(Deny), event.MethodArn)
		return resp.APIGatewayCustomAuthorizerResponse, resp.CreateErrorResponse(&protocol.PosConnectorError{
			Type:    protocol.FORBIDDEN,
			Message: message,
		})
	}

	tokenContext.DeviceUuid = deviceUuid

	// 3. If no identitySub, go to 5
	// 4. Check identitySub deviceUuid matches request deviceUuid and deviceCache entityUuid matches identitySub entityUuid
	if identityCache != nil && *identityCache.DeviceUuid == *deviceUuid && *deviceCache.EntityUuid == *identityCache.EntityUuid {

		tokenContext.CustomerUuid = identityCache.CustomerUuid
		tokenContext.EntityUuid = identityCache.EntityUuid
		tokenContext.DeviceUuid = deviceUuid

		pconnError = resp.setDeviceSitePairing(ctx, types.POSINTERFACE_PAIR_POS, &tokenContext)
		if pconnError != nil {
			logger.Error(ctx, pconnError.Error())
			resp.AddMethod(Effect(Deny), event.MethodArn)
			return resp.APIGatewayCustomAuthorizerResponse, resp.CreateErrorResponse(pconnError)
		}

		resp.AddMethod(Effect(Allow), event.MethodArn)
		resp.Context = tokenContext.ToMap()
		resp.attachZellerTraceIdFromContext(ctx)
		return resp.APIGatewayCustomAuthorizerResponse, nil
	}

	pconnError = resp.setDeviceSitePairing(ctx, types.POSINTERFACE_PAIR_POS, &tokenContext)
	if pconnError != nil {
		logger.Error(ctx, pconnError.Error())
		resp.AddMethod(Effect(Deny), event.MethodArn)
		return resp.APIGatewayCustomAuthorizerResponse, resp.CreateErrorResponse(pconnError)
	}

	// 6.5 TBC once customerUuid is part of deviceUuid cache record, check customer.entity.${entityUuid} records exists to validate customer <> entity relationship
	// if deviceCache.CustomerUuid != jwtClaims.CustomerUuid {
	// 	message := "Customer does no have access to device"
	// 	logger.Error(ctx, message)
	// 	resp.AddMethod(Effect(Deny), event.MethodArn)
	// 	return resp.APIGatewayCustomAuthorizerResponse, resp.CreateErrorResponse(&protocol.PosConnectorError{
	// 		Type:    protocol.FORBIDDEN,
	// 		Message: message,
	// 	})
	// }

	// 7. Create identitySub cache with id=accessToken, type=identitySub, auth0Sub, customerUuid, deviceUuid, entityUuid, ttl
	newSessionCache := session.IdentityCache{
		Auth0Sub:     &jwtClaims.Sub,
		CustomerUuid: jwtClaims.CustomerUuid,
		DeviceUuid:   deviceUuid,
		EntityUuid:   deviceCache.EntityUuid,
	}

	err = resp.sessionService.UpsertIdentitySubCacheItem(ctx, *jwtToken, session.ToMap(newSessionCache))
	if err != nil {
		logger.Error(ctx, pconnError.Error())
		resp.AddMethod(Effect(Deny), event.MethodArn)
		return resp.APIGatewayCustomAuthorizerResponse, resp.CreateErrorResponse(pconnError)
	}

	tokenContext.CustomerUuid = jwtClaims.CustomerUuid
	tokenContext.EntityUuid = deviceCache.EntityUuid
	tokenContext.DeviceUuid = deviceUuid

	// 8. Approve connection
	resp.AddMethod(Effect(Allow), event.MethodArn)
	resp.Context = tokenContext.ToMap()
	resp.attachZellerTraceIdFromContext(ctx)
	return resp.APIGatewayCustomAuthorizerResponse, nil
}

func (r *AuthorizerResponse) AddMethod(effect Effect, resource string) {
	s := events.IAMPolicyStatement{
		Effect:   effect.String(),
		Action:   []string{"execute-api:Invoke"},
		Resource: []string{resource},
	}
	r.PolicyDocument.Statement = append(r.PolicyDocument.Statement, s)
}

func (r *AuthorizerResponse) CheckToken(ctx context.Context, token string) (*JwtClaims, *protocol.PosConnectorError) {
	t, err := jwt.ParseSigned(token)
	if err != nil {
		logger.Error(ctx, "parse token failed: "+err.Error())
		return nil, &protocol.PosConnectorError{
			Type:    protocol.UNAUTHORIZED,
			Message: err.Error(),
		}
	}
	var claim map[string]interface{}
	err = t.UnsafeClaimsWithoutVerification(&claim)
	if err != nil {
		logger.Error(ctx, "failed to get claims from token: "+err.Error())
		return nil, &protocol.PosConnectorError{
			Type:    protocol.UNAUTHORIZED,
			Message: err.Error(),
		}
	}

	var jwtClaims JwtClaims

	sub, ok := claim[ClaimSub].(string)
	if !ok || len(sub) == 0 {
		msg := "failed to get sub claim from token"
		logger.Error(ctx, msg)
		return nil, &protocol.PosConnectorError{
			Type:    protocol.UNAUTHORIZED,
			Message: msg,
		}
	}
	jwtClaims.Sub = sub

	expSeconds, ok := claim[ClaimExpiry].(float64)
	if !ok {
		msg := "failed to get exp claim from token"
		logger.Error(ctx, msg)
		return nil, &protocol.PosConnectorError{
			Type:    protocol.UNAUTHORIZED,
			Message: msg,
		}
	}

	expTime := time.UnixMilli(int64(expSeconds * 1000))

	now := time.Now()
	valid := !now.After(expTime)
	if !valid {
		logger.Info(ctx, fmt.Sprintf("expired token %d", expTime.UnixMilli()))
		return nil, &protocol.PosConnectorError{
			Type:    protocol.UNAUTHORIZED,
			Message: TOKEN_EXPIRED_ERROR,
		}
	}

	audClaims, ok := claim[ClaimAud].([]any)
	if !ok {
		msg := "failed to get aud claim from token"
		logger.Error(ctx, msg)
		return nil, &protocol.PosConnectorError{
			Type:    protocol.UNAUTHORIZED,
			Message: msg,
		}
	}

	if len(audClaims) <= 0 {
		msg := "no aud claim specified in token"
		logger.Error(ctx, msg)
		return nil, &protocol.PosConnectorError{
			Type:    protocol.UNAUTHORIZED,
			Message: msg,
		}
	}
	var aud []string
	for _, audClaim := range audClaims {
		aud = append(aud, audClaim.(string))
	}
	jwtClaims.Aud = aud

	customerUuidClaim, ok := claim[ClaimCustomerUuid].(string)
	if !ok {
		msg := "failed to get cusotmerUUid claim from token"
		logger.Warn(ctx, msg)
	} else {
		jwtClaims.CustomerUuid = &customerUuidClaim
	}

	r.PrincipalID = sub
	return &jwtClaims, nil
}

type CustomerRole string

const (
	ADMIN   CustomerRole = "ADMIN"
	MANAGER CustomerRole = "MANAGER"
)

type DeviceStatus string

const (
	DeviceStatusInactive DeviceStatus = "INACTIVE"
	DeviceStatusActive   DeviceStatus = "ACTIVE"
	DeviceStatusDisabled DeviceStatus = "DISABLED"
	DeviceStatusDeleted  DeviceStatus = "DELETED"
)

type Auth0Metadata struct {
	Role         *CustomerRole `json:"role" dynamodbav:"role"`
	EntityUuid   *string       `json:"entityUuid" dynamodbav:"entityUuid"`
	CustomerUuid *string       `json:"customerUuid" dynamodbav:"customerUuid"`
}

type DeviceCache struct {
	Auth0Metadata
	DeviceUuid  *string      `json:"deviceUuid" dynamodbav:"deviceUuid"`
	Status      DeviceStatus `json:"status" dynamodbav:"status"`
	AccessToken *string      `json:"-" dynamodbav:"accessToken"`
}

type DeviceCacheDbRecord struct {
	DeviceCache
	Id   *string `json:"id" dynamodbav:"id"`
	Type *string `json:"type" dynamodbav:"type"`
}

type TokenContext struct {
	DeviceCache
	SiteUuid   *string `json:"siteUuid"`
	Provider   *string `json:"provider"`
	DeviceType *string `json:"deviceType"`
}

func (tc *TokenContext) ToMap() map[string]interface{} {
	jTc, err := json.Marshal(tc)
	if err != nil {
		panic(err)
	}
	var dcMap map[string]interface{}
	err = json.Unmarshal(jTc, &dcMap)
	if err != nil {
		panic(err)
	}

	return dcMap
}

func (r *AuthorizerResponse) GetSessionCache(ctx context.Context, token string, sessionTableName string, gsi string) (*DeviceCache, *protocol.PosConnectorError) {
	logger.Debug(ctx, "Check session table "+sessionTableName)
	output, err := r.dbClient.Query(sessionTableName).Index(gsi).Eq("accessToken", token).Eq("type", "deviceUuid").Exec(ctx)
	if err != nil {
		logger.Error(ctx, err.Error())
		return nil, &protocol.PosConnectorError{Type: protocol.SERVER_ERROR, Message: err.Error()}
	}
	if len(output.Data) == 0 {
		return nil, &protocol.PosConnectorError{Type: protocol.UNAUTHORIZED, Message: TOKEN_NOT_FOUND_ERROR}
	}
	data := output.Data[0]

	if err = common.CheckRequiredFields(data, []string{"id", "entityUuid", "status", "accessToken"}); err != nil {
		logger.Error(ctx, err.Error())
		return nil, &protocol.PosConnectorError{Type: protocol.UNAUTHORIZED, Message: err.Error()}
	}

	status := DeviceStatus(data["status"].(string))
	sessionCache := DeviceCache{
		Auth0Metadata: Auth0Metadata{
			EntityUuid: ptr.String(data["entityUuid"].(string)),
		},
		DeviceUuid: ptr.String(data["id"].(string)),
		Status:     status,
	}

	if customerUuid, ok := data["customerUuid"]; ok {
		sessionCache.CustomerUuid = ptr.String(customerUuid.(string))
	}

	if sessionCache.Status != DeviceStatusActive {
		return nil, &protocol.PosConnectorError{
			Type:    protocol.UNAUTHORIZED,
			Message: "Device status is " + string(sessionCache.Status),
		}
	}

	return &sessionCache, nil
}

// check pairing data for the given device
func (r *AuthorizerResponse) setDeviceSitePairing(ctx context.Context, dbType types.DbItemType, tokenContext *TokenContext) *protocol.PosConnectorError {
	deviceUuid := *tokenContext.DeviceUuid
	logger.Info(ctx, fmt.Sprintf("Check pairing table %s for device %s", common.PairingTableName(), deviceUuid))
	output, err := r.dbClient.Query(common.PairingTableName()).Index("typeGsi").Eq("type", fmt.Sprintf("%s%s", dbType, deviceUuid)).Exec(ctx)
	if err != nil {
		logger.Error(ctx, err.Error())
		return &protocol.PosConnectorError{
			Type:    protocol.SERVER_ERROR,
			Message: err.Error(),
		}
	}
	logger.Info(ctx, fmt.Sprintf("check device pair record %v", utils.BestEffortStringify(output.Data, false)))
	if len(output.Data) == 0 {
		return &protocol.PosConnectorError{
			Type:    protocol.FORBIDDEN,
			Message: fmt.Sprintf("Device is not paired %s", deviceUuid),
		}
	}
	if err := common.CheckRequiredFields(output.Data[0], []string{"provider"}); err != nil {
		return &protocol.PosConnectorError{
			Type:    protocol.SERVER_ERROR,
			Message: fmt.Sprintf("Cant find provider for the device pairing %s", deviceUuid),
		}
	}
	tokenContext.Provider = ptr.String(output.Data[0]["provider"].(string))
	if siteUuid, ok := output.Data[0]["siteUuid"]; ok {
		tokenContext.SiteUuid = ptr.String(siteUuid.(string))
	}

	if entityUuid, ok := output.Data[0]["entityUuid"]; ok {
		if tokenContext.EntityUuid != nil && *tokenContext.EntityUuid != entityUuid {
			msg := fmt.Sprintf("Session entityUuid %s doesn't match pairing entityUuid %s", *tokenContext.EntityUuid, entityUuid)
			logger.Warn(ctx, msg)
			return &protocol.PosConnectorError{
				Type:    protocol.SERVER_ERROR,
				Message: msg,
			}
		}
	}

	logger.Info(ctx, fmt.Sprintf("token context %s", utils.BestEffortStringify(tokenContext.ToMap(), false)))

	return nil
}

func (r *AuthorizerResponse) CreateErrorResponse(err *protocol.PosConnectorError) error {
	switch err.Type {
	case protocol.UNAUTHORIZED:
		return errors.New("Unauthorized") // return 401
	case protocol.FORBIDDEN:
		return nil // return 403
	default:
		return err // return 500
	}
}

func ExtractDeviceUuid(event events.APIGatewayCustomAuthorizerRequestTypeRequest) (*string, *protocol.PosConnectorError) {
	protocolHeader, hasProtocolHeader := common.GetHeaderValue(event.Headers, types.SEC_WEBSOCKET_PROTOCOL)
	if hasProtocolHeader {
		smuggledCustomHeaders := extractSmuggledCustomHeaders(protocolHeader)
		deviceUuid, ok := common.GetHeaderValue(smuggledCustomHeaders, types.DEVICEUUID)
		if ok {
			return &deviceUuid, nil
		}
	}

	deviceUuid, ok := common.GetHeaderValue(event.Headers, types.DEVICEUUID)
	if !ok {
		return nil, &protocol.PosConnectorError{
			Type:    protocol.UNAUTHORIZED,
			Message: "Invalid deviceUuid",
		}
	}
	return &deviceUuid, nil
}

func ExtractBearerToken(event events.APIGatewayCustomAuthorizerRequestTypeRequest) (*string, *protocol.PosConnectorError) {
	protocolHeader, hasProtocolHeader := common.GetHeaderValue(event.Headers, types.SEC_WEBSOCKET_PROTOCOL)
	if hasProtocolHeader {
		smuggledCustomHeaders := extractSmuggledCustomHeaders(protocolHeader)
		jwtToken, hasAuthHeader := common.GetHeaderValue(smuggledCustomHeaders, types.AUTHORIZATION)
		if hasAuthHeader {
			return &jwtToken, nil
		}
	}

	jwtToken, hasAuthHeader := common.GetHeaderValue(event.Headers, types.AUTHORIZATION)
	if !hasAuthHeader || !strings.HasPrefix(jwtToken, types.BEARER) {
		return nil, &protocol.PosConnectorError{
			Type:    protocol.UNAUTHORIZED,
			Message: "Invalid access token",
		}
	}
	token := strings.TrimPrefix(jwtToken, types.BEARER)
	return &token, nil
}

func extractSmuggledCustomHeaders(protocolHeader string) map[string]string {
	customHeaders := map[string]string{}
	subProtocolValues := strings.Split(protocolHeader, ",")
	if len(subProtocolValues) > 0 {
		for _, subProtocolValue := range subProtocolValues {
			keyValue := strings.Split(strings.Trim(subProtocolValue, " "), "|")
			if len(keyValue) == 2 {
				customHeaders[keyValue[0]] = keyValue[1]
			}
		}
	}
	return customHeaders
}
