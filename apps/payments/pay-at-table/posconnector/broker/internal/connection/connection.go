package connection

import (
	"context"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	dbtypes "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

type IfConnectionManager interface {
	Connect(ctx context.Context, connectionId string, endpoint string, authorizer *types.RequestAuthorizer) error
	Disconnect(ctx context.Context, connectionId string) (*ConnectionModel, error)
	GetConnection(ctx context.Context, connectionId string) (*ConnectionModel, error)
	GetConnectedConnectionsByDevice(ctx context.Context, deviceUuid string, provider string) ([]*ConnectionModel, error)
	GetConnectionWithConsistentRead(ctx context.Context, connectionId string) (*ConnectionModel, error)
}

type IDynamoDB interface {
	GetItem(tableName string) *db.DynamoDb
	Exec(ctx context.Context) (*db.OperationOutput, error)
	InsertItem(ctx context.Context, tableName string, attributes *map[string]dbtypes.AttributeValue, conditionExpression *string) (*dynamodb.PutItemOutput, error)
	Query(tableName string) *db.DynamoDb
	UpdateItem(ctx context.Context, tableName string, key map[string]dbtypes.AttributeValue, item map[string]interface{}, conditionExpression *db.ConditionExpression) error
}

type ConnectionManager struct {
	dbClient IDynamoDB
}

func New(ctx context.Context, dbClient IDynamoDB) IfConnectionManager {
	return &ConnectionManager{dbClient}
}

func (cm *ConnectionManager) GetTtlInHours(authorizer *types.RequestAuthorizer) int64 {
	ttl := common.GetTtl(common.TtlInHours())
	if authorizer.DeviceType == string(types.DeviceTypePOS) && authorizer.Provider == string(types.SDK) {
		ttl = common.GetTtl(common.SdkPosTtlInHours())
	}
	return ttl
}

func (cm *ConnectionManager) Connect(ctx context.Context, connectionId string, endpoint string, authorizer *types.RequestAuthorizer) error {
	logger.Info(ctx, fmt.Sprintf("Create connection %s for device %s", connectionId, authorizer.DeviceUuid))

	ttl := cm.GetTtlInHours(authorizer)

	itemMap := ConnectionModel{
		Id:                 connectionId,
		Status:             string(CONNECTED),
		EntityUuid:         authorizer.EntityUuid,
		CustomerUuid:       authorizer.CustomerUuid,
		DeviceUuid:         authorizer.DeviceUuid,
		SiteUuid:           authorizer.SiteUuid,
		Provider:           authorizer.Provider,
		DeviceType:         authorizer.DeviceType,
		Type:               string(types.CONNECTION_CORE),
		CreatedTime:        time.Now().Format(time.RFC3339),
		ZellerTraceId:      authorizer.ZellerTraceId,
		ConnectionEndpoint: endpoint,
		TTL:                ttl,
	}
	item, _ := attributevalue.MarshalMap(itemMap)
	_, err := cm.dbClient.InsertItem(ctx, common.PairingTableName(), &item, nil)
	if err != nil {
		logger.Error(ctx, err.Error())
		return err
	}
	return nil
}

func (cm *ConnectionManager) Disconnect(ctx context.Context, connectionId string) (*ConnectionModel, error) {
	logger.Info(ctx, fmt.Sprintf("disconnecting connection %s", connectionId))
	connectionItem, err := cm.GetConnection(ctx, connectionId)
	if err != nil {
		logger.Error(ctx, "failed to disconnect "+connectionId)
		logger.Error(ctx, err)
		return nil, err
	}
	if connectionItem == nil {
		logger.Error(ctx, "connection not found "+connectionId)
		return nil, fmt.Errorf("connection not found %s", connectionId)
	}
	key, _ := attributevalue.MarshalMap(map[string]interface{}{
		"id":   connectionId,
		"type": string(types.CONNECTION_CORE),
	})
	err = cm.dbClient.UpdateItem(ctx, common.PairingTableName(), key, map[string]interface{}{
		"status":           string(DISCONNECTED),
		"disconnectedTime": time.Now().Format(time.RFC3339),
	}, nil)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to disconnect connection %s\n", err.Error()))
		return nil, err
	}
	return connectionItem, nil
}

func (cm *ConnectionManager) GetConnection(ctx context.Context, connectionId string) (*ConnectionModel, error) {
	var model ConnectionModel
	err := cm.dbClient.GetItem(common.PairingTableName()).Eq("id", connectionId).Eq("type", types.CONNECTION_CORE).ExecWithOutputStruct(ctx, &model)

	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to get connection %s", connectionId))
		return nil, err
	}

	return &model, nil
}

func (cm *ConnectionManager) GetConnectionWithConsistentRead(ctx context.Context, connectionId string) (*ConnectionModel, error) {
	var queryErr error
	var model *ConnectionModel

	for readAttempt := 1; readAttempt <= 2; readAttempt++ {
		operation := cm.dbClient.GetItem(common.PairingTableName()).Eq("id", connectionId).Eq("type", types.CONNECTION_CORE)

		if readAttempt == 2 {
			operation.Consistent()
		}

		err := operation.ExecWithOutputStruct(ctx, &model)

		if err == nil {
			break
		}

		logger.Error(ctx, fmt.Sprintf("Failed to get connection %s", connectionId))
		queryErr = err

		time.Sleep(time.Millisecond * 250)
	}

	if queryErr != nil {
		return nil, queryErr
	}

	return model, nil
}

func (cm *ConnectionManager) GetConnectedConnectionsByDevice(ctx context.Context, deviceUuid string, provider string) ([]*ConnectionModel, error) {
	output, err := cm.dbClient.Query(common.PairingTableName()).Index("deviceUuidGsi").Eq("type", string(types.CONNECTION_CORE)).
		Eq("deviceUuid", deviceUuid).
		Where().Eq("status", string(CONNECTED)).AndWhere().Eq("provider", provider).Exec(ctx)

	if err != nil {
		logger.Error(ctx, "Error querying connections by device "+err.Error())
		return nil, err
	}

	if len(output.Data) == 0 {
		return nil, nil
	}

	var connections []*ConnectionModel
	for _, connectionRecord := range output.Data {
		conn := cm.mapConnectionItemToConnectionModel(connectionRecord)
		connections = append(connections, &conn)
	}

	return connections, nil
}

func (cm *ConnectionManager) mapConnectionItemToConnectionModel(connectionItem map[string]interface{}) ConnectionModel {
	model := ConnectionModel{
		Id:          connectionItem["id"].(string),
		Type:        connectionItem["type"].(string),
		CreatedTime: connectionItem["createdTime"].(string),
		DeviceType:  connectionItem["deviceType"].(string),
		DeviceUuid:  connectionItem["deviceUuid"].(string),
		EntityUuid:  connectionItem["entityUuid"].(string),
		Provider:    connectionItem["provider"].(string),
		Status:      connectionItem["status"].(string),
	}
	if v, ok := connectionItem["customerUuid"]; ok {
		model.CustomerUuid = v.(string)
	}
	if v, ok := connectionItem["siteUuid"]; ok {
		model.SiteUuid = v.(string)
	}
	if v, ok := connectionItem["disconnectedTime"]; ok {
		model.DisconnectedTime = v.(string)
	}
	if v, ok := connectionItem["updatedTime"]; ok {
		model.UpdatedTime = v.(string)
	}
	if v, ok := connectionItem["zellerTraceId"]; ok {
		model.ZellerTraceId = v.(string)
	}
	if v, ok := connectionItem["connectionEndpoint"]; ok {
		model.ConnectionEndpoint = v.(string)
	}

	return model
}
