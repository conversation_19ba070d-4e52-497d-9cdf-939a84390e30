// package handler implements high level logic to handle websocket event such as connected, disconnected, etc.
package handler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

type Handler struct {
	connectionMgr connection.IfConnectionManager
	protocol      protocol.PosConnectorProtocolIf
	mgrApi        common.ManagementApi
}

func New(ctx context.Context) *Handler {
	var api common.ManagementApi
	a := ctx.Value(common.ManagementApiConfig{})
	if v, ok := a.(common.ManagementApi); ok {
		api = v
	} else {
		api = common.NewManagementApi(ctx)
	}
	db := dynamodb.NewDynamoDb(ctx)
	db.AddSensitiveTableNames(common.DbsSessionTableName(), common.SessionCacheTableName())

	h := Handler{connection.New(ctx, db), protocol.NewFromContext(ctx), api}
	return &h
}

func (h *Handler) HandleConnection(ctx context.Context, event events.APIGatewayWebsocketProxyRequest) (*events.APIGatewayProxyResponse, error) {
	connectionId := getConnectionIdFromRequest(event)
	ctx = logger.AddMetadata(ctx, "connectionId", connectionId)

	authorizer, err := types.NewRequestAuthorizerFromInterface(ctx, event.RequestContext.Authorizer)
	if err != nil {
		logger.Error(ctx, err.Error())
		return &events.APIGatewayProxyResponse{Headers: map[string]string{}, StatusCode: 401}, errors.New("invalid authorizer")
	}

	connections, _ := h.connectionMgr.GetConnectedConnectionsByDevice(ctx, authorizer.DeviceUuid, authorizer.Provider)
	if len(connections) > 0 {
		logger.Warn(ctx, "Multiple active connections found for device "+authorizer.DeviceUuid+" provider "+authorizer.Provider)
		for _, connection := range connections {
			_, err := h.mgrApi.DeleteConnection(ctx, &apigatewaymanagementapi.DeleteConnectionInput{
				ConnectionId: &connection.Id,
			})
			if err != nil {
				logger.Warn(ctx, fmt.Sprintf("Failed to delete connection %s error: %s. Manually disconnecting", connection.Id, err.Error()))
				h.disconnectConnectionAndSubscriberRecords(ctx, connection.Id)
			}
		}
	}

	endpoint := getEndpointFromRequest(event)
	err = h.connectionMgr.Connect(ctx, connectionId, endpoint, authorizer)
	if err != nil {
		logger.Error(ctx, err.Error())
		return &events.APIGatewayProxyResponse{Headers: map[string]string{}, StatusCode: 500}, err
	}

	headers := map[string]string{}

	_, hasProtocolHeader := common.GetHeaderValue(event.Headers, types.SEC_WEBSOCKET_PROTOCOL)
	if hasProtocolHeader {
		headers[types.SEC_WEBSOCKET_PROTOCOL] = "posconnector"
	}

	return &events.APIGatewayProxyResponse{Headers: headers, StatusCode: 200}, nil
}

func (h *Handler) disconnectConnectionAndSubscriberRecords(ctx context.Context, connectionId string) {
	_, err := h.connectionMgr.Disconnect(ctx, connectionId)
	if err != nil {
		logger.Error(ctx, "Failed to disconnect "+connectionId)
	}
	resp := h.protocol.HandleDisconnect(ctx, &protocol.PosConnectorDisconnectRequest{ConnectionId: connectionId})
	if resp.Error != nil {
		logger.Error(ctx, "failed to unsubscribe for connection "+connectionId)
		logger.Error(ctx, resp.Error)
	}
}

func (h *Handler) HandleDisconnection(ctx context.Context, event events.APIGatewayWebsocketProxyRequest) (*events.APIGatewayProxyResponse, error) {
	connectionId := getConnectionIdFromRequest(event)
	ctx = logger.AddMetadata(ctx, "connectionId", connectionId)
	logger.Info(ctx, "Disconnect connection "+connectionId)
	connectionModel, err := h.connectionMgr.Disconnect(ctx, connectionId)
	if err != nil {
		logger.Error(ctx, "Failed to disconnect "+connectionId)
	}
	resp := h.protocol.HandleDisconnect(ctx, &protocol.PosConnectorDisconnectRequest{ConnectionId: connectionModel.Id})
	if resp.Error != nil {
		logger.Error(ctx, "failed to unsubscribe for connection "+connectionId)
		logger.Error(ctx, resp.Error)
		return &events.APIGatewayProxyResponse{Body: "failed to disconnect", StatusCode: 500}, resp.Error
	}
	return &events.APIGatewayProxyResponse{Body: "", StatusCode: 200}, nil
}

func (h *Handler) addEventIdAndActionToLoggerMetadata(ctx context.Context, data map[string]interface{}) context.Context {
	if action, ok := data["action"].(string); ok {
		ctx = logger.AddMetadata(ctx, "action", action)
	}
	if eventId, ok := data["eventId"].(string); ok {
		ctx = logger.AddMetadata(ctx, "eventId", eventId)
	}

	return ctx
}

func (h *Handler) HandleMessage(ctx context.Context, event events.APIGatewayWebsocketProxyRequest) {
	connectionId := getConnectionIdFromRequest(event)
	connectionEndpoint := getEndpointFromRequest(event)
	ctx = logger.AddMetadata(ctx, "connectionId", connectionId)

	data := map[string]interface{}{}
	resp := &protocol.PosConnectorResponse{}
	err := json.Unmarshal([]byte(event.Body), &data)

	ctx = h.addEventIdAndActionToLoggerMetadata(ctx, data)

	logger.Info(ctx, "body:"+event.Body)

	if err != nil {
		logger.Error(ctx, "cant parse request body, invalid request")
		logger.Error(ctx, err)
		resp.Error = &protocol.PosConnectorError{
			Type:    protocol.INVALID_REQUEST,
			Message: "Can't parse request body:" + event.Body,
		}
	} else {
		data["connectionId"] = connectionId
		data["connectionEndpoint"] = connectionEndpoint
		resp = h.protocol.HandleAction(ctx, &data, h.mgrApi)
	}

	if resp != nil && (!common.IsNil(resp.Data) || !common.IsNil(resp.Error)) {
		conn, err := h.mgrApi.GetConnection(ctx, &apigatewaymanagementapi.GetConnectionInput{
			ConnectionId: &connectionId,
		})

		if conn != nil {
			response := resp.Bytes()
			logger.Info(ctx, "get action response:"+string(response))
			_, err := h.mgrApi.PostToConnection(ctx, &apigatewaymanagementapi.PostToConnectionInput{
				ConnectionId: &connectionId,
				Data:         response,
			})
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("failed to post to connection %s", err.Error()))
			}
		} else if err != nil {
			logger.Warn(ctx, fmt.Sprintf("cannot get connection %s: %s", connectionId, err.Error()))
		}
	}
}

func (h *Handler) HandleWebhook(ctx context.Context, event events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	logger.Info(ctx, fmt.Sprintf("receive webhook event: %v", event))
	resp := h.protocol.HandleWebhook(ctx, &event, h.mgrApi)
	statusCode := 200
	var body string
	if resp != nil {
		b, err := json.Marshal(resp)
		if err != nil {
			logger.Error(ctx, err.Error())
			statusCode = 500
		} else {
			body = string(b)
			logger.Info(ctx, fmt.Sprintf("handled webhook action %s", string(body)))
			if resp.Error != nil {
				switch resp.Error.Type {
				case protocol.INVALID_REQUEST:
					statusCode = 400
				case protocol.FORBIDDEN:
					statusCode = 401
				default:
					statusCode = 500
				}
			}
		}
	} else {
		logger.Error(ctx, "get nil response from protocol handler")
		statusCode = 500
	}
	logger.Info(ctx, fmt.Sprintf("response to webhook event %d", statusCode))
	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Body:       body,
	}, nil
}

func getConnectionIdFromRequest(event events.APIGatewayWebsocketProxyRequest) string {
	return event.RequestContext.ConnectionID
}

func getEndpointFromRequest(event events.APIGatewayWebsocketProxyRequest) string {
	endpoint := fmt.Sprintf("https://%s", event.RequestContext.DomainName)

	// ST env, need stage in the endpoint
	if strings.Contains(event.RequestContext.DomainName, event.RequestContext.APIID) {
		endpoint = fmt.Sprintf("https://%s/%s", event.RequestContext.DomainName, event.RequestContext.Stage)
	}

	return endpoint
}
