package handler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/apigatewaymanagementapi"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	coreTypes "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/types"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/test"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

var zellerTraceId = uuid.NewString()
var ctx = context.Background()
var dbClient *db.DynamoDb
var authorizer types.RequestAuthorizer

func init() {
	test.SetTestEnv()
	ctx = test.LoadAwsMockConfig(ctx)
	ctx = context.WithValue(ctx, coreTypes.ZellerTraceId{}, zellerTraceId)
	test.SetupLocalTable(ctx)
	dbClient = db.NewDynamoDb(ctx)
	authorizer = types.RequestAuthorizer{
		EntityUuid:   uuid.NewString(),
		DeviceUuid:   uuid.NewString(),
		CustomerUuid: uuid.NewString(),
		Status:       "VALID",
		Provider:     string(types.HL),
		SiteUuid:     uuid.NewString(),
	}
}

func TestConnectDisconnectHandler(t *testing.T) {
	h := New(ctx)
	connId := uuid.NewString()
	authBytes, _ := json.Marshal(authorizer)
	var authMap map[string]interface{}
	json.Unmarshal(authBytes, &authMap)
	event := events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			ConnectionID: connId,
			Authorizer:   authMap,
		},
	}
	response, err := h.HandleConnection(ctx, event)
	assert.Nil(t, err, err)
	assert.Equal(t, 200, response.StatusCode)
	output, err := dbClient.Query(test.PairingTableName).Eq("id", connId).Exec(ctx)
	assert.Nil(t, err, err)
	assert.Equal(t, connId, output.Data[0]["id"])
	assert.Equal(t, string(connection.CONNECTED), output.Data[0]["status"])
	assert.Equal(t, authorizer.EntityUuid, output.Data[0]["entityUuid"])
	assert.Equal(t, authorizer.DeviceUuid, output.Data[0]["deviceUuid"])
	assert.Equal(t, authorizer.CustomerUuid, output.Data[0]["customerUuid"])

	response, err = h.HandleDisconnection(ctx, event)
	assert.Nil(t, err, err)
	assert.Equal(t, 200, response.StatusCode)
	output, err = dbClient.Query(test.PairingTableName).Eq("id", connId).Exec(ctx)
	assert.Nil(t, err, err)
	assert.Equal(t, connId, output.Data[0]["id"])
	assert.Equal(t, string(connection.DISCONNECTED), output.Data[0]["status"])
}

func TestConnectMultipleConnections(t *testing.T) {
	defer func() {
		authorizer.Provider = string(types.HL)
	}()
	h := New(ctx)
	mgrMock := common.MockManagementApi{}
	h.mgrApi = &mgrMock

	for _, provider := range []string{
		string(types.ORACLE),
		string(types.HL),
	} {
		t.Run(provider, func(t *testing.T) {
			connId := uuid.NewString()
			authorizer.Provider = provider
			authBytes, _ := json.Marshal(authorizer)
			var authMap map[string]interface{}
			json.Unmarshal(authBytes, &authMap)
			event := events.APIGatewayWebsocketProxyRequest{
				RequestContext: events.APIGatewayWebsocketProxyRequestContext{
					ConnectionID: connId,
					Authorizer:   authMap,
				},
			}
			response, err := h.HandleConnection(ctx, event)
			assert.Nil(t, err, err)
			assert.Equal(t, 200, response.StatusCode)
			output, err := dbClient.Query(test.PairingTableName).Eq("id", connId).Exec(ctx)
			fmt.Println(utils.BestEffortStringify(output, true))
			assert.Nil(t, err, err)
			assert.Equal(t, connId, output.Data[0]["id"])
			assert.Equal(t, string(connection.CONNECTED), output.Data[0]["status"])
			assert.Equal(t, authorizer.EntityUuid, output.Data[0]["entityUuid"])
			assert.Equal(t, authorizer.DeviceUuid, output.Data[0]["deviceUuid"])
			assert.Equal(t, authorizer.CustomerUuid, output.Data[0]["customerUuid"])
			assert.Equal(t, authorizer.Provider, output.Data[0]["provider"])

			newConnectionId := uuid.NewString()
			event.RequestContext.ConnectionID = newConnectionId
			mgrMock.On("DeleteConnection", mock.Anything, &apigatewaymanagementapi.DeleteConnectionInput{ConnectionId: aws.String(connId)}, mock.Anything).Return(&apigatewaymanagementapi.DeleteConnectionOutput{}, nil)

			response, err = h.HandleConnection(ctx, event)
			assert.Nil(t, err, err)
			assert.Equal(t, 200, response.StatusCode)

			mgrMock.AssertExpectations(t)
		})
	}
}

func TestConnectMultipleConnectionsManualDisconnectOnDeleteError(t *testing.T) {
	defer func() {
		authorizer.Provider = string(types.HL)
	}()
	h := New(ctx)
	mgrMock := common.MockManagementApi{}
	h.mgrApi = &mgrMock

	for _, provider := range []string{
		string(types.ORACLE),
		string(types.HL),
	} {
		t.Run(provider, func(t *testing.T) {
			connId := uuid.NewString()
			auth := types.RequestAuthorizer{
				EntityUuid:   uuid.NewString(),
				DeviceUuid:   uuid.NewString(),
				CustomerUuid: uuid.NewString(),
				Status:       "VALID",
				Provider:     string(types.HL),
				SiteUuid:     uuid.NewString(),
			}
			authBytes, _ := json.Marshal(auth)
			var authMap map[string]interface{}
			json.Unmarshal(authBytes, &authMap)
			event := events.APIGatewayWebsocketProxyRequest{
				RequestContext: events.APIGatewayWebsocketProxyRequestContext{
					ConnectionID: connId,
					Authorizer:   authMap,
				},
			}
			response, err := h.HandleConnection(ctx, event)
			assert.Nil(t, err, err)
			assert.Equal(t, 200, response.StatusCode)
			output, err := dbClient.Query(test.PairingTableName).Eq("id", connId).Exec(ctx)
			fmt.Println(utils.BestEffortStringify(output, true))
			assert.Nil(t, err, err)
			assert.Equal(t, connId, output.Data[0]["id"])
			assert.Equal(t, string(connection.CONNECTED), output.Data[0]["status"])
			assert.Equal(t, auth.EntityUuid, output.Data[0]["entityUuid"])
			assert.Equal(t, auth.DeviceUuid, output.Data[0]["deviceUuid"])
			assert.Equal(t, auth.CustomerUuid, output.Data[0]["customerUuid"])
			assert.Equal(t, auth.Provider, output.Data[0]["provider"])

			subscriber := map[string]string{
				"id":           uuid.NewString(),
				"type":         fmt.Sprintf("%s%s.%s", types.SUBSCRIBER, auth.SiteUuid, "subscribeOrderUpdates"),
				"status":       string(connection.CONNECTED),
				"entityUuid":   auth.EntityUuid,
				"deviceUuid":   auth.DeviceUuid,
				"connectionId": connId,
				"action":       "subscribeOrderUpdates",
			}
			item, _ := attributevalue.MarshalMap(subscriber)
			dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

			newConnectionId := uuid.NewString()
			event.RequestContext.ConnectionID = newConnectionId
			mgrMock.On("DeleteConnection", mock.Anything, &apigatewaymanagementapi.DeleteConnectionInput{ConnectionId: aws.String(connId)}, mock.Anything).Return(nil, errors.New("410 Gone Exception"))

			response, err = h.HandleConnection(ctx, event)
			assert.Nil(t, err, err)
			assert.Equal(t, 200, response.StatusCode)

			mgrMock.AssertExpectations(t)

			//	Check old connection and subscriber is disconnected.
			output, err = dbClient.Query(test.PairingTableName).Eq("id", connId).Exec(ctx)
			assert.Nil(t, err, err)
			assert.Equal(t, connId, output.Data[0]["id"])
			assert.Equal(t, auth.DeviceUuid, output.Data[0]["deviceUuid"])
			assert.Equal(t, string(connection.DISCONNECTED), output.Data[0]["status"])
			assert.GreaterOrEqual(t, int(output.Data[0]["ttl"].(float64)), 0)

			output, err = dbClient.Query(test.PairingTableName).Index("connectionIdGsi").Eq("connectionId", connId).Begin("type", types.SUBSCRIBER).Exec(ctx)
			assert.Nil(t, err, err)
			assert.Equal(t, connId, output.Data[0]["connectionId"])
			assert.Equal(t, string(connection.DISCONNECTED), output.Data[0]["status"])
			assert.Equal(t, auth.DeviceUuid, output.Data[0]["deviceUuid"])
			assert.GreaterOrEqual(t, int(output.Data[0]["ttl"].(float64)), 0)
		})
	}
}

func TestNoAuthorizerConnection(t *testing.T) {
	h := New(ctx)
	connId := uuid.NewString()
	event := events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			ConnectionID: connId,
		},
	}
	response, err := h.HandleConnection(ctx, event)
	assert.NotNil(t, err, err)
	assert.Equal(t, 401, response.StatusCode)
}

func TestSubscriptionResponse(t *testing.T) {
	h := New(ctx)
	m := protocol.MockPosInterface{}
	resp := &protocol.PosConnectorResponse{Error: nil, EventId: "", Timestamp: "", Data: map[string]string{"action": string(protocol.SUB_ORDERS_UPDATE)}}
	m.On("HandleAction", mock.Anything, mock.Anything).Return(resp)
	respB, _ := json.Marshal(resp)
	h.protocol = &m
	mgrMock := common.MockManagementApi{}
	connId := uuid.NewString()
	mgrMock.On("GetConnection", mock.Anything, &apigatewaymanagementapi.GetConnectionInput{
		ConnectionId: aws.String(connId),
	}).Return(&apigatewaymanagementapi.GetConnectionOutput{}, nil)
	mgrMock.On("PostToConnection", mock.Anything, &apigatewaymanagementapi.PostToConnectionInput{
		ConnectionId: aws.String(connId),
		Data:         respB,
	}).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	h.mgrApi = &mgrMock
	h.HandleMessage(ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			ConnectionID: connId,
			Authorizer:   map[string]interface{}{"entityUuid": uuid.NewString(), "deviceUuid": uuid.NewString(), "customerUuid": uuid.NewString(), "status": "VALID", "provider": string(types.HL), "siteUuid": uuid.NewString()},
		},
		Body: "{}",
	})
	mgrMock.AssertExpectations(t)
	m.AssertExpectations(t)
}

func TestInvalidBody(t *testing.T) {
	connId := uuid.NewString()
	h := New(ctx)
	mgrMock := common.MockManagementApi{}
	h.mgrApi = &mgrMock

	resp := &protocol.PosConnectorResponse{Error: &protocol.PosConnectorError{Type: protocol.INVALID_REQUEST, Message: "Can't parse request body:not a json"}}
	respB, _ := json.Marshal(resp)
	mgrMock.On("GetConnection", mock.Anything, &apigatewaymanagementapi.GetConnectionInput{
		ConnectionId: aws.String(connId),
	}).Return(&apigatewaymanagementapi.GetConnectionOutput{}, nil)
	mgrMock.On("PostToConnection", mock.Anything, &apigatewaymanagementapi.PostToConnectionInput{
		ConnectionId: aws.String(connId),
		Data:         respB,
	}).Return(&apigatewaymanagementapi.PostToConnectionOutput{}, nil)
	h.HandleMessage(ctx, events.APIGatewayWebsocketProxyRequest{
		Body:           "not a json",
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{ConnectionID: connId},
	})
	mgrMock.AssertExpectations(t)
}

func TestConnectionIdInvalid(t *testing.T) {
	connId := uuid.NewString()
	h := New(ctx)
	mgrMock := common.MockManagementApi{}
	h.mgrApi = &mgrMock

	mgrMock.On("GetConnection", mock.Anything, &apigatewaymanagementapi.GetConnectionInput{
		ConnectionId: aws.String(connId),
	}, mock.Anything).Return(nil, errors.New("GoneException"))
	h.HandleMessage(ctx, events.APIGatewayWebsocketProxyRequest{
		Body:           "{}",
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{ConnectionID: connId},
	})
	mgrMock.AssertExpectations(t)
}

func TestSubscriptionFailed(t *testing.T) {
	h := New(ctx)

	connId := uuid.NewString()
	cm := map[string]string{
		"id":          connId,
		"type":        string(types.CONNECTION_CORE),
		"status":      string(connection.CONNECTED),
		"entityUuid":  uuid.NewString(),
		"deviceUuid":  uuid.NewString(),
		"provider":    uuid.NewString(),
		"deviceType":  string(types.DeviceTypeTerminal),
		"createdTime": time.Now().UTC().Format(time.RFC3339),
	}
	item, _ := attributevalue.MarshalMap(cm)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	m := protocol.MockPosInterface{}
	m.On("HandleDisconnect", mock.Anything, mock.Anything).Return(&protocol.PosConnectorResponse{Error: &protocol.PosConnectorError{Type: protocol.INVALID_REQUEST}})
	h.protocol = &m
	_, err := h.HandleDisconnection(ctx, events.APIGatewayWebsocketProxyRequest{RequestContext: events.APIGatewayWebsocketProxyRequestContext{ConnectionID: connId}})
	assert.NotNil(t, err, err)
}

func TestHandleMessage(t *testing.T) {
	h := New(ctx)
	connId := uuid.NewString()
	bodyMaps := []map[string]interface{}{
		{"Type": "connection_init", "eventId": " ", "action": " ", "timestamp": ""},
	}
	m := protocol.MockPosInterface{}
	m.On("HandleAction", mock.Anything, mock.Anything).Return(&protocol.PosConnectorResponse{Error: nil})
	h.protocol = &m
	for _, bodyMap := range bodyMaps {
		body, _ := json.Marshal(bodyMap)
		h.HandleMessage(ctx, events.APIGatewayWebsocketProxyRequest{
			RequestContext: events.APIGatewayWebsocketProxyRequestContext{
				EventType:    "MESSAGE",
				ConnectionID: connId,
			},
			Body: string(body),
		})
	}
}

func TestMessageHandlerFailure(t *testing.T) {
	h := New(ctx)
	h.HandleMessage(ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: "test",
		},
		Body: "invalid body",
	})
}

func TestHandleProtocolAction(t *testing.T) {
	m := protocol.MockPosInterface{}
	var da map[string]string
	fmt.Println(da == nil)
	m.On("HandleAction", mock.Anything, mock.Anything).Return(&protocol.PosConnectorResponse{
		EventId:   uuid.NewString(),
		Action:    "purchase",
		Timestamp: uuid.NewString(),
		Data:      nil,
	})
	h := New(ctx)
	h.protocol = &m
	body := map[string]interface{}{
		"action":    string(protocol.PURCHASE),
		"timestamp": "",
		"eventId":   "",
		"data": map[string]string{
			"sessionUuid":     "46c06931-df17-4665-be0b-599f2b2ed4f5",
			"transactionUuid": "{{$randomUUID}}",
			"type":            "PURCHASE",
			"status":          "PROCESSING",
			"terminalEvent":   "TipEntryShown",
		},
	}
	b, _ := json.Marshal(body)
	h.HandleMessage(ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: "test",
		},
		Body: string(b),
	})
}

func TestHandleAction(t *testing.T) {
	m := protocol.MockPosInterface{}
	m.On("HandleAction", mock.Anything, mock.Anything).Return(&protocol.PosConnectorResponse{})
	h := New(ctx)
	h.protocol = &m
	h.HandleMessage(ctx, events.APIGatewayWebsocketProxyRequest{
		RequestContext: events.APIGatewayWebsocketProxyRequestContext{
			EventType:    "MESSAGE",
			ConnectionID: "test",
		},
		Body: "{}",
	})
}

func TestHandleWebhookNilValue(t *testing.T) {
	m := &protocol.MockPosInterface{}
	m.On("HandleWebhook", mock.Anything, &events.APIGatewayProxyRequest{}, mock.Anything).Return(nil)
	h := New(ctx)
	h.protocol = m
	resp, err := h.HandleWebhook(ctx, events.APIGatewayProxyRequest{})
	assert.Nil(t, err, err)
	assert.Equal(t, 500, resp.StatusCode)
}

func TestHandleWebhookValidResponse(t *testing.T) {
	m := &protocol.MockPosInterface{}
	m.On("HandleWebhook", ctx, &events.APIGatewayProxyRequest{}, mock.Anything).Return(&protocol.PosConnectorResponse{})
	h := New(ctx)
	h.protocol = m
	resp, err := h.HandleWebhook(ctx, events.APIGatewayProxyRequest{})
	assert.Nil(t, err, err)
	assert.Equal(t, 200, resp.StatusCode)
}

func TestHandleWebhookInvalidRequest(t *testing.T) {
	m := &protocol.MockPosInterface{}
	m.On("HandleWebhook", ctx, &events.APIGatewayProxyRequest{}, mock.Anything).Return(&protocol.PosConnectorResponse{Error: &protocol.PosConnectorError{Type: protocol.INVALID_REQUEST}})
	h := New(ctx)
	h.protocol = m
	resp, err := h.HandleWebhook(ctx, events.APIGatewayProxyRequest{})
	assert.Nil(t, err, err)
	assert.Equal(t, 400, resp.StatusCode)
}

func TestHandleWebhookForbiddenRequest(t *testing.T) {
	m := &protocol.MockPosInterface{}
	m.On("HandleWebhook", ctx, &events.APIGatewayProxyRequest{}, mock.Anything).Return(&protocol.PosConnectorResponse{Error: &protocol.PosConnectorError{Type: protocol.FORBIDDEN}})
	h := New(ctx)
	h.protocol = m
	resp, err := h.HandleWebhook(ctx, events.APIGatewayProxyRequest{})
	assert.Nil(t, err, err)
	assert.Equal(t, 401, resp.StatusCode)
}

func TestHandleWebhookServerError(t *testing.T) {
	m := &protocol.MockPosInterface{}
	m.On("HandleWebhook", ctx, &events.APIGatewayProxyRequest{}, mock.Anything).Return(&protocol.PosConnectorResponse{Error: &protocol.PosConnectorError{Type: protocol.SERVER_ERROR}})
	h := New(ctx)
	h.protocol = m
	resp, err := h.HandleWebhook(ctx, events.APIGatewayProxyRequest{})
	assert.Nil(t, err, err)
	assert.Equal(t, 500, resp.StatusCode)
}
