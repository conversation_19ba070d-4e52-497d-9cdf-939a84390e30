package handler

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/auth"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/test"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

var aud []string
var hour = time.Hour
var negativeHour = -time.Hour

var tokenSet = map[string]interface{}{
	"access_token": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
	"scope":        "read:users read:users_app_metadata",
	"expires_in":   123312312321,
	"token_type":   "Bearer",
}
var auth0Email = "<EMAIL>"

func init() {
	test.SetTestEnv()
	aud = []string{common.DbsAuth0Audience()}
	ctx = test.LoadAwsMockConfig(context.Background())
	test.SetupLocalTable(ctx)
}

func TestDeviceAHNoToken(t *testing.T) {
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{}
	_, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Equal(t, "Unauthorized", err.Error())
}

func TestDeviceAHInvalidToken(t *testing.T) {
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": "invalid",
		},
	}
	_, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Equal(t, "Unauthorized", err.Error())
}

func TestDeviceAHExpiredToken(t *testing.T) {
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + test.GenerateAccessToken(&negativeHour, aud, ptr.String(uuid.NewString()), nil),
		},
	}
	_, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Equal(t, "Unauthorized", err.Error())
}

func TestDeviceAHNoAud(t *testing.T) {
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + test.GenerateAccessToken(&hour, nil, ptr.String(uuid.NewString()), nil),
		},
	}
	_, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Equal(t, "Unauthorized", err.Error())
}

func TestDeviceAHTokenNotInDbs(t *testing.T) {
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + test.GenerateAccessToken(&hour, aud, ptr.String(uuid.NewString()), nil),
		},
	}
	_, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Equal(t, "Unauthorized", err.Error())
}

func TestDeviceAHValidTokenHL(t *testing.T) {
	dbClient := db.NewDynamoDb(ctx)
	token := test.GenerateAccessToken(&hour, []string{common.DbsAuth0Audience()}, ptr.String(uuid.NewString()), nil)
	data := map[string]string{
		"entityUuid":  uuid.NewString(),
		"accessToken": token,
		"id":          uuid.NewString(),
		"status":      "ACTIVE",
		"type":        "deviceUuid",
	}
	item, _ := attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, test.DbsSessionCacheTable, &item, nil)
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
		},
	}
	pairingData := map[string]string{
		"id":       uuid.NewString(),
		"type":     fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_DEVICE, data["id"]),
		"siteUuid": uuid.NewString(),
		"provider": string(types.HL),
	}
	item, _ = attributevalue.MarshalMap(&pairingData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Equal(t, map[string]interface{}{
		"entityUuid":    data["entityUuid"],
		"deviceUuid":    data["id"],
		"status":        data["status"],
		"siteUuid":      pairingData["siteUuid"],
		"provider":      pairingData["provider"],
		"customerUuid":  nil,
		"role":          nil,
		"deviceType":    string(types.DeviceTypeTerminal),
		"zellerTraceId": zellerTraceId,
	}, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Allow).String(), statement.Effect)
	}
}

func TestDeviceAHValidTokenOracle(t *testing.T) {
	dbClient := db.NewDynamoDb(ctx)
	token := test.GenerateAccessToken(&hour, []string{common.DbsAuth0Audience()}, ptr.String(uuid.NewString()), nil)
	data := map[string]string{
		"entityUuid":  uuid.NewString(),
		"accessToken": token,
		"id":          uuid.NewString(),
		"status":      "ACTIVE",
		"type":        "deviceUuid",
	}
	item, _ := attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, test.DbsSessionCacheTable, &item, nil)
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
		},
	}
	pairingData := map[string]string{
		"id":         uuid.NewString(),
		"type":       fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_DEVICE, data["id"]),
		"deviceUuid": data["id"],
		"provider":   string(types.ORACLE),
	}
	item, _ = attributevalue.MarshalMap(&pairingData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Equal(t, map[string]interface{}{
		"entityUuid":    data["entityUuid"],
		"deviceUuid":    data["id"],
		"status":        data["status"],
		"provider":      pairingData["provider"],
		"siteUuid":      nil,
		"customerUuid":  nil,
		"role":          nil,
		"deviceType":    string(types.DeviceTypeTerminal),
		"zellerTraceId": zellerTraceId,
	}, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Allow).String(), statement.Effect)
	}
}

func TestDeviceAHMissingDevicePairing(t *testing.T) {
	dbClient := db.NewDynamoDb(ctx)
	token := test.GenerateAccessToken(&hour, aud, ptr.String(uuid.NewString()), nil)
	data := map[string]string{
		"entityUuid":  uuid.NewString(),
		"accessToken": token,
		"id":          uuid.NewString(),
		"status":      "ACTIVE",
		"type":        "deviceUuid",
	}
	item, _ := attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, test.DbsSessionCacheTable, &item, nil)
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
		},
	}
	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Nil(t, resp.Context, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Deny).String(), statement.Effect)
	}
}

func TestDeviceAHMissingSiteUuidInPairing(t *testing.T) {
	dbClient := db.NewDynamoDb(ctx)
	token := test.GenerateAccessToken(&hour, aud, ptr.String(uuid.NewString()), nil)
	data := map[string]string{
		"entityUuid":  uuid.NewString(),
		"accessToken": token,
		"id":          uuid.NewString(),
		"status":      "ACTIVE",
		"type":        "deviceUuid",
	}
	item, _ := attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, test.DbsSessionCacheTable, &item, nil)
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
		},
	}
	pairingData := map[string]string{
		"id":   uuid.NewString(),
		"type": fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_DEVICE, data["id"]),
	}
	item, _ = attributevalue.MarshalMap(&pairingData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Nil(t, resp.Context, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Deny).String(), statement.Effect)
	}
}

func TestDeviceAHMissingCacheDbField(t *testing.T) {
	dbClient := db.NewDynamoDb(ctx)
	token := test.GenerateAccessToken(&hour, aud, ptr.String(uuid.NewString()), nil)
	// missing entityUuid
	data := map[string]string{
		"accessToken": token,
		"id":          uuid.NewString(),
		"status":      "ACTIVE",
		"type":        "deviceUuid",
	}
	item, _ := attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, test.DbsSessionCacheTable, &item, nil)
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
		},
	}
	_, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Equal(t, "Unauthorized", err.Error())

	// missing access token
	data = map[string]string{
		"entityUuid": token,
		"id":         uuid.NewString(),
		"status":     "ACTIVE",
		"type":       "deviceUuid",
	}
	item, _ = attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, test.DbsSessionCacheTable, &item, nil)
	event = events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
		},
	}
	_, err = h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Equal(t, "Unauthorized", err.Error())

	// missing status
	data = map[string]string{
		"entityUuid":  token,
		"id":          uuid.NewString(),
		"accessToken": uuid.NewString(),
		"type":        "deviceUuid",
	}
	item, _ = attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, test.DbsSessionCacheTable, &item, nil)
	event = events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
		},
	}
	_, err = h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Equal(t, "Unauthorized", err.Error())
}

func TestDeviceAHDeviceDeleted(t *testing.T) {
	dbClient := db.NewDynamoDb(ctx)
	token := test.GenerateAccessToken(&hour, aud, ptr.String(uuid.NewString()), nil)
	data := map[string]string{
		"entityUuid":   uuid.NewString(),
		"accessToken":  token,
		"id":           uuid.NewString(),
		"customerUuid": uuid.NewString(),
		"status":       "DELETED",
		"type":         "deviceUuid",
	}
	item, _ := attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, test.DbsSessionCacheTable, &item, nil)
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
		},
	}
	_, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Equal(t, "Unauthorized", err.Error())
}

func TestDeviceAHNoDeviceUuidHeader(t *testing.T) {
	token := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), nil)
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
		},
	}
	_, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Equal(t, "Unauthorized", err.Error())
}

func TestDeviceAHUnknownAudience(t *testing.T) {
	token := test.GenerateAccessToken(&hour, []string{uuid.NewString()}, ptr.String(uuid.NewString()), nil)
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
		},
	}
	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.NotNil(t, err, err)
	assert.Equal(t, "Unauthorized", err.Error())
	assert.Nil(t, resp.Context, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Deny).String(), statement.Effect)
	}
}

func TestDeviceAHPosconnectorSessionDeviceDeleted(t *testing.T) {
	token := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), nil)
	data := map[string]string{
		"entityUuid":   uuid.NewString(),
		"accessToken":  token,
		"id":           uuid.NewString(),
		"customerUuid": uuid.NewString(),
		"status":       "DELETED",
		"type":         "deviceUuid",
	}
	item, _ := attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, test.PosconnectorSessionCacheTable, &item, nil)
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
			"DeviceUuid":    data["id"],
		},
	}
	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Nil(t, resp.Context, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Deny).String(), statement.Effect)
	}
}

func TestDeviceAHPosconnectorSessionDeviceUuidMismatch(t *testing.T) {
	token := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), nil)
	data := map[string]string{
		"entityUuid":   uuid.NewString(),
		"accessToken":  token,
		"id":           uuid.NewString(),
		"customerUuid": uuid.NewString(),
		"status":       "ACTIVE",
		"type":         "deviceUuid",
	}
	item, _ := attributevalue.MarshalMap(&data)
	dbClient.InsertItem(ctx, test.PosconnectorSessionCacheTable, &item, nil)
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
			"DeviceUuid":    uuid.NewString(),
		},
	}
	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Nil(t, resp.Context, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Deny).String(), statement.Effect)
	}
}

func TestDeviceAHPosconnectorExistingSession(t *testing.T) {
	token := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), nil)
	sessionData := map[string]string{
		"entityUuid":   uuid.NewString(),
		"accessToken":  token,
		"id":           uuid.NewString(),
		"customerUuid": uuid.NewString(),
		"status":       "ACTIVE",
		"type":         "deviceUuid",
	}
	item, _ := attributevalue.MarshalMap(&sessionData)
	dbClient.InsertItem(ctx, test.PosconnectorSessionCacheTable, &item, nil)
	pairingData := map[string]string{
		"id":         uuid.NewString(),
		"type":       fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, sessionData["id"]),
		"entityUuid": sessionData["entityUuid"],
		"deviceUuid": sessionData["id"],
		"provider":   string(types.IMPOS),
	}
	item, _ = attributevalue.MarshalMap(&pairingData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
			"DeviceUuid":    sessionData["id"],
		},
	}
	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Equal(t, map[string]interface{}{
		"entityUuid":    sessionData["entityUuid"],
		"deviceUuid":    sessionData["id"],
		"status":        sessionData["status"],
		"siteUuid":      nil,
		"provider":      pairingData["provider"],
		"customerUuid":  nil,
		"role":          nil,
		"deviceType":    string(types.DeviceTypePOS),
		"zellerTraceId": zellerTraceId,
	}, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Allow).String(), statement.Effect)
	}
}

func TestDeviceAHPosconnectorExistingSessionSmuggledHeaders(t *testing.T) {
	token := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), nil)
	sessionData := map[string]string{
		"entityUuid":   uuid.NewString(),
		"accessToken":  token,
		"id":           uuid.NewString(),
		"customerUuid": uuid.NewString(),
		"status":       "ACTIVE",
		"type":         "deviceUuid",
	}
	item, _ := attributevalue.MarshalMap(&sessionData)
	dbClient.InsertItem(ctx, test.PosconnectorSessionCacheTable, &item, nil)
	pairingData := map[string]string{
		"id":         uuid.NewString(),
		"type":       fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, sessionData["id"]),
		"entityUuid": sessionData["entityUuid"],
		"deviceUuid": sessionData["id"],
		"provider":   string(types.IMPOS),
	}
	item, _ = attributevalue.MarshalMap(&pairingData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)
	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			types.SEC_WEBSOCKET_PROTOCOL: fmt.Sprintf("%s|%s, %s|%s", types.AUTHORIZATION, token, types.DEVICEUUID, sessionData["id"]),
		},
	}
	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Equal(t, map[string]interface{}{
		"entityUuid":    sessionData["entityUuid"],
		"deviceUuid":    sessionData["id"],
		"status":        sessionData["status"],
		"siteUuid":      nil,
		"provider":      pairingData["provider"],
		"customerUuid":  nil,
		"role":          nil,
		"deviceType":    string(types.DeviceTypePOS),
		"zellerTraceId": zellerTraceId,
	}, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Allow).String(), statement.Effect)
	}
}

func TestDeviceAHPosconnectorExistingSessionNoDevicePairing(t *testing.T) {
	entityUuid := uuid.NewString()
	token := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), nil)
	sessionData := map[string]string{
		"entityUuid":   entityUuid,
		"accessToken":  token,
		"id":           uuid.NewString(),
		"customerUuid": uuid.NewString(),
		"status":       "ACTIVE",
		"type":         "deviceUuid",
	}
	item, _ := attributevalue.MarshalMap(&sessionData)
	dbClient.InsertItem(ctx, test.PosconnectorSessionCacheTable, &item, nil)

	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
			"DeviceUuid":    sessionData["id"],
		},
	}
	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Equal(t, resp.Context, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Deny).String(), statement.Effect)
	}
}

func TestDeviceAHPosconnectorExistingSessionDevicePairingMismatchEntityUuid(t *testing.T) {
	token := test.GenerateAccessToken(&hour, []string{common.SdkAuth0Audience()}, ptr.String(uuid.NewString()), nil)
	sessionEntityUuid := uuid.NewString()
	pairingEntityUuid := uuid.NewString()
	sessionData := map[string]string{
		"entityUuid":   sessionEntityUuid,
		"accessToken":  token,
		"id":           uuid.NewString(),
		"customerUuid": uuid.NewString(),
		"status":       "ACTIVE",
		"type":         "deviceUuid",
	}
	item, _ := attributevalue.MarshalMap(&sessionData)
	dbClient.InsertItem(ctx, test.PosconnectorSessionCacheTable, &item, nil)

	pairingData := map[string]string{
		"id":         uuid.NewString(),
		"type":       fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_POS, sessionData["id"]),
		"entityUuid": pairingEntityUuid,
		"deviceUuid": sessionData["id"],
		"provider":   string(types.IMPOS),
	}
	item, _ = attributevalue.MarshalMap(&pairingData)
	dbClient.InsertItem(ctx, test.PairingTableName, &item, nil)

	h := New(ctx)
	event := events.APIGatewayCustomAuthorizerRequestTypeRequest{
		Headers: map[string]string{
			"Authorization": types.BEARER + token,
			"DeviceUuid":    sessionData["id"],
		},
	}
	resp, err := h.DeviceAuthorizerHandler(ctx, event)
	assert.Nil(t, err, err)
	assert.Equal(t, map[string]interface{}{
		"entityUuid":    sessionEntityUuid,
		"deviceUuid":    sessionData["id"],
		"status":        sessionData["status"],
		"siteUuid":      nil,
		"provider":      pairingData["provider"],
		"customerUuid":  nil,
		"role":          nil,
		"deviceType":    string(types.DeviceTypePOS),
		"zellerTraceId": zellerTraceId,
	}, resp.Context)
	for _, statement := range resp.PolicyDocument.Statement {
		assert.Equal(t, auth.Effect(auth.Allow).String(), statement.Effect)
	}
}
