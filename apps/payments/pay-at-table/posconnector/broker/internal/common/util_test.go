package common

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestCheckRequiredFields(t *testing.T) {
	data := map[string]interface{}{
		"entityUuid": uuid.New().String(),
		"deviceUuid": uuid.New().String(),
	}
	err := CheckRequiredFields(data, []string{"entityUuid", "deviceUuid"})
	assert.Nil(t, err, err)

	err = CheckRequiredFields(data, []string{"deviceUuid"})
	assert.Nil(t, err, err)

	err = CheckRequiredFields(data, []string{"customerUuid"})
	assert.NotNil(t, err, err)
}

func TestGetHeaderValue(t *testing.T) {
	tests := []struct {
		name      string
		headers   map[string]string
		key       string
		wantValue string
		wantOk    bool
	}{
		{
			name: "Exact match",
			headers: map[string]string{
				"Authorization": "Bearer abc",
			},
			key:       "Authorization",
			wantValue: "Bearer abc",
			wantOk:    true,
		},
		{
			name: "Case insensitive match - lower",
			headers: map[string]string{
				"authorization": "Bearer xyz",
			},
			key:       "Authorization",
			wantValue: "Bearer xyz",
			wantOk:    true,
		},
		{
			name: "Case insensitive match - upper",
			headers: map[string]string{
				"AUTHORIZATION": "Bearer 123",
			},
			key:       "authorization",
			wantValue: "Bearer 123",
			wantOk:    true,
		},
		{
			name: "Header not found",
			headers: map[string]string{
				"X-Other": "value",
			},
			key:       "Authorization",
			wantValue: "",
			wantOk:    false,
		},
		{
			name: "Empty value",
			headers: map[string]string{
				"Authorization": "",
			},
			key:       "Authorization",
			wantValue: "",
			wantOk:    false,
		},
		{
			name:      "No headers",
			headers:   map[string]string{},
			key:       "Authorization",
			wantValue: "",
			wantOk:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			val, ok := GetHeaderValue(tt.headers, tt.key)
			assert.Equal(t, tt.wantValue, val)
			assert.Equal(t, tt.wantOk, ok)
		})
	}
}
