package protocol

import (
	"context"
	"encoding/json"
	"fmt"

	"slices"

	"github.com/aws/smithy-go/ptr"
	"github.com/go-playground/validator/v10"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

type ActionType string

const (
	GET_ORDERS                  ActionType = "getOrders"
	GET_ORDER                   ActionType = "getOrder"
	SUB_ORDERS_UPDATE           ActionType = "subscribeOrdersUpdate"
	UNSUB_ORDERS_UPDATE         ActionType = "unsubscribeOrdersUpdate"
	GET_POS_CONNECTION_STATUS   ActionType = "getPosConnectionStatus"
	SUB_POS_CONNECTION_STATUS   ActionType = "subscribePosConnectionStatus"
	UNSUB_POS_CONNECTION_STATUS ActionType = "unsubscribePosConnectionStatus"
	PING                        ActionType = "ping"
	PAYMENT                     ActionType = "payment"
	UPDATE_ORDER_ITEM           ActionType = "updateOrderItem"
	PURCHASE                    ActionType = "purchase"
	REFUND                      ActionType = "refund"
	REVERSAL                    ActionType = "reversal"
	GET_PAIRED_DEVICES          ActionType = "getPairedDevices"
	CANCEL                      ActionType = "cancel"
	TRANSACTION_EVENT           ActionType = "transactionEvent"
	SIGNATURE_VERIFICATION      ActionType = "signatureVerification"
)

type PaymentStatus string

const (
	SUCCESS      PaymentStatus = "SUCCESS"
	FAIL         PaymentStatus = "FAIL"
	ACKNOWLEDGED PaymentStatus = "ACKNOWLEDGED"
)

type PosConnectorByter interface {
	Bytes() []byte
}

type PosConnectorRequest struct {
	Action             string             `json:"action" dynamodbav:"action" validate:"required"`
	EventId            string             `json:"eventId" dynamodbav:"eventId" validate:"required"`
	Timestamp          string             `json:"timestamp" dynamodbav:"timestamp" validate:"required"`
	Data               interface{}        `json:"data" dynamodbav:"data,omitempty"`
	Error              *PosConnectorError `json:"error" dynamodbav:"error,omitempty"`
	Provider           string             `json:"provider,omitempty" dynamodbav:"provider" validate:"required"`
	DeviceType         string             `json:"deviceType,omitempty" dynamodbav:"deviceType"`
	EntityUuid         string             `json:"entityUuid,omitempty" dynamodbav:"entityUuid"`
	SiteUuid           string             `json:"siteUuid,omitempty" dynamodbav:"siteUuid"`
	Locations          []string           `json:"locations,omitempty" dynamodbav:"locations,omitempty"`
	DeviceUuid         string             `json:"deviceUuid,omitempty" dynamodbav:"deviceUuid"`                         // The device that sent the message - sender
	PaymentDeviceUuid  string             `json:"paymentDeviceUuid,omitempty" dynamodbav:"paymentDeviceUuid,omitempty"` // Terminal, other payment devices
	PosDeviceUuid      string             `json:"posDeviceUuid,omitempty" dynamodbav:"posDeviceUuid,omitempty"`         // POS
	PairedDeviceId     string             `json:"pairedDeviceId,omitempty" dynamodbav:"pairedDeviceId,omitempty"`       // The device the sender is paired with - receiver
	VenueId            string             `json:"venueId,omitempty" dynamodbav:"venueId,omitempty"`                     // The venue id
	CustomerUuid       string             `json:"customerUuid,omitempty" dynamodbav:"customerUuid,omitempty"`
	ConnectionId       string             `json:"connectionId,omitempty" dynamodbav:"connectionId"`
	ConnectionEndpoint string             `json:"connectionEndpoint,omitempty" dynamodbav:"connectionEndpoint"`
}

func (p PosConnectorRequest) Bytes() []byte {
	b, _ := json.Marshal(p)
	return b
}

const (
	DefaultDeclinedResponseText = "Something went wrong. We were unable to process the transaction. Please try again or contact Zeller if this issue persists."
	DefaultApprovedResponseText = "Success"
	DefaultUnknownResponseText  = "Unknown response"
)

func NewPosConnectorRequest(ctx context.Context, data *map[string]interface{}) (*PosConnectorRequest, error) {
	b, _ := json.Marshal(*data)
	var request PosConnectorRequest
	err := json.Unmarshal(b, &request)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("cant unmarsha request %s", err.Error()))
		return nil, err
	}
	return &request, nil
}

func (p *PosConnectorRequest) validate(ctx context.Context) error {
	validate := validator.New()
	err := validate.Struct(p)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to validate require %s", err.Error()))
	}
	return err
}

type PosConnectorDisconnectRequest struct {
	EventId      string `dynamodbav:"eventId"`
	Timestamp    string `dynamodbav:"timestamp"`
	Action       string `dynamodbav:"action"`
	ConnectionId string `dynamodbav:"connectionId"`
}

type PosConnectorResponse struct {
	Action    string             `json:"action"`
	EventId   string             `json:"eventId"`
	Timestamp string             `json:"timestamp"`
	Data      interface{}        `json:"data"`
	Error     *PosConnectorError `json:"error"`
}

func (p PosConnectorResponse) Bytes() []byte {
	b, _ := json.Marshal(p)
	return b
}

type PosConnectorDisconnect struct {
	ConnectionId string `json:"connectionId"`
	EntityUuid   string `json:"entityUuid"`
	SiteUuid     string `json:"siteUuid"`
}

type PosInterfaceResponse struct {
	EventId   string      `json:"eventId"`
	Action    string      `json:"action"`
	Timestamp string      `json:"timestamp"`
	Data      interface{} `json:"data"`
}

type Order struct {
	DeviceOrder
	SiteUuid *string `json:"siteUuid" validate:"required"`
}

// the order returned to device
type DeviceOrder struct {
	OrderId            *string   `json:"orderId" validate:"required"`
	TableNumber        *string   `json:"tableNumber,omitempty"`
	TableName          *string   `json:"tableName,omitempty"`
	SubTableName       *string   `json:"subTableName,omitempty"`
	TotalAmount        *int      `json:"totalAmount,omitempty"`
	OwedAmount         *int      `json:"owedAmount,omitempty"`
	PosSurchargeAmount *int      `json:"posSurchargeAmount,omitempty"`
	Status             *string   `json:"status,omitempty"`
	Location           *Location `json:"location,omitempty"`
	ExternalReference  *string   `json:"externalReference,omitempty"`
	Items              []Item    `json:"items,omitempty" validate:"dive"`
}

type Location struct {
	LocationId   *string `json:"locationId"`
	LocationName *string `json:"locationName"`
}

func NewOrder(ctx context.Context, data *map[string]interface{}) (*Order, error) {
	return NewValidatedStruct[Order](ctx, data)
}

type Item struct {
	ItemId       *string  `json:"itemId"`
	Description  *string  `json:"description"`
	TotalAmount  *int     `json:"totalAmount" validate:"required"`
	Amount       *int     `json:"amount" validate:"required"`
	Quantity     *float64 `json:"quantity" validate:"required"`
	RelatedItems []Item   `json:"relatedItems"`
}

type PosConnectionStatus struct {
	PosInterface *string `json:"posinterface"`
	Status       *string `json:"status"`
}

type Payment struct {
	OrderId         *string `json:"orderId" validate:"required"`
	LocationId      *string `json:"locationId,omitempty"`
	Amount          *int    `json:"amount" validate:"required"`
	Tip             *int    `json:"tip,omitempty"`
	Surcharge       *int    `json:"surcharge,omitempty"`
	ServiceCharge   *int    `json:"serviceCharge,omitempty"`
	CurrencyCode    *string `json:"currencyCode,omitempty"`
	MaskedPan       *string `json:"maskedPan,omitempty"`
	MerchantId      *string `json:"merchantId,omitempty"`
	Tid             *string `json:"tid,omitempty"`
	Rrn             *string `json:"rrn,omitempty"`
	Stan            *string `json:"stan,omitempty"`
	AuthCode        *string `json:"authCode,omitempty"`
	AccountType     *string `json:"accountType,omitempty"`
	TransactionTime *string `json:"transactionTime" validate:"required"`
	TransactionUuid *string `json:"transactionUuid" validate:"required"`
}

type PaymentAck struct {
	OrderId *string `json:"orderId" validate:"required"`
	Status  *string `json:"status" validate:"required"`
}

type PaymentResponse struct {
	Status *string `json:"status" validate:"required"`
}

type SubscriptionResponse struct {
	Status string `json:"status" validate:"required"`
}

type TransactionBase struct {
	SessionUuid     string `json:"sessionUuid" validate:"required"`
	TransactionUuid string `json:"transactionUuid,omitempty"`
	Type            string `json:"type,omitempty"`
	Status          string `json:"status,omitempty"`
	State           string `json:"state,omitempty"`
}

type TransactionLifecycleEvent struct {
	TransactionBase
	TerminalEvent string `json:"terminalEvent,omitempty"`
}

type ReceiptData struct {
	CardType string `json:"cardType,omitempty"`
	AID      string `json:"aid,omitempty"`
}

type TransactionResponse struct {
	TransactionBase
	Amount            int         `json:"amount" validate:"required,gte=0"`
	TipAmount         int         `json:"tipAmount,omitempty" validate:"omitempty,gte=0"`
	SurchargeAmount   int         `json:"surchargeAmount,omitempty" validate:"omitempty,gte=0"`
	ResponseCode      string      `json:"responseCode" validate:"required"`
	ResponseText      string      `json:"responseText"`
	Rrn               string      `json:"rrn,omitempty"`
	ApprovalCode      string      `json:"approvalCode,omitempty"`
	Scheme            string      `json:"scheme,omitempty" validate:"omitempty,oneof=VISA MC AMEX JCB EFTPOS CUP DINERS OTHER"`
	PanMasked         string      `json:"panMasked,omitempty"`
	Caid              string      `json:"caid" validate:"required"`
	Catid             string      `json:"catid" validate:"required"`
	IsoProcessingCode string      `json:"isoProcessingCode,omitempty" validate:"omitempty"`
	CardholderUuid    string      `json:"cardholderUuid,omitempty"`
	CardExpiryDate    string      `json:"cardExpiryDate,omitempty"`
	CardMedia         string      `json:"cardMedia,omitempty" validate:"omitempty,oneof=MANUAL MSR ICC PICC NFC CNP"`
	Cvm               string      `json:"cvm,omitempty" validate:"omitempty,oneof=CVM_FAILED NO_CVM SIGNATURE ONLINE_PIN OFFLINE_PIN PIN_AND_SIGNATURE CD_CVM UNKNOWN_CVM"`
	CardType          string      `json:"cardType,omitempty"`
	AID               string      `json:"aid,omitempty"`
	ReceiptData       ReceiptData `json:"receiptData,omitempty" validate:"omitempty"`
}

type TransactionResponseStatus struct {
	SessionUuid string                `json:"sessionUuid" validate:"required"`
	Type        types.TransactionType `json:"type" validate:"required"`
	Status      PaymentStatus         `json:"status" validate:"required"`
}

func NewPayment(ctx context.Context, data *map[string]interface{}) (*Payment, error) {
	return NewValidatedStruct[Payment](ctx, data)
}

func NewPaymentAck(ctx context.Context, data *map[string]interface{}) *PaymentAck {
	payment, err := NewPayment(ctx, data)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}

	return &PaymentAck{
		OrderId: payment.OrderId,
		Status:  ptr.String(string(ACKNOWLEDGED)),
	}
}

func NewTransactionLifecycleEvent(ctx context.Context, data *map[string]interface{}) (*TransactionLifecycleEvent, error) {
	return NewValidatedStruct[TransactionLifecycleEvent](ctx, data)
}

func NewTransactionBase(ctx context.Context, data *map[string]interface{}) (*TransactionBase, error) {
	return NewValidatedStruct[TransactionBase](ctx, data)
}

func NewTransactionResponse(ctx context.Context, data *map[string]interface{}) (*TransactionResponse, error) {
	var p TransactionResponse
	b, _ := json.Marshal(data)
	err := json.Unmarshal(b, &p)
	if err != nil {
		logger.Info(ctx, fmt.Sprintf("Failed to create TransactionResponse struct %s", err.Error()))
		return nil, err
	}

	if p.ResponseText == "" {
		if slices.Contains([]string{"FAILED", "DECLINED"}, p.Status) {
			p.ResponseText = DefaultDeclinedResponseText
		} else if slices.Contains([]string{"APPROVED", "SUCCESS"}, p.Status) {
			p.ResponseText = DefaultApprovedResponseText
		} else {
			p.ResponseText = DefaultUnknownResponseText
		}
	}

	validate := validator.New()
	if err = validate.Struct(p); err != nil {
		return nil, err
	}
	return &p, nil
}

type UpdateItem struct {
	ItemId      *string `json:"itemId" validate:"required"`
	Description *string `json:"description"`
	Amount      *int    `json:"amount" validate:"required"`
	Quantity    *int    `json:"quantity" validate:"required"`
}

type UpdateOrderItem struct {
	OrderId    *string    `json:"orderId" validate:"required,number"`
	Reference  *string    `json:"reference" validate:"required"`
	DeviceTime *string    `json:"deviceTime" validate:"required"`
	Item       UpdateItem `json:"item" validate:"required"`
}

func NewUpdateOrderItem(ctx context.Context, data *map[string]interface{}) (*UpdateOrderItem, error) {
	return NewValidatedStruct[UpdateOrderItem](ctx, data)
}

type PurchaseRequest struct {
	SessionUuid         *string `json:"sessionUuid" validate:"required"`
	Amount              *int    `json:"amount" validate:"required"`
	ExternalReference   *string `json:"externalReference" validate:"required"`
	Caid                *string `json:"caid,omitempty"`
	DeviceUuid          *string `json:"deviceUuid,omitempty"`
	IsCardPresent       *bool   `json:"isCardPresent,omitempty"`
	Timestamp           *string `json:"timestamp,omitempty"`
	TipAmount           *int    `json:"tipAmount,omitempty"`
	TransactionCurrency *string `json:"transactionCurrency,omitempty"`
	TransactionUuid     *string `json:"transactionUuid,omitempty"`
	Type                *string `json:"type,omitempty"`
}

type RefundRequest struct {
	PurchaseRequest
	OriginalTransactionUuid   *string `json:"originalTransactionUuid,omitempty"`
	OriginalIsoProcessingCode *string `json:"originalIsoProcessingCode,omitempty"`
}

type ReversalRequest struct {
	PurchaseRequest
	OriginalTransactionUuid *string `json:"originalTransactionUuid" validate:"required"`
}

func NewPurchaseRequest(ctx context.Context, data *map[string]interface{}) (*PurchaseRequest, error) {
	return NewValidatedStruct[PurchaseRequest](ctx, data)
}

func NewRefundRequest(ctx context.Context, data *map[string]interface{}) (*RefundRequest, error) {
	return NewValidatedStruct[RefundRequest](ctx, data)
}

func NewReversalRequest(ctx context.Context, data *map[string]interface{}) (*ReversalRequest, error) {
	return NewValidatedStruct[ReversalRequest](ctx, data)
}
