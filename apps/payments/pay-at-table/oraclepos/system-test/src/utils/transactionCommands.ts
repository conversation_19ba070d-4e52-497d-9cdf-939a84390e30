import { DynamodbClient, getSsmValue, invokeSyncLambda, sleep } from '@npco/bff-systemtest-utils';

import { randomUUID } from 'crypto';
import xml2js from 'xml2js';

import { SystemTestEnvironmentService } from './envService';
import { region } from './globalVariables';

const SCHEME_MAP: any = {
  VISA: '01',
  OTHER: '00',
};
export const stage = process.env.STAGE ?? 'dev';
export const RT_TERMINAL_BUSY_TEXT =
  'Zeller Terminal is busy or unable to receive a payment request. Ensure the paired Zeller Terminal is in POS Mode.';
export const RT_ORIGINAL_SESSION_TOO_OLD =
  'Refund unavailable. Transaction exceeds refund window (72 hours). Please refund via Zeller directly.';

export const getSequenceNo = () => Math.floor(Math.random() * 1000000000000).toString();

export const sendApiGatewayTransactionRequest = async (endpoint: string, body: string) => {
  const purchaseRequest = await fetch(endpoint, {
    method: 'POST',
    body,
  });

  const responseText = await purchaseRequest.text();
  console.log(endpoint, purchaseRequest.status, purchaseRequest.statusText, responseText);

  return {
    statusCode: purchaseRequest.status,
    body: responseText,
  };
};

export const assertEqual = (
  xmlResponse: any,
  requestPayload: TransactionRequestPayload,
  responsePayload: TransactionResponsePayload,
) => {
  const transactionType = requestPayload.TransactionRequest.TransType;
  expect(xmlResponse).toEqual({
    SequenceNo: requestPayload.TransactionRequest.SequenceNo,
    TransType: transactionType,
    TransAmount: responsePayload.amount.toString(),
    RespCode: responsePayload.responseCode,
    RespText: responsePayload.responseText,
    RRN: responsePayload.rrn,
    AuthCode: responsePayload.approvalCode,
    IssuerId: SCHEME_MAP[responsePayload.scheme],
    MerchantId: responsePayload.caid,
    TerminalId: responsePayload.catid,
    PAN: responsePayload.panMasked,
    PrintData: expect.any(String),
    AlternateTransRef: responsePayload.transactionUuid,
    OfflineFlag: 'N',
    TransToken: expect.any(String),
    ...(transactionType !== '04' && {
      EntryMode: expect.any(String),
      ExpiryDate: responsePayload.cardExpiryDate,
    }),
    ...(transactionType === '01' && {
      TipAmount: responsePayload.tipAmount.toString(),
      OtherAmount: responsePayload.tipAmount.toString(),
    }),
    ...(transactionType !== '01' && {
      TipAmount: undefined,
      OtherAmount: undefined,
    }),
  });
};

export const sendTransactionResponse = async (
  dbClient: DynamodbClient,
  env: SystemTestEnvironmentService,
  sequenceNo: string,
  transactionType: string,
  responsePayload: TransactionResponsePayload,
) => {
  // test lambda has timeout of 5s
  await sleep(4000);
  const session = await dbClient.query({
    TableName: env.componentTable,
    IndexName: 'sequenceNoGsi',
    KeyConditionExpression: 'sequenceNo = :sequenceNo',
    FilterExpression: 'transactionType = :transactionType',
    ExpressionAttributeValues: {
      ':sequenceNo': sequenceNo,
      ':transactionType': transactionType,
    },
  });
  const sessionUuid = session.Items?.[0]?.id;
  if (!sessionUuid) {
    throw new Error(`Session record not found for sequenceNo: ${sequenceNo}`);
  }

  // eslint-disable-next-line
  responsePayload.sessionUuid = sessionUuid;
  // eslint-disable-next-line
  responsePayload.transactionUuid = sessionUuid;

  const event = {
    body: JSON.stringify({
      eventId: randomUUID(),
      action: responsePayload.type.toLowerCase(),
      timestamp: '2021-08-31T11:14:18+11:00',
      data: responsePayload,
    }),
    path: `/v1/${transactionType.toLowerCase()}/${responsePayload.deviceUuid}`,
    httpMethod: 'POST',
    pathParameters: {
      deviceUuid: responsePayload.deviceUuid,
    },
  };
  const payload = await invokeSyncLambda(
    `${env.stage}-${env.COMPONENT_NAME}-${env.PART_NAME}-app-api-handler`,
    event,
    true,
  );
  if (payload.statusCode !== 200) {
    throw new Error(
      `request did not execute successfully StatusCode : ${payload.statusCode} Body: ${payload.body}, ${JSON.stringify(
        payload,
      )}`,
    );
  }
};

export const createTransactionRequest = (
  sequenceNo: string,
  overrides?: any,
): { xml: string; transactionPayload: TransactionRequestPayload } => {
  const transactionPayload = {
    TransactionRequest: {
      SequenceNo: sequenceNo,
      TransType: '01',
      TransAmount: 3100,
      TransCurrency: '036',
      TransDateTime: new Date().toISOString(),
      CardPresent: 1,
      PartialAuthFlag: 0,
      SiteId: Math.floor(Math.random() * 1000000000000).toString(),
      WSNo: Math.floor(Math.random() * 100000).toString(),
      Operator: 5,
      GuestNo: 8,
      ChargeInfo: 0,
      IndustryCode: 2,
      ProxyInfo: 'SPIV22.2',
      POSInfo: 'Simphony 19.5.3.0',
      ...(overrides ?? { ...overrides }),
    },
  };
  for (const [key, value] of Object.entries(transactionPayload.TransactionRequest)) {
    if (value === undefined) {
      delete transactionPayload.TransactionRequest[key];
    }
  }
  const builder = new xml2js.Builder();
  const xml = builder.buildObject(transactionPayload);
  console.log(xml);
  return {
    xml,
    transactionPayload,
  };
};

export const createTransactionResponse = (overrides?: any): TransactionResponsePayload => {
  return {
    deviceUuid: randomUUID(),
    type: 'PURCHASE',
    status: 'APPROVED',
    amount: 1000,
    surchargeAmount: 0,
    responseCode: '00',
    responseText: 'Approved',
    rrn: Math.floor(Math.random() * 1000000000000).toString(),
    approvalCode: Math.floor(Math.random() * 1000000000000).toString(),
    scheme: 'VISA',
    catid: Math.floor(Math.random() * 1000000000000).toString(),
    transactionUuid: randomUUID(),
    isoProcessingCode: randomUUID(),
    caid: randomUUID(),
    panMasked: `...${Math.floor(Math.random() * 10000).toString()}`,
    cardExpiryDate: '0228',
    cardMedia: 'NFC',
    cardholderUuid: randomUUID(),
    cvm: 'ONLINE_PIN',
    receiptData: {
      cardType: 'Visa Credit',
      aid: 'A0000000031010',
    },
    ...(overrides ?? { ...overrides }),
  };
};

export const createPurchaseRecord = async (
  endpoint: string,
  caid: string,
  clientId: string,
  deviceUuid: string,
  transactionResponseOverride?: Partial<TransactionResponsePayload>,
  transactionTime?: string,
) => {
  const dbClient = new DynamodbClient({ region });
  const env = new SystemTestEnvironmentService();

  const sequenceNo = Math.floor(Math.random() * 1000000000000).toString();

  // post purchase to external api endpoint
  const requestPayload = createTransactionRequest(sequenceNo, {
    SiteId: caid,
    WSNo: clientId,
    ...(transactionTime && { TransDateTime: transactionTime }),
  });

  const purchaseRequest = sendApiGatewayTransactionRequest(endpoint, requestPayload.xml);

  const purchaseResponsePayload = createTransactionResponse({
    deviceUuid,
    caid,
    ...(transactionResponseOverride && { ...transactionResponseOverride }),
  });

  const transactionResponse = sendTransactionResponse(dbClient, env, sequenceNo, 'PURCHASE', purchaseResponsePayload);

  // sleep for 2 seconds and update dynamodb session record based on sequence number
  const [purchaseResponse] = await Promise.all([purchaseRequest, transactionResponse]);

  // assert response from post is 200
  expect(purchaseResponse.statusCode).toEqual(200);

  return requestPayload.transactionPayload;
};

export type TransactionRequestPayload = {
  TransactionRequest: {
    SequenceNo: string;
    TransType: string;
    TransAmount: number;
    TipAmount?: number;
    OtherAmount?: number;
    TransCurrency: number;
    TransDateTime: string;
    CardPresent: number;
    PartialAuthFlag: number;
    SiteId: string;
    WSNo: string;
    Operator: number;
    GuestNo: number;
    ChargeInfo: number;
    IndustryCode: number;
    ProxyInfo: string;
    POSInfo: string;
  };
};

export type TransactionResponsePayload = {
  sessionUuid?: string;
  deviceUuid: string;
  transactionUuid: string;
  type: string;
  status: string;
  amount: number;
  tipAmount: number;
  surchargeAmount: number;
  responseCode: string;
  responseText: string;
  rrn: string;
  approvalCode: string;
  scheme: string;
  panMasked: string;
  catid: string;
  caid: string;
  isoProcessingCode: string;
  cardholderUuid: string;
  cardExpiryDate: string;
  cardMedia: string;
  cvm: string;
  receiptData?: {
    cardType?: string;
    aid?: string;
  };
};

export const getApiGatewayEndpoint = async () => {
  return getSsmValue(`${stage}-oraclepos-engine-external-api-endpoint`);
};

export const getHealthCheckPathV2 = async () => {
  return getSsmValue(`${stage}-oraclepos-engine-external-api-health-check-pathV2`);
};

export const getCommandHandlerPathV2 = async () => {
  return getSsmValue(`${stage}-oraclepos-engine-external-api-command-handler-pathV2`);
};
