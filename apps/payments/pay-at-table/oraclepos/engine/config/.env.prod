COMPONENT_NAME=oraclepos
PART_NAME=engine

LAMBDA_TIMEOUT_IN_SECONDS=30
LOG_LEVEL=info
LAMBDA_MEMORY_DEFAULT=512

#Keep warm
KEEP_WARM_SCHEDULER=cron(0/5 * ? * * *)
KEEP_WARM_SOURCE=keep.warm
LAMBDA_KEEP_WARM_STATE=ENABLED

# VPC
VPC_ENV_NAME=prod
SHOULD_CREATE_CUSTOM_DOMAIN=true

# global event bus name
GLOBAL_EVENT_BUS_NAME=prod-eventBus-global

# Billing
BILLING_API_ENDPOINT_VERSION=v1
BILLING_API_STAGE=prod

# Multiple region
DEPLOY_MULTI_REGION=false
PRIMARY_REGION=ap-southeast-2

# Static ENV
STATIC_ENV_NAME=prod

# Custom domain
DOMAIN_ENABLED=true
DOMAIN_NAME=*.oracle-pos.myzeller.com

COMMAND_RESPONSE_RECEIVED_RETRIES=35
COMMAND_RESPONSE_RECEIVED_RETRY_INTERVAL_MILLISECONDS=3000

COMMAND_ACK_RECEIVED_RETRIES=5
COMMAND_ACK_RECEIVED_RETRY_INTERVAL_MILLISECONDS=2000