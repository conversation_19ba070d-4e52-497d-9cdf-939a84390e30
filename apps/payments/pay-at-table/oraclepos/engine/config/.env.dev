COMPONENT_NAME=oraclepos
PART_NAME=engine

LAMBDA_TIMEOUT_IN_SECONDS=30
LAMBDA_MEMORY_DEFAULT=512

LOG_LEVEL=debug

#Keep warm
KEEP_WARM_SCHEDULER=cron(0/5 * ? * * *)
KEEP_WARM_SOURCE=keep.warm
LAMBDA_KEEP_WARM_STATE=ENABLED

# VPC
VPC_ENV_NAME=dev
SHOULD_CREATE_CUSTOM_DOMAIN=true

# global event bus name
GLOBAL_EVENT_BUS_NAME=dev-eventBus-global

# Multiple region
DEPLOY_MULTI_REGION=true
PRIMARY_REGION=ap-southeast-2

# Static ENV
STATIC_ENV_NAME=dev

# Custom domain
DOMAIN_ENABLED=true
DOMAIN_NAME=*.oracle-pos.myzeller.dev

COMMAND_RESPONSE_RECEIVED_RETRIES=35
COMMAND_RESPONSE_RECEIVED_RETRY_INTERVAL_MILLISECONDS=3000

COMMAND_ACK_RECEIVED_RETRIES=5
COMMAND_ACK_RECEIVED_RETRY_INTERVAL_MILLISECONDS=2000