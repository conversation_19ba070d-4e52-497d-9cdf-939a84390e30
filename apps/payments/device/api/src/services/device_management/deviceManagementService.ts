import { NotFoundError, ServerError } from '@npco/component-bff-core/dist/error';
import { info, error, debug } from '@npco/component-bff-core/dist/utils/logger';
import { EnvironmentService } from '@npco/component-dbs-mp-common/dist/config';
import { DynamodbService } from '@npco/component-dbs-mp-common/dist/dynamodb';
import { AmsApiService } from '@npco/component-dbs-mp-common/dist/interface/amsApiService';
import { SessionService } from '@npco/component-dbs-mp-common/dist/session/sessionService';
import { DbRecordType } from '@npco/component-dto-core';
import type {
  DeviceInfoUpdateDto,
  DeviceSoftwareInfo,
  SoftwareUpdateEventInput,
  SoftwareUpdateInfoInput,
} from '@npco/component-dto-device';

import { Injectable } from '@nestjs/common';
import axios from 'axios';
import * as semver from 'semver';

import { DeviceManagementDb } from './deviceManagementDb';
import type {
  CheckSoftwareUpdateInput,
  RequestKeyInitialisationInput,
  DeviceHardwareInformation,
  DeviceInformationDbItem,
  DeviceNetworkCellDetails,
  DeviceNetworkCellularInformation,
  DeviceNetworkEthernetInformation,
  DeviceNetworkInformation,
  DeviceNetworkWifiInformation,
  DeviceNetworkWifiSetting,
} from './types';
import { ConnectionType } from './types';

export const connectionTypes: { [key: number]: string } = {
  [ConnectionType.UNKNOWN]: 'UNKNOWN',
  [ConnectionType.WIFI]: 'WIFI',
  [ConnectionType.CELLULAR]: 'CELLULAR',
  [ConnectionType.ETHERNET]: 'ETHERNET',
};

@Injectable()
export class DeviceManagementService {
  minDeviceSoftwareVersion = '1.3.24';

  versionErrorMsg = 'Incorrect version format';

  apiService: AmsApiService;

  deviceMangementDb: DeviceManagementDb;

  constructor(
    private readonly environmentService: EnvironmentService,
    private readonly sessionService: SessionService,
    private readonly dbService: DynamodbService,
  ) {
    this.apiService = new AmsApiService(
      this.environmentService,
      this.environmentService.amsDeviceMgnEndpointPath,
      'devicemgn',
    );
    this.deviceMangementDb = new DeviceManagementDb(environmentService, dbService);
  }

  isActualVersionLowerThanTarget = (rawActual: string, rawTarget = this.minDeviceSoftwareVersion) => {
    if (!semver.valid(rawActual) || !semver.valid(rawTarget)) {
      throw new ServerError(this.versionErrorMsg);
    }
    return semver.lt(rawActual, rawTarget);
  };

  isDifferentVersion = (actual: string, target: string) => actual.toLocaleLowerCase() !== target.toLocaleLowerCase();

  /**
   * Update the information about a device such as software version, android os version, etc.
   * This information does not need to be projected.
   * @param dto Device information required for management
   */
  updateDeviceInfo = async (dto: DeviceInfoUpdateDto) => {
    try {
      debug(`update device info for device ${dto.id}, ${JSON.stringify(dto, null, 2)}`);
      await this.sessionService.checkDeviceCache(dto.id);
      return await this.apiService.updateDeviceInfo<DeviceInfoUpdateDto>(dto.id, dto);
    } catch (e: any) {
      error(e, dto.id);
      throw e;
    }
  };

  isDeviceExisted = async (entityUuid: string, deviceUuid: string) => {
    return this.deviceMangementDb.isDeviceExisted(entityUuid, deviceUuid);
  };

  getDeviceInfo = async (deviceUuid: string) => {
    const deviceInfo = await this.deviceMangementDb.getDeviceInfo(deviceUuid);
    if (deviceInfo) {
      return deviceInfo;
    }
    return Promise.reject(new NotFoundError('Device information not found')); // NOSONAR
  };

  getTargetSoftwareVersions = async (deviceUuid: string) => {
    await this.sessionService.checkDeviceCache(deviceUuid);
    const softwareRecord = await this.deviceMangementDb.getDeviceSoftware(deviceUuid);
    if (!softwareRecord || (softwareRecord && !(softwareRecord.appUrl && softwareRecord.firmwareUrl))) {
      return Promise.reject(new NotFoundError('Target software version not configured for this device')); // NOSONAR
    }
    return {
      forceUpdate: !!softwareRecord.forceUpdate,
      appUrl: softwareRecord.appUrl,
      appVersion: softwareRecord.appVersion,
      firmwareUrl: softwareRecord.firmwareUrl,
      firmwareVersion: softwareRecord.firmwareVersion,
      additionalApps: softwareRecord.additionalApps,
    } as DeviceSoftwareInfo;
  };

  checkSoftwareUpdate = async (input: CheckSoftwareUpdateInput) => {
    const deviceUuid = input.id;
    const target = await this.getTargetSoftwareVersions(deviceUuid);
    const response = {
      id: deviceUuid,
      forceUpdate: target.forceUpdate,
      app: this.createAppUpdateIfRequired(input, target),
      firmware: this.createFirmwareUpdateIfRequired(input, target),
      additionalApps: this.createAdditionalAppUpdateIfRequired(input, target),
    } as SoftwareUpdateEventInput;
    debug(`check software update for device ${deviceUuid}, ${JSON.stringify(response, null, 2)}`);
    return response;
  };

  requestKeyInitialisation = async (input: RequestKeyInitialisationInput) => {
    const { tcuid, kbpkSignature } = input;
    if (this.environmentService.usePgsRki) {
      const param = {
        tcuid,
        kcv: kbpkSignature,
      };
      info(`send to pgs rki endpoint ${this.environmentService.pgsRKIEndpoint} ${JSON.stringify(param)}`);
      try {
        const resp = await axios.post(this.environmentService.pgsRKIEndpoint, JSON.stringify(param), {
          headers: { 'Content-Type': 'application/json' },
        });
        info(`receive response data ${JSON.stringify(resp.data)}`);
        return { tr31KeyBlock: resp.data.tr31KeyBlock, error: false };
      } catch (err: any) {
        error(`failed to send rki request to pgs: ${err.message}`);
        error(err);
        return { error: true };
      }
    }
    const deviceKeys = await this.deviceMangementDb.getDeviceKeys(tcuid);
    const response = { tr31KeyBlock: '', error: true };
    if (deviceKeys.kbpkSignature === kbpkSignature) {
      response.tr31KeyBlock = deviceKeys.tr31KeyBlock;
      response.error = false;
    }
    return response;
  };

  saveDeviceInformationUpdateProjection = async (dto: DeviceInfoUpdateDto) => {
    const hardware = this.updateDeviceInformationIfRequired<DeviceHardwareInformation>(
      {
        model: dto.model,
        serial: dto.serial,
        androidOs: dto.androidOs,
        androidKernel: dto.androidKernel,
        androidBuild: dto.androidBuild,
        androidDevice: dto.androidDevice,
        androidModel: dto.androidModel,
        ramTotal: dto.ramTotal,
        ramAvail: dto.ramAvail,
        flashTotal: dto.flashTotal,
        flashAvail: dto.flashAvail,
        emvL1Version: dto.emvL1Version,
        emvL2Version: dto.emvL2Version,
        hardwareVersion: dto.hardwareVersion,
        pciFirmwareVersion: dto.pciFirmwareVersion,
        securityVersion: dto.securityVersion,
        secureCpuId: dto.secureCpuId,
        numMasterKeys: dto.numMasterKeys,
        numDukptKeys: dto.numDukptKeys,
        ksn: dto.ksn,
        numCapk: dto.numCapk,
        numAids: dto.numAids,
        pedCurrentTemp: dto.pedCurrentTemp,
        pedVersion: dto.pedVersion,
        pedModel: dto.pedModel,
        pedStatus: dto.pedStatus,
        softwareVersion: dto.softwareVersion,
        firmwareVersion: dto.firmwareVersion,
        heightPixels: dto.heightPixels,
        widthPixels: dto.widthPixels,
        ydpi: dto.ydpi,
        xdpi: dto.xdpi,
        density: dto.density,
        densityDpi: dto.densityDpi,
        os: dto.os,
        kernel: dto.kernel,
        uiSoftwareVersion: dto.uiSoftwareVersion,
      },
      false,
    );
    const wifiInfo = this.updateDeviceInformationIfRequired<DeviceNetworkWifiInformation>({
      state: dto.wifiState,
      ssid: dto.wifiSsid,
      bssid: dto.wifiBssid,
      channel: dto.wifiChannel,
      frequency: dto.wifiFrequency,
      standard: dto.wifiStandard,
      avaliableNetworks: this.getAvailableNetworks(dto.wifiAvailableNetworks),
      ipAddress: dto.wifiIpAddress,
      gatewayAddress: dto.wifiGatewayAddress,
      dns1Address: dto.wifiDns1Address,
      dns2Address: dto.wifiDns2Address,
      transferSpeed: dto.wifiTransferSpeed,
      maxTransferSpeed: dto.wifiMaxTransferSpeed,
      strengthRssi: dto.wifiRssi,
      macAddress: dto.wifiMacAddress,
      speed: dto.wifiSpeed,
      securityModel: dto.wifiSecurityModel,
      strength: dto.wifiRssi?.toString(),
    });
    const cellularInfo = this.updateDeviceInformationIfRequired<DeviceNetworkCellularInformation>({
      cellDetails: this.getCellDetails(dto.simCellDetails),
      serialNumber: dto.simSerialNumber,
      subscriberId: dto.simSubscriberId,
      state: dto.simState,
      country: dto.simCountry,
      operator: dto.simOperator,
      operatorName: dto.simOperatorName,
      network: dto.simNetwork,
      networkName: dto.simNetworkName,
      networkType: dto.simNetworkType,
      ipAddress: dto.simIpAddress,
      gatewayAddress: dto.simGatewayAddress,
      dns1Address: dto.simDns1Address,
      dns2Address: dto.simDns2Address,
      strengthRssi: dto.simRssi,
      strength: dto.simRssi?.toString(),
    });
    const ethernetInfo = this.updateDeviceInformationIfRequired<DeviceNetworkEthernetInformation>({
      macAddress: dto.tcpMac,
      dhcp: dto.tcpDhcp,
      ipAddress: dto.tcpIpAddress,
      gatewayAddress: dto.tcpGatewayAddress,
      dns1Address: dto.tcpDns1Address,
      dns2Address: dto.tcpDns2Address,
      transferSpeed: dto.tcpTransferSpeeds,
      maxTransferSpeed: dto.tcpMaxTransferSpeeds,
    });
    const conectionType: number | undefined = dto.connectionType;
    const network: DeviceNetworkInformation = {
      activeConnection: conectionType ? connectionTypes[conectionType] : connectionTypes[ConnectionType.UNKNOWN],
      wifiInfo,
      cellularInfo,
      ethernetInfo,
    };
    const deviceInfo: DeviceInformationDbItem = {
      id: dto.id,
      type: DbRecordType.DEVICE_INFO,
      customerId: dto.customerId,
      customerName: dto.customerName,
      hardware,
      network,
    };
    await this.deviceMangementDb.saveDeviceInformationRecord(deviceInfo);
  };

  private updateDeviceInformationIfRequired<T extends { [key: string]: any }>(
    deviceInfo: T,
    lastUpdated = true,
  ): T | undefined {
    if (Object.values(deviceInfo).some((value) => value !== undefined)) {
      return { ...deviceInfo, ...(lastUpdated ? { lastUpdated: new Date().toISOString() } : {}) };
    }
    return undefined;
  }

  private readonly getCellDetails = (simCellDetails?: string): DeviceNetworkCellDetails[] | undefined => {
    if (!simCellDetails?.length) {
      return undefined;
    }

    const cellDetails = [];
    let cellDetail: DeviceNetworkCellDetails = {};
    const unparsedCellDetails = simCellDetails.split(',');
    while (unparsedCellDetails.length > 0) {
      const data = unparsedCellDetails.shift();
      if (data?.startsWith('ci:')) {
        if (Object.keys(cellDetail).length > 0) {
          cellDetails.push(cellDetail);
          cellDetail = {};
        }
        cellDetail.ci = data.split(':')[1];
      } else if (data?.startsWith('tac:')) {
        cellDetail.tac = data.split(':')[1];
      } else if (data?.startsWith('dbm:')) {
        cellDetail.dbm = data.split(':')[1];
      } else if (data?.startsWith('asu:')) {
        cellDetail.asu = data.split(':')[1];
      }
    }
    if (Object.keys(cellDetail).length > 0) {
      cellDetails.push(cellDetail);
    }

    return cellDetails;
  };

  private readonly getAvailableNetworks = (wifiAvailableNetworks?: string): DeviceNetworkWifiSetting[] | undefined => {
    if (!wifiAvailableNetworks?.length) {
      return undefined;
    }
    const ssidRegex = /\bssid(?!bssid|freq|rssi)\b:(.*?)[,|}]/i;
    const bssidRegex = /bssid:([\w|:]+)/i;
    const freqRegex = /freq:(\d+)/i;
    const rssiRegex = /rssi:(-?\d+)/i;
    const networks = wifiAvailableNetworks.match(/\{.*?\}/gi); // NOSONAR
    return networks?.map((network) => {
      const ssid = new RegExp(ssidRegex).exec(network)?.[1] ?? '';
      const bssid = new RegExp(bssidRegex).exec(network)?.[1] ?? '';
      const freq = new RegExp(freqRegex).exec(network)?.[1] ?? '';
      const rssi = new RegExp(rssiRegex).exec(network)?.[1] ?? '';
      return { ssid, bssid, freq: parseInt(freq, 10), rssi: parseInt(rssi, 10) };
    });
  };

  private createAppUpdateIfRequired(actual: CheckSoftwareUpdateInput, target: DeviceSoftwareInfo) {
    return this.isDifferentVersion(actual.appVersion, target.appVersion)
      ? { version: target.appVersion, url: target.appUrl }
      : (undefined as any);
  }

  private createFirmwareUpdateIfRequired(actual: CheckSoftwareUpdateInput, target: DeviceSoftwareInfo) {
    if (this.isActualVersionLowerThanTarget(actual.appVersion)) {
      return undefined;
    }
    return this.isDifferentVersion(actual.firmwareVersion, target.firmwareVersion)
      ? { version: target.firmwareVersion, url: target.firmwareUrl }
      : (undefined as any);
  }

  private createAdditionalAppUpdateIfRequired(actual: CheckSoftwareUpdateInput, target: DeviceSoftwareInfo) {
    const actualAdditionalApps = actual.additionalApps ?? [];
    const additionalAppsToUpdate: SoftwareUpdateInfoInput[] = [];

    (target.additionalApps ?? []).forEach((targetApp) => {
      const app = actualAdditionalApps.find((item) => item.name === targetApp.name);
      if (!app || (app && this.isDifferentVersion(app.version, targetApp.version))) {
        additionalAppsToUpdate.push({ ...targetApp });
      }
    });
    return additionalAppsToUpdate.length > 0 ? additionalAppsToUpdate : undefined;
  }
}
