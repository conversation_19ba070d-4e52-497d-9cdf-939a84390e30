Parameters:
  EventBusName:
    Description: A name of the component part e.g. api
    Type: String
    Default: mockEventBus

Resources:
  EventBusMock:
    Type: AWS::Events::EventBus
    Properties:
      Name: !Join
        - ''
        - - !Ref EventBusName
          - '-cqrs-iac-eventBus-projection'
  eventBusDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Join
        - ''
        - - !Ref EventBusName
          - '-cqrs-iac-eventBus-projection-dlq'
Outputs:
  EventBusProjectionArn:
    Value: !GetAtt EventBusMock.Arn
    Export:
      Name: !Join
        - ''
        - - !Ref EventBusName
          - '-cqrs-iac-eventBus-projection-EventBusProjectionArn'
  EventBusProjectionDLQArn:
    Value: !GetAtt eventBusDlq.Arn
    Export:
      Name: !Join
        - ''
        - - !Ref EventBusName
          - '-cqrs-iac-eventBus-projection-dlq-EventBusProjectionDlqArn'
