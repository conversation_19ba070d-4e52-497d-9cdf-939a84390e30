import commonConfig from '../../../../jest.preset';

const config = {
  ...commonConfig,
  preset: '@shelf/jest-dynamodb',
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
  coveragePathIgnorePatterns: ['testcases', 'index.ts'],
  reporters: [
    ['github-actions', { silent: false }],
    [
      'jest-html-reporter',
      {
        pageTitle: 'Test Report',
        outputPath: 'dist/test-report.html',
      },
    ],
  ],
};

export default config;
