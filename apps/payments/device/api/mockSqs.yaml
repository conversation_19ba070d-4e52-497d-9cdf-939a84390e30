Parameters:
  SqsName:
    Description: A name of the component part e.g. api
    Type: String
    Default: mockSqs

Resources:
  deadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Join
        - ''
        - - !Ref SqsName
          - '-cqrs-iac-projection-dlq'
  projectionQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Join
        - ''
        - - !Ref SqsName
          - '-cqrs-iac-projection'
      VisibilityTimeout: 1800
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt deadLetterQueue.Arn
        maxReceiveCount: 5

Outputs:
  QueueURL:
    Description: 'URL of new Amazon SQS Queue'
    Value: !Ref projectionQueue
    Export:
      Name: !Join
        - ''
        - - !Ref SqsName
          - '-cqrs-iac-sqsUrl'
  QueueARN:
    Description: 'ARN of new AmazonSQS Queue'
    Value: !GetAtt projectionQueue.Arn
    Export:
      Name: !Join
        - ''
        - - !Ref SqsName
          - '-cqrs-iac-sqsArn'
