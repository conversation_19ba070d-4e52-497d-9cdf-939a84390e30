/* eslint import/no-import-module-exports: 0 */
/* eslint no-template-curly-in-string: 0 */
/* eslint no-useless-concat: 0 */
// eslint-disable-next-line import/no-extraneous-dependencies
import * as dotenv from 'dotenv';
import type { Serverless } from 'serverless/aws';

import { EnvService } from './serverless/envService';
import { golangExecutable, packagePatterns, providedAl2023 } from './serverless/globals';
import { getDeploymentBucket } from './serverless/shared/deploymentBucket';
import { getTags } from './serverless/shared/getTags';
import { getEnvFilename } from './serverless/utils/getEnvFilename';

dotenv.config({ path: getEnvFilename(), debug: true });
const env = new EnvService();
const region = '${opt:region}';
const stage = '${opt:stage}';

const serviceBaseName = `\${opt:stage}-${env.COMPONENT_NAME}-${env.PART_NAME}` as const;
const serviceName = `${serviceBaseName}-warmup` as const;
const stackName = serviceName;

const generateServerless = async (): Promise<Serverless> => {
  return {
    service: serviceName,
    plugins: ['serverless-plugin-resource-tagging', 'serverless-plugin-tracing', 'serverless-plugin-scripts'],
    useDotenv: true,
    variablesResolutionMode: '20210326',
    frameworkVersion: '3',
    provider: {
      name: 'aws',
      runtime: providedAl2023,
      apiName: serviceName,
      region,
      stackName,
      versionFunctions: false,
      deploymentBucket: await getDeploymentBucket(env),
      environment: {
        COMPONENT_NAME: env.COMPONENT_NAME,
        PART_NAME: env.PART_NAME,
        STAGE: stage,
        LOG_LEVEL: env.LOG_LEVEL,
        COMPONENT_TABLE: env.COMPONENT_TABLE,
        ZELLER_DEVID: env.ZELLER_DEVID,
        TEVALIS_API_URL: env.TEVALIS_API_URL,
      },
      tags: getTags(env),
      stackTags: getTags(env),
    },
    package: packagePatterns,
    functions: {
      warmupHandler: {
        handler: golangExecutable,
        name: `${stackName}-warmupHandler`,
        timeout: 300,
        tracing: true,
        role: 'warmupHandlerRole',
        environment: {
          HANDLER_NAME: 'warmup',
        },
        events: [
          {
            schedule: {
              rate: 'rate(1 minute)',
              enabled: true,
            },
          },
        ],
      },
    },
    resources: {
      Resources: {
        logPolicy: {
          Type: 'AWS::IAM::ManagedPolicy',
          Properties: {
            ManagedPolicyName: `${stackName}-logPolicy`,
            PolicyDocument: {
              Version: '2012-10-17',
              Statement: [
                {
                  Effect: 'Allow',
                  Action: ['logs:CreateLogStream', 'logs:PutLogEvents', 'logs:CreateLogGroup'],
                  Resource: 'arn:aws:logs:${opt:region}:${aws:accountId}:log-group:/aws/lambda/' + `${stackName}-*:*`,
                },
              ],
            },
          },
        },
        warmupInvokePolicy: {
          Type: 'AWS::IAM::ManagedPolicy',
          Properties: {
            ManagedPolicyName: '${self:provider.stackName}-warmupInvokePolicy',
            PolicyDocument: {
              Version: '2012-10-17',
              Statement: [
                {
                  Effect: 'Allow',
                  Action: [
                    'lambda:InvokeFunction',
                  ],
                  Resource: `arn:aws:lambda:\${opt:region}:\${aws:accountId}:function:${serviceBaseName}-*`,
                },
              ],
            },
          },
        },
        warmupHandlerRole: {
          Type: 'AWS::IAM::Role',
          Properties: {
            AssumeRolePolicyDocument: {
              Version: '2012-10-17',
              Statement: [
                {
                  Effect: 'Allow',
                  Principal: {
                    Service: ['lambda.amazonaws.com'],
                  },
                  Action: 'sts:AssumeRole',
                },
              ],
            },
            ManagedPolicyArns: [
              {
                Ref: 'warmupInvokePolicy',
              },
              {
                Ref: 'logPolicy',
              },
            ],
          },
        },
      },
    },
  } as any;
};

module.exports = generateServerless();
