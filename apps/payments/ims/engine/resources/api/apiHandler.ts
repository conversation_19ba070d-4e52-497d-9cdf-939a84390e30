import { ServerlessFunction } from '@npco/component-bff-serverless';
import { commonLambdaPolicy } from '../common/custom';

const createMutationEventTriggers = (methods: string[] = ['POST']) =>
  methods.map((name) => ({
    http: {
      // TODO: extract v1 to env variables
      path: '/v1/{requestName}',
      method: name,
    },
  }));

const createQueryEventTriggers = (methods: string[] = ['GET']) =>
  methods.map((name) => ({
    http: {
      path: '/v1/{requestName}',
      method: name,
    },
  }));

const getCatalogTableName = () => {
  return '${opt:stage}-mp-api-dynamodb-${env:CATALOG_COMPONENT_TABLE}';
};
const getTableArn = (tableName: string) => {
  return 'arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/'.concat(getCatalogTableName());
};

const getTableIndex = (tableNameArn: string) => `${tableNameArn}/index/*`;
const catalogTableArn = getTableArn(getCatalogTableName());
const catalogTableIndexes = getTableIndex(catalogTableArn);

export const createMutationApiHandler = (lambdaCommon: any): ServerlessFunction => ({
  handler: `src/lambdas/mutationLambda.mutationHandler`,
  name: `mutationHandler`,
  policy: {
    useVpc: true,
    useXray: true,
    inline: {
      ...commonLambdaPolicy,
      invokeInternalSagaHandlerPolicy: [
        {
          actions: ['lambda:invokeFunction'],
          resources: [
            'arn:aws:lambda:${self:provider.region}:${self:custom.accountId}:function:${self:custom.sagaHandler}*',
          ],
        },
      ],
      invokeCommandHandlerPolicy: [
        {
          actions: ['lambda:invokeFunction'],
          resources: [
            'arn:aws:lambda:${self:provider.region}:${self:custom.accountId}:function:${self:custom.imsCqrsCommandHandler}*',
          ],
        },
      ],
      uploadPdfPolicy: [
        {
          actions: ['s3:PutObject', 's3:GetObject', 's3:PutObjectTagging', 's3:PutObjectVersionTagging'],
          resources: ['arn:aws:s3:::${self:custom.pdfBucketName}/*'],
        },
      ],
      checkPdfPolicy: [
        {
          actions: ['s3:ListBucket'],
          resources: ['arn:aws:s3:::${self:custom.pdfBucketName}', 'arn:aws:s3:::${self:custom.pdfBucketName}/*'],
        },
      ],
      sesPolicy: [
        {
          actions: ['ses:SendRawEmail', 'ses:SendEmail'],
          resources: ['*'],
        },
      ],
      messageMediaPolicy: [
        {
          actions: ['ssm:GetParameter'],
          resources: [
            'arn:aws:ssm:${self:provider.region}:${self:custom.accountId}:parameter/${env:AURORA_STAGE}-messagemedia-api-key',
            'arn:aws:ssm:${self:provider.region}:${self:custom.accountId}:parameter/${env:AURORA_STAGE}-messagemedia-api-secret',
          ],
        },
      ],
      sqsPolicy: [
        {
          actions: ['sqs:SendMessage', 'sqs:ChangeMessageVisibility', 'sqs:GetQueueAttributes', 'sqs:GetQueueUrl'],
          resources: ['arn:aws:sqs:${self:provider.region}:${self:custom.accountId}:${self:custom.failedEmailSqsQueue}'],
        },
      ],
      catalogTableQueryPolicy: [
        {
          actions: ['dynamodb:GetItem', 'dynamodb:Query', 'dynamodb:BatchGetItem'],
          resources: [
            catalogTableArn,
            catalogTableIndexes,
          ],
        },
      ],
    },
  },
  events: createMutationEventTriggers(),
  layers: ['arn:aws:lambda:${self:provider.region}:${self:custom.lambdaParameterExtensionAccountId}:layer:AWS-Parameters-and-Secrets-Lambda-Extension:11'],
  environment: {
    SAGA_HANDLER_NAME: '${self:custom.sagaHandler}',
    CQRS_COMMAND_HANDLER: '${self:custom.imsCqrsCommandHandler}',
    S3_INVOICES_BUCKET: '${self:custom.pdfBucketName}',
    RECEIPT_HTML_ASSETS_URL: '${env:RECEIPT_HTML_ASSETS_URL}',
    DASHBOARD_BASE_URL: '${env:DASHBOARD_BASE_URL}',
    PAY_BASE_URL: '${env:PAY_BASE_URL}',
    EMAIL_SOURCE_PDF: '${env:EMAIL_SOURCE_PDF}',
    ERS_API_ENDPOINT: '${ssm:${env:STATIC_ENV_NAME}-ers-engine-api-endpoint-v2}',
    ersApiEndpointVersion: 'v1',
    INVOICE_PDF_DOWNLOAD_URL: '${env:INVOICE_PDF_DOWNLOAD_URL}',
    CATALOG_COMPONENT_TABLE: getCatalogTableName(),
    FAILED_EMAIL_SQS_QUEUE_URL:
      'https://sqs.${self:provider.region}.amazonaws.com/${self:custom.accountId}/${self:custom.failedEmailSqsQueue}',
  },
});

export const createQueryApiHandler = (lambdaCommon: any): ServerlessFunction => ({
  handler: `src/lambdas/queryLambda.queryHandler`,
  name: `queryHandler`,
  policy: {
    useVpc: true,
    useXray: true,
    inline: {
      ...commonLambdaPolicy,
      uploadPdfPolicy: [
        {
          actions: ['s3:PutObject', 's3:GetObject', 's3:PutObjectTagging', 's3:PutObjectVersionTagging'],
          resources: ['arn:aws:s3:::${self:custom.pdfBucketName}/*'],
        },
      ],
      checkPdfPolicy: [
        {
          actions: ['s3:ListBucket'],
          resources: ['arn:aws:s3:::${self:custom.pdfBucketName}', 'arn:aws:s3:::${self:custom.pdfBucketName}/*'],
        },
      ],
    },
  },
  events: createQueryEventTriggers(),
  environment: {
    S3_INVOICES_BUCKET: '${self:custom.pdfBucketName}',
    RECEIPT_HTML_ASSETS_URL: '${env:RECEIPT_HTML_ASSETS_URL}',
    DASHBOARD_BASE_URL: '${env:DASHBOARD_BASE_URL}',
    PAY_BASE_URL: '${env:PAY_BASE_URL}',
    EMAIL_SOURCE_PDF: '${env:EMAIL_SOURCE_PDF}',
  },
});

export const createSagaHandler = (lambdaCommon: any): ServerlessFunction => ({
  handler: `src/lambdas/sagaLambda.sagaHandler`,
  name: `sagaHandler`,
  policy: {
    useVpc: true,
    useXray: true,
    inline: {
      ...commonLambdaPolicy,
      invokeCommandHandlerPolicy: [
        {
          actions: ['lambda:invokeFunction'],
          resources: [
            'arn:aws:lambda:${self:provider.region}:${self:custom.accountId}:function:${self:custom.imsCqrsCommandHandler}*',
          ],
        },
      ],
      uploadPdfPolicy: [
        {
          actions: ['s3:PutObject', 's3:GetObject', 's3:PutObjectTagging', 's3:PutObjectVersionTagging'],
          resources: ['arn:aws:s3:::${self:custom.pdfBucketName}/*'],
        },
      ],
      checkPdfPolicy: [
        {
          actions: ['s3:ListBucket'],
          resources: ['arn:aws:s3:::${self:custom.pdfBucketName}/'],
        },
      ],
      sesPolicy: [
        {
          actions: ['ses:SendRawEmail', 'ses:SendEmail'],
          resources: ['*'],
        },
      ],
    },
  },
  environment: {
    S3_INVOICES_BUCKET: '${self:custom.pdfBucketName}',
    RECEIPT_HTML_ASSETS_URL: '${env:RECEIPT_HTML_ASSETS_URL}',
    DASHBOARD_BASE_URL: '${env:DASHBOARD_BASE_URL}',
    PAY_BASE_URL: '${env:PAY_BASE_URL}',
    EMAIL_SOURCE_PDF: '${env:EMAIL_SOURCE_PDF}',
    ERS_API_ENDPOINT: '${ssm:${env:STATIC_ENV_NAME}-ers-engine-api-endpoint-v2}',
    ersApiEndpointVersion: 'v1',
    INVOICE_PDF_DOWNLOAD_URL: '${env:INVOICE_PDF_DOWNLOAD_URL}',
  },
});
