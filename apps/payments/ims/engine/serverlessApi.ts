import { ApiAppServerlessStack } from '@npco/component-bff-serverless';
import { pluginsApp } from './resources/common/policy';
import { env, envConfig as config } from './resources/common/env';
import { createHanlders } from './resources/api/lambda';
import { esbuildWithSharpAndPdfkit, prune } from './resources/common/custom';

const stackName = 'api';
const stage = process.env.STAGE || '';

const getVpcEndpointIds = () => {
  const vpcVpcEndpointIds = ['${ssm:${env:VPC_ENV_NAME}-mp-api-gateway-endpoint}'];
  if (!(stage.startsWith('st') && stage !== 'staging')) {
    vpcVpcEndpointIds.push('${ssm:${env:VPC_ENV_NAME}-ims-api-gateway-endpoint}');
  }
  return vpcVpcEndpointIds;
}
const sls = new ApiAppServerlessStack(stackName, config, {
  plugins: [...pluginsApp],
  provider: {
    name: "aws",
    tracing: {apiGateway: true},
    vpc: {
      securityGroupIds: [
        '${ssm:${env:VPC_ENV_NAME}-ims-lambda-sg}'
      ],
      subnetIds: [
        '${ssm:${env:VPC_ENV_NAME}-ims-lambda-subnet01}',
        '${ssm:${env:VPC_ENV_NAME}-ims-lambda-subnet02}',
        '${ssm:${env:VPC_ENV_NAME}-ims-lambda-subnet03}'
      ]
    },
    logs: {
      restApi: {
        accessLogging: true,
        level: 'ERROR',
        role: 'arn:aws:iam::${self:custom.accountId}:role/${self:custom.service}-apiGatewayLoggingRole',
        roleManagedExternally: true,
      },
    }
  },
  resources: {
    ApiGatewayRestApi: {
      Type: 'AWS::ApiGateway::RestApi',
      Properties: {
        Name: '${self:provider.stackName}',
        EndpointConfiguration: {
          Types: ['PRIVATE'],
          VpcEndpointIds: getVpcEndpointIds(),
        },
        Policy: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Deny',
              Principal: '*',
              Action: ['execute-api:Invoke'],
              Resource: 'execute-api:/*/*/*',
              Condition: {
                StringNotEquals: {
                  'aws:sourceVpce': [
                    '${ssm:${env:VPC_ENV_NAME}-mp-api-gateway-endpoint}',
                    '${ssm:${env:VPC_ENV_NAME}-ims-api-gateway-endpoint}',
                  ],
                },
              },
            },
            {
              Effect: 'Allow',
              Principal: '*',
              Action: ['execute-api:Invoke'],
              Resource: 'execute-api:/*/*/*',
            },
          ],
        },
      }
    },
    mutationHandlerInvokePermission: {
      Type: 'AWS::Lambda::Permission',
      Properties: {
        FunctionName: {'Fn::GetAtt': ['MutationHandlerLambdaFunction', 'Arn']},
        Action: 'lambda:InvokeFunction',
        Principal: 'apigateway.amazonaws.com',
        SourceAccount: {Ref: 'AWS::AccountId'},
        SourceArn: {
          'Fn::Join': [
            '',
            [
              'arn:aws:execute-api:',
              {'Ref': 'AWS::Region'},
              ":",
              {'Ref': 'AWS::AccountId'},
              ":",

              {'Ref': 'ApiGatewayRestApi'},
              "/*"
            ]
          ]
        }
      }
    },
    queryHandlerInvokePermission: {
      Type: 'AWS::Lambda::Permission',
      Properties: {
        FunctionName: {'Fn::GetAtt': ['QueryHandlerLambdaFunction', 'Arn']},
        Action: 'lambda:InvokeFunction',
        Principal: 'apigateway.amazonaws.com',
        SourceAccount: {Ref: 'AWS::AccountId'},
        SourceArn: {
          'Fn::Join': [
            '',
            [
              'arn:aws:execute-api:',
              {'Ref': 'AWS::Region'},
              ":",
              {'Ref': 'AWS::AccountId'},
              ":",

              {'Ref': 'ApiGatewayRestApi'},
              "/*"
            ]
          ]
        }
      }
    },
    apiGatewayEndpoint: {
      Type: 'AWS::SSM::Parameter',
      Properties: {
        Name: '${self:provider.stackName}-apiGatewayEndpoint',
        Description: 'API Gateway Endpoint',
        Type: 'String',
        Value: {
          'Fn::Join': [
            '',
            [
              'https://',
              {
                Ref: 'ApiGatewayRestApi',
              },
              '.execute-api.${self:provider.region}.amazonaws.com/',
              '${opt:stage}'
            ],
          ],
        },
      },
    },
  },

  functions: createHanlders(),
  outputs: {
    apiGatewayId: {
      Value: {Ref: 'ApiGatewayRestApi'},
      Export: {
        Name: {
          'Fn::Join': ['', ['${self:provider.stackName}', '-apiGatewayId']]
        }
      }
    },
  },
  environment: {
    STAGE: "${opt:stage}",
    DB_USER: '${self:custom.auroraUsername}',
    DB_HOST: '${self:custom.auroraEndpoint}',
    DB_NAME: '${self:custom.auroraDbName}',
    DB_PASSWORD: '${self:custom.auroraUserCredential.password}',
    SOURCE_EMAIL_ADDRESS: '${env:EMAIL_SOURCE_PDF}',
    MESSAGE_MEDIA_API_KEY_SSM_NAME: '${env:STATIC_ENV_NAME}-messagemedia-api-key',
    MESSAGE_MEDIA_API_SECRET_SSM_NAME: '${env:STATIC_ENV_NAME}-messagemedia-api-secret',
    DOMICILE_LOOKUP_ENABLED: '${env:DOMICILE_LOOKUP_ENABLED}',
  },
  custom: {
    prune,
    esbuild: esbuildWithSharpAndPdfkit,
    service: '${opt:stage}-ims-engine',
    auroraDbName: '${env:POSTGRESQL_DB_NAME}',
    auroraUsername: '${env:POSTGRESQL_DB_USER}',
    auroraUserCredential: '${ssm:/aws/reference/secretsmanager/${env:AURORA_STAGE}-bff-db/DbUserSecret}',
    lambdaParameterExtensionAccountId: '${ssm:Parameters-and-Secrets-Lambda-Extension}',
    auroraEndpoint: '${ssm:${env:AURORA_DB_ENDPOINT}}',
    imsCqrsCommandHandler: '${opt:stage}-ims-cqrs-commandHandlers-handler',
    pdfBucketName: '${ssm:${opt:stage}-ims-engine-assets-invoice-doc-uploads}',
    cqrsSqsArn: {'Fn::ImportValue': '${opt:stage}-ims-cqrs-iac-sqs-sqsArn'},
    cqrsSqsUrl: {'Fn::ImportValue': '${opt:stage}-ims-cqrs-iac-sqs-sqsUrl'},
    sagaHandler: '${opt:stage}-ims-engine-api-sagaHandler',
    failedEmailSqsQueue: '${opt:stage}-ims-engine-monitoring-sqs-failed-to-send-email',
  }
});

const app = sls.build();

module.exports = app;
