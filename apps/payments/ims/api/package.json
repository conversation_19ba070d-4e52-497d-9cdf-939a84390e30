{"name": "bff-ims-api", "version": "2.0.4", "description": "Invoice Service API to be invoked by Appsync", "compilerOptions": {"module": "commonjs", "declaration": false, "noImplicitAny": false, "noUnusedLocals": true, "removeComments": true, "noLib": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "ES2021", "sourceMap": true, "allowJs": true, "outDir": "./dist"}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"], "scripts": {"prepublish": "tsc --build tsconfig.json", "build": "rm -rf dist && NODE_OPTIONS='--max-old-space-size=8192' yarn tsc --build tsconfig.json", "test": "NODE_OPTIONS='--max-old-space-size=8192' yarn jest --no-cache --maxWorkers=75% --bail=1", "test:cov": "node --expose-gc ./node_modules/.bin/jest --no-cache --coverage", "system:test": "jest --config tests/system/systemJest.config.js --runInBand --forceExit", "lint": "yarn eslint src", "lint-fix": "yarn eslint src --fix", "run-audit": "yarn npm audit --environment production --exclude dicer", "destroy": "bin/destroy.sh", "deploy:layers": "cd layers/ && serverless deploy --componentName bff --partName ims --region ap-southeast-2 --stage", "deploy": "sh bin/deploy.sh"}, "husky": {"hooks": {"pre-push": "yarn lint && yarn test"}}, "repository": {"type": "git", "url": "git+https://bitbucket.org/npco_dev/component-bff-ims-api.git"}, "keywords": ["bff", "Backend for frontend", "backend", "service"], "author": "<PERSON><PERSON>", "license": "ISC", "homepage": "https://bitbucket.org/npco_dev/component-bff-ims-api#readme", "dependencies": {"@aws-sdk/client-dynamodb": "3.435.0", "@aws-sdk/client-lambda": "3.435.0", "@aws-sdk/client-s3": "3.435.0", "@aws-sdk/client-sqs": "3.435.0", "@aws-sdk/s3-request-presigner": "3.435.0", "@nestjs/common": "^7.4.2", "@nestjs/config": "^0.5.0", "@nestjs/core": "^7.4.2", "@nestjs/event-emitter": "^1.1.0", "@nestjs/platform-express": "^10.3.2", "@nestjs/testing": "^7.2.0", "@nestpack/hooks": "^1.0.0", "@npco/bff-common": "workspace:*", "@npco/component-bff-core": "workspace:*", "@npco/component-bff-serverless": "workspace:*", "@npco/component-dbs-mp-common": "workspace:*", "@npco/component-domain-events": "^15.0.0", "@npco/component-dto-addressbook": "workspace:*", "@npco/component-dto-catalog": "workspace:*", "@npco/component-dto-core": "workspace:*", "@npco/component-dto-customer": "workspace:*", "@npco/component-dto-invoice": "workspace:*", "@npco/component-dto-site": "workspace:*", "@npco/component-events-cims": "workspace:*", "@npco/component-events-core": "workspace:*", "@npco/component-events-crms": "workspace:*", "@npco/component-events-ims": "workspace:*", "@npco/component-events-mp": "workspace:*", "@npco/component-events-proxy": "workspace:*", "aws-lambda": "^1.0.6", "aws-xray-sdk": "3.5.1", "axios": "^1.8.2", "dicer": "^0.3.1", "joi": "^17.6.0", "lodash": "^4.17.21", "pdfkit": "0.13.0", "reflect-metadata": "^0.1.12", "rxjs": "^7.5.7", "serverless-plugin-scripts": "1.0.2", "sharp": "0.33.4", "uuid": "^8.3.2", "winston": "^3.9.0"}, "devDependencies": {"@aws-sdk/types": "3.413.0", "@nestjs/testing": "^7.4.0", "@npco/eslint-config-backend": "^1.0.12", "@nx/jest": "20.7.0", "@rushstack/eslint-patch": "^1.6.0", "@shelf/jest-dynamodb": "^3.4.1", "@swc/core": "^1.3.85", "@swc/jest": "^0.2.29", "@types/aws-lambda": "^8.10.45", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.6", "@types/lodash": "^4.17.5", "@types/mocha": "^9.0.0", "@types/node": "^18.11.18", "@types/pdfkit": "^0.11.2", "@types/react": "^17.0.14", "@types/serverless": "^3.12.7", "@types/uuid": "^8.0.0", "aws-xray-sdk-core": "^3.5.0", "aws-xray-sdk-express": "^3.5.0", "aws-xray-sdk-mysql": "^3.5.0", "constructs": "^10.1.22", "cross-fetch": "^3.0.4", "custom-env": "^2.0.1", "dotenv": "^16.0.1", "esbuild": "^0.18.2", "esbuild-plugin-tsc": "^0.4.0", "eslint": "^8.54.0", "husky": "^4.2.5", "jest": "^29.7.0", "jest-html-reporter": "^3.10.2", "jest-junit": "^16.0.0", "jest-sonar": "^0.2.16", "jest-sonar-reporter": "^2.0.0", "mock-jwks": "^1.0.1", "nock": "^13.1.3", "react": "^17.0.2", "serverless": "^3.39.0", "serverless-create-global-dynamodb-table-tags": "^1.0.2", "serverless-esbuild": "^1.52.1", "serverless-plugin-resource-tagging": "^1.2.0", "serverless-plugin-tracing": "^2.0.0", "serverless-prune-plugin": "^2.0.2", "serverless-pseudo-parameters": "^2.6.1", "ts-jest": "^29.0.5", "ts-mockito": "^2.6.1", "ts-node": "^8.10.1", "typescript": "5.4.5"}, "jestSonar": {"reportPath": "dist", "reportFile": "test-reporter.xml", "indent": 2}, "prettier": "@npco/eslint-config-backend/prettier"}