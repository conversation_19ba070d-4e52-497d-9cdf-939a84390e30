import { convertHeicToPngOrJpeg, isHei<PERSON><PERSON><PERSON><PERSON> } from '@npco/bff-common/dist/image/imageProcessUtils';
import { InvalidRequest } from '@npco/component-bff-core/dist/error';
import { error, info } from '@npco/component-bff-core/dist/utils/logger';
import { AmsApiService } from '@npco/component-dbs-mp-common/dist/';
import { DbRecordType } from '@npco/component-dto-core/dist';
import type { Site, ZellerInvoiceSettings } from '@npco/component-dto-site/dist';
import { SiteUpdatedEventDto } from '@npco/component-dto-site/dist';

import { DeleteObjectCommand, GetObjectCommand, PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { Injectable } from '@nestjs/common';
import type { S3Event } from 'aws-lambda';
import sharp from 'sharp';

import { EnvService } from '../../config/envService';
import { EntityRepository } from '../../repositories';
import type { InvoiceLogoS3Record } from '../../types';

import { InvoiceLogoImage } from './invoiceLogoImage';

@Injectable()
export class LogoService {
  amsApiSites: AmsApiService;

  private readonly s3 = new S3Client({});

  constructor(private readonly envService: EnvService, private readonly entityRepository: EntityRepository) {
    this.amsApiSites = new AmsApiService(this.envService, this.envService.amsSiteEndpointPath, 'site');
  }

  shouldScaleDown = (actualW: number, actualH: number, maxW: number, maxH: number) => actualW > maxW || actualH > maxH;

  scaleDown = async (sharpInstance: sharp.Sharp, maxW: number, maxH: number) =>
    sharpInstance.resize(maxW, maxH, {
      fit: 'inside',
    });

  build = async (
    original: Buffer,
    logoSize: { width: number; height: number } = this.envService.invoiceLogoSize,
  ): Promise<Buffer> => {
    try {
      info('building invoice logo');
      const sharpInstance = sharp(original);
      const { width: maxWidth, height: maxHeight } = logoSize;
      let { width, height } = await sharpInstance.metadata();
      width = Number(width);
      height = Number(height);
      info('should scale down');
      const shouldResize = this.shouldScaleDown(width, height, maxWidth, maxHeight);
      info(`shouldResize: ${shouldResize}`);
      const resultImage = shouldResize ? await this.scaleDown(sharpInstance, maxWidth, maxHeight) : sharpInstance;
      const formattedImage = await resultImage.toFormat('png').toBuffer();
      return formattedImage;
    } catch (e) {
      error(`LogoService:build Error is ${e}`);
      throw new Error(JSON.stringify(e));
    }
  };

  getImageFromS3 = async (bucket: string, originalKey: string): Promise<Buffer> => {
    const { Body: image } = await this.s3.send(new GetObjectCommand({ Bucket: bucket, Key: originalKey }));
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return Buffer.from(await image!.transformToByteArray());
  };

  getEntityAndSiteUuidFromKey = (key: string): { entityUuid: string; siteUuid: string } => {
    const parts = key.split('/');
    const entityUuid = parts[0];
    const siteUuid = parts[1];
    return { entityUuid, siteUuid };
  };

  buildLogoImages = async (record: InvoiceLogoS3Record): Promise<InvoiceLogoImage> => {
    const original = await this.getImageFromS3(record.bucket, record.key);
    let updatedImage = original;
    if (original && isHeicHeif(original)) {
      updatedImage = (await convertHeicToPngOrJpeg(original)) as any;
    }
    const resizedImg = await this.build(updatedImage);
    const { entityUuid, siteUuid } = this.getEntityAndSiteUuidFromKey(record.key);
    return new InvoiceLogoImage({
      entityUuid,
      siteUuid,
      resizedImg,
      baseUrl: this.envService.invoiceLogoBaseUrl,
    });
  };

  getBucketsWithKeysFromEvents = (event: S3Event): { bucket: string; key: string; size: number }[] => {
    const records: { bucket: string; key: string; size: number }[] = event.Records.map((record) => {
      const bucket = record.s3.bucket.name;
      const key = decodeURIComponent(record.s3.object.key.replace(/\+/g, ' '));
      const size = record.s3.object.size;
      return { bucket, key, size };
    });
    info(records);
    return records;
  };

  prepareInvoiceLogoForUpload = async (event: S3Event): Promise<InvoiceLogoImage[]> => {
    const records = this.getBucketsWithKeysFromEvents(event);
    info('building images...');
    const tasks = records.map((record) => this.buildLogoImages(record));
    return Promise.all(tasks);
  };

  uploadInvoiceLogo = async (bucket: string, logo: InvoiceLogoImage): Promise<void> => {
    await this.s3.send(new PutObjectCommand({ Bucket: bucket, Body: logo.resizedImg, Key: logo.fileKeyResized }));
    info(`${logo.getResizedName()} was uploaded to ${bucket}`);
  };

  deleteTemporaryLogoFiles = async (event: S3Event) => {
    const records = this.getBucketsWithKeysFromEvents(event);
    const fileCleanupTasks = records.map((record) =>
      this.s3.send(
        new DeleteObjectCommand({
          Bucket: record.bucket,
          Key: record.key,
        }),
      ),
    );
    info('Removing temporary files...');
    await Promise.all(fileCleanupTasks);
    info('Removed.');
  };

  onInvoiceLogoUpload = async (event: S3Event) => {
    info(`Invoice logo upload event: ${JSON.stringify(event)}`);
    const records = this.getBucketsWithKeysFromEvents(event);
    const { siteUuid } = this.getEntityAndSiteUuidFromKey(records[0].key);
    try {
      const siteSettings = await this.entityRepository.findOneByIdOrReject<Site>(siteUuid, DbRecordType.SITE);
      await this.checkLogoLimit(siteSettings);
      const images = await this.prepareInvoiceLogoForUpload(event);
      const uploadTasks = images.map((logo) => this.updateSiteInvoiceLogo(logo, siteSettings));
      await Promise.all(uploadTasks);
    } catch (e) {
      error(`Logo update failed: ${JSON.stringify(e)}`, siteUuid);
    } finally {
      await this.deleteTemporaryLogoFiles(event);
    }
  };

  private readonly checkLogoLimit = async (siteSettings: Site) => {
    if (siteSettings.invoice?.customisation?.logos?.length === this.envService.invoiceLogoLimit) {
      throw new InvalidRequest(`Can't upload more than ${this.envService.invoiceLogoLimit} logo`);
    }
  };

  private readonly updateSiteInvoiceLogo = async (logo: InvoiceLogoImage, siteSettings: Site) => {
    info('uploading new logo...');
    await this.uploadInvoiceLogo(this.envService.invoiceLogoProcessedBucket, logo);
    info('done.');
    info('update URLs in site.invoice.customisation.logos settings');
    const logos = [];
    let selectedLogo;
    const logoUrl = logo.urlResized.toString();

    if (siteSettings.invoice?.customisation?.logos?.length) {
      logos.push(...siteSettings.invoice.customisation.logos, logoUrl);
    } else {
      logos.push(logoUrl);
      selectedLogo = logoUrl;
    }

    const customisation = selectedLogo
      ? {
          selectedLogo,
          logos,
        }
      : {
          logos,
        };

    await this.amsApiSites.update<SiteUpdatedEventDto>(
      logo.siteUuid,
      new SiteUpdatedEventDto({
        siteUuid: logo.siteUuid,
        entityUuid: logo.entityUuid,
        invoice: {
          customisation,
        } as ZellerInvoiceSettings,
      }),
    );
    info('site logo update finished.');
  };
}
