import * as utils from '@npco/bff-common/dist/image/imageProcessUtils';

import { DeleteObjectCommand, GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import axios from 'axios';
import { instance, mock, when } from 'ts-mockito';

import { EnvService } from '../../config/envService';
import { EntityRepository } from '../../repositories';

import { LogoService } from './logoService';

jest.mock('axios');

const mockSend = jest.fn();

jest.mock('@aws-sdk/client-s3', () => {
  const mockDeleteObjectCommand = jest.fn();
  const mockGetObjectCommand = jest.fn();
  const mockPutObjectCommand = jest.fn();
  return {
    S3Client: jest.fn(() => ({
      send: mockSend,
    })),
    DeleteObjectCommand: mockDeleteObjectCommand,
    GetObjectCommand: mockGetObjectCommand,
    PutObjectCommand: mockPutObjectCommand,
  };
});

jest.mock('sharp', () =>
  jest.fn(() => ({
    resize: jest.fn().mockReturnValue({
      toFormat: jest.fn().mockReturnThis(),
      toBuffer: jest.fn().mockReturnValue(Buffer.from('new image')),
    }),
    metadata: jest.fn().mockReturnValue({ withd: 1000, height: 1000 }),
  })),
);

/* eslint-disable */
const s3Event = {
  Records: [
    {
      eventVersion: '2.2',
      eventSource: 'aws:s3',
      awsRegion: 'us-west-2',
      eventTime: 'The time, in ISO-8601 format, for example, 1970-01-01T00:00:00.000Z',
      eventName: 'event-type',
      userIdentity: {
        principalId: 'Amazon-customer-ID-of-the-user-who-caused-the-event',
      },
      requestParameters: {
        sourceIPAddress: 'ip-address-where-request-came-from',
      },
      responseElements: {
        'x-amz-request-id': 'Amazon S3 generated request ID',
        'x-amz-id-2': 'Amazon S3 host that processed the request',
      },
      s3: {
        s3SchemaVersion: '1.0',
        configurationId: 'ID found in the bucket notification configuration',
        bucket: {
          name: 'bucket-name',
          ownerIdentity: {
            principalId: 'Amazon-customer-ID-of-the-bucket-owner',
          },
          arn: 'bucket-ARN',
        },
        object: {
          key: 'object-key',
          size: 2048,
          eTag: 'object eTag',
          versionId: 'object version if bucket is versioning-enabled, otherwise null',
          sequencer:
            'a string representation of a hexadecimal value used to determine event sequence, only used with PUTs',
        },
      },
      glacierEventData: {
        restoreEventData: {
          lifecycleRestorationExpiryTime:
            'The time, in ISO-8601 format, for example, 1970-01-01T00:00:00.000Z, of Restore Expiry',
          lifecycleRestoreStorageClass: 'Source storage class for restore',
        },
      },
    },
  ],
};

describe('site logo upload tests', () => {
  let logoService: LogoService;

  const mockEnvService = mock(EnvService);
  const mockEntityRepository = mock(EntityRepository);
  const encodedImage = new TextEncoder().encode('img')
  beforeEach(() => {
    logoService = new LogoService(instance(mockEnvService), instance(mockEntityRepository));
    when(mockEnvService.invoiceLogoBaseUrl).thenReturn('https://dashboard.myzeller.dev/receipt/assets');
    (axios as any).patch.mockReset();
    (axios as any).patch.mockResolvedValue({ data: {} });
    mockSend.mockReset();
    (GetObjectCommand as any as jest.Mock).mockReset();
    (PutObjectCommand as any as jest.Mock).mockReset();
    (DeleteObjectCommand as any as jest.Mock).mockReset();
  });

  it('should upload resized images to s3', async () => {
    const processedLogoBucket = 'process';
    const uploadLogoBucket = 'bucket-name';
    const fileKey = 'object-key';
    mockSend.mockResolvedValueOnce({
      Body: { transformToByteArray: jest.fn().mockResolvedValue(encodedImage) },
      Metadata: {},
    });
    when(mockEnvService.invoiceLogoUploadBucket).thenReturn(uploadLogoBucket);
    when(mockEnvService.invoiceLogoProcessedBucket).thenReturn(processedLogoBucket);
    when(mockEnvService.invoiceLogoSize).thenReturn({ width: 88, height: 88 });

    when(mockEntityRepository.findOneByIdOrReject).thenReturn(() => Promise.resolve({} as any));
    // act
    await logoService.onInvoiceLogoUpload(s3Event as any);
    // verify
    expect(mockSend).toHaveBeenCalledTimes(3);
    expect(GetObjectCommand).toHaveBeenCalledWith({ Bucket: uploadLogoBucket, Key: fileKey });
    expect(GetObjectCommand).toHaveBeenCalledTimes(1);
    expect(PutObjectCommand).toHaveBeenCalledTimes(1);
    expect(DeleteObjectCommand).toHaveBeenCalledTimes(1);
    expect((axios as any).patch).toHaveBeenCalledTimes(1);
  });

  it('should upload original and greyscale images to s3 for .heic images', async () => {
    const processedLogoBucket = 'process';
    const uploadLogoBucket = 'bucket-name';
    const stub = jest.spyOn(utils, 'convertHeicToPngOrJpeg').mockResolvedValueOnce(new ArrayBuffer(10));
    const stub1 = jest.spyOn(utils, 'isHeicHeif').mockReturnValue(true);
    mockSend.mockResolvedValueOnce({
      Body: { transformToByteArray: jest.fn().mockResolvedValue(encodedImage) },
      Metadata: {},
    });
    when(mockEnvService.invoiceLogoUploadBucket).thenReturn(uploadLogoBucket);
    when(mockEnvService.invoiceLogoProcessedBucket).thenReturn(processedLogoBucket);
    when(mockEnvService.invoiceLogoSize).thenReturn({ width: 88, height: 88 });
    (axios as any).patch.mockResolvedValue(true);
    when(mockEntityRepository.findOneByIdOrReject).thenReturn(() => Promise.resolve({} as any));
    // act
    await logoService.onInvoiceLogoUpload(s3Event as any);
    // verify
    expect(mockSend).toHaveBeenCalledTimes(3);
    expect(GetObjectCommand).toHaveBeenCalledWith({ Bucket: uploadLogoBucket, Key: 'object-key' });
    expect(GetObjectCommand).toHaveBeenCalledTimes(1);
    expect(PutObjectCommand).toHaveBeenCalledTimes(1);
    expect(DeleteObjectCommand).toHaveBeenCalledTimes(1);
    expect((axios as any).patch).toHaveBeenCalledTimes(1);
    stub.mockRestore();
    stub1.mockRestore();
  });

  it('should throw error when limit logo array', async () => {
    const processedLogoBucket = 'process';
    const uploadLogoBucket = 'bucket-name';
    mockSend.mockResolvedValueOnce({
      Body: { transformToByteArray: jest.fn().mockResolvedValue(encodedImage) },
      Metadata: {},
    });
    when(mockEnvService.invoiceLogoUploadBucket).thenReturn(uploadLogoBucket);
    when(mockEnvService.invoiceLogoProcessedBucket).thenReturn(processedLogoBucket);
    when(mockEnvService.invoiceLogoSize).thenReturn({ width: 88, height: 88 });
    when(mockEnvService.invoiceLogoLimit).thenReturn(5);

    (axios as any).patch.mockResolvedValue(true);
    when(mockEntityRepository.findOneByIdOrReject).thenReturn(() =>
      Promise.resolve({
        invoice: {
          customisation: {
            logos: [...Array(5).keys()],
          },
        },
      } as any),
    );
    await logoService.onInvoiceLogoUpload(s3Event as any);
    expect(mockSend).toHaveBeenCalled();
  });

  it('should upload resized images to s3 when logo already exist', async () => {
    const processedLogoBucket = 'process';
    const uploadLogoBucket = 'bucket-name';
    const fileKey = 'object-key';
    mockSend.mockResolvedValueOnce({
      Body: { transformToByteArray: jest.fn().mockResolvedValue(encodedImage) },
      Metadata: {},
    });
    when(mockEnvService.invoiceLogoUploadBucket).thenReturn(uploadLogoBucket);
    when(mockEnvService.invoiceLogoProcessedBucket).thenReturn(processedLogoBucket);
    when(mockEnvService.invoiceLogoSize).thenReturn({ width: 88, height: 88 });

    (axios as any).patch.mockResolvedValue(true);
    when(mockEntityRepository.findOneByIdOrReject).thenReturn(() =>
      Promise.resolve({
        invoice: {
          customisation: {
            logos: ['https://google.com/logo.jpg'],
          },
        },
      } as any),
    );
    // act
    logoService.build = jest.fn();
    await logoService.onInvoiceLogoUpload(s3Event as any);
    // verify
    expect(mockSend).toHaveBeenCalledTimes(3);
    expect(GetObjectCommand).toHaveBeenCalledWith({ Bucket: uploadLogoBucket, Key: fileKey });
    expect(GetObjectCommand).toHaveBeenCalledTimes(1);
    expect(PutObjectCommand).toHaveBeenCalledTimes(1);
    expect(DeleteObjectCommand).toHaveBeenCalledTimes(1);
    expect((axios as any).patch).toHaveBeenCalledTimes(1);
  });

  it('should upload resized images to s3 when customisation in siteSettings', async () => {
    const processedLogoBucket = 'process';
    const uploadLogoBucket = 'bucket-name';
    const fileKey = 'object-key';
    mockSend.mockResolvedValueOnce({
      Body: { transformToByteArray: jest.fn().mockResolvedValue(encodedImage) },
      Metadata: {},
    });
    when(mockEnvService.invoiceLogoUploadBucket).thenReturn(uploadLogoBucket);
    when(mockEnvService.invoiceLogoProcessedBucket).thenReturn(processedLogoBucket);
    when(mockEnvService.invoiceLogoSize).thenReturn({ width: 88, height: 88 });

    (axios as any).patch.mockResolvedValue(true);
    when(mockEntityRepository.findOneByIdOrReject).thenReturn(() =>
      Promise.resolve({
        invoice: {
          customisation: {},
        },
      } as any),
    );
    // act
    logoService.build = jest.fn();
    await logoService.onInvoiceLogoUpload(s3Event as any);
    // verify
    expect(mockSend).toHaveBeenCalledTimes(3);
    expect(GetObjectCommand).toHaveBeenCalledWith({ Bucket: uploadLogoBucket, Key: fileKey });
    expect(GetObjectCommand).toHaveBeenCalledTimes(1);
    expect(PutObjectCommand).toHaveBeenCalledTimes(1);
    expect(DeleteObjectCommand).toHaveBeenCalledTimes(1);
    expect((axios as any).patch).toHaveBeenCalledTimes(1);
  });

  it('should upload resized images to s3 when customisation not exist in siteSettings', async () => {
    const processedLogoBucket = 'process';
    const uploadLogoBucket = 'bucket-name';
    const fileKey = 'object-key';
    mockSend.mockResolvedValueOnce({
      Body: { transformToByteArray: jest.fn().mockResolvedValue(encodedImage) },
      Metadata: {},
    });
    when(mockEnvService.invoiceLogoUploadBucket).thenReturn(uploadLogoBucket);
    when(mockEnvService.invoiceLogoProcessedBucket).thenReturn(processedLogoBucket);

    (axios as any).patch.mockResolvedValue(true);
    when(mockEntityRepository.findOneByIdOrReject).thenReturn(() =>
      Promise.resolve({
        invoice: {},
      } as any),
    );
    // act
    logoService.build = jest.fn();
    await logoService.onInvoiceLogoUpload(s3Event as any);
    // verify
    expect(mockSend).toHaveBeenCalledTimes(3);
    expect((axios as any).patch).toHaveBeenCalledTimes(1);
    expect(GetObjectCommand).toHaveBeenCalledWith({ Bucket: uploadLogoBucket, Key: fileKey });
    expect(GetObjectCommand).toHaveBeenCalledTimes(1);
    expect(PutObjectCommand).toHaveBeenCalledTimes(1);
    expect(DeleteObjectCommand).toHaveBeenCalledTimes(1);
  });
});
