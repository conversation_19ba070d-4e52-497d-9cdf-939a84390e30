import commonConfig from '../../../../jest.preset';

const config = {
  ...commonConfig,
  preset: '@shelf/jest-dynamodb',
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
  coveragePathIgnorePatterns: ['testcases', 'index.ts', 'types.ts', 'setupTests.ts'],
  reporters: [
    ['github-actions', { silent: false }],
    [
      'jest-junit',
      {
        outputDirectory: 'dist/',
        outputName: 'report.xml',
        uniqueOutputName: 'false',
        titleTemplate: '{classname}-{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: 'true',
        includeConsoleOutput: 'true',
        suiteName: 'Test Report',
      },
    ],
    [
      'jest-html-reporter',
      {
        pageTitle: 'Test Report',
        outputPath: 'dist/test-report.html',
      },
    ],
  ],
};

export default config;
