import { retry, sendSqsMessage, DynamodbClient, getCqrsSqsEndpoint } from '@npco/bff-systemtest-utils';
import { createTransactionRequestDto } from '@npco/component-dbs-mp-common/dist/transaction/testcases/utils';

import { v4 } from 'uuid';

import { createTransactionResponseDto } from './txnUtils';
import { region } from './utils';

describe('ZPOS engine transaction projections system tests', () => {
  const dbClient = new DynamodbClient({ region });
  const txnTableName = `${process.env.STAGE}-zpos-engine-dynamodb-Transactions`;

  let cqrsSqsEndpoint: string;

  const getEvent = (aggregateId: string, entityUuid: string, action: string, aggregate: string, payload: any) => {
    const uri = `zpos.${aggregate}.${action}`;
    return {
      'detail-type': uri,
      detail: {
        uri,
        aggregateId,
        payload: {
          entityUuid,
          ...payload,
        },
      },
    };
  };

  beforeAll(async () => {
    cqrsSqsEndpoint = await getCqrsSqsEndpoint('zpos');
  });

  describe('transaction projections', () => {
    const entityUuid = v4();
    let aggregateId: string;
    const siteUuid = v4();
    const deviceUuid = v4();
    const customerUuid = v4();

    it('should save Requested and Approved projections in order', async () => {
      const reqtxn = createTransactionRequestDto(entityUuid, siteUuid, deviceUuid, customerUuid);
      aggregateId = reqtxn.transactionUuid;
      // step 1: send Requested event
      await sendSqsMessage(cqrsSqsEndpoint, getEvent(aggregateId, entityUuid, 'Requested', 'Transaction', reqtxn));
      let txn;
      await retry(async () => {
        txn = await dbClient.getDbItem(txnTableName, aggregateId);
        expect(txn).not.toBeUndefined();
        console.info(txn);
        expect(txn?.status).toBe('PROCESSING');
      });
      // step 2: send Approved event
      await sendSqsMessage(
        cqrsSqsEndpoint,
        getEvent(
          aggregateId,
          entityUuid,
          'Approved',
          'Transaction',
          createTransactionResponseDto(aggregateId, siteUuid, deviceUuid, v4()),
        ),
      );
      await retry(async () => {
        const approvedTxn = await dbClient.getDbItem(txnTableName, aggregateId);
        expect(approvedTxn).not.toBeUndefined();
        expect(approvedTxn?.status).toBe('APPROVED');
      });
    });

    it('should handle Transaction.Cancelled event after Transaction.Approved', async () => {
      const reqtxn = createTransactionRequestDto(entityUuid, siteUuid, deviceUuid, customerUuid);
      aggregateId = reqtxn.transactionUuid;
      // step 1: send Requested event
      await sendSqsMessage(cqrsSqsEndpoint, getEvent(aggregateId, entityUuid, 'Requested', 'Transaction', reqtxn));
      let txn;
      await retry(async () => {
        txn = await dbClient.getDbItem(txnTableName, aggregateId);
        expect(txn).not.toBeUndefined();
        console.info(txn);
        expect(txn?.status).toBe('PROCESSING');
      });
      // step 2: send Approved event
      await sendSqsMessage(
        cqrsSqsEndpoint,
        getEvent(
          aggregateId,
          entityUuid,
          'Approved',
          'Transaction',
          createTransactionResponseDto(aggregateId, siteUuid, deviceUuid, v4()),
        ),
      );
      await retry(async () => {
        const approvedTxn = await dbClient.getDbItem(txnTableName, aggregateId);
        expect(approvedTxn).not.toBeUndefined();
        expect(approvedTxn?.status).toBe('APPROVED');
      });

      // step 3: send Cancelled event
      await sendSqsMessage(cqrsSqsEndpoint, getEvent(aggregateId, entityUuid, 'Cancelled', 'Transaction', {}));
      await retry(async () => {
        const cancelledTxn = await dbClient.getDbItem(txnTableName, aggregateId);
        expect(cancelledTxn).not.toBeUndefined();
        expect(cancelledTxn?.status).toBe('DECLINED');
      });
    });
  });
});
