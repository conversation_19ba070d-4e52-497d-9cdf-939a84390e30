// eslint-disable-next-line import/no-extraneous-dependencies
import { CloudFormation } from '@aws-sdk/client-cloudformation';
import { Lambda } from '@aws-sdk/client-lambda';
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import { SSM } from '@aws-sdk/client-ssm';

const componentName = 'zpos';
const partName = 'engine';
const dbPort = process.env.DB_PORT || 5432;
export const region = process.env.AWS_REGION || 'ap-southeast-2';
const databaseName = process.env.DB_NAME || 'ZPOSEngine';
const stage = process.env.STAGE || 'dev';
const auroraEnv = process.env.AuroraEnvironment || 'dev';
let lambda: Lambda | null = null;

let smClient: SecretsManagerClient | null = null;

export const getStackOutputs = async (stackName: any) => {
  const cloudformation = new CloudFormation({ region });
  return cloudformation
    .describeStacks({ StackName: stackName })
    .then((data: any) => {
      return data.Stacks[0].Outputs;
    })
    .catch((e: any) => {
      console.error(e);
      throw e;
    });
};

export const getEngineStackName = (stack: string) => `${stage}-${componentName}-${partName}-${stack}`;
export const getApiEndpoint = async () => {
  const stackOutputs = await getStackOutputs(getEngineStackName('api'));
  const endpoint = stackOutputs.find((output: any) => output.OutputKey === 'ServiceEndpoint').OutputValue;
  console.log('endpoint = ', endpoint);
  return endpoint;
};

const getDbUserCredential = async () => {
  if (!smClient) {
    smClient = new SecretsManagerClient({ region });
  }
  const getSecretValueCommand = new GetSecretValueCommand({
    SecretId: `${auroraEnv}-bff-db/DbUserSecret`,
  });
  const secureStringToken = await smClient.send(getSecretValueCommand);
  const credential = JSON.parse(secureStringToken.SecretString ?? '');
  return { username: credential.username, password: credential.password };
};

export const getAuroraEndpoint = async () => {
  const ssm = new SSM();
  const param = await ssm.getParameter({ Name: `/${auroraEnv}-bff-db/DatabaseEndpointURL` });
  return param.Parameter?.Value;
};

export const getDbClientOptions = async (): Promise<any> => {
  const host = await getAuroraEndpoint();
  const port = parseInt(`${dbPort}`, 10);
  const credential = await getDbUserCredential();
  return {
    host,
    user: credential.username,
    port,
    password: credential.password,
    database: databaseName,
    ssl: {
      rejectUnauthorized: false,
    },
    schemas: stage,
    search_path: stage,
  };
};

export const invokeLambda = async (handlerName: string, requestDto: any) => {
  if (!lambda) {
    lambda = new Lambda();
  }
  console.log(`send ${JSON.stringify(requestDto, null, 2)} to : ${handlerName}`);
  const result = await lambda
    .invoke({
      FunctionName: handlerName,
      Payload: JSON.stringify(requestDto),
    })
    .catch((e) => console.error(e));
  if (result && result.Payload) {
    return JSON.parse(Buffer.from(result.Payload).toString());
  }
  return undefined;
};

const getLambdaFullName = () => `${process.env.STAGE}-zpos-engine-api-mutationHandler`;

export const sendMutationRequest = async (testCaseInput: { input: any; requestName: string }) => {
  const { input, requestName } = testCaseInput;
  const res = await invokeLambda(getLambdaFullName(), {
    body: JSON.stringify({ input }),
    pathParameters: { requestName },
  });
  console.info('response:', res);
  return { status: res.statusCode, data: JSON.parse(res.body) };
};
