import { CatalogDiscountConfig, CatalogServiceChargeConfig, CatalogUnit } from '@npco/component-dto-catalog/dist';
import { OrderItemType, OrderStatus } from '@npco/component-dto-order/dist';

import { v4 } from 'uuid';

import { TenderType } from '../../services/types';
import { TRANSACTION_AMOUNT_TO_CENTICENTS_RATE } from '../const';

import { OrderDiscountModel } from './orderDiscountModel';
import { OrderItemModel } from './orderItemModel';
import { OrderModel } from './orderModel';
import { OrderServiceChargeModel } from './orderServiceChargeModel';

const convertCentsToCentiCents = (cents: number) => cents * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE;

describe('OrderModel test suite', () => {
  const discountModel = new OrderDiscountModel();
  const serviceChargeModel = new OrderServiceChargeModel();
  const orderModel = new OrderModel(
    new OrderItemModel(discountModel, serviceChargeModel),
    discountModel,
    serviceChargeModel,
  );

  it('should create order with OPEN status', async () => {
    const orderInput = {
      id: v4(),
      entityUuid: v4(),
      siteUuid: v4(),
      catalogSettings: { itemsTaxInclusive: true },
      items: [
        {
          name: v4(),
          price: 10000,
          taxes: [{ name: 'GST', value: 10, enabled: true }],
          catalogItem: {
            id: v4(),
          },
        },
      ],
      discounts: [{ config: CatalogDiscountConfig.AMOUNT, value: 1000 }],
      serviceCharges: [{ config: CatalogServiceChargeConfig.AMOUNT, value: 1000 }],
    };
    const order = await orderModel.create(orderInput as any);
    expect(order).toBeDefined();
    expect(order.status).toBe(OrderStatus.OPEN);
    expect(order.totalDiscount).toEqual(1000);
    expect(order.totalServiceCharge).toEqual(1000);
    expect(order.orderAmount).toEqual(11000);
    expect(order.dueAmount).toEqual(11000);
    expect(order.totalGst).toEqual(909);
    expect(order.orderGst).toEqual(909);
  });

  it('should throw error in updateTotalAmountsInUnpaidOrder when Order status is PAID', () => {
    expect(() => orderModel.updateAmountsInUnpaidOrder({ status: 'PAID' } as any, 0, 0, 0)).toThrow(
      `This method cannot be used on PAID orders.`,
    );
  });

  it('should update order status', async () => {
    const orderInput = {
      id: v4(),
      entityUuid: v4(),
      siteUuid: v4(),
      catalogSettings: { itemsTaxInclusive: true },
      items: [
        {
          name: v4(),
          price: 10000,
          catalogItem: {
            id: v4(),
          },
        },
      ],
      discounts: [{ config: CatalogDiscountConfig.AMOUNT, value: 1234 }],
      serviceCharges: [{ config: CatalogServiceChargeConfig.AMOUNT, value: 1234 }],
    };
    const order = await orderModel.create(orderInput as any);
    const updatedOrder = await orderModel.update(order, { ...order, status: OrderStatus.CANCELLED } as any);
    expect(updatedOrder).toBeDefined();
    expect(updatedOrder.status).toBe(OrderStatus.CANCELLED);
    updatedOrder.items!.forEach((item) => {
      expect({ ...item, discounts: undefined, serviceCharges: undefined }).toEqual(
        order.items!.find((i) => i.id === item.id),
      );
    });

    expect(updatedOrder.catalogSettings).toEqual(order.catalogSettings);
    expect(updatedOrder.entityUuid).toEqual(order.entityUuid);
    expect(updatedOrder.siteUuid).toEqual(order.siteUuid);
    expect(updatedOrder.currency).toEqual(order.currency);
    expect(updatedOrder.referenceNumber).toEqual(order.referenceNumber);
  });

  describe('Order due amount calculation after recording a payment', () => {
    it('should throw error when dueAmount is negative and is smaller than the ZERO_CENT_SALE_TRESHOLD', () => {
      expect(() =>
        orderModel.calculateDueAmountAfterPayment(
          {
            dueAmount: 10000,
          } as any,
          {
            saleAmount: 110,
          } as any,
          TenderType.CASH,
        ),
      ).toThrow('dueAmount cannot be negative');
    });
    it('should set dueAmount as 0 when card payment amount makes it negative and smaller than the ZERO_CENT_SALE_TRESHOLD', () => {
      expect(
        orderModel.calculateDueAmountAfterPayment(
          {
            dueAmount: 10000,
          } as any,
          {
            saleAmount: 110,
          } as any,
          TenderType.CARD,
        ),
      ).toEqual(0);
    });

    it('should set dueAmount as 0 when its negative but less or equal to the ZERO_CENT_SALE_TRESHOLD', () => {
      expect(
        orderModel.calculateDueAmountAfterPayment(
          {
            dueAmount: 10050, // centicents
          } as any,
          {
            saleAmount: 101, // cents
          } as any,
          TenderType.CARD,
        ),
      ).toEqual(0);
    });
    it('should set dueAmount as 0 when its positive but less than the ZERO_CENT_SALE_TRESHOLD', () => {
      expect(
        orderModel.calculateDueAmountAfterPayment(
          {
            dueAmount: 10149, // centicents
          } as any,
          {
            saleAmount: 101, // cents
          } as any,
          TenderType.CARD,
        ),
      ).toEqual(0);
    });

    it('should return dueAmount as 0 when cashRoundingAdjustment provided as negative value in cash payment', () => {
      expect(
        orderModel.calculateDueAmountAfterPayment(
          {
            dueAmount: 10300, // centicents
          } as any,
          {
            saleAmount: 100, // cents
            cashRoundingAdjustment: -3, // cents
          } as any,
          TenderType.CASH,
        ),
      ).toEqual(0);
    });
    it('should return dueAmount as 0 when cashRoundingAdjustment provided as positive value in cash payment', () => {
      expect(
        orderModel.calculateDueAmountAfterPayment(
          {
            dueAmount: 10400, // centicents
          } as any,
          {
            saleAmount: 105, // cents
            cashRoundingAdjustment: 1, // cents
          } as any,
          TenderType.CASH,
        ),
      ).toEqual(0);
    });
  });

  describe('Order status', () => {
    it('should throw error when dueAmount is negative', () => {
      expect(() =>
        orderModel.getOrderStatusForUpdatedDueAmount({
          dueAmount: -1,
        } as any),
      ).toThrow('dueAmount cannot be negative');
    });

    it('should update order status to PAID when dueAmount = 0', async () => {
      expect(
        orderModel.getOrderStatusForUpdatedDueAmount({
          dueAmount: 0,
        } as any),
      ).toEqual(OrderStatus.PAID);
    });

    it('should update order status to PAID when dueAmount > 0 and dueAmount < ZERO_CENT_SALE_TRESHOLD', async () => {
      expect(
        orderModel.getOrderStatusForUpdatedDueAmount({
          dueAmount: 49,
        } as any),
      ).toEqual(OrderStatus.PAID);
    });

    it('should update order status to PART_PAID when paidAmount > 0 but not fully paid', async () => {
      expect(
        orderModel.getOrderStatusForUpdatedDueAmount({
          dueAmount: 1000,
          paidAmount: 500,
        } as any),
      ).toEqual(OrderStatus.PART_PAID);
    });
    it('should update order status to OPEN when paidAmount = 0 and dueAmount is greater than zero dollar sale treshold', async () => {
      expect(
        orderModel.getOrderStatusForUpdatedDueAmount({
          dueAmount: 1000,
          paidAmount: 0,
        } as any),
      ).toEqual(OrderStatus.OPEN);
    });
  });

  describe('Order payments', () => {
    describe('recordNonCardPayment', () => {
      it('should throw error when cashRoundingAdjustment provided in a payment that is not the last for the order', async () => {
        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };
        const order = await orderModel.create(orderInput as any);
        await expect(
          orderModel.recordNonCardPayment({
            order,
            nonCardCheckoutInput: {
              id: v4(),
              orderUuid: order.id,
              entityUuid: order.entityUuid,
              tenderType: TenderType.CASH,
              amount: 50,
              amountTendered: 100,
              change: 0,
              cashRoundingAdjustment: 3,
              taxAmounts: [{ amount: 10 } as any],
              timestampLocal: new Date().toISOString(),
            },
          }),
        ).rejects.toThrow('[400] Cash rounding adjustment is only allowed for the last payment');
      });
      it('should update orderAmount with cashRoundingAdjustment when provided', async () => {
        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 102 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };
        const order = await orderModel.create(orderInput as any);
        const cashRoundingAdjustment = -2;
        const { order: updatedOrder } = await orderModel.recordNonCardPayment({
          order: JSON.parse(JSON.stringify(order)),
          nonCardCheckoutInput: {
            id: v4(),
            orderUuid: order.id,
            entityUuid: order.entityUuid,
            tenderType: TenderType.CASH,
            amount: 100,
            amountTendered: 100,
            change: 0,
            cashRoundingAdjustment,
            taxAmounts: [{ amount: 10 } as any],
            timestampLocal: new Date().toISOString(),
          },
        });
        expect(updatedOrder).toBeDefined();
        expect(updatedOrder.orderAmount).toBe(
          order.orderAmount + cashRoundingAdjustment * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
        );
      });
    });
    describe('updateOrderPayment', () => {
      it('should update order payment with tips provided', async () => {
        const additionalTipsCents = 100;
        const additionalTipsCentiCents = convertCentsToCentiCents(additionalTipsCents);
        const txnAmountCents = 110;
        const txnAmountCentitCents = convertCentsToCentiCents(txnAmountCents);
        const txnGst = 10;
        const txnGstInCentiCents = convertCentsToCentiCents(txnGst);

        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };
        const order = await orderModel.create(orderInput as any);
        const { order: orderWithCashPayment, newPayment: cashPayment } = await orderModel.recordNonCardPayment({
          order,
          nonCardCheckoutInput: {
            id: v4(),
            orderUuid: order.id,
            entityUuid: order.entityUuid,
            tenderType: TenderType.CASH,
            amount: txnAmountCents,
            amountTendered: 1000,
            change: 10,
            tipAmount: 10,
            taxAmounts: [{ amount: txnGst } as any],
            timestampLocal: new Date().toISOString(),
          },
        });

        expect(orderWithCashPayment.totalChange).toBe(1000);
        expect(orderWithCashPayment.totalAmountTendered).toBe(100000);
        expect(orderWithCashPayment.payments![0].change).toBe(1000);
        // update order tips, change
        const updatedOrder = await orderModel.updateOrderPayment(orderWithCashPayment, {
          paymentUuid: cashPayment.id,
          tips: additionalTipsCents,
          change: 0,
          updatedTime: new Date().toISOString(),
        } as any);
        expect(updatedOrder).toBeDefined();
        expect(updatedOrder.totalTips).toBe(additionalTipsCentiCents + order.totalTips!);
        expect(updatedOrder.orderAmount).toBe(order.orderAmount!);
        expect(updatedOrder.paidAmount).toBe(order.paidAmount! + additionalTipsCentiCents);
        expect(updatedOrder.totalGst).toBe(txnGstInCentiCents);
        expect(updatedOrder.orderGst).toBe(order.orderGst);
        expect(updatedOrder.payments![0].change).toBe(0);
        expect(updatedOrder.payments![0].amount).toBe(txnAmountCentitCents + additionalTipsCentiCents);
        expect(updatedOrder.totalChange).toBe(0);
        expect(updatedOrder.totalAmountTendered).toBe(100000);
        expect(updatedOrder.orderDisplayAmount!).toBe(order.paidAmount!);
      });
      it('should throw error when trying to update NON-CASH payment', async () => {
        const additionalTipsCents = 100;
        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };
        const order = await orderModel.create(orderInput as any);
        const { order: orderWithCashPayment, newPayment: cashPayment } = await orderModel.recordNonCardPayment({
          order,
          nonCardCheckoutInput: {
            id: v4(),
            orderUuid: order.id,
            entityUuid: order.entityUuid,
            tenderType: TenderType.CARD,
            amount: 100,
            tipAmount: 10,
            taxAmounts: [{ amount: 10 } as any],
            timestampLocal: new Date().toISOString(),
          },
        });

        await expect(
          orderModel.updateOrderPayment(orderWithCashPayment, {
            paymentUuid: cashPayment.id,
            tips: additionalTipsCents,
            updatedTime: new Date().toISOString(),
          } as any),
        ).rejects.toThrow('[400] Only cash payments can be updated');
      });
      it('should throw error when order payment is not found', async () => {
        const additionalTipsCents = 100;
        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };
        const order = await orderModel.create(orderInput as any);
        const { order: orderWithCashPayment } = await orderModel.recordNonCardPayment({
          order,
          nonCardCheckoutInput: {
            id: v4(),
            orderUuid: order.id,
            entityUuid: order.entityUuid,
            tenderType: TenderType.CARD,
            amount: 100,
            tipAmount: 10,
            taxAmounts: [{ amount: 10 } as any],
            timestampLocal: new Date().toISOString(),
          },
        });

        await expect(
          orderModel.updateOrderPayment(orderWithCashPayment, {
            paymentUuid: 'unknown',
            tips: additionalTipsCents,
            updatedTime: new Date().toISOString(),
          } as any),
        ).rejects.toThrow('[400] Payment not found');
      });
    });
    it('should update order with APPROVED payment', async () => {
      const transaction = {
        id: 'id',
        status: 'APPROVED',
        amount: 140,
        saleAmount: 100,
        taxAmounts: [
          { amount: 10, name: 'GST' },
          { amount: 30, name: 'GST' },
        ],
        timestampLocal: 'xx',
        reference: 'rrn',
      };

      const orderInput = {
        id: v4(),
        entityUuid: v4(),
        siteUuid: v4(),
        catalogSettings: { itemsTaxInclusive: true },
        items: [
          {
            name: v4(),
            price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
            catalogItem: {
              id: v4(),
            },
          },
        ],
      };
      const order = await orderModel.create(orderInput as any);
      const { order: updatedOrder, newPayment } = await orderModel.recordPayment({
        order,
        transaction: transaction as any,
      });
      expect(updatedOrder).toBeDefined();
      expect(updatedOrder.status).toBe(OrderStatus.PAID);
      expect(updatedOrder.paidAmount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
      expect(updatedOrder.orderAmount).toBe(order.orderAmount);
      expect(updatedOrder.totalGst).toBe(40 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
      expect(updatedOrder.orderGst).toBe(order.orderGst);
      expect(updatedOrder.dueAmount).toBe(
        order.orderAmount - transaction.saleAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
      );
      expect(updatedOrder.orderDisplayAmount).toBe(order.paidAmount);
      expect(newPayment).toBeDefined();
      expect(newPayment.amount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
      expect(newPayment.transactionUuid).toBeDefined();
      expect(newPayment.taxAmounts).toEqual([
        { amount: 10, name: 'GST' },
        { amount: 30, name: 'GST' },
      ]);
      expect(newPayment.status).toBe('APPROVED');
      expect(newPayment.shortId).toEqual('rrn');
      expect(newPayment.tenderType).toEqual(TenderType.CARD);
    });

    it('should update order with DECLINED payment', async () => {
      const orderInput = {
        id: v4(),
        entityUuid: v4(),
        siteUuid: v4(),
        catalogSettings: { itemsTaxInclusive: true },
        items: [
          {
            name: v4(),
            price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
            catalogItem: {
              id: v4(),
            },
          },
        ],
      };
      const order = await orderModel.create(orderInput as any);
      const { order: updatedOrder, newPayment } = await orderModel.recordPayment({
        order,
        transaction: { id: 'id', status: 'DECLINED', amount: 100 } as any,
      });
      expect(updatedOrder).toBeDefined();
      expect(updatedOrder.status).toBe(OrderStatus.OPEN);
      expect(updatedOrder.paidAmount).toBe(0);
      expect(updatedOrder.subtotalAmount).toEqual(order.subtotalAmount);
      expect(updatedOrder.orderAmount).toEqual(order.orderAmount);
      expect(newPayment).toBeDefined();
      expect(newPayment.amount).toBe(100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
      expect(newPayment.transactionUuid).toBeDefined();
      expect(newPayment.status).toBe('DECLINED');
    });

    it('should throw error when txn status is PROCESSING', async () => {
      const orderInput = {
        id: v4(),
        entityUuid: v4(),
        siteUuid: v4(),
        catalogSettings: { itemsTaxInclusive: true },
        items: [
          {
            name: v4(),
            price: 100,
            catalogItem: {
              id: v4(),
            },
          },
        ],
      };
      const order = await orderModel.create(orderInput as any);
      await expect(
        orderModel.recordPayment({
          order,
          transaction: { id: 'id', status: 'PROCESSING', amount: 100 } as any,
        }),
      ).rejects.toThrow('Invalid transaction status');
    });

    it('should roll back PAID order to OPEN status when txn status is CANCELLED', async () => {
      const transaction = {
        id: 'id',
        status: 'APPROVED',
        amount: 170,
        saleAmount: 100,
        surchargeAmount: 50,
        taxAmounts: [{ amount: 10 } as any],
        tipAmount: 10,
      };
      const orderInput = {
        id: v4(),
        entityUuid: v4(),
        siteUuid: v4(),
        catalogSettings: { itemsTaxInclusive: true },
        items: [
          {
            name: v4(),
            price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
            catalogItem: {
              id: v4(),
            },
          },
        ],
      };
      const order = await orderModel.create(orderInput as any);
      const { order: updatedOrder, newPayment } = await orderModel.recordPayment({
        order: JSON.parse(JSON.stringify(order)),
        transaction: transaction as any,
      });
      expect(updatedOrder).toBeDefined();
      expect(updatedOrder.status).toBe(OrderStatus.PAID);
      expect(updatedOrder.paidAmount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
      expect(updatedOrder.orderAmount).toBe(order.orderAmount);
      expect(updatedOrder.dueAmount).toBe(
        order.orderAmount - transaction.saleAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
      );
      expect(newPayment).toBeDefined();
      expect(newPayment.amount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
      expect(newPayment.transactionUuid).toBeDefined();
      expect(newPayment.status).toBe('APPROVED');

      const { order: cancelledOrder } = await orderModel.recordPayment({
        order: updatedOrder,
        transaction: {
          ...transaction,
          status: 'CANCELLED',
        } as any,
      });
      expect(cancelledOrder).toBeDefined();
      expect(cancelledOrder.status).toBe(OrderStatus.OPEN);
      expect(cancelledOrder.paidAmount).toBe(0);
      expect(cancelledOrder.payments).toHaveLength(1);
      expect(cancelledOrder.payments![0].status).toBe('DECLINED');
      expect(cancelledOrder.orderAmount).toBe(order.orderAmount);
      expect(cancelledOrder.subtotalAmount).toBe(order.subtotalAmount);
      expect(cancelledOrder.totalGst).toBe(order.totalGst);
      expect(cancelledOrder.subtotalAmount).toBe(order.subtotalAmount);
      expect(cancelledOrder.totalTips).toBe(order.totalTips);
    });
  });

  describe('getPaymentEntity: cash and other tender type payment', () => {
    it('should create transaction from cash payment without tips', async () => {
      const txn = orderModel.getTransactionFromNonCardCheckoutInput({
        id: v4(),
        orderUuid: v4(),
        entityUuid: v4(),
        tenderType: TenderType.CASH,
        amount: 100,
        amountTendered: 100,
        change: 0,
        currency: 'AUD',
        taxAmounts: [{ name: 'GST', amount: 1 }],
        timestampLocal: new Date().toISOString(),
      });
      expect(txn).toBeDefined();
      expect(txn.amount).toBe(100);
      expect(txn.saleAmount).toBe(100);
      expect(txn.status).toBe('APPROVED');
      expect(txn.taxAmounts).toEqual([{ name: 'GST', amount: 1 }]);
      expect(txn.tipAmount).toBe(0);
    });

    it('should create transaction from cash payment with tips', async () => {
      const txn = orderModel.getTransactionFromNonCardCheckoutInput({
        id: v4(),
        orderUuid: v4(),
        entityUuid: v4(),
        tenderType: TenderType.CASH,
        amount: 100,
        amountTendered: 100,
        change: 0,
        currency: 'AUD',
        tipAmount: 10,
        taxAmounts: [{ name: 'GST', amount: 1 }],
        timestampLocal: new Date().toISOString(),
      });
      expect(txn).toBeDefined();
      expect(txn.amount).toBe(100);
      expect(txn.saleAmount).toBe(90); // saleAmount = amount - tipAmount
      expect(txn.status).toBe('APPROVED');
      expect(txn.taxAmounts).toEqual([{ name: 'GST', amount: 1 }]);
      expect(txn.tipAmount).toBe(10);
    });
    it('should get cash and other payment entity', async () => {
      const orderInput = {
        id: v4(),
        entityUuid: v4(),
        siteUuid: v4(),
        catalogSettings: { itemsTaxInclusive: true },
        items: [
          {
            name: v4(),
            price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
            catalogItem: {
              id: v4(),
            },
          },
        ],
      };
      const order = await orderModel.create(orderInput as any);
      const payment = orderModel.getPaymentEntity({
        order,
        transaction: {
          id: 'id',
          status: 'APPROVED',
          amount: 100,
          tenderType: TenderType.CASH,
          taxAmounts: [
            { amount: 10, name: 'GST' },
            { amount: 30, name: 'GST' },
          ],
        } as any,
      });
      expect(payment.id).toEqual('id');
      expect(payment.tenderType).toEqual(TenderType.CASH);
    });
  });

  describe('isOrderUpdatedTimeValid', () => {
    it('should throw when order updated time is more than +-5 minutes from the system time', () => {
      const date = new Date();
      const oldTime = new Date(date.getTime() - 6 * 60000).toISOString();
      expect(() => orderModel.isOrderUpdatedTimeValid({ updatedTime: oldTime } as any, null)).toThrow(
        'order updatedTime is more than +-5 minutes from the system time',
      );
    });

    it('should throw when order input updated time is less than current order ', () => {
      const date = new Date();
      const oldTime = new Date(date.getTime() - 100).toISOString();
      const orderUpdatedTime = orderModel.getTimeUnixInMs();
      expect(() =>
        orderModel.isOrderUpdatedTimeValid(
          { updatedTime: oldTime } as any,
          { updatedTimeInMilliseconds: orderUpdatedTime } as any,
        ),
      ).toThrow('order input updatedTime is less than current order updatedTime');
    });

    it('should not throw error when time is provided', () => {
      const date = new Date();
      const orderUpdatedTime = orderModel.getTimeUnixInMs();
      const updatedTime = new Date(date.getTime() + 100).toISOString();
      expect(() =>
        orderModel.isOrderUpdatedTimeValid(
          { updatedTime } as any,
          { updatedTimeInMilliseconds: orderUpdatedTime } as any,
        ),
      ).not.toThrow();
    });

    it('should not throw error when updated time is not provided', () => {
      const orderUpdatedTime = orderModel.getTimeUnixInMs();
      expect(() =>
        orderModel.isOrderUpdatedTimeValid({} as any, { updatedTime: orderUpdatedTime } as any),
      ).not.toThrow();
    });
  });

  describe('POS_RECEIPTS_ENABLED is true', () => {
    const orderModel1 = new OrderModel(
      new OrderItemModel(discountModel, serviceChargeModel),
      discountModel,
      serviceChargeModel,
    );

    describe('order for multiple payments', () => {
      it('should update order with multiple payments', async () => {
        const txn1 = {
          saleAmount: 50,
          amount: 52,
          surchargeAmount: 1,
          tipAmount: 1,
        };

        const txn2 = {
          amount: 54,
          saleAmount: 50,
          surchargeAmount: 1,
          tipAmount: 1,
        };

        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };

        const order = await orderModel1.create(orderInput as any);
        const { order: updatedOrder, newPayment } = await orderModel1.recordPayment({
          order,
          transaction: {
            id: 'id',
            status: 'APPROVED',
            ...txn1,
          } as any,
        });
        expect(updatedOrder).toBeDefined();
        expect(updatedOrder.status).toBe(OrderStatus.PART_PAID);
        expect(updatedOrder.paidAmount).toBe(txn1.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder.orderAmount).toBe(order.orderAmount);
        expect(updatedOrder.dueAmount).toBe(
          order.orderAmount - txn1.saleAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
        );
        expect(updatedOrder.totalTips).toBe(txn1.tipAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder.totalSurcharge).toBe(txn1.surchargeAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(newPayment).toBeDefined();
        expect(newPayment.amount).toBe(txn1.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(newPayment.transactionUuid).toBeDefined();
        expect(newPayment.status).toBe('APPROVED');

        // payment 2
        const { order: updatedOrder2, newPayment: newPayment2 } = await orderModel1.recordPayment({
          order: updatedOrder,
          transaction: {
            id: 'id2',
            status: 'APPROVED',
            ...txn2,
          } as any,
        });
        expect(updatedOrder2).toBeDefined();
        expect(updatedOrder2.status).toBe(OrderStatus.PAID);
        expect(updatedOrder2.orderAmount).toBe(order.orderAmount);
        expect(updatedOrder2.totalTips).toBe((txn1.tipAmount + txn2.tipAmount) * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder2.totalSurcharge).toBe(
          (txn1.surchargeAmount + txn2.surchargeAmount) * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
        );
        expect(updatedOrder2.paidAmount).toBe((txn1.amount + txn2.amount) * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder2.dueAmount).toBe(0);
        expect(newPayment2).toBeDefined();
        expect(newPayment2.amount).toBe(txn2.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
      });
      it('should update order with single payment with rounding < 5', async () => {
        const transaction = {
          id: 'id',
          status: 'APPROVED',
          amount: 140,
          saleAmount: 100,
          taxAmounts: [
            { amount: 10, name: 'GST' },
            { amount: 30, name: 'GST' },
          ],
        };
        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 10049,
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };
        const order = await orderModel1.create(orderInput as any);
        const { order: updatedOrder, newPayment } = await orderModel1.recordPayment({
          order,
          transaction: transaction as any,
        });
        expect(updatedOrder).toBeDefined();
        expect(updatedOrder.status).toBe(OrderStatus.PAID);
        expect(updatedOrder.paidAmount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder.orderAmount).toBe(order.orderAmount);
        expect(updatedOrder.totalGst).toBe(40 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(newPayment).toBeDefined();
        expect(newPayment.amount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(newPayment.transactionUuid).toBeDefined();
        expect(newPayment.status).toBe('APPROVED');
      });

      it('should update order with single payment with rounding >= 5', async () => {
        const transaction = {
          id: 'id',
          status: 'APPROVED',
          saleAmount: 101,
          amount: 141,
          taxAmounts: [
            { amount: 10, name: 'GST' },
            { amount: 30, name: 'GST' },
          ],
        };
        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 10050,
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };
        const order = await orderModel1.create(orderInput as any);
        const { order: updatedOrder, newPayment } = await orderModel1.recordPayment({
          order,
          transaction: transaction as any,
        });
        expect(updatedOrder).toBeDefined();
        expect(updatedOrder.status).toBe(OrderStatus.PAID);
        expect(updatedOrder.paidAmount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder.orderAmount).toBe(order.orderAmount);
        expect(updatedOrder.dueAmount).toBe(0);
        expect(updatedOrder.totalGst).toBe(40 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(newPayment).toBeDefined();
        expect(newPayment.amount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(newPayment.transactionUuid).toBeDefined();
        expect(newPayment.status).toBe('APPROVED');
      });
    });

    describe('update an order with payments', () => {
      it('should update an order with payments', async () => {
        const transaction = {
          id: 'id',
          status: 'APPROVED',
          amount: 52,
          saleAmount: 50,
          taxAmounts: [
            { amount: 10, name: 'GST' },
            { amount: 30, name: 'GST' },
          ],
        };
        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };

        const order = await orderModel1.create(orderInput as any);
        const { order: updatedOrder } = await orderModel1.recordPayment({
          order,
          transaction: transaction as any,
        });
        expect(updatedOrder).toBeDefined();

        expect(updatedOrder.paidAmount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder.orderAmount).toBe(order.orderAmount);
        expect(updatedOrder.dueAmount).toBe(
          order.orderAmount - transaction.saleAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
        );
        expect(updatedOrder.status).toBe(OrderStatus.PART_PAID);

        const orderWithItemAdded = JSON.parse(JSON.stringify(updatedOrder));
        orderWithItemAdded.items = [
          orderInput.items[0],
          {
            name: v4(),
            price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
            catalogItem: {
              id: v4(),
            },
          },
        ];
        const finalOrder = await orderModel1.update(updatedOrder, orderWithItemAdded);
        expect(finalOrder.paidAmount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(finalOrder.orderAmount).toBe(200 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(finalOrder.dueAmount).toBe(
          finalOrder.orderAmount - transaction.saleAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
        );
      });
    });

    describe('recordFailedPayment', () => {
      it('should update order with failed payment', async () => {
        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };
        const order = await orderModel1.create(orderInput as any);
        const { order: updatedOrder, newPayment } = await orderModel1.recordPayment({
          order,
          transaction: { id: 'id', status: 'DECLINED', amount: 100 } as any,
        });
        expect(updatedOrder).toBeDefined();
        expect(updatedOrder.status).toBe(OrderStatus.OPEN);
        expect(updatedOrder.paidAmount).toBe(0);
        expect(updatedOrder.subtotalAmount).toEqual(order.subtotalAmount);
        expect(updatedOrder.orderAmount).toEqual(order.orderAmount);
        expect(newPayment).toBeDefined();
        expect(newPayment.amount).toBe(100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(newPayment.transactionUuid).toBeDefined();
        expect(newPayment.status).toBe('DECLINED');
      });
      it('should roll back PAID order to OPEN status when txn status is CANCELLED', async () => {
        const transaction = {
          id: 'id',
          status: 'APPROVED',
          amount: 102,
          saleAmount: 100,
          surchargeAmount: 1,
          taxAmounts: [],
          tipAmount: 1,
        };
        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };
        const order = await orderModel1.create(orderInput as any);
        const { order: updatedOrder, newPayment } = await orderModel1.recordPayment({
          order: JSON.parse(JSON.stringify(order)),
          transaction: transaction as any,
        });
        expect(updatedOrder).toBeDefined();
        expect(updatedOrder.status).toBe(OrderStatus.PAID);
        expect(updatedOrder.paidAmount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder.dueAmount).toEqual(0);
        expect(updatedOrder.totalSurcharge).toEqual(
          transaction.surchargeAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
        );
        expect(updatedOrder.totalTips).toEqual(transaction.tipAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(newPayment).toBeDefined();
        expect(newPayment.amount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(newPayment.transactionUuid).toBeDefined();
        expect(newPayment.status).toBe('APPROVED');

        const { order: cancelledOrder } = await orderModel1.recordPayment({
          order: updatedOrder,
          transaction: { id: 'id', status: 'CANCELLED', amount: 102, saleAmount: 100 } as any,
        });
        expect(cancelledOrder).toBeDefined();
        expect(cancelledOrder.status).toBe(OrderStatus.OPEN);
        expect(cancelledOrder.paidAmount).toBe(0);
        expect(cancelledOrder.payments).toHaveLength(1);
        expect(cancelledOrder.payments![0].status).toBe('DECLINED');
        expect(cancelledOrder.dueAmount).toBe(order.orderAmount);
        expect(cancelledOrder.totalSurcharge).toBe(order.totalSurcharge);
        expect(cancelledOrder.orderAmount).toBe(order.orderAmount);
        expect(cancelledOrder.subtotalAmount).toBe(order.subtotalAmount);
        expect(cancelledOrder.totalGst).toBe(order.totalGst);
        expect(cancelledOrder.subtotalAmount).toBe(order.subtotalAmount);
        expect(cancelledOrder.totalTips).toBe(order.totalTips);
      });

      it('should handle APPROVED transaction after CANCELLED', async () => {
        const transaction = {
          id: 'id',
          status: 'APPROVED',
          amount: 102,
          saleAmount: 100,
          surchargeAmount: 1,
          taxAmounts: [],
          tipAmount: 1,
        };
        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };
        const order = await orderModel1.create(orderInput as any);
        const { order: updatedOrder, newPayment } = await orderModel1.recordPayment({
          order: JSON.parse(JSON.stringify(order)),
          transaction: transaction as any,
        });
        expect(updatedOrder).toBeDefined();
        expect(updatedOrder.status).toBe(OrderStatus.PAID);
        expect(updatedOrder.paidAmount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder.dueAmount).toEqual(0);
        expect(updatedOrder.totalSurcharge).toEqual(
          transaction.surchargeAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
        );
        expect(updatedOrder.totalTips).toEqual(transaction.tipAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(newPayment).toBeDefined();
        expect(newPayment.amount).toBe(transaction.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(newPayment.transactionUuid).toBeDefined();
        expect(newPayment.status).toBe('APPROVED');

        const { order: cancelledOrder } = await orderModel1.recordPayment({
          order: updatedOrder,
          transaction: { id: 'id', status: 'CANCELLED', amount: 102, saleAmount: 100 } as any,
        });
        expect(cancelledOrder).toBeDefined();
        expect(cancelledOrder.status).toBe(OrderStatus.OPEN);
        expect(cancelledOrder.paidAmount).toBe(0);
        expect(cancelledOrder.payments).toHaveLength(1);
        expect(cancelledOrder.payments![0].status).toBe('DECLINED');
        expect(cancelledOrder.dueAmount).toBe(order.orderAmount);
        expect(cancelledOrder.totalSurcharge).toBe(order.totalSurcharge);
        expect(cancelledOrder.orderAmount).toBe(order.orderAmount);
        expect(cancelledOrder.subtotalAmount).toBe(order.subtotalAmount);
        expect(cancelledOrder.totalGst).toBe(order.totalGst);
        expect(cancelledOrder.subtotalAmount).toBe(order.subtotalAmount);
        expect(cancelledOrder.totalTips).toBe(order.totalTips);
        const transaction2 = {
          id: 'id2',
          status: 'APPROVED',
          amount: 102,
          saleAmount: 100,
          surchargeAmount: 1,
          taxAmounts: [],
          tipAmount: 1,
        };
        const { order: approvedOrder } = await orderModel1.recordPayment({
          order: cancelledOrder,
          transaction: transaction2 as any,
        });
        expect(approvedOrder.payments).toHaveLength(2);
        expect(approvedOrder.payments![0].transactionUuid).toBe(transaction.id);
        expect(approvedOrder.payments![0].status).toBe('DECLINED');
        expect(approvedOrder.payments![1].transactionUuid).toBe(transaction2.id);
        expect(approvedOrder.payments![1].status).toBe('APPROVED');
        expect(approvedOrder.status).toBe('PAID');
      });

      it('should roll back one of the payments', async () => {
        const transaction1 = {
          id: 'id',
          status: 'APPROVED',
          amount: 52,
          saleAmount: 50,
          surchargeAmount: 1,
          tipAmount: 1,
        };
        const transaction2 = {
          id: 'id2',
          status: 'APPROVED',
          amount: 62,
          saleAmount: 60,
          surchargeAmount: 1,
          tipAmount: 1,
        };
        const orderInput = {
          id: v4(),
          entityUuid: v4(),
          siteUuid: v4(),
          catalogSettings: { itemsTaxInclusive: true },
          items: [
            {
              name: v4(),
              price: 100 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
              taxes: [{ name: 'GST', value: 10, enabled: true }],
              catalogItem: {
                id: v4(),
              },
            },
          ],
        };

        const order = await orderModel1.create(orderInput as any);
        // APPROVED payment 1.
        const { order: updatedOrder } = await orderModel1.recordPayment({
          order,
          transaction: transaction1 as any,
        });
        expect(updatedOrder).toBeDefined();
        expect(updatedOrder.status).toBe(OrderStatus.PART_PAID);
        expect(updatedOrder.paidAmount).toBe(transaction1.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder.orderAmount).toBe(order.orderAmount);
        expect(updatedOrder.totalGst).toBe(order.totalGst);
        expect(updatedOrder.orderGst).toBe(order.orderGst);
        expect(updatedOrder.dueAmount).toBe(
          order.orderAmount - transaction1.saleAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
        );
        expect(updatedOrder.totalTips).toBe(transaction1.tipAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder.totalSurcharge).toBe(transaction1.surchargeAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder.payments).toHaveLength(1);
        expect(updatedOrder.payments![0].status).toBe('APPROVED');
        expect(updatedOrder.payments![0].transactionUuid).toBe(transaction1.id);

        // APPROVED payment 2.
        const { order: updatedOrder2 } = await orderModel1.recordPayment({
          order: updatedOrder,
          transaction: transaction2 as any,
        });
        expect(updatedOrder2).toBeDefined();
        expect(updatedOrder2.status).toBe(OrderStatus.PAID);
        expect(updatedOrder2.orderAmount).toBe(order.orderAmount);
        expect(updatedOrder2.orderGst).toBe(order.orderGst);

        expect(updatedOrder2.totalTips).toBe(
          (transaction1.tipAmount + transaction2.tipAmount) * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
        );
        expect(updatedOrder2.totalSurcharge).toBe(
          (transaction1.surchargeAmount + transaction2.surchargeAmount) * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
        );
        expect(updatedOrder2.paidAmount).toBe(
          (transaction1.amount + +transaction2.amount) * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
        );
        expect(updatedOrder2.dueAmount).toBe(0);
        expect(updatedOrder2.payments).toHaveLength(2);
        expect(updatedOrder2.payments![0].status).toBe('APPROVED');
        expect(updatedOrder2.payments![0].transactionUuid).toBe(transaction1.id);
        expect(updatedOrder2.payments![1].status).toBe('APPROVED');
        expect(updatedOrder2.payments![1].transactionUuid).toBe(transaction2.id);
        // CANCELLED payment 2.
        const { order: updatedOrder3 } = await orderModel1.recordPayment({
          order: updatedOrder2,
          transaction: {
            ...transaction2,
            id: 'id2',
            status: 'CANCELLED',
          } as any,
        });
        expect(updatedOrder3.status).toEqual(OrderStatus.PART_PAID);
        expect(updatedOrder3.paidAmount).toBe(transaction1.amount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder3.orderAmount).toBe(order.orderAmount);
        expect(updatedOrder3.totalGst).toBe(order.totalGst);
        expect(updatedOrder3.orderGst).toBe(order.orderGst);
        expect(updatedOrder3.totalTips).toBe(transaction1.tipAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder3.totalSurcharge).toBe(transaction1.surchargeAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder3.dueAmount).toBe(
          order.orderAmount - transaction1.saleAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE,
        );
        expect(updatedOrder3.totalTips).toBe(transaction1.tipAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder3.totalSurcharge).toBe(transaction1.surchargeAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder3.payments![0].status).toBe('APPROVED');
        expect(updatedOrder3.payments![0].transactionUuid).toBe(transaction1.id);
        expect(updatedOrder3.payments![1].status).toBe('DECLINED');
        expect(updatedOrder3.payments![1].transactionUuid).toBe(transaction2.id);
        // CANCELLED payment 1.
        const { order: updatedOrder4 } = await orderModel1.recordPayment({
          order,
          transaction: {
            ...transaction1,
            id: 'id',
            status: 'CANCELLED',
          } as any,
        });
        // verify
        expect(updatedOrder4.status).toBe(OrderStatus.OPEN);
        expect(updatedOrder4.paidAmount).toBe(0);
        expect(updatedOrder4.orderAmount).toBe(110 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder4.dueAmount).toBe(110 * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE);
        expect(updatedOrder4.totalTips).toBe(0);
        expect(updatedOrder4.totalSurcharge).toBe(0);
        expect(updatedOrder4.totalGst).toBe(order.totalGst);
        expect(updatedOrder4.orderGst).toBe(order.orderGst);
        expect(updatedOrder4.payments![0].status).toBe('DECLINED');
        expect(updatedOrder4.payments![0].transactionUuid).toBe(transaction2.id);
        expect(updatedOrder4.payments![0].amount).toBe(convertCentsToCentiCents(transaction2.amount));
        expect(updatedOrder4.payments![1].status).toBe('DECLINED');
        expect(updatedOrder4.payments![1].transactionUuid).toBe(transaction1.id);
        expect(updatedOrder4.payments![1].amount).toBe(convertCentsToCentiCents(transaction1.amount));
      });
    });
  });

  describe('updateOrderStatusToPaid', () => {
    let mockOrder: any;
    beforeAll(() => {
      mockOrder = {
        id: v4(),
        entityUuid: v4(),
        siteUuid: v4(),
        catalogSettings: { itemsTaxInclusive: true, itemsApplyTax: true, autoSkuEnabled: true },
        status: OrderStatus.OPEN,
        items: [
          {
            id: v4(),
            name: 'test item',
            price: '1000',
            ordinal: 1,
            type: OrderItemType.SINGLE,
            unit: CatalogUnit.QUANTITY,
            quantity: 1,
            discounts: [
              { id: v4(), config: CatalogDiscountConfig.AMOUNT, value: '1000', name: 'item level', ordinal: 1 },
            ],
            serviceCharges: [
              { id: v4(), config: CatalogServiceChargeConfig.AMOUNT, value: '1000', name: 'item level', ordinal: 1 },
            ],
          },
        ],
        updatedTime: new Date().toISOString(),
        orderAmount: 0,
        dueAmount: 0,
      } as any;
    });
    it('should mark zero dollar sale as paid and update time', () => {
      const orderCopy = { ...mockOrder };
      const updatedOrder = orderModel.updateOrderStatusToPaid(orderCopy);
      expect(updatedOrder).toBeDefined();
      expect(updatedOrder.status).toBe(OrderStatus.PAID);
      expect(updatedOrder.updatedTime).toBeGreaterThanOrEqual(
        parseInt((new Date(mockOrder.updatedTime as string).getTime() / 1000) as any, 10),
      );
      expect(updatedOrder.paidTime).toBeDefined();
    });

    it('should mark 40 centicents sale as paid and update time', () => {
      const orderCopy = { ...mockOrder, dueAmount: 40, orderAmount: 40, paidAmount: undefined };
      const updatedOrder = orderModel.updateOrderStatusToPaid(orderCopy);
      expect(updatedOrder).toBeDefined();
      expect(updatedOrder.status).toBe(OrderStatus.PAID);
      expect(updatedOrder.dueAmount).toBe(0);
      expect(updatedOrder.paidAmount).toBe(undefined);
      expect(updatedOrder.orderAmount).toBe(0);
      expect(updatedOrder.updatedTime).toBeGreaterThanOrEqual(
        parseInt((new Date(mockOrder.updatedTime as string).getTime() / 1000) as any, 10),
      );
      expect(updatedOrder.paidTime).toBeDefined();
    });

    it('should mark paid sale as paid and update time', () => {
      const orderCopy = { ...mockOrder, dueAmount: 20, orderAmount: 120, paidAmount: 100 };
      const updatedOrder = orderModel.updateOrderStatusToPaid(orderCopy);
      expect(updatedOrder).toBeDefined();
      expect(updatedOrder.status).toBe(OrderStatus.PAID);
      expect(updatedOrder.dueAmount).toBe(0);
      expect(updatedOrder.paidAmount).toBe(100);
      expect(updatedOrder.orderAmount).toBe(120);
      expect(updatedOrder.updatedTime).toBeGreaterThanOrEqual(
        parseInt((new Date(mockOrder.updatedTime as string).getTime() / 1000) as any, 10),
      );
      expect(updatedOrder.paidTime).toBeDefined();
    });

    it('should throw error when order status is PAID', () => {
      expect(() => orderModel.updateOrderStatusToPaid({ ...mockOrder, status: OrderStatus.PAID })).toThrow(
        'Invalid order status',
      );
    });

    it('should throw error when items are not provided', () => {
      expect(() =>
        orderModel.updateOrderStatusToPaid({
          ...mockOrder,
          items: [],
        }),
      ).toThrow('Invalid order status');
    });

    it('should throw error when dueAmount is greater than 50 ', () => {
      expect(() => orderModel.updateOrderStatusToPaid({ ...mockOrder, dueAmount: 50 })).toThrow('Invalid order status');
    });
  });

  describe('migration', () => {
    it('migrationSetPaidOrderAmount', async () => {
      const order = {
        id: v4(),
        entityUuid: v4(),
        siteUuid: v4(),
        catalogSettings: { itemsTaxInclusive: true, itemsApplyTax: true, autoSkuEnabled: true },
        status: OrderStatus.PAID,
        items: [
          {
            id: v4(),
            name: 'test item',
            price: '1000',
            ordinal: 1,
            type: OrderItemType.SINGLE,
            unit: CatalogUnit.QUANTITY,
            quantity: 1,
            discounts: [
              { id: v4(), config: CatalogDiscountConfig.AMOUNT, value: '1000', name: 'item level', ordinal: 1 },
            ],
            serviceCharges: [
              { id: v4(), config: CatalogServiceChargeConfig.AMOUNT, value: '1000', name: 'item level', ordinal: 1 },
            ],
          },
        ],
        updatedTime: new Date().toISOString(),
        orderAmount: 0,
        dueAmount: 0,
      } as any;
      const updatedOrder = await orderModel.migrationSetPaidOrderAmount(order, {
        amount: 100,
        tipAmount: 100,
        surchargeAmount: 100,
      } as any);
      expect(updatedOrder).toBeDefined();
      expect(updatedOrder.status).toBe(OrderStatus.PAID);
      expect(updatedOrder.orderAmount).toBe(10000);
      expect(updatedOrder.dueAmount).toBe(0);
    });
  });
});
