import type { DomainURI } from '@npco/component-dto-core/dist';
import { DomainURImap } from '@npco/component-dto-core/dist';

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EnvironmentService {
  componentName: string;

  stage: string;

  isInLambda: boolean;

  dbUser: string;

  dbHost: string;

  dbName: string;

  dbPort: number;

  dbPassword: string;

  region: string;

  dbConnectionName: string;

  cqrsCmds: DomainURI = DomainURImap;

  cqrsCommandHandler = '';

  projectionQueueUrl: string;

  projectionDlqUrl: string;

  cqrsSqsUrl: string;

  maxJobConcurrency: number;

  batchEventSize: number;

  paymentQueueUrl: string | undefined;

  componentTableName: string | undefined;

  awsRegion: string;

  constructor(readonly configService: ConfigService) {
    this.awsRegion = process.env.AWS_REGION ?? 'ap-southeast-2';
    this.componentName = this.configService.get('COMPONENT_NAME', '');
    this.stage = this.configService.get('STAGE', '');
    this.isInLambda = !!this.configService.get('LAMBDA_TASK_ROOT', '');
    this.dbUser = this.configService.get('DB_USER', '');
    this.dbPassword = this.configService.get('DB_PASSWORD', '');
    this.dbHost = this.configService.get('DB_HOST', '');
    this.dbName = this.configService.get('DB_NAME', '');
    this.dbPort = this.configService.get('DB_PORT', 5432);
    this.region = this.configService.get('AWS_REGION', '');
    this.dbConnectionName = configService.get('DB_CONNECTION_NAME', 'default');
    this.cqrsCommandHandler = this.configService.get('CQRS_COMMAND_HANDLER', '');
    this.projectionQueueUrl = this.configService.get('PROJECTION_QUEUE_URL', '');
    this.projectionDlqUrl = this.configService.get('PROJECTION_DLQ_URL', '');
    this.cqrsSqsUrl = this.configService.get('CQRS_PROJECTION_SQS_URL', '');
    this.maxJobConcurrency = this.configService.get('MAX_JOB_CONCURRENCY', 10);
    this.batchEventSize = this.configService.get('BATCH_EVENT_SIZE', 50);
    this.componentTableName = this.configService.get('TRANSACTION_PROJECTION_TABLE', '');
    this.paymentQueueUrl = this.configService.get('PAYMENT_SQS_URL', '');
  }
}
