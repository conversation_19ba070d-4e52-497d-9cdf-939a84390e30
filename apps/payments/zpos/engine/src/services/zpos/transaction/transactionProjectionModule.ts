import { SqsClient } from '@npco/component-bff-core/dist/aws/sqsClient';

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { EnvironmentService } from '../../../config';
import { BffDynamoDbClient, DynamodbService } from '../../../dynamodb/dynamoDb';

import { TransactionService } from './transactionService';

@Module({
  imports: [ConfigModule],
  providers: [
    EnvironmentService,
    TransactionService,
    DynamodbService,
    { provide: SqsClient, useValue: new SqsClient(process.env.AWS_REGION) },
    BffDynamoDbClient,
  ],
})
export class TransactionProjectionModule {}
