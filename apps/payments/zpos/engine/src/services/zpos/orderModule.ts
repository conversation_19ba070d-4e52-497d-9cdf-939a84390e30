import { SqsClient } from '@npco/component-bff-core/dist/aws/sqsClient';

import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { CommandService } from '../../commands';
import { EnvironmentService } from '../../config';
import { ConfigCommonModule } from '../../config/configCommonModule';
import { DatabaseModule } from '../../databaseModule';
import { entities } from '../../domain/entities';
import { OrderDiscountModel } from '../../domain/models/orderDiscountModel';
import { OrderItemModel } from '../../domain/models/orderItemModel';
import { OrderModel } from '../../domain/models/orderModel';
import { OrderServiceChargeModel } from '../../domain/models/orderServiceChargeModel';
import { DynamodbService, BffDynamoDbClient } from '../../dynamodb/dynamoDb';

import { CronJobRepository } from './cron-job/cronJobRepository';
import { CronJobService } from './cron-job/cronJobService';
import { MigrationService } from './migration/migrationService';
import { OrderRepository } from './order/orderRepository';
import { OrderService } from './order/orderService';
import { PaymentRepository } from './payment/paymentRepository';
import { PaymentService } from './payment/paymentService';
import { TransactionService } from './transaction/transactionService';
import { ZposApiService } from './zposApi/zposApiService';

@Module({
  imports: [DatabaseModule, ConfigCommonModule],
  providers: [
    ConfigService,
    EnvironmentService,
    ZposApiService,
    OrderService,
    PaymentService,
    OrderRepository,
    OrderModel,
    OrderItemModel,
    OrderDiscountModel,
    OrderServiceChargeModel,
    { provide: SqsClient, useValue: new SqsClient(process.env.AWS_REGION) },
    CommandService,
    TransactionService,
    PaymentRepository,
    DynamodbService,
    BffDynamoDbClient,
    MigrationService,
    CronJobService,
    CronJobRepository,
    ...entities,
  ],
  exports: [ZposApiService, OrderService],
})
export class OrderModule {}
