IDENTITY_AUTH0_TENANT=https://zeller.au.auth0.com
LOG_LEVEL=info
NODE_JS_RUNTIME=nodejs18.x
AUTH0_STAGE_NAME=prod
LAMBDA_MEMORY_DEFAULT=1024

COMPONENT_NAME=mp
PART_NAME=api

# Dynamodb
DB_STAGE=prod
COMPONENT_TABLE=Entities
SESSION_CACHE_TABLE=SessionCache
ENTITY_GSI=entityGsi
ACCESS_TOKEN_GSI=accessTokenGsi
ENTITY_CACHE_GSI=entityCacheGsi
LAMBDA_TIMEOUT_IN_SECONDS=30

# AMS
AMS_API_ENDPOINT_VERSION=v2
DOMICILE_LOOKUP_ENABLED=true

#Keep warm
KEEP_WARM_SCHEDULER=cron(6/5 * ? * * *)
KEEP_WARM_SOURCE=keep.warm
LAMBDA_KEEP_WARM_STATE=ENABLED

# VPC
VPC_ENV_NAME=prod

MFA_ENROLMENT_ENABLED=true

# RBAC
IS_RBAC_ENFORCED=false
IS_RBAC_ENFORCE_ROLE=false
