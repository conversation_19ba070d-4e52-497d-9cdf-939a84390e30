IDENTITY_AUTH0_TENANT=https://zeller-dev.au.auth0.com
LOG_LEVEL=debug
NODE_JS_RUNTIME=nodejs18.x
AUTH0_STAGE_NAME=dev
LAMBDA_MEMORY_DEFAULT=1024

COMPONENT_NAME=mp
PART_NAME=api

# Dynamodb
DB_STAGE=dev
COMPONENT_TABLE=Entities
SESSION_CACHE_TABLE=SessionCache
ENTITY_GSI=entityGsi
ACCESS_TOKEN_GSI=accessTokenGsi
ENTITY_CACHE_GSI=entityCacheGsi
LAMBDA_TIMEOUT_IN_SECONDS=30

# AMS
AMS_API_ENDPOINT_VERSION=v2
DOMICILE_LOOKUP_ENABLED=true

#Keep warm
KEEP_WARM_SCHEDULER=cron(6/5 * ? * * *)
KEEP_WARM_SOURCE=keep.warm
LAMBDA_KEEP_WARM_STATE=ENABLED

# VPC
VPC_ENV_NAME=dev

MFA_ENROLMENT_ENABLED=true
ALLOW_ZELLER_APP_FIREBASE_TEST_TOKEN=true

# RBAC
IS_RBAC_ENFORCED=true
IS_RBAC_ENFORCE_ROLE=false
