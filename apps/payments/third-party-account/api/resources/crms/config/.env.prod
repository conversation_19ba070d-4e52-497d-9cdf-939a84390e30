COMPONENT_NAME=crms
PART_NAME=engine

LAMBDA_TIMEOUT_IN_SECONDS=300

LOG_LEVEL=info
NODE_JS_RUNTIME=nodejs18.x
LAMBDA_MEMORY_DEFAULT=512

# AMS
AMS_API_ENDPOINT_VERSION=v2
AMS_STAGE_NAME=prod
DOMICILE_LOOKUP_ENABLED=true

# VPC
VPC_ENV_NAME=prod
STATIC_ENV_NAME=prod

#Keep warm
KEEP_WARM_SCHEDULER=cron(0/5 * ? * * *)
KEEP_WARM_SOURCE=keep.warm
LAMBDA_KEEP_WARM_STATE=ENABLED

# Dynamodb
DB_STAGE=prod
ACCESS_TOKEN_GSI=accessTokenGsi
ENTITY_GSI=entityGsi
COMPONENT_TABLE=Entities