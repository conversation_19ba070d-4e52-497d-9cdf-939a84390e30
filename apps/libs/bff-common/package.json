{"name": "@npco/bff-common", "version": "0.0.1", "description": "End to end encryption", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -fr dist && yarn tsc --build tsconfig.json", "test": "jest --forceExit --runInBand", "lint": "eslint src --quiet ", "run-audit": "yarn npm audit --environment production"}, "devDependencies": {"@aws-sdk/client-dynamodb": "3.435.0", "@aws-sdk/client-dynamodb-streams": "3.435.0", "@npco/eslint-config-backend": "^1.0.12", "@nx/jest": "20.7.0", "@rushstack/eslint-patch": "^1.6.0", "@swc/core": "^1.3.102", "@swc/jest": "^0.2.29", "@types/aws-lambda": "^8.10.121", "@types/heic-convert": "^1.2.0", "@types/jest": "^29.5.10", "@types/node": "^18.18.12", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "clean-package": "^2.1.1", "eslint": "^8.54.0", "eslint-plugin-sonarjs": "^0.19.0", "jest": "^29.7.0", "jest-html-reporter": "^3.10.2", "jest-junit": "^16.0.0", "jest-sonar": "^0.2.16", "jest-sonar-reporter": "^2.0.0", "nx": "20.7.0", "prettier": "^3.1.1", "ts-jest": "^29.1.1", "ts-loader": "^9.5.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "prettier": "@npco/eslint-config-backend/prettier", "dependencies": {"@aws-sdk/lib-dynamodb": "3.435.0", "@aws-sdk/util-dynamodb": "3.435.0", "@npco/component-bff-core": "workspace:*", "@npco/component-dto-core": "workspace:*", "@npco/component-dto-customer": "workspace:*", "@vingle/bmp-js": "0.2.5", "heic-convert": "1.2.4", "sharp": "0.33.4"}, "jestSonar": {"reportPath": "dist"}}