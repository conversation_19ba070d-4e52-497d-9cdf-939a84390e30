import { info, error } from '@npco/component-bff-core/dist/utils/logger';

import bmp from '@vingle/bmp-js';
import convert from 'heic-convert';
import sharp from 'sharp';

export const convertHeicToPngOrJpeg = async (image: Buffer) => {
  const startTime = new Date().getTime();
  const outputBuffer = await convert({
    buffer: image, // the HEIC file buffer
    format: 'PNG', // output format
  });
  const endTime = new Date().getTime();
  info(`time to convert HEIC / HEIF image was ${(endTime - startTime) / 1000} seconds`);
  return outputBuffer;
};

export const convertBmpToPng = async (input: Buffer): Promise<Buffer> => {
  const bitmap = bmp.decode(input, true);
  const sharpInstance = sharp(bitmap.data, {
    raw: {
      width: bitmap.width,
      height: bitmap.height,
      channels: 4,
    },
  });

  return sharpInstance.png().toBuffer();
};

export const isHeicHeif = (buffer: Buffer) => {
  if (!buffer || buffer.length < 24) {
    return false;
  }
  return buffer[20] === 0x68 && buffer[21] === 0x65 && buffer[22] === 0x69 && buffer[23] === 0x63;
};

export const isBmp = (buffer: Buffer) => {
  if (!buffer || buffer.length < 2) {
    return false;
  }
  return buffer[0] === 0x42 && buffer[1] === 0x4d;
};

export const compressToPng = async (imageBuffer: Buffer, quality = 80): Promise<Buffer> => {
  try {
    info('compressToPng');
    return await sharp(imageBuffer).toFormat('png').png({ quality }).toBuffer();
  } catch (e) {
    error(e);
    throw e;
  }
};

export const resizeAndCompressToPng = async (
  imageBuffer: Buffer,
  maxSize: number,
  quality = 80,
): Promise<Buffer | null> => {
  try {
    info(`resizeAndCompressToPng:size ${maxSize}`);
    const image = await sharp(imageBuffer);
    const { width, height } = await image.metadata();
    const shouldScaleDown = Number(width) > maxSize || Number(height) > maxSize;
    info(`shouldScaleDown: ${shouldScaleDown} ${width} ${height}`);
    if (!shouldScaleDown) {
      return null;
    }
    return await image
      .resize(maxSize, maxSize, {
        fit: 'inside',
      })
      .toFormat('png')
      .png({ quality })
      .toBuffer();
  } catch (e) {
    error(e);
    throw e;
  }
};
