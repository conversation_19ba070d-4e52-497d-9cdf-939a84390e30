import fs from 'fs';
import sharp from 'sharp';

import { convertHeicToPngOrJpeg, compressToPng, resizeAndCompressToPng, isHeicHeif, convertBmpToPng, isBmp } from './imageProcessUtils';

describe('ConvertHeic tests converting HEIC and HEIF formats', () => {
  test.each(['sloth.heic', 'heic.heic', 'sample1.heic', 'IMG_2633.HEIC'])(
    'should be able to convert heic/heif image %s format to PNG',
    async (testImg) => {
      const original = fs.readFileSync(`src/image/__mocks__/${testImg}`);
      const response = await convertHeicToPngOrJpeg(original);
      console.log('size -> ', response.byteLength);
      expect(response.byteLength > 0).toBe(true);
    },
  );

  // execution time 240sec+
  xit('should be able to convert large heic/heif format to PNG', async () => {
    const testImg = 'sample1.heif'; // 2.5mb
    const original = fs.readFileSync(`src/image/__mocks__/${testImg}`);
    const response = await convertHeicToPngOrJpeg(original);
    console.log('size -> ', response.byteLength);
    expect(response.byteLength > 0).toBe(true);
  });
});

describe('convertBmp tests converting BMP formats', () => {
  test.each(['apple.bmp'])(
    'should be able to convert BMP image %s format to PNG',
    async (testImg) => {
      const original = fs.readFileSync(`src/image/__mocks__/${testImg}`);
      const buffer = await convertBmpToPng(original);
      console.log('size -> ', buffer.byteLength);
      expect(buffer.byteLength > 0).toBe(true);
      fs.writeFileSync(`dist/png_${testImg}.png`, buffer);
    },
  );
});

describe('isHeicHeif', () => {
  it('should test is heic/heif', async () => {
    const heic = fs.readFileSync(`src/image/__mocks__/sample1.heic`);
    expect(isHeicHeif(heic)).toBe(true);
    const heif = fs.readFileSync(`src/image/__mocks__/sample1.heif`);
    expect(isHeicHeif(heif)).toBe(true);
    const jpg = fs.readFileSync(`src/image/__mocks__/arnold.jpeg`);
    expect(isHeicHeif(jpg)).toBe(false);
    const png = fs.readFileSync(`src/image/__mocks__/should-resize.png`);
    expect(isHeicHeif(png)).toBe(false);
    expect(isHeicHeif(Buffer.from('hello'))).toBe(false);
  }, 20000);
});

describe('isBmp', () => {
  it('should test is bmp', async () => {
    const bmp = fs.readFileSync(`src/image/__mocks__/apple.bmp`);
    expect(isBmp(bmp)).toBe(true);
    const heic = fs.readFileSync(`src/image/__mocks__/sample1.heic`);
    expect(isBmp(heic)).toBe(false);
    const heif = fs.readFileSync(`src/image/__mocks__/sample1.heif`);
    expect(isBmp(heif)).toBe(false);
    const jpg = fs.readFileSync(`src/image/__mocks__/arnold.jpeg`);
    expect(isBmp(jpg)).toBe(false);
    const png = fs.readFileSync(`src/image/__mocks__/should-resize.png`);
    expect(isBmp(png)).toBe(false);
    expect(isBmp(Buffer.from('h'))).toBe(false);
  }, 20000);
});

describe('resizeAndCompressToPng', () => {
  const imageName = 'imageName-';
  it('should should handle an error', async () => {
    await expect(resizeAndCompressToPng(Buffer.from('hello'), 1024)).rejects.toThrowError(
      'Input buffer contains unsupported image format',
    );
  });

  it('should not scale size if its less than 1024x1024', async () => {
    const testImg = 'should-no-resize.png';
    const original = fs.readFileSync(`src/image/__mocks__/${testImg}`);

    expect(await resizeAndCompressToPng(original, 1024)).toEqual(null);
  });

  it('should scale image size', async () => {
    const testImg = 'should-resize.png';
    const original = fs.readFileSync(`src/image/__mocks__/${testImg}`);
    const sharpInstance = sharp(original);

    const buffer = (await resizeAndCompressToPng(original, 300)) as Buffer;
    fs.writeFileSync(`dist/${imageName}${testImg}`, buffer);

    const { width: actualWidth, height: actualHeight } = await sharp(`dist/${imageName}${testImg}`).metadata();
    expect(actualWidth).toEqual(300);
    expect(actualHeight).toEqual(89);
    const { width: originalWidth, height: originalHeight } = await sharpInstance.metadata();
    expect(originalWidth).toEqual(720);
    expect(originalHeight).toEqual(213);
  }, 60000);

  it('should scale image size from jpeg', async () => {
    const testImg = 'should-resize.png';
    const original = fs.readFileSync(`src/image/__mocks__/${testImg}`);

    const sharpInstance = sharp(original);
    const buffer = (await resizeAndCompressToPng(original, 300)) as Buffer;
    fs.writeFileSync(`dist/${imageName}${testImg}`, buffer);

    const { width: actualWidth, height: actualHeight } = await sharp(`dist/${imageName}${testImg}`).metadata();
    expect(actualWidth).toEqual(300);
    expect(actualHeight).toEqual(89);
    const { width: originalWidth, height: originalHeight } = await sharpInstance.metadata();
    expect(originalWidth).toEqual(720);
    expect(originalHeight).toEqual(213);
  }, 60000);
});

describe('compressToPng', () => {
  const imageName = 'imageName-';

  it('should should handle an error', async () => {
    await expect(compressToPng(Buffer.from('hello'))).rejects.toThrowError(
      'Input buffer contains unsupported image format',
    );
  });

  it('should compress image', async () => {
    const testImg = 'should-no-resize.png';
    const original = fs.readFileSync(`src/image/__mocks__/${testImg}`);

    const sharpInstance = sharp(original);
    const buffer = await compressToPng(original);
    fs.writeFileSync(`dist/${imageName}${testImg}`, buffer);

    const { width: actualWidth, height: actualHeight } = await sharp(`dist/${imageName}${testImg}`).metadata();
    const { width: originalWidth, height: originalHeight } = await sharpInstance.metadata();
    expect(originalWidth).toEqual(actualWidth);
    expect(originalHeight).toEqual(actualHeight);
  }, 30000);

  it('should compress and not scale image', async () => {
    const testImg = 'should-resize.png';
    const original = fs.readFileSync(`src/image/__mocks__/${testImg}`);

    const sharpInstance = sharp(original);
    const buffer = await compressToPng(original);
    fs.writeFileSync(`dist/${imageName}${testImg}`, buffer);

    const { width: actualWidth, height: actualHeight } = await sharp(`dist/${imageName}${testImg}`).metadata();
    expect(actualWidth).toEqual(720);
    expect(actualHeight).toEqual(213);
    const { width: originalWidth, height: originalHeight } = await sharpInstance.metadata();
    expect(originalWidth).toEqual(720);
    expect(originalHeight).toEqual(213);
  }, 60000);
});
