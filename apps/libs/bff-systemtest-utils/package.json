{"name": "@npco/bff-systemtest-utils", "version": "1.0.38", "author": "alex", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "compilerOptions": {"module": "commonjs", "declaration": false, "noImplicitAny": false, "noUnusedLocals": true, "removeComments": true, "noLib": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "ES2021", "sourceMap": true, "allowJs": true, "outDir": "./dist"}, "scripts": {"isPublished": "bash ./bin/is-published.sh", "lint": "eslint src --quiet ", "test": "jest --runInBand", "build": "rm -fr dist && yarn tsc --build tsconfig.json", "run-audit": "yarn npm audit --environment production", "deploy": "yarn npm publish", "clean": "rm -rf dist"}, "repository": {"type": "git", "url": "git+https://bitbucket.org/npco_dev/bff-system-testing-utils.git"}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"], "keywords": ["bff", "system-test", "utils"], "homepage": "https://bitbucket.org/npco_dev/bff-system-testing-utils#readme", "dependencies": {"@aws-sdk/client-appsync": "3.435.0", "@aws-sdk/client-cloudformation": "3.435.0", "@aws-sdk/client-dynamodb": "3.435.0", "@aws-sdk/client-dynamodb-streams": "3.435.0", "@aws-sdk/client-eventbridge": "3.435.0", "@aws-sdk/client-iam": "3.435.0", "@aws-sdk/client-lambda": "3.435.0", "@aws-sdk/client-secrets-manager": "3.435.0", "@aws-sdk/client-sqs": "3.435.0", "@aws-sdk/client-ssm": "3.435.0", "@aws-sdk/lib-dynamodb": "3.435.0", "@npco/component-bff-core": "workspace:*", "@npco/component-dto-core": "workspace:*", "@npco/component-dto-customer": "workspace:*", "@npco/component-dto-device": "workspace:*", "@npco/component-dto-entity": "workspace:*", "@npco/component-dto-site": "workspace:*", "@smithy/node-http-handler": "^2.1.3", "apollo-client": "^2.4", "auth0": "^3.7.2", "aws-appsync": "4.1.10", "aws-appsync-auth-link": "^2.0.8", "axios": "^1.5.0", "graphql": "^15.3.0", "graphql-tag": "^2.12.6", "isomorphic-fetch": "^3.0.0", "uuid": "^9.0.1", "ws": "^8.17.1"}, "devDependencies": {"@npco/eslint-config-backend": "^1.0.12", "@nx/jest": "20.7.0", "@rushstack/eslint-patch": "^1.6.0", "@shelf/jest-dynamodb": "^3.4.1", "@smithy/types": "^2.3.2", "@swc/core": "^1.3.72", "@swc/jest": "^0.2.29", "@types/auth0": "^3.3.4", "@types/graphql": "^14.5.0", "@types/jest": "^29.5.11", "@types/mocha": "^10.0.1", "@types/node": "18.19.14", "@types/uuid": "^9.0.2", "@types/ws": "^8.5.8", "aws-sdk-client-mock": "^3.0.0", "eslint": "^8.54.0", "eslint-plugin-sonarjs": "^0.19.0", "husky": "^4.2.5", "jest": "^29.7.0", "jest-html-reporter": "^3.10.2", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "husky": {"hooks": {"pre-push": "yarn lint && yarn test --silent"}}, "prettier": "@npco/eslint-config-backend/prettier", "jestSonar": {"reportPath": "dist"}}