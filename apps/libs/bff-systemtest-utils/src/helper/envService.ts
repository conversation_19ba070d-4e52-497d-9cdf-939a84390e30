import { Auth0AppClients } from '../authzero/managementEnv';

import { ComponentClients } from './componentClients';

export abstract class BaseEnvService {
  password = 'abcABC123!@#';

  public adminToken: string = process.env.ADMIN_TOKEN || '';

  public stage: string = process.env.STAGE || 'dev';

  public stStage: string = process.env.STAGE || 'dev';

  public auth0Domain?: string = process.env.AUTH0_DOMAIN;

  public auth0Audience?: string = process.env.AUTH0_AUDIENCE;

  public stack = `${this.stage}-${this.componentName}-${this.partName}`;

  public service = `${this.stStage}-${this.componentName}-${this.partName}`;

  public highPriorityQueueStackName?: string;

  public region = process.env.AWS_REGION || 'eu-west-1';

  public globalAccountId = process.env.SYDNEY_ACCOUNT_ID ?? '';

  public STATIC_ENV_NAME = process.env.STATIC_ENV_NAME ?? 'dev';

  public domicileLookUpTable =
    process.env.DOMICILE_LOOKUP_TABLE ||
    `arn:aws:dynamodb:${this.region}:${this.globalAccountId}:table/${this.STATIC_ENV_NAME}-ams-engine-DomicileLookup`;

  abstract userEmail: string;

  abstract auth0AppClient: Auth0AppClients;

  abstract sessionTable: string;

  abstract componentTable: string;

  abstract client: ComponentClients;

  constructor(readonly componentName: string, readonly partName: string, readonly bffName?: string) {
    this.stStage = bffName ? 'dev' : this.stage;
    this.service = `${this.stStage}-${this.componentName}-${this.partName}`;
  }

  isTest = () => this.stage !== 'staging' && this.stage.includes('st');

  getEventBusName = () =>
    `${this.stage}-${this.componentName}-cqrs-iac-eventBus-${
      this.isTest() && this.bffName ? `${this.bffName}-` : ''
    }projection`;
}

export class MpEnvService extends BaseEnvService {
  auth0AppClient = Auth0AppClients.MerchantPortal;

  sessionTable = `${this.service}-dynamodb-SessionCache`;

  componentTable = `${this.service}-dynamodb-Entities`;

  client = ComponentClients.MerchantPortal;

  userEmail = `<EMAIL>`;

  constructor(bffName?: string) {
    super('mp', 'api', bffName);
  }
}

export class DeviceEnvService extends BaseEnvService {
  auth0AppClient = Auth0AppClients.DeviceBackend;

  sessionTable = `${this.service}-dynamodb-SessionCache`;

  componentTable = `${this.service}-dynamodb-Devices`;

  client = ComponentClients.DeviceBackend;

  userEmail = '<EMAIL>'; // auth0|61b6bdbc4cedfe00709a4739

  constructor(bffName?: string) {
    super('dbs', 'api', bffName);
  }
}

export class CrmsEnvService extends BaseEnvService {
  auth0AppClient = Auth0AppClients.CrmsEngine;

  sessionTable = '';

  componentTable = `${this.service}-Entities`;

  client = ComponentClients.CrmsEngine;

  userEmail = `<EMAIL>`;

  constructor(bffName?: string) {
    super('crms', 'engine', bffName);
  }
}

export class ZappEnvService extends MpEnvService {
  auth0AppClient = Auth0AppClients.ZellerApp;

  client = ComponentClients.ZellerApp;
}

export class SdkEnvironmentService extends BaseEnvService {
  auth0AppClient = Auth0AppClients.SoftwareDevelopmentKit;

  sessionTable = `${this.service}-dynamodb-SessionCache`;

  componentTable = `${this.service}-dynamodb-Entities`;

  client = ComponentClients.SoftwareDevelopmentKit;

  userEmail = '<EMAIL>';

  highPriorityQueueStackName = `${this.stage}-sdk-api-projection`;

  constructor(bffName?: string) {
    super('sdk', 'api', bffName);
  }
}

export const envServiceFactory = (component: ComponentClients, bffName?: string): BaseEnvService => {
  switch (component) {
    case ComponentClients.MerchantPortal:
      return new MpEnvService(bffName);
    case ComponentClients.DeviceBackend:
      return new DeviceEnvService(bffName);
    case ComponentClients.CrmsEngine:
      return new CrmsEnvService(bffName);
    case ComponentClients.ZellerApp:
      return new ZappEnvService(bffName);
    case ComponentClients.SoftwareDevelopmentKit:
      return new SdkEnvironmentService(bffName);
    default:
      throw new Error(`Component ${component} not found`);
  }
};
