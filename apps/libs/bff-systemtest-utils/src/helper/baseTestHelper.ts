import { CustomerRole, DbRecordType, EntityType, ISO4217 } from '@npco/component-dto-core';
import {
  CustomerCreateRequestedEventDto,
  type CustomerEntityLinkedEventDto,
  type CustomerUpdatedEventDto,
} from '@npco/component-dto-customer';
import type { DeviceCreatedEventDto, DeviceUpdatedEventDto } from '@npco/component-dto-device';
import { DeviceStatus } from '@npco/component-dto-device';
import type {
  EntityCreateRequestedEventDto,
  EntityUpdatedEventDto,
  OnboardingStatus,
} from '@npco/component-dto-entity';
import type { SiteCreatedEventDto, SiteUpdatedEventDto } from '@npco/component-dto-site';
import { DiscountPinType, RefundPinType, SiteType } from '@npco/component-dto-site';

import { SQS } from '@aws-sdk/client-sqs';
import type { PutCommandOutput, QueryCommandOutput, UpdateCommandInput } from '@aws-sdk/lib-dynamodb';
import type AWSAppSyncClient from 'aws-appsync';
import { v4 as uuidv4 } from 'uuid';

import { AppsyncClient } from '../appsync/appsyncClient';
import { Auth0Helper } from '../authzero/auth0Helper';
import { getStackOutputs } from '../cloudformation';
import { createDomicileValue } from '../domicile/utils';
import { DynamodbClient } from '../dynamodb/dynamodbClient';
import { EventBridgeService } from '../eventBridge/eventBridge';
import { retry, sleep } from '../utils';

import { ComponentClients } from './componentClients';
import { envServiceFactory, type BaseEnvService } from './envService';
import { TestData } from './testData';

export enum ProjectionMode {
  EventBridge = 'EventBridge',
  HighPriorityQueue = 'HighPriorityQueue',
}

const region = process.env.AWS_REGION || 'ap-southeast-2';

export const isRegionAP = region === 'ap-southeast-2';

export abstract class BaseTestHelper {
  public testData = new TestData();

  readonly dbClient: DynamodbClient;

  readonly appsyncClient: AppsyncClient;

  readonly eventBridge: EventBridgeService;

  readonly sqs: SQS;

  readonly env: BaseEnvService;

  protected auth0Helper: Auth0Helper;

  protected sqsEndpoint: string | undefined;

  constructor(
    readonly client: ComponentClients,
    readonly bffName?: string,
    readonly projectionMode = ProjectionMode.EventBridge,
  ) {
    this.env = envServiceFactory(client, bffName);
    console.log('env', this.env);
    this.appsyncClient = new AppsyncClient(this.env);
    this.eventBridge = new EventBridgeService(this.env.getEventBusName());
    this.auth0Helper = new Auth0Helper(this.env.client);
    this.dbClient = new DynamodbClient({ region });
    this.sqs = new SQS({ region });
  }

  getTestData = () => this.testData;

  setTestData = (data: Partial<TestData>) => {
    this.testData = { ...this.testData, ...data };
  };

  getEntityUuid = () => this.testData.entityUuid;

  getSiteUuid = () => this.testData.siteUuid;

  getDeviceUuid = () => this.testData.deviceUuid;

  getCustomerUuid = () => this.testData.customerUuid;

  getStage = () => this.env.stage;

  isDev = () => this.env.stage === 'dev';

  getComponentTableName = () => this.env.componentTable;

  setAccessToken = (token: string) => {
    this.testData.accessToken = token;
  };

  getAppsyncEndpoint = () => this.appsyncClient.appsyncEndpoint;

  getOpenIdClient = (rebuild?: boolean): Promise<AWSAppSyncClient<any>> => this.appsyncClient.getOpenIdClient(rebuild);

  public getApiKeyClient = (): Promise<AWSAppSyncClient<any>> => this.appsyncClient.getApiKeyClient();

  getSubscriptionOpenIdClient = (): Promise<AWSAppSyncClient<any>> => this.appsyncClient.getSubscriptionOpenIdClient();

  setAppsyncTestData = async () => {
    await this.appsyncClient.initAppsync(
      `${this.env.componentName}-${this.env.partName}-appsync`,
      this.testData.accessToken,
    );
  };

  publishEvent = (uri: string, payload: any) => this.eventBridge.publishProjectionEvent(uri, payload);

  getSqsEndpoint = async () => {
    if (!this.env.highPriorityQueueStackName) {
      throw new Error('highPriorityQueueStackName is not defined');
    }

    if (!this.sqsEndpoint) {
      const stackOutputs = await getStackOutputs(this.env.highPriorityQueueStackName);
      this.sqsEndpoint = stackOutputs.find((output: any) => output.OutputKey === 'QueueUrl').OutputValue;
      console.log('sqs endpoint:', this.sqsEndpoint);
    }
    return this.sqsEndpoint;
  };

  sendHighPriorityQueueEvent = async (uri: string, payload: any, aggregrateId: string) => {
    return this.sqs.sendMessage({
      MessageBody: JSON.stringify({
        'detail-type': uri,
        detail: {
          ...payload,
        },
      }),
      QueueUrl: await this.getSqsEndpoint(),
      MessageGroupId: aggregrateId,
    });
  };

  projectionFunction = async (uri: string, payload: any, aggregateId = payload.entityUuid ?? uuidv4()) =>
    this.projectionMode === ProjectionMode.EventBridge
      ? this.publishEvent(uri, payload)
      : this.sendHighPriorityQueueEvent(uri, payload, aggregateId);

  sleep = (time = 3000) => {
    process.stdout.write(`wait ${time / 1000}s`);
    return new Promise((resolve) => {
      setTimeout(resolve, time);
    });
  };

  createEntity = async (dto: EntityCreateRequestedEventDto) => {
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Entity.Created`, { ...dto, status: 'ACTIVE' });
    } else {
      const entity = {
        ...dto,
        ...dto.accountStatus,
        status: 'ACTIVE',
        id: dto.entityUuid,
        entityType: dto.type,
        type: 'entity.core',
      };
      await this.saveItem(entity);
    }
  };

  updateEntity = async (dto: EntityUpdatedEventDto) => {
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Entity.Updated`, dto);
    } else {
      const entity = await this.queryItemWithType(dto.entityUuid, 'entity.core');
      if (entity.Items?.length) {
        await this.saveItem({
          ...entity.Items[0],
          ...dto,
          ...dto.accountStatus,
          entityType: dto.type,
          type: 'entity.core',
        });
      } else {
        await this.saveItem({
          ...dto,
          ...dto.accountStatus,
          id: dto.entityUuid,
          entityType: dto.type,
          type: 'entity.core',
        });
      }
    }
  };

  createCustomer = async (
    dtoIn:
      | CustomerCreateRequestedEventDto
      | CustomerEntityLinkedEventDto
      | (CustomerCreateRequestedEventDto & CustomerEntityLinkedEventDto),
  ) => {
    const dto = { ...dtoIn };

    if (!dto.entityUuid) {
      dto.entityUuid = this.testData.entityUuid;
    }

    if (!(dto as CustomerCreateRequestedEventDto).defaultEntityUuid) {
      (dto as CustomerCreateRequestedEventDto).defaultEntityUuid = dto.entityUuid;
    }

    const customer = {
      status: 'ACTIVE',
      ...dto,
    };

    const customerEntity = {
      permissions: {
        allowItemManagement: false,
        allowDiscountManagement: false,
        allowZellerInvoices: false,
        allowXeroPaymentServices: false,
      },
      ...dto,
      role: dto.role ?? CustomerRole.ADMIN,
    };

    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Customer.Created`, customer);
      await sleep(200); // reduce likelihood of out of order events causing test failures
      await this.projectionFunction(`${this.env.componentName}.CustomerEntity.Linked`, {
        ...customerEntity,
        customerUuid: customer.customerUuid,
        entityUuid: dto.entityUuid,
      });
    } else {
      await this.saveItem({
        ...customer,
        id: dto.customerUuid,
        type: 'customer.core',
      });
      await this.saveItem({
        ...customerEntity,
        id: dto.customerUuid,
        type: `customer.entity.${dto.entityUuid}`,
      });
    }
  };

  updateCustomer = async (dto: CustomerUpdatedEventDto) => {
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Customer.Updated`, dto);
    } else {
      const result = await this.queryItemWithType(dto.customerUuid, 'customer.core');
      if (result.Items?.[0]) {
        await this.saveItem({
          ...result.Items?.[0],
          ...dto,
        });

        if (dto.entityUuid) {
          const customerEntity = await this.queryItemWithType(dto.customerUuid, `customer.entity.${dto.entityUuid}`);
          await this.saveItem({
            ...result.Items?.[0],
            ...customerEntity.Items?.[0],
            ...dto,
            type: `customer.entity.${dto.entityUuid}`,
          });
        }
      } else {
        await this.saveItem({
          ...dto,
          customerUuid: dto.customerUuid,
          id: dto.customerUuid,
          type: 'customer.core',
        });
        await this.saveItem({
          ...dto,
          customerUuid: dto.customerUuid,
          id: dto.customerUuid,
          type: `customer.entity.${dto.entityUuid}`,
        });
      }
    }
  };

  createSite = async (dto: Partial<SiteCreatedEventDto> & { customers?: string[] }) => {
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Site.Created`, dto, dto.siteUuid);
    } else {
      await this.saveItem({
        ...dto,
        status: 'ACTIVE',
        siteType: dto.type || SiteType.FIXED,
        siteNameSortKey: dto.name,
        siteName: dto.name,
        siteUuid: dto.siteUuid,
        id: dto.siteUuid,
        type: `${
          dto.type && [SiteType.CNP_ZELLER_INVOICE, SiteType.CNP_ZELLER_INVOICE].includes(dto.type)
            ? `site.core.${dto.type}`
            : 'site.core'
        }`,
      });
    }
  };

  updateSite = async (dto: SiteUpdatedEventDto & { customers?: string[] }) => {
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Site.Updated`, dto);
    } else {
      const site = await this.getDbItem(dto.siteUuid);
      if (site) {
        await this.saveItem({
          ...site,
          ...dto,
          siteNameSortKey: dto.name || site.siteName,
          siteName: dto.name || site.siteName,
        });
      } else {
        await this.saveItem({
          ...dto,
          siteType: dto.type || SiteType.FIXED,
          siteNameSortKey: dto.name,
          siteName: dto.name,
          siteUuid: dto.siteUuid,
          id: dto.siteUuid,
          type: 'site.core',
        });
      }
    }
  };

  setSiteInTestData = async () => {
    this.testData.site = {
      entityUuid: this.testData.entityUuid,
      siteUuid: this.testData.siteUuid,
      pin: uuidv4(),
      type: SiteType.FIXED,
      refundPin: '1234',
      refundPinType: RefundPinType.SITE_PIN,
      discountPin: '5678',
      discountPinType: DiscountPinType.SITE_PIN,
      name: uuidv4(),
    };
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Site.Created`, {
        ...this.testData.site,
        currency: ISO4217.AUD,
        reportingDayStartHour: 0,
        timezone: 'Australia/Melbourne',
      });
    } else {
      await this.saveItem({
        ...this.testData.site,
        status: 'ACTIVE',
        siteNameSortKey: this.testData.site.name,
        siteName: this.testData.site.name,
        id: this.testData.siteUuid,
        type: 'site.core',
        siteType: 'FIXED',
        currency: ISO4217.AUD,
        reportingDayStartHour: 0,
        timezone: 'Australia/Melbourne',
      });
    }
  };

  createDeviceInTestData = async (deviceUuid?: string) => {
    const device = {
      entityUuid: this.testData.entityUuid,
      model: uuidv4(),
      serial: uuidv4(),
      deviceUuid: deviceUuid ?? this.testData.deviceUuid,
      status: DeviceStatus.ACTIVE,
      receipt: {
        name: 'Zeller System Test Entity',
      } as any,
      terminalConfig: JSON.stringify({ mcc: '1000', caid: '000000000272720' }),
    };
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Device.Created`, {
        ...device,
        name: uuidv4(),
      });
    } else {
      await this.saveItem({
        ...device,
        id: device.deviceUuid,
        type: 'device.settings',
        deviceName: uuidv4(),
      });
    }
  };

  setDeviceInTestData = async (deviceUuid?: string) => {
    if (this.client === ComponentClients.DeviceBackend) {
      await this.createDeviceCacheSession({ deviceUuid } as any);
    } else {
      await this.createDeviceInTestData(deviceUuid);
    }
  };

  createDevice = async (dto: DeviceCreatedEventDto) => {
    const device = {
      model: uuidv4(),
      serial: uuidv4(),
      terminalConfig: JSON.stringify({ mcc: '1000', caid: '000000000272720' }),
      ...dto,
      deviceUuid: dto.deviceUuid,
    };
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Device.Created`, device);
    } else {
      await this.saveItem({
        ...device,
        status: 'ACTIVE',
        deviceName: dto.name,
        id: dto.deviceUuid,
        type: 'device.settings',
      });
    }
  };

  updateDevice = async (dto: Partial<DeviceUpdatedEventDto>) => {
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Device.Updated`, dto);
    } else {
      const device = await this.getDbItem(dto.deviceUuid as string);
      if (device) {
        await this.saveItem({
          ...device,
          ...dto,
          deviceName: dto.name || device.deviceName,
        });
      } else {
        await this.saveItem({
          ...dto,
          deviceName: dto.name,
          id: dto.deviceUuid,
          type: 'device.settings',
        });
      }
    }
  };

  createTestEntitySession = async () => {
    const item = {
      id: this.testData.accessToken,
      status: 'ACTIVE',
      entityUuid: this.testData.entityUuid,
      customerUuid: this.testData.customerUuid,
      role: 'ADMIN',
      ttl: Math.floor(new Date().getTime() / 1000) + 86400,
      ...(this.client === ComponentClients.SoftwareDevelopmentKit ? { deviceUuid: this.testData.deviceUuid } : {}),
    };
    console.log('set up session ', item);
    await this.dbClient.put({
      TableName: this.env.sessionTable,
      Item: {
        type: 'identitySub',
        ...item,
      },
    });
    await this.dbClient.put({
      TableName: this.env.sessionTable,
      Item: {
        type: 'onboarding',
        ...item,
      },
    });
  };

  createTestCustomer = async () => {
    const entityUuid = this.getEntityUuid();
    const customerUuid = this.getCustomerUuid();
    await this.createCustomer(
      new CustomerCreateRequestedEventDto({
        customerUuid,
        entityUuid,
        email: `${uuidv4()}@email.com`,
        type: EntityType.INDIVIDUAL,
        registeringIndividual: false,
      }),
    );
  };

  createDeviceUuid = async (model = uuidv4().slice(-16), serial = uuidv4().slice(-16)) => {
    if (this.isDev()) {
      const params = {
        TableName: `${this.getStage()}-dbs-api-dynamodb-SessionCache`,
        Item: {
          id: this.testData.deviceUuid,
          type: 'deviceUuid',
          status: 'ACTIVE',
          model,
          serial,
          modelSerial: `${model}.${serial}`,
        },
      };
      console.log('set up session ', params);
      await this.dbClient.put(params);
      return this.testData.deviceUuid;
    }
    return uuidv4();
  };

  createDeviceCacheSession = async (data?: Partial<TestData> & { model?: string; serial?: string }) => {
    const model = data?.model ? data.model : uuidv4().slice(-16);
    const serial = data?.serial ? data.serial : uuidv4().slice(-16);
    const deviceUuid = await this.createDeviceUuid(model, serial);
    this.testData.deviceUuid = data && data.deviceUuid ? data.deviceUuid : deviceUuid;
    console.log('deviceUuid', this.testData.deviceUuid);
    const params: UpdateCommandInput = {
      TableName: this.env.sessionTable,
      Key: {
        id: this.testData.deviceUuid,
        type: 'deviceUuid',
      },
      UpdateExpression: `set #status = :status, accessToken = :accessToken, refreshToken = :refreshToken, entityUuid = :entityUuid, customerUuid = :customerUuid, #role = :role${
        this.isDev() ? '' : ', model = :model, serial = :serial, modelSerial = :modelSerial'
      }`,
      ExpressionAttributeNames: { '#status': 'status', '#role': 'role' },
      ExpressionAttributeValues: {
        ':status': 'ACTIVE',
        ...(this.isDev() ? {} : { ':model': model, ':serial': serial, ':modelSerial': `${model}.${serial}` }),
        ':accessToken': data && data.accessToken ? data.accessToken : this.testData.accessToken,
        ':refreshToken': data && data.refreshToken ? data.refreshToken : this.testData.refreshToken,
        ':entityUuid': data && data.entityUuid ? data.entityUuid : this.testData.entityUuid,
        ':customerUuid': data && data.customerUuid ? data.customerUuid : this.testData.customerUuid,
        ':role': data && data.role ? data.role : this.testData.role,
      },
    };
    console.log('set up session ', params);
    const output = await this.dbClient.update(params);
    console.log('update device output:', output);

    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Device.Updated`, {
        status: 'ACTIVE',
        name: uuidv4(),
        model,
        deviceUuid: this.testData.deviceUuid,
        entityUuid: this.testData.entityUuid,
      });
    } else {
      await this.dbClient.update({
        TableName: this.env.componentTable,
        Key: {
          id: this.testData.deviceUuid,
          type: 'device.settings',
        },
        UpdateExpression:
          'set #status = :status, #deviceName = :deviceName, #model = :model, #entityUuid = :entityUuid',
        ExpressionAttributeNames: {
          '#status': 'status',
          '#deviceName': 'deviceName',
          '#model': 'model',
          '#entityUuid': 'entityUuid',
        },
        ExpressionAttributeValues: {
          ':status': 'ACTIVE',
          ':deviceName': uuidv4(),
          ':model': model,
          ':entityUuid': this.testData.entityUuid,
        },
      });
    }
  };

  createMobileCacheSession = async (deviceUuid: string, data = {}) => {
    const item = {
      id: deviceUuid,
      status: 'ACTIVE',
      entityUuid: this.testData.entityUuid,
      customerUuid: this.testData.customerUuid,
      role: 'ADMIN',
      ...data,
    };
    await this.dbClient.put({
      TableName: this.env.sessionTable,
      Item: {
        type: 'deviceUuid',
        ...item,
      },
    });
  };

  getAccessToken = async () => {
    if (!this.testData.accessToken) {
      await this.setAccessTokenInTestData();
    }
    return this.testData.accessToken;
  };

  setEntityOnboardingStatus = async (entityUuid: string, onboardingStatus: OnboardingStatus) => {
    const param = {
      TableName: this.env.componentTable,
      Key: { id: entityUuid, type: DbRecordType.ENTITY },
      UpdateExpression: 'set onboardingStatus = :onboardingStatus',
      ExpressionAttributeValues: {
        ':onboardingStatus': onboardingStatus,
      },
    };
    await this.dbClient.update(param);
  };

  assignDeviceToSite = async (deviceUuid = this.testData.deviceUuid, siteUuid = this.testData.siteUuid) => {
    console.log('assign device ', deviceUuid, ', to site ', siteUuid);
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Device.Updated`, {
        siteUuid,
        deviceUuid,
        entityUuid: this.testData.entityUuid,
      });
    } else {
      const params = {
        TableName: this.env.componentTable,
        Key: {
          id: deviceUuid,
          type: 'device.settings',
        },
        UpdateExpression: 'set #siteUuid = :siteUuid',
        ExpressionAttributeNames: { '#siteUuid': 'siteUuid' },
        ExpressionAttributeValues: {
          ':siteUuid': siteUuid,
        },
      };
      await this.dbClient.update(params);
    }
  };

  assignCustomerToSite = async () => {
    const customerUuid = this.testData.customerUuid;
    const siteUuid = this.testData.siteUuid;
    console.log('assign customer ', customerUuid, ', to site ', siteUuid);
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Customer.Updated`, {
        sites: [siteUuid],
        entityUuid: this.testData.entityUuid,
        customerUuid: this.testData.customerUuid,
      });
      await this.projectionFunction(`${this.env.componentName}.Site.Updated`, {
        customers: [this.testData.customerUuid],
        entityUuid: this.testData.entityUuid,
        siteUuid: this.testData.siteUuid,
      });
    } else {
      await this.dbClient.update({
        TableName: this.env.componentTable,
        Key: {
          id: this.testData.customerUuid,
          type: 'customer.core',
        },
        UpdateExpression: 'set #sites = :sites',
        ExpressionAttributeNames: { '#sites': 'sites' },
        ExpressionAttributeValues: {
          ':sites': [siteUuid],
        },
      });
      await this.dbClient.update({
        TableName: this.env.componentTable,
        Key: {
          id: this.testData.customerUuid,
          type: `customer.entity.${this.testData.entityUuid}`,
        },
        UpdateExpression: 'set #sites = :sites',
        ExpressionAttributeNames: { '#sites': 'sites' },
        ExpressionAttributeValues: {
          ':sites': [siteUuid],
        },
      });
      await this.dbClient.update({
        TableName: this.env.componentTable,
        Key: {
          id: this.testData.siteUuid,
          type: 'site.core',
        },
        UpdateExpression: 'set #customers = :customers',
        ExpressionAttributeNames: { '#customers': 'customers' },
        ExpressionAttributeValues: {
          ':customers': [this.testData.customerUuid],
        },
      });
    }
  };

  unassignCustomerFromSites = async () => {
    const customerUuid = this.testData.customerUuid;
    const entityUuid = this.testData.entityUuid;
    console.log('unassign customer ', customerUuid, ', from sites');
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Customer.Updated`, {
        sites: [],
        entityUuid: this.testData.entityUuid,
        customerUuid: this.testData.customerUuid,
      });
      await this.projectionFunction(`${this.env.componentName}.Site.Updated`, {
        customers: [],
        entityUuid: this.testData.entityUuid,
        siteUuid: this.testData.siteUuid,
      });
    } else {
      await this.dbClient.update({
        TableName: this.env.componentTable,
        Key: {
          id: this.testData.customerUuid,
          type: 'customer.core',
        },
        UpdateExpression: 'set #sites = :sites',
        ExpressionAttributeNames: { '#sites': 'sites' },
        ExpressionAttributeValues: {
          ':sites': [],
        },
      });
      await this.dbClient.update({
        TableName: this.env.componentTable,
        Key: {
          id: this.testData.customerUuid,
          type: `customer.entity.${entityUuid}`,
        },
        UpdateExpression: 'set #sites = :sites',
        ExpressionAttributeNames: { '#sites': 'sites' },
        ExpressionAttributeValues: {
          ':sites': [],
        },
      });
      await this.dbClient.update({
        TableName: this.env.componentTable,
        Key: {
          id: this.testData.siteUuid,
          type: 'site.core',
        },
        UpdateExpression: 'set #customers = :customers',
        ExpressionAttributeNames: { '#customers': 'customers' },
        ExpressionAttributeValues: {
          ':customers': [],
        },
      });
    }
  };

  setTestCustomerAsRole = async (role: string) => {
    if (this.isDev()) {
      await this.projectionFunction(`${this.env.componentName}.Customer.Updated`, {
        role,
        customerUuid: this.testData.customerUuid,
        entityUuid: this.testData.entityUuid,
      });
    } else {
      await this.dbClient.update({
        TableName: this.env.componentTable,
        Key: {
          id: this.testData.customerUuid,
          type: 'customer.core',
        },
        UpdateExpression: 'set #role = :role',
        ExpressionAttributeNames: { '#role': 'role' },
        ExpressionAttributeValues: {
          ':role': role,
        },
      });
      await this.dbClient.update({
        TableName: this.env.componentTable,
        Key: {
          id: this.testData.customerUuid,
          type: `customer.entity.${this.testData.entityUuid}`,
        },
        UpdateExpression: 'set #role = :role, #entityUuid = :entityUuid',
        ExpressionAttributeNames: { '#role': 'role', '#entityUuid': 'entityUuid' },
        ExpressionAttributeValues: {
          ':role': role,
          ':entityUuid': this.testData.entityUuid,
        },
      });
    }
    let id = this.testData.accessToken;
    let type = 'identitySub';
    if (this.client === ComponentClients.DeviceBackend) {
      id = this.testData.deviceUuid;
      type = 'deviceUuid';
    }
    const params = {
      TableName: this.env.sessionTable,
      Key: {
        id,
        type,
      },
      UpdateExpression: 'set #role = :role',
      ExpressionAttributeNames: { '#role': 'role' },
      ExpressionAttributeValues: {
        ':role': role,
      },
    };
    console.log('set session role ', this.testData.customerUuid, role);
    await this.dbClient.update(params);
    await this.expectCustomerAsRole(role);
  };

  getDbItem = async (uuid: string, tableName = this.env.componentTable) => {
    return this.dbClient.getDbItem(tableName, uuid);
  };

  queryItemWithType = async (uuid: string, type: string): Promise<QueryCommandOutput> => {
    return this.dbClient.queryById(this.env.componentTable, uuid, type);
  };

  saveItem = async (item: { [key: string]: any }, tableName = this.env.componentTable): Promise<PutCommandOutput> =>
    this.dbClient.put({
      TableName: tableName,
      Item: item,
    });

  setAccessTokenInTestData = async (accessToken?: string) => {
    if (this.client === ComponentClients.DeviceBackend) {
      if (accessToken) {
        this.testData.accessToken = accessToken;
        this.appsyncClient.setAccessToken(accessToken);
      } else {
        const token = await this.auth0Helper.loginUser(this.env.userEmail, this.env.password);
        this.testData.accessToken = token.access_token;
        this.testData.refreshToken = token.refresh_token;
      }
      console.log('get access token:', this.testData);
    } else {
      this.testData.accessToken = (await this.auth0Helper.getAccessToken(
        this.env.userEmail,
        this.env.password,
      )) as string;
    }
  };

  expectCustomerAsRole = async (role: string) => {
    await retry(async () => {
      const customer = await this.queryItemWithType(this.getCustomerUuid(), `customer.entity.${this.getEntityUuid()}`);
      expect(customer?.Items?.[0]?.role).toEqual(role);
    });
  };

  createDomicileValue = async () => {
    const entityUuid = this.getEntityUuid();
    const customerUuid = this.getCustomerUuid();
    await createDomicileValue(this.dbClient, entityUuid, customerUuid, this.env.domicileLookUpTable);
  };
}
