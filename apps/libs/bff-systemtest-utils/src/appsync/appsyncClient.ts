import type { SubscriptionOptions } from 'apollo-client';
import type { AWSAppSyncClient } from 'aws-appsync';
import { AUTH_TYPE } from 'aws-appsync';
import type { AuthOptions } from 'aws-appsync-auth-link';
import gql from 'graphql-tag';

import { getStackOutputs } from '../cloudformation';
import { ensureWebsocket } from '../ensureWebsocket';
import type { BaseEnvService } from '../helper/envService';
import { getSecretValue } from '../secrets/getSecretValue';
import { deleteKeyFromObject } from '../utils';

import { createClient } from './createClient';

ensureWebsocket();

export class AppsyncClient {
  public stage: string = process.env.STAGE ?? 'dev';

  appsyncEndpoint = '';

  apiKey = '';

  protected accessToken = '';

  private apiKeyClient: AWSAppSyncClient<any> | null = null;

  private openIdClient: AWSAppSyncClient<any> | null = null;

  private currentToken = '';

  constructor(readonly env: BaseEnvService) {}

  static readonly createAppsync = async (appsyncStackName: string, env: BaseEnvService) => {
    const client = new AppsyncClient(env);
    await client.initAppsync(appsyncStackName);
    return client;
  };

  initAppsync = async (appsyncStackName: string, accessToken?: string) => {
    if (accessToken) {
      this.setAccessToken(accessToken);
    }
    const stackName = `${this.stage}-${appsyncStackName}`;
    const appsyncOutputs = await getStackOutputs(stackName);
    console.log(`load appsync url for ${stackName}`);
    appsyncOutputs.forEach((output: any) => {
      switch (output.OutputKey) {
        case 'GraphQlApiUrl':
          this.appsyncEndpoint = output.OutputValue;
          console.log('appsyncEndpoint:', this.appsyncEndpoint);
          break;
        case 'GraphQlApiKeyDefault':
          this.apiKey = output.OutputValue;
          console.log(stackName, 'apiKey:', this.apiKey);
          break;
        default:
          break;
      }
    });
  };

  setAccessToken = (accessToken: string) => {
    this.accessToken = accessToken;
  };

  createClient = (auth: AuthOptions) => {
    return createClient(this.appsyncEndpoint, auth, this.env.region).hydrated();
  };

  getIAMClient = async (): Promise<AWSAppSyncClient<any>> => {
    const accessKeyId = (
      await getSecretValue(`${this.stage}-${this.env.componentName}-${this.env.partName}/IAM_USER_KEY`, this.env.region)
    ).SecretString!;
    const secretAccessKey = (
      await getSecretValue(
        `${this.stage}-${this.env.componentName}-${this.env.partName}/IAM_USER_SECRET`,
        this.env.region,
      )
    ).SecretString!;
    return this.createClient({
      type: AUTH_TYPE.AWS_IAM,
      credentials: () => ({
        secretAccessKey,
        accessKeyId,
      }),
    });
  };

  getApiKeyClient = async (): Promise<AWSAppSyncClient<any>> => {
    if (!this.apiKeyClient) {
      this.apiKeyClient = await this.createClient({
        type: AUTH_TYPE.API_KEY,
        apiKey: this.apiKey,
      });
    }
    await this.apiKeyClient.resetStore();
    return this.apiKeyClient;
  };

  getOpenIdClient = async (rebuild = false): Promise<AWSAppSyncClient<any>> => {
    if (!this.currentToken && !this.accessToken) {
      throw new Error('Missing token');
    }
    if (rebuild || !this.openIdClient || this.currentToken !== this.accessToken) {
      this.openIdClient = await this.createClient({
        type: AUTH_TYPE.OPENID_CONNECT,
        jwtToken: () => this.accessToken,
      });
      this.currentToken = this.accessToken;
    } else {
      await this.openIdClient.resetStore();
    }
    return this.openIdClient;
  };

  getSubscriptionUrl = async (useOpenIdClient = false) => {
    const client = await (useOpenIdClient ? this.getOpenIdClient(true) : this.getApiKeyClient());
    const result = await client.query({
      query: gql`
        query GetSubscriptionUrl {
          getSubscriptionUrl {
            apiUrl
            wssUrl
            region
          }
        }
      `,
      fetchPolicy: 'network-only',
    });
    const { apiUrl } = (result as any).data.getSubscriptionUrl;
    console.log('get wss url:', apiUrl);
    return apiUrl;
  };

  getSubscriptionOpenIdClient = async (): Promise<AWSAppSyncClient<any>> => {
    const apiUrl = await this.getSubscriptionUrl(true);
    return createClient(
      `https://${apiUrl}/graphql`,
      {
        type: AUTH_TYPE.OPENID_CONNECT,
        jwtToken: () => this.accessToken,
      },
      this.env.region,
    );
  };

  getSubscriptionApiKeyClient = async (): Promise<AWSAppSyncClient<any>> => {
    const apiUrl = await this.getSubscriptionUrl();
    return createClient(
      `https://${apiUrl}/graphql`,
      {
        type: AUTH_TYPE.API_KEY,
        apiKey: this.apiKey,
      },
      this.env.region,
    );
  };

  createSubscriber = async (
    query: SubscriptionOptions,
    callback: <T>(data: T, subscriberClient: ZenObservable.Subscription) => any,
  ) => {
    const client = await this.getOpenIdClient(true);
    const subscriber = client.subscribe(query).subscribe(
      {
        next: ({ data }: any) => {
          deleteKeyFromObject(data, '__typename');
          console.log(data);
          return callback(data, subscriber);
        },
      },
      (error: any) => {
        console.log(JSON.stringify(error));
        throw error;
      },
    );
  };
}
