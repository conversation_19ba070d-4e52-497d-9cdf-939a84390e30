{"name": "@npco/component-bff-cicd", "version": "1.0.189", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "yarn eslint src", "test": "jest", "run-audit": "yarn npm audit --environment production", "deploy": "yarn npm publish", "clean": "rm -rf dist"}, "dependencies": {"@aws-sdk/client-codepipeline": "3.435.0", "@aws-sdk/client-eventbridge": "3.435.0", "@aws-sdk/client-secrets-manager": "3.435.0", "aws-cdk": "2.1007.0", "aws-cdk-lib": "2.189.1", "axios": "^1.6.2", "constructs": "^10.0.100", "custom-env": "^2.0.1", "source-map-support": "^0.5.21", "uuid": "^8.3.2"}, "devDependencies": {"@npco/eslint-config-backend": "^1.0.12", "@nx/jest": "20.7.0", "@rushstack/eslint-patch": "^1.6.0", "@smithy/types": "^2.6.0", "@swc/core": "^1.3.102", "@swc/jest": "^0.2.29", "@types/aws-lambda": "^8.10.106", "@types/aws-sdk": "^2.7.0", "@types/glob": "^8.1.0", "@types/jest": "^29.5.11", "@types/node": "18.19.14", "@types/serverless": "^3.12.22", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "aws-sdk-client-mock": "^3.0.0", "esbuild": "^0.19.11", "eslint": "8.45.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-folders": "^1.0.4", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-unicorn": "^46.0.0", "jest": "^29.7.0", "jest-html-reporter": "^3.10.2", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "prettier": "^3.1.1", "serverless": "^3.39.0", "serverless-domain-manager": "^7.1.2", "serverless-dotenv-plugin": "^6.0.0", "serverless-esbuild": "^1.52.1", "serverless-plugin-resource-tagging": "^1.2.0", "serverless-plugin-tracing": "^2.0.0", "serverless-prune-plugin": "^2.0.2", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "prettier": "@npco/eslint-config-backend/prettier"}