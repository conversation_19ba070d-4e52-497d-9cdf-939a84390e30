import * as cdk from 'aws-cdk-lib';
import * as codebuild from 'aws-cdk-lib/aws-codebuild';
import { ComputeType } from 'aws-cdk-lib/aws-codebuild';
import * as codepipeline from 'aws-cdk-lib/aws-codepipeline';
import * as actions from 'aws-cdk-lib/aws-codepipeline-actions';
import type * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';

import type { PipelineBuildOptions } from './basePipelineStack';
import { BasePipeline } from './basePipelineStack';
import { BucketCdkStack, createBuckets } from './bucketStack';
import type {
  BucketOptions,
  BuildImageOption,
  CommonOptions,
  NetworkSetting,
  NetworkSettingSsm,
  NodeRuntimeOption,
  SonarOptions,
} from './common';
import { getPrimaryRegion } from './multipleRegionConfig';
import {
  createPipelineNotificationRule,
  createSystemTestPipelineStatusChangeRule,
  createEventRulesTaggingTrigger,
} from './notificationRule';
import { getAppName, getRegionDisplayName, npmRegistry } from './parameters';
import { PipelineBuildStack } from './pipelineBuildStack';
import { resolveBuildImage } from './resolveBuildImage';
import { createBuildRole, createCodeBuildActionRole, createPipelineRole } from './roles';

export interface PipelineOptions
  extends CommonOptions,
    BuildImageOption,
    NodeRuntimeOption,
    BucketOptions,
    SonarOptions {
  bitbucketOwner: string;
  codeStar: string;
  bitbucketName: string;
  branchName: string;
  sonar: boolean;
  primaryRegion?: string;
  chatBotNotiArn?: string;
  shouldSetupSystemTest: boolean;
  shouldDeployMultiRegion: boolean;
  shouldSetupManualApproval: boolean;
  codeBuildCloneOutput?: boolean;
  notificationHandlerPath?: string;
}

export type CodebuildEnvironmentVariables = {
  [name: string]: cdk.aws_codebuild.BuildEnvironmentVariable;
};

export interface PipelineActionOptions extends BuildImageOption {
  name: string;
  codebuildProjectName?: string;
  specFile?: string;
  buildSpec?: { [key: string]: any };
  runOrder: number;
  codebuildProjectEnvVars?: CodebuildEnvironmentVariables;
  envVars?: CodebuildEnvironmentVariables;
  computeType?: codebuild.ComputeType;
  duration?: cdk.Duration;
  network?: NetworkSetting;
  networkSsm?: NetworkSettingSsm;
}
export interface PipelineStageOptions extends BuildImageOption {
  name: string;
  specFile?: string;
  account?: string;
  region?: string;
  input?: codepipeline.Artifact;
  network?: NetworkSetting;
  networkSsm?: NetworkSettingSsm;
  actions: PipelineActionOptions[];
}

export class Pipeline extends BasePipeline {
  projectName: string;

  pipeline: codepipeline.Pipeline;

  pipelineRole: iam.IRole;

  codebuildActionRole: iam.IRole;

  sourceOutput: codepipeline.Artifact;

  actionOutputs: { [name: string]: codepipeline.Artifact };

  appName: string;

  buildStacks: { [region: string]: PipelineBuildStack } = {};

  primaryRegion: {
    name: string;
    displayName?: string;
  };

  constructor(private scope: cdk.App, protected options: PipelineOptions) {
    super(scope, `${getAppName(options)}-iac-codePipeline`, options);
    this.primaryRegion = getPrimaryRegion(options.primaryRegion);
    this.appName = `${options.stage}-${options.componentName}-${options.partName}`;
    this.projectName = `${this.appName}-iac`;

    this.pipelineRole = createPipelineRole(this, this.projectName);
    this.codebuildActionRole = createCodeBuildActionRole(this, this.projectName);

    const crossRegionReplicationBuckets = createBuckets(scope, options, options.shouldDeployMultiRegion);
    this.pipeline = new codepipeline.Pipeline(this, this.projectName, {
      pipelineName: this.projectName,
      role: this.pipelineRole,
      artifactBucket: this.options.shouldDeployMultiRegion
        ? undefined
        : s3.Bucket.fromBucketName(this, 'deploymentS3Bucket', BucketCdkStack.getBucketName(options)),
      crossRegionReplicationBuckets: this.options.shouldDeployMultiRegion ? crossRegionReplicationBuckets : undefined,
    });
    this.pipeline.node.addDependency(crossRegionReplicationBuckets[this.primaryRegion.name].stack);
    this.sourceOutput = new codepipeline.Artifact('sourceOutput');
    this.actionOutputs = {};
    this.pipeline.addStage(this.createSourceStage());

    if (this.options.chatBotNotiArn) {
      createPipelineNotificationRule({
        ...this.options,
        scope: this,
        pipelineName: this.projectName,
        chatBotNotiArn: this.options.chatBotNotiArn,
      });
    }
    if (this.options.shouldSetupSystemTest) {
      createSystemTestPipelineStatusChangeRule(
        this,
        options.region,
        options.account,
        options.componentName,
        options.partName,
        this.projectName,
      );
    }
    if (this.options.chatBotNotiArn ?? this.options.shouldSetupSystemTest) {
      createEventRulesTaggingTrigger(this, options);
    }
  }

  /**
   * if shouldSetupManualApproval is true, it will add a manual approval stage.
   *
   * @param stage
   */
  addDeployStage = (stage: PipelineStageOptions) => {
    if (this.options.shouldSetupManualApproval) {
      const region = stage.region ?? this.options.region;
      const displayName = getRegionDisplayName(region);
      this.addManualApproveStage(displayName ?? region);
    }
    this.addStage(stage);
    return this;
  };

  addStage = (stage: PipelineStageOptions) => {
    const region = stage.region ?? this.options.region;
    const account = stage.account ?? this.options.account;
    const codeBuildProjects: { [spec: string]: cdk.aws_codebuild.PipelineProject } = {};
    const stageActions = stage.actions.map((action) => {
      const buildSpec = action.specFile ?? action.buildSpec ?? stage.specFile;
      const codebuildProjectName = action.codebuildProjectName ?? action.name;
      if (!buildSpec) {
        throw new Error(`Build spec is not specified for action ${action.name} in stage ${stage.name}`);
      }
      let buildProjectScope: BasePipeline | null = null;
      if (this.primaryRegion.name !== region) {
        if (!this.buildStacks[region]) {
          this.buildStacks[region] = new PipelineBuildStack(this.scope, `${this.appName}-iac-pipeline-build`, {
            ...this.options,
            region,
            account,
          });
        }
        buildProjectScope = this.buildStacks[region];
      }
      const buildOptions: PipelineBuildOptions = {
        branchName: this.options.branchName,
        npmRegistry,
        region,
        account: stage.account ?? this.options.account,
        componentName: this.options.componentName,
        partName: this.options.partName,
        stage: this.options.stage,
        network: action.network ?? stage.network,
        networkSsm: stage.networkSsm ?? action.networkSsm,
        computeType: action.computeType ?? ComputeType.MEDIUM,
        sonar: this.options.sonar,
        duration: action.duration ?? cdk.Duration.hours(1),
        buildImage: action.buildImage ?? stage.buildImage ?? this.options.buildImage,
        nodeRuntime: this.options.nodeRuntime,
        envVars: action.codebuildProjectEnvVars,
      };
      if (!codeBuildProjects[codebuildProjectName]) {
        const scope = buildProjectScope ?? this;
        codeBuildProjects[codebuildProjectName] = scope.createCodebuildProject(
          `${this.appName}-iac-pipeline-build-${codebuildProjectName}`,
          buildSpec,
          {
            ...buildOptions,
          },
        );
      }
      const output = new codepipeline.Artifact(`${stage.name}-${action.name}`);
      this.actionOutputs[`${stage.name}-${action.name}`] = output;
      return new actions.CodeBuildAction({
        actionName: action.name,
        type: actions.CodeBuildActionType.BUILD,
        role: this.codebuildActionRole,
        input: stage.input ?? this.sourceOutput,
        project: codeBuildProjects[codebuildProjectName],
        runOrder: action.runOrder,
        outputs: [output],
        environmentVariables: action.envVars,
      });
    });
    this.pipeline.addStage({
      stageName: stage.name,
      actions: stageActions,
    });
    return this;
  };

  addManualApproveStage = (region: string) => {
    this.pipeline.addStage({
      stageName: `Approve_${region}`,
      actions: [
        new actions.ManualApprovalAction({
          actionName: `Approval_${region}`,
        }),
      ],
    });
  };

  addReleaseTagStage = () => {
    this.pipeline.addStage({
      stageName: 'ReleaseTag',
      actions: [
        new actions.CodeBuildAction({
          actionName: 'ReleaseTags',
          input: this.sourceOutput,
          type: actions.CodeBuildActionType.BUILD,
          project: new codebuild.PipelineProject(this, `${this.projectName}-iac-release-tags`, {
            projectName: `${this.projectName}-iac-release-tags`,
            role: createBuildRole(this, 'ReleaseTags'),
            environment: {
              computeType: codebuild.ComputeType.SMALL,
              buildImage: resolveBuildImage(this.options),
              privileged: true,
            },
            environmentVariables: {
              bitbucketUsername: {
                type: codebuild.BuildEnvironmentVariableType.SECRETS_MANAGER,
                value: this.bitbucketCredential.bitbucketUsername.secretName,
              },
              bitbucketPassword: {
                type: codebuild.BuildEnvironmentVariableType.SECRETS_MANAGER,
                value: this.bitbucketCredential.bitbucketAppPassword.secretName,
              },
              bitbucketRepoOwner: {
                type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
                value: this.options.bitbucketOwner,
              },
              bitbucketName: {
                type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
                value: this.options.bitbucketName,
              },
              stage: {
                type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
                value: this.options.stage,
              },
            },
            buildSpec: codebuild.BuildSpec.fromObject({
              version: '0.2',
              phases: {
                install: {
                  commands: [
                    'mkdir tmp',
                    'echo $CODEBUILD_RESOLVED_SOURCE_VERSION',
                    'echo $CODEBUILD_START_TIME',
                    'git clone -n https://${bitbucketUsername}:${bitbucketPassword}@bitbucket.org/${bitbucketRepoOwner}/${bitbucketName}.git tmp',
                    'cd tmp',
                    'git checkout ${CODEBUILD_RESOLVED_SOURCE_VERSION}',
                    'git tag ${stage}-${CODEBUILD_START_TIME}',
                    'git push origin --tags',
                  ],
                },
              },
              artifacts: {
                files: ['**/*', '*'],
              },
            }),
          }),
        }),
      ],
    });
  };

  private createSourceStage = () => {
    return {
      stageName: 'Source',
      actions: [
        new actions.CodeStarConnectionsSourceAction({
          actionName: 'SourceAction',
          owner: this.options.bitbucketOwner,
          connectionArn: this.options.codeStar,
          branch: this.options.branchName,
          output: this.sourceOutput,
          repo: this.options.bitbucketName,
          codeBuildCloneOutput: this.options.codeBuildCloneOutput,
          triggerOnPush: this.options.detectSourceChanges,
        }),
      ],
    };
  };
}
