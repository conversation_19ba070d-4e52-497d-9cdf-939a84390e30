export const primaryRegion = {
  name: 'ap-southeast-2',
  displayName: 'Sydney',
};
export const londonRegion = {
  name: 'eu-west-2',
  displayName: 'London',
};

export const getPrimaryRegion = (region?: string) => {
  switch (region) {
    case 'eu-west-2': {
      return londonRegion;
    }
    case 'ap-southeast-2':
    default: {
      return primaryRegion;
    }
  }
};

export const secondaryRegions = [
  {
    name: 'ap-southeast-1',
    displayName: 'Singapore',
  },
];
