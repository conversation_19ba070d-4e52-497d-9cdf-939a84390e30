import type * as cdk from 'aws-cdk-lib';

import { defaultNodeRuntime } from './parameters';

// NOSONAR
export const getCommonEnv = (stage: string, config = './config') => {
  require('custom-env').env(stage, config); // eslint-disable-line

  const {
    COMPONENT_NAME,
    PART_NAME,
    BITBUCKET_NAME,
    BITBUCKET_REPO_OWNER,
    BITBUCKET_CLONE_URL,

    NPM_REGISTRY,
    CODE_STAR_CONNECTION_ARN,
    CODE_STAR_CONNECTION_ARN_UK,
    CHATBOT_NOTIFICATION_ARN,

    // SONAR
    SONAR_ORGANISATION,
    SONAR_SOURCES,
    SONAR_EXCLUSIONS,
    SONAR_SCANNER_VERSION,

    // FLAG
    SHOULD_SETUP_SYSTEM_TEST,
    SHOULD_SETUP_SONAR,
    SHOULD_RUN_NOTIFICATION,
    SHOULD_MANUAL_APPROVAL,
    SHOULD_MOCK_EVENT_BRIDGE,
    CONTRACT_CONSUMER_TAGS,
    SHOULD_RELEASE_TAG,
    PUBLISH_VERIFICATION_RESULT,
    DEPLOY_MULTI_REGION,
    SHOULD_PUBLISH_CONTRACT_TEST,
    BITBUCKET_OWNER,
    NODE_RUNTIME,

    // system test account
    USE_ST_ACCOUNT,
    ST_ACCOUNT,
    ST_REGION,
  } = process.env;

  const componentName = COMPONENT_NAME ?? '';
  const partName = PART_NAME ?? '';
  const appName = `${componentName}-${partName}`;
  const bitbucketName = BITBUCKET_NAME ?? '';
  const bitbucketOwner = BITBUCKET_OWNER ?? 'zeller-dev';
  const bitbucketRepoOwner = BITBUCKET_REPO_OWNER ?? '';
  const bitbucketCloneUrl = BITBUCKET_CLONE_URL ?? '';

  const npmRegistry = NPM_REGISTRY ?? '';
  const codeStar = CODE_STAR_CONNECTION_ARN ?? '';
  const codeStarUK = CODE_STAR_CONNECTION_ARN_UK ?? '';

  const chatBotNotiArn = CHATBOT_NOTIFICATION_ARN ?? '';

  const shouldSetupSonar = SHOULD_SETUP_SONAR === 'true';
  const shouldSetupSystemTest = SHOULD_SETUP_SYSTEM_TEST === 'true';
  const shouldRunNotficiation = SHOULD_RUN_NOTIFICATION === 'true';
  const shouldSetupManualApproval = SHOULD_MANUAL_APPROVAL === 'true';
  const shouldMockEventBridge = SHOULD_MOCK_EVENT_BRIDGE === 'true';
  const shouldPublishVerificationResult = PUBLISH_VERIFICATION_RESULT === 'true';
  const shouldPublishContractTest = SHOULD_PUBLISH_CONTRACT_TEST === 'true';
  const shouldDeployMultiRegion = DEPLOY_MULTI_REGION === 'true';
  const shouldReleaseTag = SHOULD_RELEASE_TAG === 'true';

  const contractConsumerTags = CONTRACT_CONSUMER_TAGS;
  const sonarOrg = SONAR_ORGANISATION ?? 'npco-dev';
  const sonarSources = SONAR_SOURCES ?? '';
  const sonarExclusions = SONAR_EXCLUSIONS ?? '';
  const sonarScannerVersion = SONAR_SCANNER_VERSION ?? '5.0.1.3006';

  const isSystemTestPipeline = stage.startsWith('st') && stage !== 'staging';

  const nodeRuntime = NODE_RUNTIME ?? defaultNodeRuntime;

  const useSystemTestAccount = USE_ST_ACCOUNT === 'true' ?? false; // NOSONAR
  const systemTestAccount = ST_ACCOUNT;
  const systemTestRegion = ST_REGION;

  const commonEvns: { [key: string]: any } = {
    codeStar,
    codeStarUK,
    chatBotNotiArn,
    componentName,
    partName,
    appName,
    bitbucketCloneUrl,
    bitbucketName,
    bitbucketOwner,
    bitbucketRepoOwner,
    npmRegistry,
    shouldMockEventBridge,
    shouldSetupManualApproval,
    shouldSetupSonar,
    shouldSetupSystemTest,
    shouldRunNotficiation,
    shouldPublishVerificationResult,
    contractConsumerTags,
    shouldReleaseTag,
    shouldDeployMultiRegion,
    sonarExclusions,
    sonarOrg,
    sonarSources,
    sonarScannerVersion,
    shouldPublishContractTest,
    isSystemTestPipeline,
    nodeRuntime,
    useSystemTestAccount,
    systemTestAccount,
    systemTestRegion,
  };

  Object.keys(process.env)
    .filter((e) => e?.startsWith('CICD_'))
    .forEach((e) => {
      commonEvns[e] = process.env[e];
    });
  return commonEvns;
};

export const getContextParameter = (app: cdk.App, parameter: string) => app.node.tryGetContext(parameter);

export const getParametersFromContext = (app: cdk.App) => {
  const stage = getContextParameter(app, 'stage');
  const region = getContextParameter(app, 'region') ?? process.env.CDK_DEFAULT_REGION;
  const account = getContextParameter(app, 'account') ?? process.env.CDK_DEFAULT_ACCOUNT;
  const type = getContextParameter(app, 'type');
  const branchName = getContextParameter(app, 'branch');
  return {
    stage,
    region,
    account,
    type,
    branchName,
  };
};
