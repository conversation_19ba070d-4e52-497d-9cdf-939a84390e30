import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { NodeHttpHandler } from '@smithy/node-http-handler';

import { EnvironmentService } from '../config';

export class BffDynamoDbClient extends DynamoDBClient {
  version = 'real';

  constructor(
    envService$: Pick<EnvironmentService, 'dynamodbTimeout' | 'dynamodbConnectTimeout' | 'maxRetries'> & {
      awsRegion?: string;
    } = new EnvironmentService(),
  ) {
    super({
      region: envService$.awsRegion || 'ap-southeast-2',
      requestHandler: new NodeHttpHandler({
        connectionTimeout: envService$.dynamodbConnectTimeout,
        requestTimeout: envService$.dynamodbTimeout,
        socketTimeout: envService$.dynamodbTimeout,
      }),
      maxAttempts: envService$.maxRetries,
    });
  }
}
