import type {
  SendMessage<PERSON>atchCommandInput,
  SendMessageCommandInput,
  DeleteMessageBatchCommandInput,
} from '@aws-sdk/client-sqs';
import {
  DeleteMessageCommand,
  SendMessageBatchCommand,
  SendMessageCommand,
  DeleteMessageBatchCommand,
  SQSClient,
} from '@aws-sdk/client-sqs';
import { NodeHttpHandler } from '@smithy/node-http-handler';

import type { PartialRequired } from '../utils/partialRequired';

export type SendFifoMessageCommandInput = PartialRequired<SendMessageCommandInput, 'MessageGroupId'>;

export class SqsClient {
  protected readonly sqsClient: SQSClient;

  constructor(readonly region = 'ap-southeast-2') {
    this.region = region || 'ap-southeast-2';
    this.sqsClient = new SQSClient({
      region: this.region,
      requestHandler: new NodeHttpHandler({
        requestTimeout: 2000,
        socketTimeout: 2000,
        connectionTimeout: 2000,
      }),
      maxAttempts: 3,
    });
  }

  sendMessage = async (queryUrl: string, message: any) =>
    this.sendRawMessage({
      MessageBody: JSON.stringify(message),
      QueueUrl: queryUrl,
    });

  sendFifoMessage = async (queryUrl: string, messageBody: any, messageGroupId: string) =>
    this.sendRawFifoMessage({
      MessageBody: JSON.stringify(messageBody),
      QueueUrl: queryUrl,
      MessageGroupId: messageGroupId,
    });

  sendRawFifoMessage = async (params: SendFifoMessageCommandInput) =>
    this.sqsClient.send(new SendMessageCommand(params));

  sendRawMessage = async (params: SendMessageCommandInput) => this.sqsClient.send(new SendMessageCommand(params));

  sendMessageBatch = (params: SendMessageBatchCommandInput) => this.sqsClient.send(new SendMessageBatchCommand(params));

  deleteMessageBatch = async (params: DeleteMessageBatchCommandInput) =>
    this.sqsClient.send(new DeleteMessageBatchCommand(params));

  deleteMessage = async (sqsQueue: string, receiptHandle: string) =>
    this.sqsClient.send(
      new DeleteMessageCommand({
        QueueUrl: sqsQueue,
        ReceiptHandle: receiptHandle,
      }),
    );
}
