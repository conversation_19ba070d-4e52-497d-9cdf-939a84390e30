import {
  S3Client as S3,
  GetO<PERSON><PERSON>ommand,
  DeleteObjectCommand,
  PutO<PERSON>Command,
  HeadObjectCommand,
  ListObjectsV2Command,
} from '@aws-sdk/client-s3';
import type {
  GetObjectCommandInput,
  GetObjectCommandOutput,
  DeleteObjectCommandInput,
  PutObjectCommandInput,
  HeadObjectCommandInput,
  ListObjectsV2CommandInput,
} from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import type { PresignedPostOptions } from '@aws-sdk/s3-presigned-post';
import { createPresignedPost } from '@aws-sdk/s3-presigned-post';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { NodeHttpHandler } from '@smithy/node-http-handler';

import { error, debug } from '../utils/logger';

type PutObjectInput = {
  Expires?: number;
} & Omit<PutObjectCommandInput, 'Expires'>;

export class S3Client {
  protected readonly s3Client: S3;

  constructor(readonly region?: string) {
    this.s3Client = new S3({
      region: region || 'ap-southeast-2',
      requestHandler: new NodeHttpHandler({
        requestTimeout: 2000,
        socketTimeout: 2000,
        connectionTimeout: 2000,
      }),
      maxAttempts: 3,
    });
  }

  listObjectsV2 = async (params: ListObjectsV2CommandInput) => this.s3Client.send(new ListObjectsV2Command(params));

  /**
   * @deprecated use getObjectAndTransformToString or getObjectAndTransformToByteArray
   */
  getObject = async (params: GetObjectCommandInput): Promise<GetObjectCommandOutput> =>
    this.s3Client.send(new GetObjectCommand(params));

  getObjectAndTransformToString = async (params: GetObjectCommandInput): Promise<string | undefined> => {
    try {
      const response = await this.s3Client.send(new GetObjectCommand(params));
      debug(`getObjectAndTransformToString: ${JSON.stringify(response.$metadata)}`);
      return await response.Body?.transformToString('utf-8');
    } catch (err) {
      error(err);
      throw err;
    }
  };

  /**
   * use when downloading objects such as images/archives and streamed objects
   */
  getObjectAndTransformToByteArray = async (params: GetObjectCommandInput): Promise<Uint8Array | undefined> => {
    try {
      const response = await this.s3Client.send(new GetObjectCommand(params));
      debug(`getObjectAndTransformToByteArray: ${JSON.stringify(response.$metadata)}`);
      return await response.Body?.transformToByteArray();
    } catch (err) {
      error(err);
      throw err;
    }
  };

  deleteObject = async (params: DeleteObjectCommandInput) => this.s3Client.send(new DeleteObjectCommand(params));

  upload = async (params: PutObjectCommandInput) => {
    try {
      const parallelUploads3 = new Upload({ client: this.s3Client, params });
      parallelUploads3.on('httpUploadProgress', (progress) => {
        debug(progress);
      });
      return await parallelUploads3.done();
    } catch (err) {
      error(err);
      throw err;
    }
  };

  putObject = async (params: PutObjectCommandInput) => this.s3Client.send(new PutObjectCommand(params));

  getSignedUrl = async (
    params: PutObjectInput | GetObjectCommandInput,
    operation: 'getObject' | 'putObject' = 'getObject',
  ) => {
    if (operation === 'putObject') {
      const putParams: PutObjectInput = { ...params };
      const expiresIn = putParams.Expires ? { expiresIn: putParams.Expires } : undefined;
      const Expires = expiresIn ? new Date(expiresIn.expiresIn) : undefined;
      const command = new PutObjectCommand({ ...putParams, Expires });
      return getSignedUrl(this.s3Client, command, expiresIn);
    }
    const command = new GetObjectCommand(params as GetObjectCommandInput);
    return getSignedUrl(this.s3Client, command);
  };

  getDownloadSignedUrl = async (params: GetObjectCommandInput, expiresIn?: number) => {
    const command = new GetObjectCommand(params);
    return getSignedUrl(this.s3Client, command, expiresIn ? { expiresIn } : undefined);
  };

  createPresignedPost = async (params: PresignedPostOptions) => createPresignedPost(this.s3Client, params);

  headObject = (params: HeadObjectCommandInput) => this.s3Client.send(new HeadObjectCommand(params));
}
