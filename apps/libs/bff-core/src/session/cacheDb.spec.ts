import { CustomerRole, Status } from '@npco/component-dto-core';
import { DeviceStatus } from '@npco/component-dto-device';

import { spy, when } from 'ts-mockito';
import { v4 as uuidv4 } from 'uuid';

import { EnvironmentService } from '../config/envService';
import { BffDynamoDbClient } from '../dynamodb/bffDynamoDbClient';
import { DynamodbService } from '../dynamodb/dynamodbService';
import { CacheType } from '../types/cacheType';
import type { IdentityDeviceCacheItem, MobileDeviceCache } from '../types/sessionCache';

import { CacheDb } from './cacheDb';

jest.mock('../dynamodb/bffDynamoDbClient');
jest.mock('../config/envService');

describe('cache db test suite', () => {
  let cacheDb: CacheDb;
  let dynamoDb: DynamodbService;

  beforeAll(async () => {
    const envService = new EnvironmentService();
    dynamoDb = new DynamodbService(
      envService,
      new BffDynamoDbClient({
        awsRegion: 'ap-southeast-2',
        dynamodbTimeout: 5000,
        dynamodbConnectTimeout: 5000,
        maxRetries: 3,
      }),
    );
    cacheDb = new CacheDb(envService, dynamoDb);
  });
  let accessToken = '';
  let deviceUuid = '';
  let deviceCache: MobileDeviceCache;
  let deviceCacheResult: IdentityDeviceCacheItem;

  beforeEach(() => {
    accessToken = uuidv4();
    deviceUuid = uuidv4();
    const model = uuidv4().slice(-16);
    const serial = uuidv4().slice(-16);
    deviceCache = {
      status: DeviceStatus.ACTIVE,
      model,
      serial,
      modelSerial: `${model}.${serial}`,
      accessToken,
      auth0sub: 'auth0',
      refreshToken: 'refreshToken',
      idToken: 'idToken',
      entityUuid: 'entityId',
      customerUuid: 'customerId',
      role: CustomerRole.MANAGER,
    };
    deviceCacheResult = {
      id: deviceUuid,
      status: DeviceStatus.ACTIVE,
      model,
      serial,
      modelSerial: `${model}.${serial}`,
      accessToken,
      auth0sub: 'auth0',
      refreshToken: 'refreshToken',
      idToken: 'idToken',
      entityUuid: 'entityId',
      type: CacheType.device,
      customerUuid: 'customerId',
      role: CustomerRole.MANAGER,
    };
  });

  it('should save and update an initial device item', async () => {
    let result = await cacheDb.getDeviceByAccessToken(accessToken);
    expect(result.Items![0]).toEqual(undefined);

    const initialDevice = {
      model: deviceCache.model,
      serial: deviceCache.serial,
      modelSerial: `${deviceCache.model}.${deviceCache.serial}`,
    };

    const initResult = {
      model: deviceCache.model,
      serial: deviceCache.serial,
      modelSerial: `${deviceCache.model}.${deviceCache.serial}`,
      type: 'deviceUuid',
    };

    await cacheDb.saveDeviceCache(deviceUuid, initialDevice);

    result = await cacheDb.getCachedItem(deviceUuid, CacheType.device);
    expect(result.Items![0]).toEqual({
      id: deviceUuid,
      ...initResult,
    });

    const newAccessToken = uuidv4();
    await cacheDb.saveDeviceCache(deviceUuid, {
      ...initialDevice,
      accessToken: newAccessToken,
    });

    result = await cacheDb.getCachedItem(deviceUuid, CacheType.device);
    expect(result.Items![0]).toEqual({
      ...initResult,
      id: deviceUuid,
      accessToken: newAccessToken,
    });
  });

  it('should save and update a device item', async () => {
    let result = await cacheDb.getDeviceByAccessToken(accessToken);
    expect(result.Items![0]).toEqual(undefined);

    await cacheDb.saveDeviceCache(deviceUuid, deviceCache);

    result = await cacheDb.getDeviceByAccessToken(accessToken);

    expect(result.Items![0]).toEqual(deviceCacheResult);

    const newAccessToken = uuidv4();
    await cacheDb.saveDeviceCache(deviceUuid, {
      ...deviceCache,
      accessToken: newAccessToken,
    });

    result = await cacheDb.getDeviceByAccessToken(newAccessToken);
    expect(result.Items![0]).toEqual({
      ...deviceCacheResult,
      accessToken: newAccessToken,
    });
  });

  it('should be able to get Device By ModelSerial', async () => {
    let result = await cacheDb.getDeviceByModelSerial('1234', '56789');
    expect(result.Items![0]).toEqual(undefined);

    await cacheDb.saveDeviceCache(deviceUuid, deviceCache);

    result = await cacheDb.getDeviceByModelSerial(deviceCache.model!, deviceCache.serial!);
    expect(result.Items![0]).toEqual({
      id: deviceCacheResult.id,
      model: deviceCacheResult.model,
      serial: deviceCacheResult.serial,
      modelSerial: deviceCacheResult.modelSerial,
      type: 'deviceUuid',
    });
  });

  it('should be able to get Device By AccessToken', async () => {
    let result = await cacheDb.getDeviceByAccessToken(accessToken);
    expect(result.Items![0]).toEqual(undefined);
    await cacheDb.saveDeviceCache(deviceUuid, deviceCache);

    result = await cacheDb.getDeviceByAccessToken(accessToken);
    expect(result.Items![0]).toEqual(deviceCacheResult);
  });

  it('should be able to find Device By Tokens', async () => {
    let result = await cacheDb.findDeviceByTokens(deviceUuid, accessToken, 'refreshToken');
    expect(result.Items![0]).toEqual(undefined);

    await cacheDb.saveDeviceCache(deviceUuid, deviceCache);

    result = await cacheDb.findDeviceByTokens(deviceUuid, accessToken, 'refreshToken');
    expect(result.Items![0]).toEqual(deviceCacheResult);
  });

  it('should be able to persist model serial during update device cache', async () => {
    const deviceId = uuidv4();
    const model = uuidv4().slice(-16);
    const serial = uuidv4().slice(-16);
    const device = {
      model,
      serial,
      accessToken: uuidv4(),
      entityUuid: uuidv4(),
      status: DeviceStatus.ACTIVE,
      modelSerial: `${model}.${serial}`,
    };
    await cacheDb.saveDeviceCache(deviceId, device);

    const updatedDevice = {
      entityUuid: device.entityUuid,
      accessToken: uuidv4(),
    };
    await cacheDb.saveDeviceCache(deviceId, updatedDevice);
    const cache = await cacheDb.getDeviceByAccessToken(updatedDevice.accessToken!);
    expect(cache.Items![0]).toEqual({
      ...device,
      ...updatedDevice,
      id: deviceId,
      type: 'deviceUuid',
      deviceUuid: undefined,
    });
  });

  it('should be able to clear the device cache (logoff/reset)', async () => {
    const deviceId = uuidv4();
    const model = uuidv4().slice(-16);
    const serial = uuidv4().slice(-16);
    const device = {
      model,
      serial,

      accessToken: uuidv4(),
      entityUuid: uuidv4(),
      status: DeviceStatus.ACTIVE,
      modelSerial: `${model}.${serial}`,
    };
    await cacheDb.saveDeviceCache(deviceId, device);
    let cache = await cacheDb.getDeviceByAccessToken(device.accessToken!);
    expect(cache.Items![0]).toEqual({
      id: deviceId,
      type: 'deviceUuid',
      ...device,
    });
    await cacheDb.clearDeviceCache(deviceId, { status: DeviceStatus.INACTIVE } as any);
    cache = await cacheDb.getCachedItem(deviceId, CacheType.device);
    expect(cache.Items![0]).toEqual({
      id: deviceId,
      type: 'deviceUuid',
      status: DeviceStatus.INACTIVE,
      model: device.model,
      serial: device.serial,
      modelSerial: `${device.model}.${device.serial}`,
    });
    cache = await cacheDb.getDeviceByModelSerial(device.model, device.serial);
    expect(cache.Items![0]).toEqual({
      id: deviceId,
      type: 'deviceUuid',
      model: device.model,
      serial: device.serial,
      modelSerial: `${device.model}.${device.serial}`,
    });
  });

  it('should be able to clear the identity device entity cache (logoff/reset)', async () => {
    const entityUuid = uuidv4();
    const expiry = cacheDb.getExpiry();
    for (let i = 0; i < 5; i += 1) {
      const item = {
        entityUuid,
        deviceUuid,
        id: uuidv4(),
        type: CacheType.identity,
        ttl: expiry,
      };
      await dynamoDb.put({
        TableName: 'SessionCache',
        Item: item,
      });
    }

    await cacheDb.clearIdentityDeviceEntityCache(deviceUuid);

    const data = await dynamoDb.query({
      TableName: 'SessionCache',
      IndexName: 'deviceGsi',
      KeyConditionExpression: 'deviceUuid = :deviceUuid',
      ExpressionAttributeValues: {
        ':deviceUuid': deviceUuid,
      },
    });
    expect(data.Items!.length).toEqual(5);
    data.Items!.forEach((item: any) => {
      expect(item.entityUuid).toBeUndefined();
    });
  });

  describe('Account status', () => {
    it('should be able to update cache records for entity', async () => {
      const entityUuid = uuidv4();
      const res = await cacheDb.getAllByEntityUuid(entityUuid, CacheType.identity);
      expect(res.length).toEqual(0);
      await dynamoDb.put({
        TableName: 'SessionCache',
        Item: {
          id: uuidv4(),
          type: CacheType.identity,
        },
      });
      for (let i = 0; i < 50; i += 1) {
        const item = {
          entityUuid,
          id: uuidv4(),
          type: CacheType.identity,
        };
        await dynamoDb.put({
          TableName: 'SessionCache',
          Item: item,
        });
      }
      await cacheDb.updateCacheForEntityStatus(entityUuid, Status.DISABLED);
      const data = await cacheDb.getAllByEntityUuid(entityUuid, CacheType.identity);
      expect(data.length).toEqual(50);
      data.forEach((item: any) => {
        expect(item.entityUuid).toEqual(entityUuid);
        expect(item.type).toEqual(CacheType.identity);
        expect(item.accountStatus).toEqual(Status.DISABLED);
      });
    });
  });

  describe('Identity & onboarding Cache', () => {
    it('should not find a cache item', async () => {
      expect(((await cacheDb.getCachedItem('1', CacheType.identity)).Items || [])[0]).toEqual(undefined);
    });

    it('should have 24hour expiry', async () => {
      const expiry = cacheDb.getExpiry();
      const ttl = Math.floor(new Date().getTime() / 1000);
      expect((expiry - ttl) / (60 * 60)).toBeLessThanOrEqual(24);
    });

    it('should not find an expired session', async () => {
      const spiedService = spy(cacheDb);
      const ttl = Math.floor(new Date().getTime() / 1000);
      when(spiedService.getTimestampSeconds()).thenReturn(ttl);
      when(spiedService.getExpiry()).thenReturn(ttl - 10);
      await cacheDb.saveCachedSession(
        'token1',
        {
          auth0sub: '123123',
          customerUuid: 'customerUuid',
          entityUuid: 'entityUuid',
          role: CustomerRole.ADMIN,
        },
        CacheType.identity,
      );
      expect(((await cacheDb.getCachedItem('token1', CacheType.identity)).Items || [])[0]).toEqual(undefined);
    });

    it('should successfully create and query a cache item', async () => {
      const spiedService = spy(cacheDb);
      const ttl = Math.floor(new Date().getTime() / 1000);
      when(spiedService.getTimestampSeconds()).thenReturn(ttl);
      when(spiedService.getExpiry()).thenReturn(ttl + 10);
      await cacheDb.saveCachedSession(
        'token2',
        {
          auth0sub: '123123',

          customerUuid: 'customerUuid',
          entityUuid: 'entityUuid',
          role: CustomerRole.ADMIN,
        },
        CacheType.identity,
      );
      expect(((await cacheDb.getCachedItem('token2', CacheType.identity)).Items || [])[0]).toEqual({
        id: 'token2',
        type: 'identitySub',
        auth0sub: '123123',
        customerUuid: 'customerUuid',
        entityUuid: 'entityUuid',
        role: 'ADMIN',
        ttl: ttl + 10,
      });
    });

    it('should be able to persist token duing update identity cache', async () => {
      const cacheData = {
        role: CustomerRole.ADMIN,
        entityUuid: uuidv4(),
        customerUuid: uuidv4(),
        auth0sub: uuidv4(),
        accessToken: uuidv4(),
      };
      await cacheDb.saveCachedSession(cacheData.accessToken, cacheData, CacheType.identity);
      const updatedData: any = {
        auth0sub: uuidv4(),
      };
      await cacheDb.saveCachedSession(cacheData.accessToken, updatedData, CacheType.identity);
      const data = await dynamoDb.query({
        TableName: 'SessionCache',
        KeyConditionExpression: 'id = :id AND #type = :type',
        ExpressionAttributeValues: {
          ':id': cacheData.accessToken,
          ':type': CacheType.identity,
        },
        ExpressionAttributeNames: {
          '#type': 'type',
        },
      });
      expect((data.Items || [])[0].entityUuid).toBe(cacheData.entityUuid);
    });

    describe('zeller session cachDb test suite', () => {
      it('should be able to save the zellerSessionId', async () => {
        const zellerSessionId = uuidv4();
        const customerUuid = uuidv4();
        const entityUuid = uuidv4();
        const result = await cacheDb.saveZellerSession(
          { zellerSessionId, customerUuid, entityUuid },
          CacheType.zellerSession,
        );
        expect(result).not.toBe(null);
      });
      it('should return null when trying to save duplicate zellerSessionId', async () => {
        const zellerSessionId = uuidv4();
        const customerUuid = uuidv4();
        const entityUuid = uuidv4();
        const result = await cacheDb.saveZellerSession(
          { zellerSessionId, customerUuid, entityUuid },
          CacheType.zellerSession,
        );
        expect(result).not.toBe(null);
        const duplicate = await cacheDb.saveZellerSession(
          { zellerSessionId, customerUuid, entityUuid },
          CacheType.zellerSession,
        );
        expect(duplicate).toBe(null);
      });
    });
  });
});
