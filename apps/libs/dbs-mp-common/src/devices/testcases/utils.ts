import type { StandInRule } from '@npco/component-dto-core';
import { Source, StandInField, StandInOperation } from '@npco/component-dto-core';
import {
  DeviceCreatedEventDto,
  DeviceInfoUpdateDto,
  DeviceStatus,
  DeviceUpdatedEventDto,
  PosMethod,
  PosMode,
  ZellerPosFavouriteType,
} from '@npco/component-dto-device';

import { v4 as uuidv4 } from 'uuid';

export const defaultStandInRules: StandInRule[] = [
  {
    operation: StandInOperation.BELOW,
    field: StandInField.TRANSACTION_AMOUNT,
    value: '5999999',
  },
];

export const getFullDeviceCreatedDto = (optionalUpdates?: Partial<DeviceCreatedEventDto>) => {
  return new DeviceCreatedEventDto({
    deviceUuid: uuidv4(),
    entityUuid: uuidv4(),
    siteUuid: uuidv4(),
    name: uuidv4(),
    status: DeviceStatus.ACTIVE,
    model: uuidv4(),
    serial: uuidv4(),
    network: {
      wifiEnabled: true,
      wifiSsid: uuidv4(),
      cellularEnabled: false,
      cellularNetwork: uuidv4(),
      ethernetEnabled: false,
    },
    terminalConfig: JSON.stringify({ mcc: '3333', catid: '00001', caid: '0002' }),
    emvTables: uuidv4(),
    emvCaKeys: uuidv4(),
    emvConfig: uuidv4(),
    standInRules: [
      {
        operation: StandInOperation.BELOW,
        field: StandInField.TRANSACTION_AMOUNT,
        value: uuidv4(),
      },
      {
        operation: StandInOperation.BELOW,
        field: StandInField.OFFLINE_AMOUNT,
        value: uuidv4(),
      },
      {
        operation: StandInOperation.BELOW,
        field: StandInField.OFFLINE_COUNT,
        value: uuidv4(),
      },
    ],
    ...optionalUpdates,
  });
};

export const getFullDeviceUpdatedDto = (optionalUpdates?: Partial<DeviceUpdatedEventDto>) => {
  return new DeviceUpdatedEventDto({
    deviceUuid: uuidv4(),
    entityUuid: uuidv4(),
    name: uuidv4(),
    status: DeviceStatus.ACTIVE,
    model: uuidv4(),
    serial: uuidv4(),
    network: {
      wifiEnabled: true,
      wifiSsid: uuidv4(),
      cellularEnabled: false,
      cellularNetwork: uuidv4(),
      ethernetEnabled: false,
    },
    posSettings: {
      mode: PosMode.INTEGRATED,
      posReceipt: false,
      ipAddress: uuidv4(),
      port: 1234,
      posSoftwareName: uuidv4(),
      active: Source.LINKLY,
      connectionMethod: PosMethod.LAN,
      posRegisterName: uuidv4(),
      exitRequiresPin: false,
      zellerPosSettings: {
        favourites: [{ id: 'id', type: ZellerPosFavouriteType.ITEM }],
      },
    },
    terminalConfig: JSON.stringify({ mcc: '3333', catid: '00001', caid: '0002' }),
    emvTables: uuidv4(),
    emvCaKeys: uuidv4(),
    emvConfig: uuidv4(),
    standInRules: defaultStandInRules,
    ...optionalUpdates,
  });
};

export const getFullInformationUpdatedDto = (optionalUpdates?: Partial<DeviceInfoUpdateDto>) => {
  return new DeviceInfoUpdateDto({
    id: uuidv4(),
    timestamp: uuidv4(),
    timestampUtc: uuidv4(),
    model: uuidv4(),
    serial: uuidv4(),
    heightPixels: uuidv4(),
    widthPixels: uuidv4(),
    ydpi: uuidv4(),
    xdpi: uuidv4(),
    density: uuidv4(),
    densityDpi: uuidv4(),
    androidOs: uuidv4(),
    androidKernel: uuidv4(),
    androidBuild: uuidv4(),
    androidDevice: uuidv4(),
    androidModel: uuidv4(),
    ramTotal: uuidv4(),
    ramAvail: uuidv4(),
    flashTotal: uuidv4(),
    flashAvail: uuidv4(),
    emvL1Version: uuidv4(),
    emvL2Version: uuidv4(),
    hardwareVersion: uuidv4(),
    pciFirmwareVersion: uuidv4(),
    securityVersion: uuidv4(),
    secureCpuId: uuidv4(),
    customerName: uuidv4(),
    customerId: uuidv4(),
    numMasterKeys: 123,
    numDukptKeys: 123,
    ksn: uuidv4(),
    numCapk: 123,
    numAids: 123,
    pedCurrentTemp: 123,
    pedVersion: uuidv4(),
    pedModel: uuidv4(),
    pedStatus: 123,
    softwareVersion: uuidv4(),
    firmwareVersion: uuidv4(),
    simState: 123,
    simSubscriberId: uuidv4(),
    simSerialNumber: uuidv4(),
    simCountry: uuidv4(),
    simOperator: uuidv4(),
    simOperatorName: uuidv4(),
    simNetwork: uuidv4(),
    simNetworkName: uuidv4(),
    simNetworkType: uuidv4(),
    simCellDetails: uuidv4(),
    simIpAddress: uuidv4(),
    simGatewayAddress: uuidv4(),
    simDns1Address: uuidv4(),
    simDns2Address: uuidv4(),
    simRssi: 123,
    connectionType: 123,
    wifiState: 123,
    wifiSsid: uuidv4(),
    wifiBssid: uuidv4(),
    wifiFrequency: uuidv4(),
    wifiChannel: 123,
    wifiSpeed: 123,
    wifiRssi: 123,
    wifiStandard: uuidv4(),
    wifiSecurityModel: uuidv4(),
    wifiAvailableNetworks: uuidv4(),
    wifiMacAddress: uuidv4(),
    wifiIpAddress: uuidv4(),
    wifiGatewayAddress: uuidv4(),
    wifiDns1Address: uuidv4(),
    wifiDns2Address: uuidv4(),
    wifiTransferSpeed: 123,
    wifiMaxTransferSpeed: 123,
    tcpMac: uuidv4(),
    tcpDhcp: true,
    tcpIpAddress: uuidv4(),
    tcpGatewayAddress: uuidv4(),
    tcpDns1Address: uuidv4(),
    tcpDns2Address: uuidv4(),
    tcpTransferSpeeds: uuidv4(),
    tcpMaxTransferSpeeds: uuidv4(),
    os: uuidv4(),
    kernel: uuidv4(),
    uiSoftwareVersion: uuidv4(),
    ...optionalUpdates,
  });
};
