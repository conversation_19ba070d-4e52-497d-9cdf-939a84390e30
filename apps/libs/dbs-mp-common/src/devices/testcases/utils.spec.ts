import type { StandInRule } from '@npco/component-dto-core';
import { Source, StandInField, StandInOperation } from '@npco/component-dto-core';
import { DeviceStatus, PosMethod, PosMode, ZellerPosFavouriteType } from '@npco/component-dto-device';

import { v4 as uuidv4 } from 'uuid';

import { getFullDeviceCreatedDto, getFullDeviceUpdatedDto, getFullInformationUpdatedDto } from './utils';

export const defaultStandInRules: StandInRule[] = [
  {
    operation: StandInOperation.BELOW,
    field: StandInField.TRANSACTION_AMOUNT,
    value: '5999999',
  },
];

describe('validate device dto utils.ts', () => {
  it('should be get full device created dto', async () => {
    const deviceUuid = uuidv4();
    const dto = getFullDeviceCreatedDto({
      deviceUuid,
    });

    expect(dto).toEqual({
      deviceUuid,
      entityUuid: expect.any(String),
      siteUuid: expect.any(String),
      name: expect.any(String),
      status: DeviceStatus.ACTIVE,
      model: expect.any(String),
      serial: expect.any(String),
      network: {
        wifiEnabled: true,
        wifiSsid: expect.any(String),
        cellularEnabled: false,
        cellularNetwork: expect.any(String),
        ethernetEnabled: false,
      },
      terminalConfig: expect.any(String),
      emvTables: expect.any(String),
      emvCaKeys: expect.any(String),
      emvConfig: expect.any(String),
      standInRules: [
        {
          operation: StandInOperation.BELOW,
          field: StandInField.TRANSACTION_AMOUNT,
          value: expect.any(String),
        },
        {
          operation: StandInOperation.BELOW,
          field: StandInField.OFFLINE_AMOUNT,
          value: expect.any(String),
        },
        {
          operation: StandInOperation.BELOW,
          field: StandInField.OFFLINE_COUNT,
          value: expect.any(String),
        },
      ],
    });
  });

  it('should be get full device updated dto', async () => {
    const deviceUuid = uuidv4();
    const dto = getFullDeviceUpdatedDto({
      deviceUuid,
    });

    expect(dto).toEqual({
      deviceUuid,
      entityUuid: expect.any(String),
      name: expect.any(String),
      status: DeviceStatus.ACTIVE,
      model: expect.any(String),
      serial: expect.any(String),
      network: {
        wifiEnabled: true,
        wifiSsid: expect.any(String),
        cellularEnabled: false,
        cellularNetwork: expect.any(String),
        ethernetEnabled: false,
      },
      posSettings: {
        mode: PosMode.INTEGRATED,
        posReceipt: false,
        ipAddress: expect.any(String),
        port: 1234,
        posSoftwareName: expect.any(String),
        active: Source.LINKLY,
        connectionMethod: PosMethod.LAN,
        posRegisterName: expect.any(String),
        exitRequiresPin: false,
        zellerPosSettings: {
          favourites: [
            {
              id: 'id',
              type: ZellerPosFavouriteType.ITEM,
            },
          ],
        },
      },
      terminalConfig: expect.any(String),
      emvTables: expect.any(String),
      emvCaKeys: expect.any(String),
      emvConfig: expect.any(String),
      standInRules: defaultStandInRules,
    });
  });

  it('should be get full device information updated dto', async () => {
    const deviceUuid = uuidv4();
    const dto = getFullInformationUpdatedDto({
      id: deviceUuid,
    });

    expect(dto).toEqual({
      id: deviceUuid,
      timestamp: expect.any(String),
      timestampUtc: expect.any(String),
      model: expect.any(String),
      serial: expect.any(String),
      heightPixels: expect.any(String),
      widthPixels: expect.any(String),
      ydpi: expect.any(String),
      xdpi: expect.any(String),
      density: expect.any(String),
      densityDpi: expect.any(String),
      androidOs: expect.any(String),
      androidKernel: expect.any(String),
      androidBuild: expect.any(String),
      androidDevice: expect.any(String),
      androidModel: expect.any(String),
      ramTotal: expect.any(String),
      ramAvail: expect.any(String),
      flashTotal: expect.any(String),
      flashAvail: expect.any(String),
      emvL1Version: expect.any(String),
      emvL2Version: expect.any(String),
      hardwareVersion: expect.any(String),
      pciFirmwareVersion: expect.any(String),
      securityVersion: expect.any(String),
      secureCpuId: expect.any(String),
      customerName: expect.any(String),
      customerId: expect.any(String),
      numMasterKeys: 123,
      numDukptKeys: 123,
      ksn: expect.any(String),
      numCapk: 123,
      numAids: 123,
      pedCurrentTemp: 123,
      pedVersion: expect.any(String),
      pedModel: expect.any(String),
      pedStatus: 123,
      softwareVersion: expect.any(String),
      firmwareVersion: expect.any(String),
      simState: 123,
      simSubscriberId: expect.any(String),
      simSerialNumber: expect.any(String),
      simCountry: expect.any(String),
      simOperator: expect.any(String),
      simOperatorName: expect.any(String),
      simNetwork: expect.any(String),
      simNetworkName: expect.any(String),
      simNetworkType: expect.any(String),
      simCellDetails: expect.any(String),
      simIpAddress: expect.any(String),
      simGatewayAddress: expect.any(String),
      simDns1Address: expect.any(String),
      simDns2Address: expect.any(String),
      simRssi: 123,
      connectionType: 123,
      wifiState: 123,
      wifiSsid: expect.any(String),
      wifiBssid: expect.any(String),
      wifiFrequency: expect.any(String),
      wifiChannel: 123,
      wifiSpeed: 123,
      wifiRssi: 123,
      wifiStandard: expect.any(String),
      wifiSecurityModel: expect.any(String),
      wifiAvailableNetworks: expect.any(String),
      wifiMacAddress: expect.any(String),
      wifiIpAddress: expect.any(String),
      wifiGatewayAddress: expect.any(String),
      wifiDns1Address: expect.any(String),
      wifiDns2Address: expect.any(String),
      wifiTransferSpeed: 123,
      wifiMaxTransferSpeed: 123,
      tcpMac: expect.any(String),
      tcpDhcp: true,
      tcpIpAddress: expect.any(String),
      tcpGatewayAddress: expect.any(String),
      tcpDns1Address: expect.any(String),
      tcpDns2Address: expect.any(String),
      tcpTransferSpeeds: expect.any(String),
      tcpMaxTransferSpeeds: expect.any(String),
      os: expect.any(String),
      kernel: expect.any(String),
      uiSoftwareVersion: expect.any(String),
    });
  });
});
