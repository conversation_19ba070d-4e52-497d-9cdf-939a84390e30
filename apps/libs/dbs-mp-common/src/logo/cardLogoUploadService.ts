import {
  convertBmpToPng,
  convertHeicToPngOrJpeg,
  isBmp,
  isHeicHeif,
} from '@npco/bff-common/dist/image/imageProcessUtils';
import { info, error } from '@npco/component-bff-core/dist/utils/logger';
import type { CardLogo } from '@npco/component-dto-issuing-card';

import { Injectable } from '@nestjs/common';
import gql from 'graphql-tag';

import { AppSyncClient } from '../appsync';
import { EnvironmentService } from '../config/envService';
import { DynamodbService } from '../dynamodb/dynamodbService';
import { S3BaseService } from '../images/s3baseService';
import { CardLogoImage } from '../images/utils/cardLogoImage';
import { EreceiptLogo } from '../images/utils/ereceiptLogo';
import { PrintedLogo } from '../images/utils/printedLogo';

import type { CardLogoProcessResultInput } from './types';

@Injectable()
export class CardLogoUploadService extends S3BaseService {
  constructor(
    protected readonly envService: EnvironmentService,
    private readonly dbService: DynamodbService,
    private readonly printedLogo: PrintedLogo,
    private readonly ereceiptLogo: EreceiptLogo,
  ) {
    super(envService);
  }

  async handle(record: { bucket: string; key: string; size: number }) {
    const appsyncClient = await this.getAppSyncClient();
    const { entityUuid, logoUuid } = this.getEntityAndLogoUuidFromKey(record.key);
    try {
      info(`Starting card logo processing for key: ${record.key}, bucket: ${record.bucket}`);
      const logoImage = await this.buildLogoImages(record);
      await this.uploadCardLogo(this.envService.cardLogoProcessedBucket, logoImage);
      const metadata = this.createCardLogoMetadata(logoImage);
      await this.saveCardLogoMetadata(metadata);
      info(`Saved card logo metadata for logoUuid: ${logoUuid}`);
      const cardLogo = await this.getCardLogoFromDb(metadata);
      const mutationInput = this.buildMutationInput(cardLogo, entityUuid);
      await this.publishCardLogoProcessed(appsyncClient, mutationInput);
      info(`Successfully published card logo processed event for logoUuid: ${logoUuid}`);
    } catch (e: any) {
      error(`handleCardLogoUploaded:failed -> ${e.message}`, logoUuid);
      const mutationInput = this.buildErrorMutationInput(e, logoUuid, entityUuid);
      info(`Publishing card logo processed error event for logoUuid: ${logoUuid}`);
      await this.publishCardLogoProcessed(appsyncClient, mutationInput);
      throw e;
    }
  }

  private async saveCardLogoMetadata(metadata: CardLogo) {
    await this.dbService.put({
      TableName: this.envService.componentTableName,
      Item: metadata,
    });
  }

  private async getCardLogoFromDb(metadata: CardLogo): Promise<CardLogo> {
    const result = await this.dbService.queryIdByType(metadata.entityUuid, metadata.id, metadata.type);
    const cardLogo = result.Items?.[0];
    if (!cardLogo) {
      throw new Error(`CardLogo with entityUuid: ${metadata.entityUuid} and id: ${metadata.id} not found`);
    }
    return cardLogo as CardLogo;
  }

  private buildMutationInput(cardLogo: CardLogo, entityUuid: string): CardLogoProcessResultInput {
    return {
      entityUuid,
      cardLogo: {
        id: cardLogo.id,
        colorPreviewUrl: cardLogo.colorPreviewUrl,
        monochromePreviewUrl: cardLogo.monochromePreviewUrl,
      },
      error: null,
    };
  }

  private buildErrorMutationInput(e: any, cardLogoUuid: string, entityUuid: string): CardLogoProcessResultInput {
    return {
      entityUuid,
      cardLogo: null,
      error: {
        code: e.code || e.message || 'CARD_LOGO_UNKNOWN_ERROR',
        cardLogoUuid,
      },
    };
  }

  private async getAppSyncClient() {
    return new AppSyncClient(this.envService).getAppSyncClient();
  }

  private async publishCardLogoProcessed(appsyncClient: any, mutationInput: CardLogoProcessResultInput) {
    await appsyncClient.mutate({
      mutation: gql`
        mutation PublishCardLogoProcessed($input: CardLogoProcessResultInput) {
          publishCardLogoProcessed(input: $input) {
            entityUuid
            cardLogo {
              id
              colorPreviewUrl
              monochromePreviewUrl
            }
            error {
              code
              cardLogoUuid
            }
          }
        }
      `,
      variables: {
        input: mutationInput,
      },
    });
  }

  private createCardLogoMetadata(logo: CardLogoImage): CardLogo {
    return {
      entityUuid: logo.entityUuid,
      id: logo.logoUuid,
      type: logo.DB_RECORD_TYPE,
      colorPreviewUrl: logo.urlColorPreview,
      monochromePreviewUrl: logo.urlMonoPreview,
      monochromePrintUrl: logo.urlMonoPrintScale,
    } as CardLogo;
  }

  private getEntityAndLogoUuidFromKey(key: string): { entityUuid: string; logoUuid: string } {
    const parts = key.split('/');
    const entityUuid = parts[1];
    const logoUuid = parts[2];
    return { entityUuid, logoUuid };
  }

  private async buildLogoImages(record: { bucket: string; key: string; size: number }): Promise<CardLogoImage> {
    let updatedImage;
    const original = await this.getImageFromS3(record.bucket, record.key);

    if (isHeicHeif(original)) {
      updatedImage = (await convertHeicToPngOrJpeg(original)) as any;
    }
    if (isBmp(original)) {
      updatedImage = (await convertBmpToPng(original)) as any;
    }
    const colorPreview = await this.ereceiptLogo.buildCardLogo(updatedImage ?? original, this.envService.cardLogoSize);
    const monoPreview = await this.printedLogo.buildCardLogo({
      original: updatedImage ?? original,
      options: {
        logoSize: this.envService.cardLogoMonoPrintSize,
        backgroundColor: { r: 0, g: 0, b: 0, alpha: 0 },
        bias: 30,
        invert: true,
      },
    });
    const monoPrintScale = await this.printedLogo.buildCardLogo({
      original: updatedImage ?? original,
      options: {
        logoSize: this.envService.cardLogoMonoPrintSize,
        backgroundColor: { r: 255, g: 255, b: 255, alpha: 1 },
        bias: 30,
      },
    });
    const { entityUuid, logoUuid } = this.getEntityAndLogoUuidFromKey(record.key);
    return new CardLogoImage({
      entityUuid,
      logoUuid,
      colorPreview,
      monoPreview,
      monoPrintScale,
      baseUrl: this.envService.cardLogoBaseUrl,
    });
  }

  private async uploadCardLogo(bucket: string, logo: CardLogoImage): Promise<void> {
    await this.s3.putObject({ Bucket: bucket, Body: logo.colorPreview, Key: logo.fileKeyColorPreview });
    info(`${logo.getColorPreviewName()} was uploaded to ${bucket}`);
    await this.s3.putObject({ Bucket: bucket, Body: logo.monoPreview, Key: logo.fileKeyMonoPreview });
    info(`${logo.getMonoPreviewName()} was uploaded to ${bucket}`);
    await this.s3.putObject({ Bucket: bucket, Body: logo.monoPrintScale, Key: logo.fileKeyMonoPrintScale });
    info(`${logo.getMonoPrintScaleName()} was uploaded to ${bucket}`);
  }
}
