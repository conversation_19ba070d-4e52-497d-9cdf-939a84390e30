import * as utils from '@npco/bff-common/dist/image/imageProcessUtils';
import { S3Client } from '@npco/component-bff-core/dist/aws/s3Client';
import { InvalidRequest } from '@npco/component-bff-core/dist/error';

import axios from 'axios';
import { instance, mock, when } from 'ts-mockito';
import { v4 } from 'uuid';

import { EnvironmentService } from '../config/envService';
import type { DynamodbService } from '../dynamodb/dynamodbService';
import type { UpdateScreensaverInput } from '../images/types';

import { CardLogoUploadService } from './cardLogoUploadService';
import { LogoService } from './logoService';
/* eslint-disable */

jest.mock('@npco/component-bff-core/dist/aws/s3Client', () => {
  const mockS3 = {
    putObject: jest.fn(),
    headObject: jest.fn(),
    getObjectAndTransformToByteArray: jest.fn(),
    getSignedUrl: jest.fn(),
    createPresignedPost: jest.fn(),
    deleteObject: jest.fn(),
  };
  return { S3Client: jest.fn(() => mockS3) };
});

jest.mock('axios');

const s3Event = {
  Records: [
    {
      eventVersion: '2.2',
      eventSource: 'aws:s3',
      awsRegion: 'us-west-2',
      eventTime: 'The time, in ISO-8601 format, for example, 1970-01-01T00:00:00.000Z',
      eventName: 'event-type',
      userIdentity: {
        principalId: 'Amazon-customer-ID-of-the-user-who-caused-the-event',
      },
      requestParameters: {
        sourceIPAddress: 'ip-address-where-request-came-from',
      },
      responseElements: {
        'x-amz-request-id': 'Amazon S3 generated request ID',
        'x-amz-id-2': 'Amazon S3 host that processed the request',
      },
      s3: {
        s3SchemaVersion: '1.0',
        configurationId: 'ID found in the bucket notification configuration',
        bucket: {
          name: 'bucket-name',
          ownerIdentity: {
            principalId: 'Amazon-customer-ID-of-the-bucket-owner',
          },
          arn: 'bucket-ARN',
        },
        object: {
          key: 'receipt/object-key',
          size: 2048,
          eTag: 'object eTag',
          versionId: 'object version if bucket is versioning-enabled, otherwise null',
          sequencer:
            'a string representation of a hexadecimal value used to determine event sequence, only used with PUTs',
        },
      },
      glacierEventData: {
        restoreEventData: {
          lifecycleRestorationExpiryTime:
            'The time, in ISO-8601 format, for example, 1970-01-01T00:00:00.000Z, of Restore Expiry',
          lifecycleRestoreStorageClass: 'Source storage class for restore',
        },
      },
    },
  ],
};
const mockGetSiteDbItem = jest.fn();
const mockGetSiteDbItemOrThrow = jest.fn();

jest.mock('../site/getSiteDbItem', () => ({
  getSiteDbItem: () => mockGetSiteDbItem(),
  getSiteDbItemOrThrow: () => mockGetSiteDbItemOrThrow(),
}));

describe('site logo upload tests', () => {
  let logoService: LogoService;

  const mockEnvironmentService: any = mock(EnvironmentService);
  let mockS3: any;

  beforeEach(() => {
    mockS3 = new S3Client();

    const logoBuilder = { build: jest.fn() } as any;
    logoService = new LogoService(instance(mockEnvironmentService), {} as any, logoBuilder, logoBuilder, {} as any);
    when(mockEnvironmentService.receiptLogoBaseUrl).thenReturn('https://dashboard.myzeller.dev/receipt/assets');

    mockS3.putObject.mockReset();
    mockS3.headObject = jest.fn().mockResolvedValue({
      Metadata: { metadata: undefined },
    });
    mockS3.getObjectAndTransformToByteArray = jest.fn().mockResolvedValue(new TextEncoder().encode('img'));
    mockS3.deleteObject.mockReset();
    (axios as any).patch.mockReset();
    (axios as any).patch.mockResolvedValue(true);
  });

  it('should generate upload url', async () => {
    const url = 'url';
    mockS3.getSignedUrl.mockReturnValueOnce(url);
    await expect(logoService.getReceiptSettingsLogoUploadUrl('entityUuid', 'siteUuid')).resolves.toEqual(url);
  });

  it('should generate screensaver presigned post data', async () => {
    const presignedPostData: any = {
      url: 'https://s3.ap-southeast-2.amazonaws.com/bucket',
      fields: {
        key: 'screensaver/entityUuid/siteUuid/logoUuid',
        'x-amz-meta-metadata': '{"originalFileName":"test.png"}',
        bucket: 'bucket',
        'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
        'X-Amz-Date': '20230730T040909Z',
        'X-Amz-Credential': 'TW9ja0NyZWRlbnRpYWw=',
        'X-Amz-Security-Token': 'TW9ja1Rva2Vu',
        Policy: 'eyJleHBpcmF0aW9uIjoiMjAyMy0wNy0zMFQwNTowOTowOVoifQ==', // base64 encoded {"expiration":"2023-07-30T05:09:09Z"}
        'X-Amz-Signature': 'TW9ja1NpZw==',
      },
    };

    mockS3.createPresignedPost.mockReturnValueOnce(presignedPostData);

    expect(await logoService.getScreensaverLogoUploadDetails('entityUuid', 'siteUuid', 'test.png')).toEqual({
      logoUuid: expect.any(String),
      presignedDetails: {
        url: presignedPostData.url,
        jsonFields: JSON.stringify(presignedPostData.fields),
      },
      expireDate: '2023-07-30T05:09:09Z',
    });
  });

  it('should throw invalid file type error when generate screensaver presigned post data', async () => {
    await expect(
      logoService.getScreensaverLogoUploadDetails('entityUuid', 'siteUuid', 'test.heif'),
    ).rejects.toThrowError(new InvalidRequest('Invalid file type. Only support .png, .jpg, .jpeg file.'));
  });

  it('should ignore s3 upload event when the key prefix has no handler', async () => {
    const catalogS3Event = JSON.parse(JSON.stringify(s3Event));
    const fileKey = 'catalog/entityUuid/siteUuid/logoUuid';
    catalogS3Event.Records[0].s3.object.key = fileKey;

    // act
    await logoService.onLogoUpload(catalogS3Event as any);
    // verify
    expect(mockS3.getObjectAndTransformToByteArray).toHaveBeenCalledTimes(0);
    expect(mockS3.putObject).toHaveBeenCalledTimes(0);
    expect(mockS3.deleteObject).toHaveBeenCalledTimes(0);
  });

  it('should process and upload screensaver image to s3', async () => {
    const screensaverS3Event = JSON.parse(JSON.stringify(s3Event));
    const processedLogoBucket = 'process';
    const uploadLogoBucket = 'bucket-name';
    const fileKey = 'screensaver/entityUuid/siteUuid/logoUuid';
    screensaverS3Event.Records[0].s3.object.key = fileKey;

    when(mockEnvironmentService.screensaverLogoBaseUrl).thenReturn(
      'https://dashboard.myzeller.dev/screensaver/assets/',
    );
    when(mockEnvironmentService.receiptLogoUploadBucket).thenReturn(uploadLogoBucket);
    when(mockEnvironmentService.receiptLogoProcessedBucket).thenReturn(processedLogoBucket);
    // act
    await logoService.onLogoUpload(screensaverS3Event as any);
    // verify
    expect(mockS3.getObjectAndTransformToByteArray).toHaveBeenCalledTimes(1);
    expect(mockS3.getObjectAndTransformToByteArray).toHaveBeenCalledWith({ Bucket: uploadLogoBucket, Key: fileKey });
    expect(mockS3.putObject).toHaveBeenCalledTimes(1);
    expect(mockS3.headObject).toHaveBeenCalledTimes(0);
    expect(mockS3.deleteObject).toHaveBeenCalledTimes(1);
  });

  it('should throw error when onLogoUpload has error', async () => {
    mockS3.getObjectAndTransformToByteArray = jest.fn().mockRejectedValue(new Error('mockError'));

    expect(logoService.onLogoUpload(s3Event as any)).rejects.toThrow('mockError');
    expect(mockS3.deleteObject).toHaveBeenCalledTimes(0);

    const screensaverS3Event = JSON.parse(JSON.stringify(s3Event));
    const fileKey = 'screensaver/entityUuid/siteUuid/logoUuid';
    screensaverS3Event.Records[0].s3.object.key = fileKey;
    expect(logoService.onLogoUpload(screensaverS3Event as any)).rejects.toThrowError('mockError');
    expect(mockS3.deleteObject).toHaveBeenCalledTimes(0);
  });

  it('should upload original and greyscale images to s3', async () => {
    const processedLogoBucket = 'process';
    const uploadLogoBucket = 'bucket-name';
    const fileKey = 'receipt/object-key';
    mockS3.getObjectAndTransformToByteArray = jest.fn().mockResolvedValue(new TextEncoder().encode('img'));
    mockS3.headObject = jest.fn().mockResolvedValue({
      Metadata: { metadata: undefined },
    });
    logoService.getMetaDataFromObject;
    when(mockEnvironmentService.receiptLogoUploadBucket).thenReturn(uploadLogoBucket);
    when(mockEnvironmentService.receiptLogoProcessedBucket).thenReturn(processedLogoBucket);
    when(mockEnvironmentService.printedReceiptLogoSize).thenReturn({ width: 100, height: 200 });

    (axios as any).patch.mockResolvedValue(true);
    mockGetSiteDbItem.mockResolvedValue({
      receipt: {
        logo: 'https://dashboard.myzeller.dev/receipt/assets/uuid-orig.png',
        logoMonochrome: 'https://dashboard.myzeller.dev/receipt/assets/uuid-mono.png',
      },
    });

    // act
    await logoService.onLogoUpload(s3Event as any);
    // verify
    expect(mockS3.getObjectAndTransformToByteArray).toHaveBeenCalledTimes(1);
    expect(mockS3.headObject).toHaveBeenCalledWith({ Bucket: uploadLogoBucket, Key: fileKey });
    expect(mockS3.putObject).toHaveBeenCalledTimes(2);
    expect((axios as any).patch).toHaveBeenCalledTimes(1);
    expect(mockS3.deleteObject).toHaveBeenCalledTimes(3);
  });

  it('should upload original and greyscale images to s3 for .heic images', async () => {
    const processedLogoBucket = 'process';
    const uploadLogoBucket = 'bucket-name';
    const fileKey = 'receipt/object-key';
    const fileNameMetaData = { originalFileName: 'filename-test.heic' };
    const stub = jest.spyOn(utils, 'convertHeicToPngOrJpeg').mockResolvedValueOnce(new ArrayBuffer(10));
    const stub1 = jest.spyOn(utils, 'isHeicHeif').mockReturnValue(true);
    mockS3.getObjectAndTransformToByteArray = jest.fn().mockResolvedValue(new TextEncoder().encode('img'));
    mockS3.headObject = jest.fn().mockResolvedValue({
      Metadata: { metadata: JSON.stringify(fileNameMetaData) },
    });
    when(mockEnvironmentService.receiptLogoUploadBucket).thenReturn(uploadLogoBucket);
    when(mockEnvironmentService.receiptLogoProcessedBucket).thenReturn(processedLogoBucket);
    when(mockEnvironmentService.printedReceiptLogoSize).thenReturn({ width: 100, height: 200 });
    (axios as any).patch.mockResolvedValue(true);
    mockGetSiteDbItem.mockResolvedValue({
      receipt: {
        logo: 'https://dashboard.myzeller.dev/receipt/assets/uuid-orig.png',
        logoMonochrome: 'https://dashboard.myzeller.dev/receipt/assets/uuid-mono.png',
      },
    });
    // act
    await logoService.onLogoUpload(s3Event as any);
    // verify
    expect(mockS3.getObjectAndTransformToByteArray).toHaveBeenCalledTimes(1);
    expect(mockS3.headObject).toHaveBeenCalledWith({ Bucket: uploadLogoBucket, Key: fileKey });
    expect(mockS3.putObject).toHaveBeenCalledTimes(2);
    expect((axios as any).patch).toHaveBeenCalledTimes(1);
    expect(mockS3.deleteObject).toHaveBeenCalledTimes(3);
    expect(stub).toHaveBeenCalled();
    stub.mockRestore();
    stub1.mockRestore();
  });

  it('should upload original and greyscale images to s3 for .heif images', async () => {
    const processedLogoBucket = 'process';
    const uploadLogoBucket = 'bucket-name';
    const fileKey = 'receipt/object-key';
    const fileNameMetaData = { originalFileName: 'filename-test.heif' };
    const stub = jest.spyOn(utils, 'convertHeicToPngOrJpeg').mockResolvedValueOnce(new ArrayBuffer(10));
    const stub1 = jest.spyOn(utils, 'isHeicHeif').mockReturnValue(true);
    mockS3.getObjectAndTransformToByteArray = jest.fn().mockResolvedValue(new TextEncoder().encode('img'));
    mockS3.headObject = jest.fn().mockResolvedValue({
      Metadata: { metadata: JSON.stringify(fileNameMetaData) },
    });
    when(mockEnvironmentService.receiptLogoUploadBucket).thenReturn(uploadLogoBucket);
    when(mockEnvironmentService.receiptLogoProcessedBucket).thenReturn(processedLogoBucket);
    when(mockEnvironmentService.printedReceiptLogoSize).thenReturn({ width: 100, height: 200 });
    (axios as any).patch.mockResolvedValue(true);
    mockGetSiteDbItem.mockResolvedValue({
      receipt: {
        logo: 'https://dashboard.myzeller.dev/receipt/assets/uuid-orig.png',
        logoMonochrome: 'https://dashboard.myzeller.dev/receipt/assets/uuid-mono.png',
      },
    });
    // act
    await logoService.onLogoUpload(s3Event as any);
    // verify
    expect(mockS3.getObjectAndTransformToByteArray).toHaveBeenCalledTimes(1);
    expect(mockS3.headObject).toHaveBeenCalledWith({ Bucket: uploadLogoBucket, Key: fileKey });
    expect(mockS3.putObject).toHaveBeenCalledTimes(2);
    expect((axios as any).patch).toHaveBeenCalledTimes(1);
    expect(mockS3.deleteObject).toHaveBeenCalledTimes(3);
    expect(stub).toHaveBeenCalled();
    stub.mockRestore();
    stub1.mockRestore();
  });

  it('should upload original and greyscale images to s3 for .png images and metadata', async () => {
    const processedLogoBucket = 'process';
    const uploadLogoBucket = 'bucket-name';
    const fileKey = 'receipt/object-key';
    const fileNameMetaData = { originalFileName: 'filename-test.png' };
    const stub = jest.spyOn(utils, 'convertHeicToPngOrJpeg').mockResolvedValueOnce(new ArrayBuffer(10));

    mockS3.getObjectAndTransformToByteArray = jest.fn().mockResolvedValue(new TextEncoder().encode('img'));
    mockS3.headObject = jest.fn().mockResolvedValue({
      Metadata: { metadata: JSON.stringify(fileNameMetaData) },
    });
    when(mockEnvironmentService.receiptLogoUploadBucket).thenReturn(uploadLogoBucket);
    when(mockEnvironmentService.receiptLogoProcessedBucket).thenReturn(processedLogoBucket);
    when(mockEnvironmentService.printedReceiptLogoSize).thenReturn({ width: 100, height: 200 });
    (axios as any).patch.mockResolvedValue(true);
    mockGetSiteDbItem.mockResolvedValue({
      receipt: {
        logo: 'https://dashboard.myzeller.dev/receipt/assets/uuid-orig.png',
        logoMonochrome: 'https://dashboard.myzeller.dev/receipt/assets/uuid-mono.png',
      },
    });
    // act
    await logoService.onLogoUpload(s3Event as any);
    // verify
    expect(mockS3.getObjectAndTransformToByteArray).toHaveBeenCalledTimes(1);
    expect(mockS3.headObject).toHaveBeenCalledWith({ Bucket: uploadLogoBucket, Key: fileKey });
    expect(mockS3.putObject).toHaveBeenCalledTimes(2);
    expect((axios as any).patch).toHaveBeenCalledTimes(1);
    expect(mockS3.deleteObject).toHaveBeenCalledTimes(3);
    expect(stub).not.toHaveBeenCalled();
    stub.mockRestore();
  });

  it('should be able pass through metadata for original file name', async () => {
    const url = 'url';
    const spy = jest.spyOn(logoService, 'getPreSignedUrlForUpload');
    mockS3.getSignedUrl.mockReturnValueOnce(url);

    await expect(
      logoService.getReceiptSettingsLogoUploadUrl('entityUuid', 'siteUuid', 'filename.heic'),
    ).resolves.toEqual(url);
    expect(spy).toHaveBeenCalledWith('bucket-name', expect.stringContaining('entityUuid/siteUuid'), {
      originalFileName: 'filename.heic',
    });
    spy.mockRestore();
  });

  it('should remove original and greyscale images from s3 and update site', async () => {
    const processedLogoBucket = 'process';

    when(mockEnvironmentService.receiptLogoProcessedBucket).thenReturn(processedLogoBucket);
    (axios as any).patch.mockResolvedValue(true);
    mockGetSiteDbItem.mockResolvedValue({
      receipt: {
        logo: 'https://dashboard.myzeller.dev/receipt/assets/uuid-orig.png',
        logoMonochrome: 'https://dashboard.myzeller.dev/receipt/assets/uuid-mono.png',
      },
    });

    // act
    await expect(logoService.removeLogo('', '')).resolves.toEqual(true);
    // verify
    expect(mockS3.deleteObject).toHaveBeenCalledTimes(2);
    expect((axios as any).patch).toHaveBeenCalledTimes(1);
  });

  it('should remove original image from s3 and update site', async () => {
    const processedLogoBucket = 'process';

    when(mockEnvironmentService.receiptLogoProcessedBucket).thenReturn(processedLogoBucket);
    (axios as any).patch.mockResolvedValue(true);
    mockGetSiteDbItem.mockResolvedValue({
      receipt: {
        logo: 'https://dashboard.myzeller.dev/receipt/assets/uuid-orig.png',
      },
    });

    // act
    await expect(logoService.removeLogo('', '')).resolves.toEqual(true);
    // verify
    expect(mockS3.deleteObject).toHaveBeenCalledTimes(1);
    expect((axios as any).patch).toHaveBeenCalledTimes(1);
  });

  it('should remove monochrome image from s3 and update site', async () => {
    const processedLogoBucket = 'process';

    when(mockEnvironmentService.receiptLogoProcessedBucket).thenReturn(processedLogoBucket);
    (axios as any).patch.mockResolvedValue(true);
    mockGetSiteDbItem.mockResolvedValue({
      receipt: {
        logoMonochrome: 'https://dashboard.myzeller.dev/receipt/assets/uuid-mono.png',
      },
    });
    // act
    await expect(logoService.removeLogo('', '')).resolves.toEqual(true);
    // verify
    expect(mockS3.deleteObject).toHaveBeenCalledTimes(1);
    expect((axios as any).patch).toHaveBeenCalledTimes(1);
  });

  it('should not update site or call s3 if receipt urls already empty', async () => {
    const processedLogoBucket = 'process';

    when(mockEnvironmentService.receiptLogoProcessedBucket).thenReturn(processedLogoBucket);

    mockGetSiteDbItemOrThrow.mockResolvedValue({ receipt: {} } as any);
    (axios as any).patch.mockResolvedValue(true);
    mockGetSiteDbItem.mockResolvedValue({
      receipt: {},
    });
    // act
    await expect(logoService.removeLogo('', '')).resolves.toEqual(true);
    // verify
    expect(mockS3.deleteObject).toHaveBeenCalledTimes(0);
    expect((axios as any).patch).toHaveBeenCalledTimes(0);
  });

  it('should return false when removing logo fails', async () => {
    const processedLogoBucket = 'process';

    mockS3.headObject = jest.fn().mockResolvedValue({ message: 'error' });
    when(mockEnvironmentService.receiptLogoProcessedBucket).thenReturn(processedLogoBucket);
    (axios as any).patch.mockRejectedValue(false);
    mockGetSiteDbItem.mockResolvedValue({
      receipt: {
        logo: 'https://dashboard.myzeller.dev/receipt/assets/uuid-orig.png',
        logoMonochrome: 'https://dashboard.myzeller.dev/receipt/assets/uuid-mono.png',
      },
    });
    await expect(logoService.removeLogo('', '')).resolves.toEqual(false);
  });

  describe('updateSiteScreensaver', () => {
    const entityUuid = 'entityUuid';
    const screensaverInput: UpdateScreensaverInput = {
      siteUuid: 'siteUuid',
      primaryColour: 'colour',
      primaryLogoUuid: 'logoUuid',
      logoUuids: ['logoUuid'],
      customColours: ['colour'],
    };
    const existingScreensaver = {
      logos: [
        {
          logoUuid: 'logoUuid1',
          url: 'https://dashboard.myzeller.dev/screensaver/assets/logoUrl1-screensaver.png',
        },
      ],
      customColours: ['colour1'],
    };

    beforeEach(() => {
      mockS3.deleteObject.mockClear();

      when(mockEnvironmentService.screensaverLogoBaseUrl).thenReturn(
        'https://dashboard.myzeller.dev/screensaver/assets/',
      );
    });

    it('should handle updateSiteScreensaver', async () => {
      mockGetSiteDbItemOrThrow.mockResolvedValue({});

      const result = await logoService.updateSiteScreensaver(entityUuid, screensaverInput);

      expect(result).toBeTruthy();
      expect((axios as any).patch).toBeCalledTimes(1);
      expect((axios as any).patch).toHaveBeenLastCalledWith(
        'null/v1null/siteUuid',
        JSON.stringify({
          entityUuid: 'entityUuid',
          siteUuid: screensaverInput.siteUuid,
          screensaver: {
            primaryColour: screensaverInput.primaryColour,
            primaryLogoUuid: screensaverInput.primaryLogoUuid,
            primaryLogoUrl: 'https://dashboard.myzeller.dev/screensaver/assets/logoUuid-screensaver.png',
            logos: [
              {
                logoUuid: 'logoUuid',
                url: 'https://dashboard.myzeller.dev/screensaver/assets/logoUuid-screensaver.png',
              },
            ],
            customColours: ['colour'],
          },
        }),
        { timeout: 10000, headers: { 'Content-Type': 'application/json' }, params: undefined },
      );
      expect(mockS3.deleteObject).toBeCalledTimes(0);
    });

    it('should handle duplicate logos and customColours when updateSiteScreensaver', async () => {
      const screensaverInput1 = {
        ...screensaverInput,
        logosUuids: ['logoUuid', 'logoUuid'],
        customColours: ['colour', 'colour'],
      };
      mockGetSiteDbItemOrThrow.mockResolvedValue({});

      await logoService.updateSiteScreensaver(entityUuid, screensaverInput1);

      expect((axios as any).patch).toBeCalledTimes(1);
      expect((axios as any).patch).toHaveBeenLastCalledWith(
        'null/v1null/siteUuid',
        JSON.stringify({
          entityUuid: 'entityUuid',
          siteUuid: screensaverInput1.siteUuid,
          screensaver: {
            primaryColour: screensaverInput1.primaryColour,
            primaryLogoUuid: screensaverInput1.primaryLogoUuid,
            primaryLogoUrl: 'https://dashboard.myzeller.dev/screensaver/assets/logoUuid-screensaver.png',
            logos: [
              {
                logoUuid: 'logoUuid',
                url: 'https://dashboard.myzeller.dev/screensaver/assets/logoUuid-screensaver.png',
              },
            ],
            customColours: ['colour'],
          },
        }),
        { timeout: 10000, headers: { 'Content-Type': 'application/json' }, params: undefined },
      );
      expect(mockS3.deleteObject).toBeCalledTimes(0);
    });

    it('should handle removed logos when updateSiteScreensaver', async () => {
      mockGetSiteDbItemOrThrow.mockResolvedValue({ screensaver: existingScreensaver });

      await logoService.updateSiteScreensaver(entityUuid, {
        siteUuid: 'siteUuid',
        primaryColour: '',
        primaryLogoUuid: '',
        logoUuids: [],
        customColours: [],
      });

      expect((axios as any).patch).toBeCalledTimes(1);
      expect((axios as any).patch).toHaveBeenLastCalledWith(
        'null/v1null/siteUuid',
        JSON.stringify({
          entityUuid: 'entityUuid',
          siteUuid: screensaverInput.siteUuid,
          screensaver: { primaryColour: '', primaryLogoUuid: '', primaryLogoUrl: '', logos: [], customColours: [] },
        }),
        { timeout: 10000, headers: { 'Content-Type': 'application/json' }, params: undefined },
      );
      expect(mockS3.deleteObject).toBeCalledTimes(1);
      expect(mockS3.deleteObject).toHaveBeenLastCalledWith({
        Bucket: 'process',
        Key: 'screensaver/assets/logoUrl1-screensaver.png',
      });
    });
  });

  describe('handleCardLogoUploaded', () => {
    const cardS3Event = JSON.parse(JSON.stringify(s3Event));
    const cardLogoBucket = 'bucket-name';
    const processedCardLogoBucket = 'processed';
    const entityUuid = v4();
    const logoUuid = v4();
    const fileKey = `cardlogo/${entityUuid}/${logoUuid}/object-key`;

    let dbServiceInstance: DynamodbService;
    let logoService: LogoService;
    let cardLogoUploadService: CardLogoUploadService;
    let mockS3: any;
    let mockEnvironmentService: any;
    let appsyncClientMock: any;

    beforeEach(() => {
      mockEnvironmentService = {
        cardLogoBaseUrl: 'https://dashboard.myzeller.dev/cardlogo/assets/',
        componentTableName: 'mp-api-dynamodb-Entities',
        cardLogoUploadBucket: cardLogoBucket,
        cardLogoProcessedBucket: processedCardLogoBucket,
      };

      dbServiceInstance = {
        put: jest.fn().mockResolvedValue(undefined),
        queryIdByType: jest.fn().mockResolvedValue({
          Items: [
            {
              id: 'logo-id',
              colorPreviewUrl: 'color-url',
              monochromePreviewUrl: 'mono-url',
            },
          ],
        }),
      } as unknown as DynamodbService;

      mockS3 = new S3Client();

      const logoBuilder = {
        buildCardLogo: jest.fn().mockResolvedValue(new Uint8Array([4, 5, 6])),
      } as any;

      appsyncClientMock = { mutate: jest.fn().mockResolvedValue({}) };
      jest.spyOn(require('../appsync/appSyncClient'), 'AppSyncClient').mockImplementation(() => ({
        getAppSyncClient: () => Promise.resolve(appsyncClientMock),
      }));

      cardLogoUploadService = new CardLogoUploadService(
        mockEnvironmentService,
        dbServiceInstance,
        logoBuilder,
        logoBuilder,
      );

      logoService = new LogoService(
        mockEnvironmentService,
        dbServiceInstance,
        logoBuilder,
        logoBuilder,
        cardLogoUploadService,
      );

      cardS3Event.Records[0].s3.object.key = fileKey;

      mockS3.getObjectAndTransformToByteArray = jest.fn().mockResolvedValue(new TextEncoder().encode('img'));
      mockS3.putObject.mockReset();
      mockS3.deleteObject.mockReset();
      mockS3.headObject = jest.fn().mockResolvedValue({
        Metadata: { metadata: undefined },
      });
    });

    it('should process and upload card logo image to s3 and publish mutation on success', async () => {
      await logoService.onLogoUpload(cardS3Event as any);

      expect(mockS3.getObjectAndTransformToByteArray).toHaveBeenCalledTimes(1);
      expect(mockS3.putObject).toHaveBeenCalledTimes(3);

      expect(appsyncClientMock.mutate).toHaveBeenCalledWith(
        expect.objectContaining({
          variables: {
            input: {
              entityUuid,
              cardLogo: {
                id: 'logo-id',
                colorPreviewUrl: 'color-url',
                monochromePreviewUrl: 'mono-url',
              },
              error: null,
            },
          },
        }),
      );
    });

    it('should publish mutation with error when handleCardLogoUploaded fails', async () => {
      jest.spyOn(cardLogoUploadService as any, 'buildLogoImages').mockRejectedValueOnce(new Error('mockCardError'));

      await expect(logoService.onLogoUpload(cardS3Event as any)).rejects.toThrow('mockCardError');

      expect(appsyncClientMock.mutate).toHaveBeenCalledWith(
        expect.objectContaining({
          variables: {
            input: {
              entityUuid,
              cardLogo: null,
              error: {
                code: 'mockCardError',
                cardLogoUuid: expect.any(String),
              },
            },
          },
        }),
      );
    });

    it('should upload card logo image to s3 for .heic images and publish mutation', async () => {
      const stub1 = jest.spyOn(utils, 'isHeicHeif').mockReturnValue(true);

      const stub = jest.spyOn(utils, 'convertHeicToPngOrJpeg').mockResolvedValueOnce(new ArrayBuffer(10));

      await logoService.onLogoUpload(cardS3Event as any);

      expect(mockS3.getObjectAndTransformToByteArray).toHaveBeenCalledTimes(1);
      expect(mockS3.putObject).toHaveBeenCalledTimes(3);
      expect(dbServiceInstance.put).toHaveBeenCalledWith({
        TableName: 'mp-api-dynamodb-Entities',
        Item: expect.objectContaining({
          entityUuid,
          id: logoUuid,
          type: 'cardlogo.core',
          colorPreviewUrl: expect.any(String),
          monochromePreviewUrl: expect.any(String),
          monochromePrintUrl: expect.any(String),
        }),
      });
      expect(appsyncClientMock.mutate).toHaveBeenCalledTimes(1);
      expect(stub).toHaveBeenCalled();
      stub.mockRestore();
      stub1.mockRestore();
    });


    it('should upload card logo image to s3 for .bmp images and publish mutation', async () => {
      const stub1 = jest.spyOn(utils, 'isBmp').mockReturnValue(true);

      const stub = jest.spyOn(utils, 'convertBmpToPng').mockResolvedValueOnce(new Buffer(10));

      await logoService.onLogoUpload(cardS3Event as any);

      expect(mockS3.getObjectAndTransformToByteArray).toHaveBeenCalledTimes(1);
      expect(mockS3.putObject).toHaveBeenCalledTimes(3);
      expect(dbServiceInstance.put).toHaveBeenCalledWith({
        TableName: 'mp-api-dynamodb-Entities',
        Item: expect.objectContaining({
          entityUuid,
          id: logoUuid,
          type: 'cardlogo.core',
          colorPreviewUrl: expect.any(String),
          monochromePreviewUrl: expect.any(String),
          monochromePrintUrl: expect.any(String),
        }),
      });
      expect(appsyncClientMock.mutate).toHaveBeenCalledTimes(1);
      expect(stub).toHaveBeenCalled();
      stub.mockRestore();
      stub1.mockRestore();
    });


    it('should throw error if fetching cardLogo returns no items', async () => {
      dbServiceInstance.queryIdByType = jest.fn().mockResolvedValue({ Items: [] });
      await expect(logoService.onLogoUpload(cardS3Event as any)).rejects.toThrow(
        /CardLogo with entityUuid: .* and id: .* not found/,
      );
    });
  });
});
