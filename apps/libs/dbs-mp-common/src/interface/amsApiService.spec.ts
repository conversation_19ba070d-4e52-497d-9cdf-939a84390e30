import { MutationAttributionService } from '@npco/component-bff-core/dist/attribution/mutationAttributionService';
import { InvalidRequest } from '@npco/component-bff-core/dist/error/graphQlError';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import {
  EntityCategories,
  MutationAttributionPlatform,
  MutationAttributionTokenGrant,
  MutationAttributionUserRole,
} from '@npco/component-dto-core';
import { KycCheckpoint } from '@npco/component-dto-customer';

import xray from 'aws-xray-sdk-core';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

import { AmsApiService } from './amsApiService';

jest.mock('axios');
jest.mock('aws-xray-sdk-core');

const dto = {
  deviceUuid: uuidv4(),
  name: 'name',
};

describe('ams api service test suite', () => {
  let apiService: AmsApiService;

  const attributionService = MutationAttributionService.getInstance();

  const envService: any = {
    amsEndpointVersion: 'v1',
    amsDeviceEndpointPath: '/device',
    amsEndpoint: 'amsApi',
  };

  beforeEach(() => {
    attributionService.mutationAttribution = undefined;
    apiService = new AmsApiService(envService as any, '/device', 'device');
    jest.resetAllMocks();
    (axios as any).get.mockResolvedValue({ status: 200, data: { test: 'data' } });
    (axios as any).post.mockResolvedValue({ status: 200 });
    (axios as any).put.mockResolvedValue({ status: 200 });
    (axios as any).patch.mockResolvedValue({ status: 200 });
    (axios as any).delete.mockResolvedValue({ status: 200 });
    (xray as any).captureHTTPsGlobal.mockReset();
  });

  it('should be able to send xray to ams api', async () => {
    const isInLambda = jest.fn().mockReturnValue(true);
    // eslint-disable-next-line no-new
    new AmsApiService({ isInLambda } as any, '/device', 'device');
    expect((xray as any).captureHTTPsGlobal).toHaveBeenCalledTimes(2);
  });

  it('should handle get api request', async () => {
    await expect(apiService.get(dto.deviceUuid)).resolves.toEqual({ test: 'data' });
    expect((axios as any).get.mock.calls[0][1].headers).toEqual({ 'Content-Type': 'application/json' });
  });

  it('should handle get api request with attribution headers', async () => {
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    await expect(apiService.get(dto.deviceUuid)).resolves.toEqual({ test: 'data' });
    expect((axios as any).get.mock.calls[0][1].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should handle get api request with path', async () => {
    await expect(apiService.get(dto.deviceUuid, '/path')).resolves.toEqual({ test: 'data' });
  });

  it('should not get record on ams error', async () => {
    (axios as any).get.mockResolvedValue({ status: 400 });
    await apiService.get(dto.deviceUuid).catch((err) => {
      expect(err).toEqual(Error(`Failed to get "device" with id: "${dto.deviceUuid}"`));
    });
  });

  it('should not get record on ams failure', async () => {
    (axios as any).get.mockRejectedValue(Error('Failed to get device.'));
    await apiService.get(dto.deviceUuid).catch((err) => {
      expect(err).toEqual(Error(`Failed to get "device" with id: "${dto.deviceUuid}"`));
    });
  });

  it('should handle update api request', async () => {
    await expect(apiService.update(dto.deviceUuid, dto as any)).resolves.toBe(true);
    expect((axios as any).patch.mock.calls[0][2].headers).toEqual({ 'Content-Type': 'application/json' });
  });

  it('should handle update api request with attribution headers', async () => {
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    await expect(apiService.update(dto.deviceUuid, dto as any)).resolves.toBe(true);
    expect((axios as any).patch.mock.calls[0][2].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should not update record on ams error', async () => {
    (axios as any).patch.mockRejectedValue(Error('Failed to update device.'));
    await apiService.update(dto.deviceUuid, dto as any).catch((err) => {
      expect(err).toEqual(Error(`Failed to update "device" with id: "${dto.deviceUuid}"`));
    });
  });

  it('should be able to create', async () => {
    const result = await apiService.create(dto.deviceUuid, dto as any);
    expect(result.id).toEqual(dto.deviceUuid);
    expect((axios as any).post.mock.calls[0][2].headers).toEqual({ 'Content-Type': 'application/json' });
  });

  it('should handle create api request with attribution headers', async () => {
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    const result = await apiService.create(dto.deviceUuid, dto as any);
    expect(result.id).toEqual(dto.deviceUuid);
    expect((axios as any).post.mock.calls[0][2].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should not create on ams failure', async () => {
    (axios as any).post.mockRejectedValue(Error('Failed to create device.'));
    await apiService.create(dto.deviceUuid, dto as any).catch((err) => {
      expect(err).toEqual(Error(`Failed to create "device"`));
    });
  });

  it('should be able to delete', async () => {
    expect(await apiService.delete(dto.deviceUuid)).toEqual(true);
    expect((axios as any).delete.mock.calls[0][1].headers).toEqual({ 'Content-Type': 'application/json' });
  });

  it('should handle delete api request with attribution headers', async () => {
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    await expect(apiService.delete(dto.deviceUuid, dto as any)).resolves.toBe(true);
    expect((axios as any).delete.mock.calls[0][1].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should not delete record on ams error', async () => {
    (axios as any).delete.mockResolvedValue({ status: 400 });
    await apiService.get(dto.deviceUuid).catch((err) => {
      expect(err).toEqual(Error('Failed to delete device.'));
    });
  });

  it('should not delete on ams failure', async () => {
    (axios as any).delete.mockRejectedValue(Error('Failed to delete device.'));
    await apiService.delete(dto.deviceUuid).catch((err) => {
      expect(err).toEqual(Error(`Failed to delete "device" with id: "${dto.deviceUuid}"`));
    });
  });

  it('should be able to assign', async () => {
    expect(await apiService.assign('deviceUuid', 'siteUuid', 'site')).toEqual(true);
    expect((axios as any).post.mock.calls[0][2].headers).toEqual({ 'Content-Type': 'application/json' });
  });

  it('should handle assign api request with attribution headers', async () => {
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    expect(await apiService.assign('deviceUuid', 'siteUuid', 'site')).toEqual(true);
    expect((axios as any).post.mock.calls[0][2].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should be able to assign with body', async () => {
    expect(await apiService.assign('deviceUuid', 'siteUuid', 'site', { data: 'some data' })).toEqual(true);
    expect(axios.post).toHaveBeenLastCalledWith(
      'amsApi/v1/device/deviceUuid/site/siteUuid',
      JSON.stringify({ data: 'some data' }),
      { headers: { 'Content-Type': 'application/json' }, params: undefined, timeout: 10000 },
    );
  });

  it('should be able to assign with headers', async () => {
    expect(
      await apiService.assign(
        'deviceUuid',
        'siteUuid',
        'site/type',
        { data: 'some data' },
        { 'X-ZELLER-ENTITY-UUID': 'entityUuid' },
      ),
    ).toEqual(true);
    expect(axios.post).toHaveBeenLastCalledWith(
      'amsApi/v1/device/deviceUuid/site/type/siteUuid',
      JSON.stringify({ data: 'some data' }),
      {
        headers: { 'Content-Type': 'application/json', 'X-ZELLER-ENTITY-UUID': 'entityUuid' },
        params: undefined,
        timeout: 10000,
      },
    );
  });

  it('should not assign on ams failure', async () => {
    (axios as any).post.mockResolvedValue({ status: 400 });
    await expect(apiService.assign('deviceUuid', 'siteUuid', 'site')).rejects.toThrowError(
      'Failed to assign device deviceUuid to site siteUuid',
    );
  });

  it('should handle error assign on ams failure', async () => {
    (axios as any).post.mockRejectedValue(Error('Failed to assign device to site.'));
    await apiService.assign('deviceUuid', 'siteUuid', 'site').catch((err) => {
      expect(err.message).toEqual('Failed to assign device deviceUuid to site siteUuid');
    });
  });

  it('should be able to unassign', async () => {
    expect(await apiService.unassign('deviceUuid', 'siteUuid', 'site')).toEqual(true);
    expect(axios.delete).toHaveBeenLastCalledWith('amsApi/v1/device/deviceUuid/site/siteUuid', {
      headers: { 'Content-Type': 'application/json' },
      params: undefined,
      timeout: 10000,
    });
  });

  it('should handle unassign api request with attribution headers', async () => {
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    expect(await apiService.unassign('deviceUuid', 'siteUuid', 'site')).toEqual(true);
    expect((axios as any).delete.mock.calls[0][1].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should be able to unassign with headers', async () => {
    expect(
      await apiService.unassign('deviceUuid', 'siteUuid', 'site/type', { 'X-ZELLER-ENTITY-UUID': 'entityUuid' }),
    ).toEqual(true);
    expect(axios.delete).toHaveBeenLastCalledWith('amsApi/v1/device/deviceUuid/site/type/siteUuid', {
      headers: { 'Content-Type': 'application/json', 'X-ZELLER-ENTITY-UUID': 'entityUuid' },
      params: undefined,
      timeout: 10000,
    });
  });

  it('should not unassign on ams failure', async () => {
    (axios as any).delete.mockResolvedValue({ status: 400 });
    await expect(apiService.unassign('deviceUuid', 'siteUuid', 'site')).rejects.toThrowError(
      'Failed to unassign device deviceUuid from site siteUuid',
    );
  });

  it('should handle error unassign on ams failure', async () => {
    (axios as any).delete.mockRejectedValue(Error('Failed to unassign device to site.'));
    await apiService.unassign('deviceUuid', 'siteUuid', 'site').catch((err) => {
      expect(err).toEqual(Error('Failed to unassign device deviceUuid from site siteUuid'));
    });
  });

  it('should handle update device info api request', async () => {
    await expect(apiService.updateDeviceInfo(dto.deviceUuid, dto as any)).resolves.toBe(true);
    expect((axios as any).patch.mock.calls[0][2].headers).toEqual({ 'Content-Type': 'application/json' });
  });

  it('should handle update device api request with attribution headers', async () => {
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    await expect(apiService.updateDeviceInfo(dto.deviceUuid, dto as any)).resolves.toBe(true);
    expect((axios as any).patch.mock.calls[0][2].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should not update device info record on ams error', async () => {
    (axios as any).patch.mockResolvedValue({ status: 400 });
    await expect(apiService.updateDeviceInfo(dto.deviceUuid, dto as any)).rejects.toThrowError(
      `Device info update failed ${dto.deviceUuid}`,
    );
  });

  it('should handle error updateDeviceInfo on ams failure', async () => {
    (axios as any).patch.mockRejectedValue(Error('Failed to unassign device to site.'));
    await apiService.updateDeviceInfo(dto.deviceUuid, dto as any).catch((err) => {
      expect(err.errorMessage).toEqual(`Device info update failed ${dto.deviceUuid}`);
    });
  });

  it('should handle optOutCardholder api request', async () => {
    await expect(apiService.optOutCardholder(uuidv4(), uuidv4())).resolves.toBe(undefined);
    expect((axios as any).post.mock.calls[0][2].headers).toEqual({ 'Content-Type': 'application/json' });
  });

  it('should handle optOutCardholder api request with attribution headers', async () => {
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    await expect(apiService.optOutCardholder(uuidv4(), uuidv4())).resolves.toBe(undefined);
    expect((axios as any).post.mock.calls[0][2].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should handle optOutCardholder ams error', async () => {
    (axios as any).post.mockRejectedValue({ status: 400 });
    const uuid = uuidv4();
    await expect(apiService.optOutCardholder(uuid, uuidv4())).rejects.toThrowError('Failed complete opt-out.');
  });

  it('should handle create transaction notes api request', async () => {
    await expect(apiService.createTransactionNotes(uuidv4(), uuidv4())).resolves.toBe(true);
    expect((axios as any).post.mock.calls[0][2].headers).toEqual({ 'Content-Type': 'application/json' });
  });

  it('should handle create transaction api request with attribution headers', async () => {
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    await expect(apiService.createTransactionNotes(uuidv4(), uuidv4())).resolves.toBe(true);
    expect((axios as any).post.mock.calls[0][2].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should not create transaction notes record on ams error', async () => {
    (axios as any).post.mockResolvedValue({ status: 400 });
    const uuid = uuidv4();
    await expect(apiService.createTransactionNotes(uuid, uuidv4())).rejects.toThrowError(
      `Transaction notes create failed ${uuid}`,
    );
  });

  it('should handle error create transaction notes on ams failure', async () => {
    (axios as any).post.mockRejectedValue(Error('Failed to unassign device to site.'));
    const uuid = uuidv4();
    await apiService.createTransactionNotes(uuid, uuidv4()).catch((err) => {
      expect(err.errorMessage).toEqual(`Transaction notes create failed ${uuid}`);
    });
  });

  it('should handle update transaction notes api request', async () => {
    await expect(apiService.updateTransactionNotes(uuidv4(), uuidv4())).resolves.toBe(true);
    expect((axios as any).patch.mock.calls[0][2].headers).toEqual({ 'Content-Type': 'application/json' });
  });

  it('should handle update transaction api request with attribution headers', async () => {
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    await expect(apiService.updateTransactionNotes(uuidv4(), uuidv4())).resolves.toBe(true);
    expect((axios as any).patch.mock.calls[0][2].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should not update transaction notes record on ams error', async () => {
    (axios as any).patch.mockResolvedValue({ status: 400 });
    const uuid = uuidv4();
    await expect(apiService.updateTransactionNotes(uuid, uuidv4())).rejects.toThrowError(
      `Transaction notes update failed ${uuid}`,
    );
  });

  it('should handle error update transaction notes on ams failure', async () => {
    const uuid = uuidv4();
    (axios as any).patch.mockRejectedValue(Error());
    await apiService.updateTransactionNotes(uuid, uuidv4()).catch((err) => {
      expect(err.errorMessage).toEqual(`Transaction notes update failed ${uuid}`);
    });
  });

  it('should handle create transaction image request', async () => {
    const file = {
      fileName: '',
      fileUuid: '',
      imageUrl: '',
    };
    await expect(apiService.createTransactionImage(uuidv4(), file)).resolves.toBe(true);
    expect((axios as any).post.mock.calls[0][2].headers).toEqual({ 'Content-Type': 'application/json' });
  });

  it('should handle create transaction image request with attribution headers', async () => {
    const file = {
      fileName: '',
      fileUuid: '',
      imageUrl: '',
    };
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    await expect(apiService.createTransactionImage(uuidv4(), file)).resolves.toBe(true);
    expect((axios as any).post.mock.calls[0][2].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should not create transaction image record on ams error', async () => {
    const file = {
      fileName: '',
      fileUuid: '',
      imageUrl: '',
    };
    (axios as any).post.mockResolvedValue({ status: 400 });
    const uuid = uuidv4();
    await expect(apiService.createTransactionImage(uuid, file)).resolves.toBe(false);
  });

  it('should handle remove transaction image request', async () => {
    const fileUuid = uuidv4();
    await expect(apiService.removeTransactionImage(uuidv4(), fileUuid)).resolves.toBe(true);
    expect((axios as any).delete.mock.calls[0][1].headers).toEqual({ 'Content-Type': 'application/json' });
    expect((axios as any).delete.mock.calls[0][1].data).toEqual({ fileUuid });
  });

  it('should handle remove transaction image request with attribution headers', async () => {
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    await expect(apiService.removeTransactionImage(uuidv4(), uuidv4())).resolves.toBe(true);
    expect((axios as any).delete.mock.calls[0][1].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should not delete transaction image record on ams error', async () => {
    (axios as any).delete.mockResolvedValue({ status: 400 });
    const uuid = uuidv4();
    await expect(apiService.removeTransactionImage(uuid, uuidv4())).rejects.toThrowError(
      'Failed remove transaction image.',
    );
  });

  it('should handle delete transaction notes api request', async () => {
    await expect(apiService.deleteTransactionNotes(uuidv4())).resolves.toBe(true);
    expect((axios as any).delete.mock.calls[0][1].headers).toEqual({ 'Content-Type': 'application/json' });
  });

  it('should handle delete transaction api request with attribution headers', async () => {
    const sessionId = uuidv4();
    const userId = uuidv4();
    const reason = uuidv4();
    const createdTimestamp = new Date().getTime();
    attributionService.mutationAttribution = {
      userIdentifier: userId,
      tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
      userRole: MutationAttributionUserRole.ZELLER,
      platform: MutationAttributionPlatform.ADMIN,
      createdTimestamp,
      sessionKey: sessionId,
      reason,
    };
    await expect(apiService.deleteTransactionNotes(uuidv4())).resolves.toBe(true);
    expect((axios as any).delete.mock.calls[0][1].headers).toEqual({
      'Content-Type': 'application/json',
      'x-zeller-platform': 'ADMIN',
      'x-zeller-session-id': sessionId,
      'x-zeller-token-grant': 'ATTESTATION',
      'x-zeller-user-identifier': userId,
      'x-zeller-user-role': 'ZELLER',
      'x-zeller-mutation-created-timestamp': createdTimestamp,
      'x-zeller-mutation-reason': reason,
    });
  });

  it('should not delete transaction notes record on ams error', async () => {
    (axios as any).delete.mockResolvedValue({ status: 400 });
    const uuid = uuidv4();
    await expect(apiService.deleteTransactionNotes(uuid)).rejects.toThrowError(
      `Transaction notes deleted failed ${uuid}`,
    );
  });

  it('should handle error delete transaction notes on ams failure', async () => {
    (axios as any).delete.mockRejectedValue(Error());
    const uuid = uuidv4();
    await apiService.deleteTransactionNotes(uuid).catch((err) => {
      expect(err.errorMessage).toEqual(`Transaction notes deleted failed ${uuid}`);
    });
  });

  describe('post test suite', () => {
    it('should handle post api request', async () => {
      await expect(
        apiService.post('some url', {} as any, {
          aggregateId: 'aggregateUuid',
          errorMessage: 'error message',
        }),
      ).resolves.toEqual(undefined);
      expect((axios as any).post.mock.calls[0][2].headers).toEqual({ 'Content-Type': 'application/json' });
    });

    it('should handle post api request with attribution headers', async () => {
      const sessionId = uuidv4();
      const userId = uuidv4();
      const reason = uuidv4();
      const createdTimestamp = new Date().getTime();
      attributionService.mutationAttribution = {
        userIdentifier: userId,
        tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
        userRole: MutationAttributionUserRole.ZELLER,
        platform: MutationAttributionPlatform.ADMIN,
        createdTimestamp,
        sessionKey: sessionId,
        reason,
      };
      await expect(
        apiService.post('some url', {} as any, {
          aggregateId: 'aggregateUuid',
          errorMessage: 'error message',
        }),
      ).resolves.toEqual(undefined);
      expect((axios as any).post.mock.calls[0][2].headers).toEqual({
        'Content-Type': 'application/json',
        'x-zeller-platform': 'ADMIN',
        'x-zeller-session-id': sessionId,
        'x-zeller-token-grant': 'ATTESTATION',
        'x-zeller-user-identifier': userId,
        'x-zeller-user-role': 'ZELLER',
        'x-zeller-mutation-created-timestamp': createdTimestamp,
        'x-zeller-mutation-reason': reason,
      });
    });

    it('should not get record on api error', async () => {
      (axios as any).post.mockResolvedValue({ status: 400, data: { errorMessage: 'Ams error' } });
      await apiService
        .post('some url', {} as any, {
          aggregateId: 'aggregateUuid',
          errorMessage: 'error message',
        })
        .catch((err) => {
          expect(err.message).toEqual('Ams error');
          expect(err.errorType).toBe('INVALID_REQUEST');
        });
    });
  });

  describe('patch test suite', () => {
    it('should handle patch api request', async () => {
      await expect(
        apiService.patch(
          'some url',
          {} as any,
          {
            aggregateId: 'aggregateUuid',
            errorMessage: 'error message',
          },
          {},
        ),
      ).resolves.toEqual(undefined);
      expect((axios as any).patch.mock.calls[0][2].headers).toEqual({ 'Content-Type': 'application/json' });
    });

    it('should handle patch api request with attribution headers', async () => {
      const sessionId = uuidv4();
      const userId = uuidv4();
      const reason = uuidv4();
      const createdTimestamp = new Date().getTime();
      attributionService.mutationAttribution = {
        userIdentifier: userId,
        tokenGrant: MutationAttributionTokenGrant.ATTESTATION,
        userRole: MutationAttributionUserRole.ZELLER,
        platform: MutationAttributionPlatform.ADMIN,
        createdTimestamp,
        sessionKey: sessionId,
        reason,
      };
      await expect(
        apiService.patch(
          'some url',
          {} as any,
          {
            aggregateId: 'aggregateUuid',
            errorMessage: 'error message',
          },
          { headerTest: 'headerTest' },
        ),
      ).resolves.toEqual(undefined);
      expect((axios as any).patch.mock.calls[0][2].headers).toEqual({
        'Content-Type': 'application/json',
        'x-zeller-platform': 'ADMIN',
        'x-zeller-session-id': sessionId,
        'x-zeller-token-grant': 'ATTESTATION',
        'x-zeller-user-identifier': userId,
        'x-zeller-user-role': 'ZELLER',
        'x-zeller-mutation-created-timestamp': createdTimestamp,
        'x-zeller-mutation-reason': reason,
        headerTest: 'headerTest',
      });
    });

    it('should not get record on api error', async () => {
      (axios as any).patch.mockResolvedValue({ status: 400, data: { errorMessage: 'Ams error' } });
      await expect(
        apiService.patch(
          'some url',
          {} as any,
          {
            aggregateId: 'aggregateUuid',
            errorMessage: 'error message',
          },
          {},
        ),
      ).rejects.toMatchObject({ message: 'Ams error', errorType: 'INVALID_REQUEST' });
    });
  });

  it('should update primary account holder', async () => {
    const customerUuid = uuidv4();
    const entityUuid = uuidv4();

    await expect(apiService.updatePAH(customerUuid, entityUuid)).resolves.toBe(true);
  });

  it('should not update primary account holder on api error', async () => {
    const customerUuid = uuidv4();
    const entityUuid = uuidv4();
    (axios as any).put.mockRejectedValue(Error());

    await apiService.updatePAH(customerUuid, entityUuid).catch((err) => {
      expect(err).toEqual(Error('Failed to set PAH'));
    });
  });

  it('should be able to unlink customer api', async () => {
    (axios as any).delete.mockResolvedValue({ status: 200 });
    const ret = await apiService.unlinkCustomer(uuidv4(), uuidv4());
    expect(ret).toBeTruthy();
  });

  it('should be able to catch 400 errors from unlink customer api', async () => {
    (axios as any).delete.mockResolvedValue({ status: 400 });
    await expect(apiService.unlinkCustomer(uuidv4(), uuidv4())).rejects.toThrow(Error);
  });

  it('should be able to catch errors from unlink customer api', async () => {
    (axios as any).delete.mockRejectedValue(new Error());
    await expect(apiService.unlinkCustomer(uuidv4(), uuidv4())).rejects.toThrow(Error);
  });

  it('should be able to use v2 endpoint when apiVersion is v2 and domicile is passed', async () => {
    const environmentService = {
      amsEndpointVersion: 'v2', // api version is v2
      amsDeviceEndpointPath: '/device',
      amsEndpoint: 'amsApi',
    } as any;

    const { amsEndpoint, amsEndpointVersion, amsDeviceEndpointPath } = environmentService;

    apiService = new AmsApiService(environmentService, '/device', 'device');
    await expect(
      apiService.post(
        'some-url',
        {} as any,
        {
          aggregateId: 'aggregateUuid',
          errorMessage: 'error message',
        },
        Domicile.AU, // domicile value is passed
      ),
    ).resolves.toEqual(undefined);
    const endpoint = (axios as any).post.mock.calls[0][0];
    expect(endpoint).toBe(`${amsEndpoint}/${amsEndpointVersion}/${Domicile.AU}${amsDeviceEndpointPath}/some-url`);
  });
});

describe('AMS API (v2) service test suite', () => {
  let apiServiceV2: AmsApiService;

  const attributionService = MutationAttributionService.getInstance();

  const envService: any = {
    amsEndpointVersion: 'v2',
    amsDeviceEndpointPath: '/device',
    amsEndpoint: 'amsApi',
  };

  beforeEach(() => {
    attributionService.mutationAttribution = undefined;
    apiServiceV2 = new AmsApiService(envService as any, '/device', 'device');
    jest.resetAllMocks();
    (axios as any).get.mockResolvedValue({ status: 200, data: { test: 'data' } });
    (axios as any).post.mockResolvedValue({ status: 200 });
    (axios as any).put.mockResolvedValue({ status: 200 });
    (axios as any).patch.mockResolvedValue({ status: 200 });
    (axios as any).delete.mockResolvedValue({ status: 200 });
    (xray as any).captureHTTPsGlobal.mockReset();
  });

  it('should be able to send GET API request with "domicile" specified', async () => {
    const path = undefined;
    const params = undefined;
    const headers = undefined;
    await expect(apiServiceV2.get(dto.deviceUuid, path, params, headers, Domicile.AU)).resolves.toEqual({
      test: 'data',
    });
    const endpoint = (axios as any).get.mock.calls[0][0];
    expect(endpoint).toBe(
      `${envService.amsEndpoint}/${envService.amsEndpointVersion}/${Domicile.AU}/device/${dto.deviceUuid}`,
    );

    // with "/deviceConfig" specified as path
    await expect(apiServiceV2.get(dto.deviceUuid, '/deviceConfig', params, headers, Domicile.AU)).resolves.toEqual({
      test: 'data',
    });
    const urlEndpointWithPath = (axios as any).get.mock.calls[1][0];
    expect(urlEndpointWithPath).toBe(
      `${envService.amsEndpoint}/${envService.amsEndpointVersion}/${Domicile.AU}/device/${dto.deviceUuid}/deviceConfig`,
    );
  });

  it('should be able to send DELETE API request with "domicile" specified', async () => {
    const path = undefined;
    const headers = undefined;
    (axios as any).delete.mockResolvedValue({});
    await apiServiceV2.delete(dto.deviceUuid, path, headers, Domicile.AU);
    const urlEndpoint = (axios as any).delete.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${dto.deviceUuid}`);
  });

  it('should be able to send CREATE API request with "domicile" specified', async () => {
    // with 'body' being parsed
    (axios as any).post.mockResolvedValue({
      data: {
        body: JSON.stringify({ deviceName: 'new device!' }),
      },
    });

    const result = await apiServiceV2.create(dto.deviceUuid, dto as any, Domicile.AU);
    expect(result).toEqual({
      id: dto.deviceUuid,
      deviceName: 'new device!', // parsed from response body
    });
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device`);

    // no 'body' being returned from request
    (axios as any).post.mockResolvedValue({});
    const createdDevice = await apiServiceV2.create(dto.deviceUuid, dto as any, Domicile.AU);
    expect(createdDevice).toEqual({ id: dto.deviceUuid });
  });

  describe('createEntity Tests', () => {
    const mockDto = { entityUuid: 'ent-uuid', name: 'Test Entity' };
    it('should create entity and return parsed response', async () => {
      (axios.post as any).mockResolvedValue({
        data: {
          body: JSON.stringify({ entityUuid: 'ent-uuid', name: 'Test Entity' }),
        },
      });
      const result = await apiServiceV2.createEntity(mockDto, Domicile.AU);
      expect(result).toEqual({ entityUuid: 'ent-uuid', name: 'Test Entity' });
    });

    it('should handle response with no body and uuid provided', async () => {
      (axios.post as any).mockResolvedValue({});
      const result = await apiServiceV2.createEntity(mockDto, Domicile.AU);
      expect(result).toEqual({});
    });

    it('should handle errors and call handleApiError', async () => {
      const error = new Error('Network failure');
      const spy = jest.spyOn(apiServiceV2 as any, 'handleApiError').mockReturnValue('handled');
      (axios.post as any).mockRejectedValue(error);
      const result = await apiServiceV2.createEntity(mockDto, Domicile.AU);
      expect(spy).toHaveBeenCalledWith(error, expect.stringContaining('Failed to createEntity'));
      expect(result).toBe('handled');
    });
  });

  it('should be able to send UPDATE API request with "domicile" specified', async () => {
    const path = undefined;
    (axios as any).patch.mockResolvedValue({});
    await apiServiceV2.update(dto.deviceUuid, dto as any, path, Domicile.AU);
    const urlEndpoint = (axios as any).patch.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${dto.deviceUuid}`);
  });

  it('should be able to send addEntityTag API request with "domicile" specified', async () => {
    const entityUuid = uuidv4();
    const tagName = uuidv4();
    (axios as any).post.mockResolvedValue({});
    await apiServiceV2.addEntityTag(entityUuid, tagName, Domicile.AU);
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${entityUuid}/tag`);

    (axios as any).post.mockRejectedValue({ status: 400 });
    await apiServiceV2.addEntityTag(entityUuid, tagName, Domicile.AU).catch((err) => {
      expect(err).toEqual(Error(`failed to add '${tagName}' tag from ${entityUuid}`));
    });
  });

  it('should be able to send addEntitySubcategory API request with "domicile" specified', async () => {
    const entityUuid = uuidv4();
    const entityCategory = EntityCategories.FREIGHT_COURIER;
    const entitySubcategory = 'Logistics';
    (axios as any).post.mockResolvedValue({});
    await apiServiceV2.addEntitySubcategory(entityUuid, entityCategory, entitySubcategory, Domicile.AU);
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${entityUuid}/subcategory`);

    (axios as any).post.mockResolvedValue({
      status: 400,
      data: {
        errorMessage: 'Invalid subcategory value',
        errorType: 'BadRequestError',
      },
    });

    await apiServiceV2.addEntitySubcategory(entityUuid, entityCategory, entitySubcategory, Domicile.AU).catch((err) => {
      expect(err).toEqual(new InvalidRequest(`Invalid subcategory value`));
    });
  });

  it('should be able to send updateEntitySubcategory API request with "domicile" specified', async () => {
    const entityUuid = uuidv4();
    const subcategoryUuid = uuidv4();
    const subcategory = 'Logistics';
    (axios as any).patch.mockResolvedValue({});
    await apiServiceV2.updateEntitySubcategory(entityUuid, subcategoryUuid, subcategory, Domicile.AU);
    const urlEndpoint = (axios as any).patch.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${entityUuid}/subcategory/${subcategoryUuid}`);
  });

  it('should be able to send linkTags API request with "domicile" specified', async () => {
    const entityUuid = uuidv4();
    const tagNames = [uuidv4()];
    const type = 'linkType';
    const aggregateId = uuidv4();
    (axios as any).post.mockResolvedValue({});
    await apiServiceV2.linkTags(entityUuid, tagNames, type, aggregateId, Domicile.AU);
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${entityUuid}/tag/link`);
  });

  it('should be able to send linkTags API request with "domicile" specified', async () => {
    const entityUuid = uuidv4();
    const uuid = uuidv4();
    const remitToCard = false;
    (axios as any).patch.mockResolvedValue({});
    await apiServiceV2.selectDepositAccount(entityUuid, uuid, remitToCard, Domicile.AU);
    const urlEndpoint = (axios as any).patch.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${entityUuid}/depositaccount/${uuid}`);
  });

  it('should be able to send sendInviteEmail API request with "domicile" specified', async () => {
    const uuid = uuidv4();
    const entityUuid = uuidv4();
    (axios as any).post.mockResolvedValue({});
    await apiServiceV2.sendInviteEmail(uuid, entityUuid, Domicile.AU);
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${uuid}/email-invite`);

    (axios as any).post.mockRejectedValue({ status: 400 });
    await apiServiceV2.sendInviteEmail(uuid, entityUuid, Domicile.AU).catch((err) => {
      expect(err).toEqual(Error(`Failed to send reset password email.`));
    });
  });

  it('should be able to send updateCustomerPassword API request with "domicile" specified', async () => {
    const uuid = uuidv4();
    const password = uuidv4();
    (axios as any).post.mockResolvedValue({});
    await apiServiceV2.updateCustomerPassword(uuid, password, Domicile.AU);
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${uuid}/update-password`);

    (axios as any).post.mockRejectedValue({ status: 400 });
    await apiServiceV2.updateCustomerPassword(uuid, password, Domicile.AU).catch((err) => {
      expect(err).toEqual(Error(`Failed to update customer password.`));
    });
  });

  it('should be able to send createResetPasswordLink API request with "domicile" specified', async () => {
    const uuid = uuidv4();
    (axios as any).post.mockResolvedValue({ data: { link: uuidv4() } });
    await apiServiceV2.createResetPasswordLink(uuid, Domicile.AU);
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${uuid}/password-reset-link`);

    (axios as any).post.mockRejectedValue({ status: 400 });
    await apiServiceV2.createResetPasswordLink(uuid, Domicile.AU).catch((err) => {
      expect(err).toEqual(Error(`Failed to create password reset link.`));
    });
  });

  it('should be able to send requestEmailChange API request with "domicile" specified', async () => {
    const customerUuid = uuidv4();
    const newEmail = uuidv4();
    const redirectUrl = uuidv4();
    (axios as any).post.mockResolvedValue({});
    await apiServiceV2.requestEmailChange(customerUuid, newEmail, redirectUrl, Domicile.AU);
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${customerUuid}/request-email-change`);

    (axios as any).post.mockRejectedValue({ status: 400 });
    await apiServiceV2.requestEmailChange(customerUuid, newEmail, redirectUrl, Domicile.AU).catch((err) => {
      expect(err).toEqual(Error(`Failed request email change.`));
    });
  });

  it('should be able to send completeEmailChange API request with "domicile" specified', async () => {
    const code = uuidv4();
    const newEmail = uuidv4();
    (axios as any).post.mockResolvedValue({});
    await apiServiceV2.completeEmailChange(code, newEmail, Domicile.AU);
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/complete-email-change`);

    (axios as any).post.mockRejectedValue({ status: 400 });
    await apiServiceV2.completeEmailChange(code, newEmail, Domicile.AU).catch((err) => {
      expect(err).toEqual(Error(`Failed complete email change.`));
    });
  });

  it('should be able to send enrolPhoneMfa API request with "domicile" specified', async () => {
    const uuid = uuidv4();
    (axios as any).post.mockResolvedValue({});
    await apiServiceV2.enrolPhoneMfa(uuid, Domicile.AU);
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${uuid}/enrol-phone-mfa`);

    (axios as any).post.mockRejectedValue({ status: 400 });
    await apiServiceV2.enrolPhoneMfa(uuid, Domicile.AU).catch((err) => {
      expect(err).toEqual(Error(`Customer phone enrolment failed ${uuid}`));
    });
  });

  it('should be able to send requestPhoneChange API request with "domicile" specified', async () => {
    const customerUuid = uuidv4();
    const newPhone = uuidv4();
    (axios as any).post.mockResolvedValue({});
    await apiServiceV2.requestPhoneChange(customerUuid, newPhone, Domicile.AU);
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${customerUuid}/request-phone-change`);

    (axios as any).post.mockRejectedValue({ status: 400 });
    await apiServiceV2.requestPhoneChange(customerUuid, newPhone, Domicile.AU).catch((err) => {
      expect(err).toEqual(Error(`Failed request phone change.`));
    });
  });

  it('should be able to send updateKycCheckpoint API request with "domicile" specified', async () => {
    const customerUuid = uuidv4();
    const kycCheckpoints: KycCheckpoint[] = [KycCheckpoint.IDV];
    (axios as any).post.mockResolvedValue({});
    await apiServiceV2.updateKycCheckpoint(customerUuid, kycCheckpoints, Domicile.AU);
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${customerUuid}/updateKycCheckpoint`);

    (axios as any).post.mockRejectedValue({ status: 400 });
    await apiServiceV2.updateKycCheckpoint(customerUuid, kycCheckpoints, Domicile.AU).catch((err) => {
      expect(err).toEqual(Error(`Failed request update kyc checkpoint.`));
    });
  });

  it('should be able to send completePhoneChange API request with "domicile" specified', async () => {
    const customerUuid = uuidv4();
    const otpCode = uuidv4();
    (axios as any).post.mockResolvedValue({});
    await apiServiceV2.completePhoneChange(customerUuid, otpCode, Domicile.AU);
    const urlEndpoint = (axios as any).post.mock.calls[0][0];
    expect(urlEndpoint).toBe(`amsApi/v2/AUS/device/${customerUuid}/complete-phone-change`);

    (axios as any).post.mockRejectedValue({ status: 400 });
    await apiServiceV2.completePhoneChange(customerUuid, otpCode, Domicile.AU).catch((err) => {
      expect(err).toEqual(Error(`Failed complete phone change.`));
    });
  });

  describe('generate device uuid', () => {
    it('should be able to call ams engine to generate device uuid', async () => {
      const input = {
        model: 'model',
        serial: 'serial',
      };
      await apiServiceV2.generateDeviceUuid(input);
      expect(axios.post).toHaveBeenCalledWith(
        `amsApi/v2/device/device-uuid/${input.model}/${input.serial}`,
        JSON.stringify({ model: input.model, serial: input.serial }),
        { ...AmsApiService.DEFAULT_HTTP_CONFIG, timeout: 10000 },
      );
    });
  });

  describe('generate entity device uuid', () => {
    it('should be able to call ams engine to generate entity device uuid', async () => {
      const input = {
        entityUuid: uuidv4(),
        model: 'model',
        serial: 'serial',
      };
      await apiServiceV2.generateEntityDeviceUuid(input);
      expect(axios.post).toHaveBeenCalledWith(
        `amsApi/v2/device/entity-device-uuid/${input.entityUuid}/${input.model}/${input.serial}`,
        JSON.stringify({ entityUuid: input.entityUuid, model: input.model, serial: input.serial }),
        { ...AmsApiService.DEFAULT_HTTP_CONFIG, timeout: 10000 },
      );
    });
  });
});
