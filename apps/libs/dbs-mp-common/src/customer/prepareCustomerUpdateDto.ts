import { CustomerUpdatedEventDto } from '@npco/component-dto-customer';
import type { ProductTourStatus, CustomerUpdateInput } from '@npco/component-dto-customer';

const setProductTourStatus = (customer: CustomerUpdateInput): ProductTourStatus | undefined => {
  const productTourStatusKeys: Array<keyof ProductTourStatus> = [
    'showOnboardingShop',
    'showAdminMerchantPortalWelcome',
    'showInvoiceInstructions',
    'showInvoicesWelcome',
    'showItemsWelcome',
    'showItemInstructions',
    'showInvoicesCustomisationWelcome',
    'showInvoicesScheduleSendWelcome',
    'showInvoicesSendBySmsWelcome',
    'showInvoiceSendViaInfo',
    'showInvoicingCustomisationSettingsWelcome',
    'showTapToPayInstructions',
    'showTapToPayMayJune',
    'showSavingsAccountWelcome',
    'showSavingsAccountMarch',
    'showSavingsAccountMay',
    'showCorporateCardsMayOffer',
    'showCorporateCardsWalkthrough',
    'showCorporateCardsSettingsWalkthrough',
    'showCustomScreensaverPromo',
    'showNotificationsWelcome',
    'showCorporateCardsAdminWelcome',
    'showInvoiceApril',
    'showCatalogueItemsWelcome',
    'profileAvatarWalkthrough',
    'showServiceChargesWelcome',
  ];

  const productTourStatus: ProductTourStatus = {};

  for (const ptsKey of productTourStatusKeys) {
    const ptsValue = customer[ptsKey];
    if (ptsValue !== undefined) {
      productTourStatus[ptsKey] = ptsValue;
    }
  }

  const isEmptyObject = Object.entries(productTourStatus).length === 0;
  return isEmptyObject ? undefined : productTourStatus;
};

export const prepareCustomerUpdateDto = (
  customer: CustomerUpdateInput,
  entityUuid?: string,
): CustomerUpdatedEventDto => {
  const customerUuid = customer.id;
  const productTourStatus = setProductTourStatus(customer);
  return new CustomerUpdatedEventDto({
    customerUuid,
    entityUuid,
    ...(customer as any),
    productTourStatus,
  });
};
