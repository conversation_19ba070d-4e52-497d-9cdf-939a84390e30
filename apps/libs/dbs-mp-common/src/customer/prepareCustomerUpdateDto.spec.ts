import type { CustomerUpdateInput } from '@npco/component-dto-customer';

import { v4 as uuidv4 } from 'uuid';

import { prepareCustomerUpdateDto } from './prepareCustomerUpdateDto';

describe('prepareCustomerUpdateDto', () => {
  const email = 'test@t.t'; // provided during sign up

  const entityUuid = uuidv4();

  it('should handle prepareDto with no productTourStatus fields', async () => {
    const customerDto: CustomerUpdateInput = { id: uuidv4(), entityUuid, email };
    const dto = prepareCustomerUpdateDto(customerDto, entityUuid);
    expect(dto.customerUuid).toBe(customerDto.id);
    expect(dto.entityUuid).toBe(entityUuid);
    expect(dto.email).toBe(customerDto.email);
    expect(dto.productTourStatus).toBeUndefined();
  });

  it('should handle prepareDto with productTourStatus fields set to true', async () => {
    const customerDto: CustomerUpdateInput = {
      id: uuidv4(),
      entityUuid,
      email,
      showOnboardingShop: true,
      showAdminMerchantPortalWelcome: true,
      showInvoiceInstructions: true,
      showInvoicesWelcome: true,
      showItemsWelcome: true,
      showItemInstructions: true,
      showInvoicesCustomisationWelcome: true,
      showInvoicesScheduleSendWelcome: true,
      showInvoicesSendBySmsWelcome: true,
      showInvoiceSendViaInfo: true,
      showInvoicingCustomisationSettingsWelcome: true,
      showNotificationsWelcome: true,
      showTapToPayInstructions: true,
      showTapToPayMayJune: true,
      showCustomScreensaverPromo: true,
      showSavingsAccountWelcome: true,
      showSavingsAccountMarch: true,
      showSavingsAccountMay: true,
      showCorporateCardsMayOffer: true,
      showCorporateCardsWalkthrough: true,
      showCorporateCardsSettingsWalkthrough: true,
      showCorporateCardsAdminWelcome: true,
      showCatalogueItemsWelcome: false,
      profileAvatarWalkthrough: true,
      showServiceChargesWelcome: true,
    };
    const dto = prepareCustomerUpdateDto(customerDto, entityUuid);
    expect(dto.customerUuid).toBe(customerDto.id);
    expect(dto.entityUuid).toBe(entityUuid);
    expect(dto.email).toBe(customerDto.email);
    expect(dto.productTourStatus).not.toBeUndefined();
    expect(dto.productTourStatus).toStrictEqual({
      showOnboardingShop: true,
      showAdminMerchantPortalWelcome: true,
      showInvoiceInstructions: true,
      showInvoicesWelcome: true,
      showItemsWelcome: true,
      showItemInstructions: true,
      showInvoicesCustomisationWelcome: true,
      showInvoicesScheduleSendWelcome: true,
      showInvoicesSendBySmsWelcome: true,
      showInvoiceSendViaInfo: true,
      showInvoicingCustomisationSettingsWelcome: true,
      showNotificationsWelcome: true,
      showTapToPayInstructions: true,
      showTapToPayMayJune: true,
      showCustomScreensaverPromo: true,
      showSavingsAccountWelcome: true,
      showSavingsAccountMarch: true,
      showSavingsAccountMay: true,
      showCorporateCardsMayOffer: true,
      showCorporateCardsWalkthrough: true,
      showCorporateCardsSettingsWalkthrough: true,
      showCorporateCardsAdminWelcome: true,
      showCatalogueItemsWelcome: false,
      profileAvatarWalkthrough: true,
      showServiceChargesWelcome: true,
    });
  });

  it('should handle prepareDto with productTourStatus fields set to false', async () => {
    const customerDto: CustomerUpdateInput = {
      id: uuidv4(),
      entityUuid,
      email,
      showOnboardingShop: false,
      showAdminMerchantPortalWelcome: false,
      showInvoiceInstructions: false,
      showItemsWelcome: true,
      showItemInstructions: false,
      showInvoicesCustomisationWelcome: false,
      showInvoicesScheduleSendWelcome: false,
      showInvoicesSendBySmsWelcome: false,
      showInvoiceSendViaInfo: false,
      showInvoicingCustomisationSettingsWelcome: false,
      showNotificationsWelcome: false,
      showTapToPayInstructions: false,
      showTapToPayMayJune: false,
      showCustomScreensaverPromo: false,
      showSavingsAccountWelcome: false,
      showSavingsAccountMarch: false,
      showSavingsAccountMay: false,
      showCorporateCardsMayOffer: false,
      showCorporateCardsWalkthrough: false,
      showCorporateCardsSettingsWalkthrough: false,
      showCorporateCardsAdminWelcome: false,
      showInvoiceApril: false,
      showCatalogueItemsWelcome: true,
      profileAvatarWalkthrough: false,
    };
    const dto = prepareCustomerUpdateDto(customerDto, entityUuid);
    expect(dto.customerUuid).toBe(customerDto.id);
    expect(dto.entityUuid).toBe(entityUuid);
    expect(dto.email).toBe(customerDto.email);
    expect(dto.productTourStatus).not.toBeUndefined();
    expect(dto.productTourStatus).toStrictEqual({
      showOnboardingShop: false,
      showAdminMerchantPortalWelcome: false,
      showInvoiceInstructions: false,
      showItemsWelcome: true,
      showItemInstructions: false,
      showInvoicesCustomisationWelcome: false,
      showInvoicesScheduleSendWelcome: false,
      showInvoicesSendBySmsWelcome: false,
      showInvoiceSendViaInfo: false,
      showInvoicingCustomisationSettingsWelcome: false,
      showNotificationsWelcome: false,
      showTapToPayInstructions: false,
      showTapToPayMayJune: false,
      showCustomScreensaverPromo: false,
      showSavingsAccountWelcome: false,
      showSavingsAccountMarch: false,
      showSavingsAccountMay: false,
      showCorporateCardsMayOffer: false,
      showCorporateCardsWalkthrough: false,
      showCorporateCardsSettingsWalkthrough: false,
      showCorporateCardsAdminWelcome: false,
      showInvoiceApril: false,
      showCatalogueItemsWelcome: true,
      profileAvatarWalkthrough: false,
    });
  });
});
