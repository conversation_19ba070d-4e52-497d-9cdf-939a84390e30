import { info } from '@npco/component-bff-core/dist/utils/logger';

import { Injectable } from '@nestjs/common';
import sharp from 'sharp';

// eslint-disable-next-line import/order
import { EnvironmentService } from '../../config/envService';
import { LogoUtil } from './logoUtils';

@Injectable()
export class EreceiptLogo {
  constructor(private readonly utils: LogoUtil, private readonly envService: EnvironmentService) {}

  // Used for building ereceipt logos
  build = async (original: Buffer, logoSize?: { width: number; height: number }): Promise<Buffer> => {
    const size = logoSize ?? this.envService.elecronicReceipLogoSize;
    return this.processLogo(original, size, 'receipt');
  };

  buildCardLogo = async (original: Buffer, logoSize?: { width: number; height: number }): Promise<Buffer> => {
    const size = logoSize ?? this.envService.cardLogoSize;
    return this.processLogo(original, size, 'card');
  };

  private async processLogo(
    original: Buffer,
    logoSize: { width: number; height: number },
    mode: 'receipt' | 'card',
  ): Promise<Buffer> {
    try {
      const sharpInstance = sharp(original);
      let { width, height } = await sharpInstance.metadata();
      width = Number(width);
      height = Number(height);

      let resultImage = sharpInstance;

      info('building electronic receipt logo');
      const { width: maxWidth, height: maxHeight } = logoSize;
      if (this.utils.shouldScaleDown(width, height, maxWidth, maxHeight)) {
        info('scaling down');
        resultImage = await this.utils.scaleDown(sharpInstance, maxWidth, maxHeight);
      }

      const formattedImage = await resultImage.toFormat('png').toBuffer();
      return formattedImage;
    } catch (e) {
      const context = mode === 'card' ? 'buildCardLogo' : 'build';
      info(`EreceiptLogo:${context} Error is `, e instanceof Error ? e.message : String(e));
      throw new Error(JSON.stringify(e));
    }
  }
}
