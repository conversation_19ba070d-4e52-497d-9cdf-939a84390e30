import fs from 'fs';
import sharp from 'sharp';

import { EreceiptLogo } from './ereceiptLogo';
import { LogoUtil } from './logoUtils';

describe('e-receipt logo tests', () => {
  const electronicReceipt = 'ereceipt-';
  const elecronicReceipLogoSize = { width: 300, height: 500 };

  it('should not scale size if its less than 1024x1024', async () => {
    const testImg = 'should-no-dither-no-resize.png';
    const logoUtil = new LogoUtil();
    const logoBuilder = new EreceiptLogo(logoUtil, {
      elecronicReceipLogoSize: { width: 1024, height: 1024 },
    } as any);
    const original = fs.readFileSync(`src/images/__mocks__/${testImg}`);
    const sharpInstance = sharp(original);
    const buffer = await logoBuilder.build(original);
    fs.writeFileSync(`dist/${electronicReceipt}${testImg}`, buffer);
    const result = await sharp(`dist/${electronicReceipt}${testImg}`);
    const { width: actualWidth, height: actualHeight } = await result.metadata();
    const { width: originalWidth, height: originalHeight } = await sharpInstance.metadata();
    expect(originalWidth).toEqual(actualWidth);
    expect(originalHeight).toEqual(actualHeight);
  }, 60000);

  it('should scale image size', async () => {
    const testImg = 'should-resize-no-dither.png';
    const logoUtil = new LogoUtil();
    const logoBuilder = new EreceiptLogo(logoUtil, {
      elecronicReceipLogoSize,
    } as any);
    const original = fs.readFileSync(`src/images/__mocks__/${testImg}`);
    const sharpInstance = sharp(original);
    const buffer = await logoBuilder.build(original);
    fs.writeFileSync(`dist/${electronicReceipt}${testImg}`, buffer);

    const result = await sharp(`dist/${electronicReceipt}${testImg}`);
    const { width: actualWidth, height: actualHeight } = await result.metadata();
    expect(actualWidth).toBeLessThanOrEqual(300);
    expect(actualHeight).toBeLessThanOrEqual(500);
    const { width: originalWidth } = await sharpInstance.metadata();
    expect(originalWidth).toBeGreaterThan(300);
  }, 60000);

  it('should log and throw error if image buffer is invalid', async () => {
    const testImg = 'not-an-image';
    const logoUtil = new LogoUtil();
    const logoBuilder = new EreceiptLogo(logoUtil, {
      elecronicReceipLogoSize: { width: 100, height: 100 },
    } as any);
    const invalidBuffer = Buffer.from(testImg);
    await expect(logoBuilder.build(invalidBuffer)).rejects.toThrow();
  });
});

describe('card logo tests', () => {
  const cardLogo = 'cardlogo-';
  const cardLogoSize = { width: 100, height: 100 };

  it.each(['apple_logo_large.png', 'apple_logo_small.png'])(
    'should scale down card logo %p',
    async (testImg) => {
      const logoUtil = new LogoUtil();
      const logoBuilder = new EreceiptLogo(logoUtil, {
        cardLogoSize,
      } as any);
      const original = fs.readFileSync(`src/images/__mocks__/${testImg}`);
      const sharpInstance = sharp(original);
      const buffer = await logoBuilder.buildCardLogo(original, cardLogoSize);
      fs.writeFileSync(`dist/${cardLogo}${testImg}`, buffer);

      const result = await sharp(`dist/${cardLogo}${testImg}`);
      const { width: actualWidth, height: actualHeight } = await result.metadata();
      expect(actualWidth).toBeLessThanOrEqual(cardLogoSize.width);
      expect(actualHeight).toBeLessThanOrEqual(cardLogoSize.height);
      const { width: originalWidth, height: originalHeight } = await sharpInstance.metadata();
      expect(originalWidth).toBeGreaterThan(cardLogoSize.width);
      expect(originalHeight).toBeGreaterThan(cardLogoSize.height);
    },
    60000,
  );

  it('should use default logo size from envService if not provided', async () => {
    const testImg = 'apple_logo_large.png';
    const logoUtil = new LogoUtil();
    const envService = { cardLogoSize } as any;
    const logoBuilder = new EreceiptLogo(logoUtil, envService);
    const original = fs.readFileSync(`src/images/__mocks__/${testImg}`);
    const buffer = await logoBuilder.buildCardLogo(original);
    fs.writeFileSync(`dist/${cardLogo}-defaultsize-${testImg}`, buffer);
    const result = await sharp(`dist/${cardLogo}-defaultsize-${testImg}`);
    const { width: actualWidth, height: actualHeight } = await result.metadata();
    expect(actualWidth).toBeLessThanOrEqual(cardLogoSize.width);
    expect(actualHeight).toBeLessThanOrEqual(cardLogoSize.height);
  });

  it('should keep the size for smaller card logo', async () => {
    const testImg = 'should-no-dither-no-resize.png';
    const logoUtil = new LogoUtil();
    const logoBuilder = new EreceiptLogo(logoUtil, {
      cardLogoSize,
    } as any);
    const original = fs.readFileSync(`src/images/__mocks__/${testImg}`);
    const sharpInstance = sharp(original);
    const buffer = await logoBuilder.buildCardLogo(original, cardLogoSize);
    fs.writeFileSync(`dist/${cardLogo}${testImg}`, buffer);
    const result = await sharp(`dist/${cardLogo}${testImg}`);
    const { width: actualWidth, height: actualHeight } = await result.metadata();
    const { width: originalWidth, height: originalHeight } = await sharpInstance.metadata();
    expect(originalWidth).toBeLessThan(cardLogoSize.width);
    expect(originalHeight).toBeLessThan(cardLogoSize.height);
    expect(actualWidth).toBe(originalWidth);
    expect(actualHeight).toBe(originalHeight);
  }, 60000);
});
