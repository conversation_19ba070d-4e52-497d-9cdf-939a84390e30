import { Auth0Urls } from '@npco/component-bff-core/dist/authzero/utils/auth0Urls';
import { ConfigService } from '@npco/component-bff-core/dist/config';
import type { DomainURI } from '@npco/component-dto-core';
import { DomainURImap } from '@npco/component-dto-core';

import { Injectable } from '@nestjs/common';

@Injectable()
export class EnvironmentService {
  componentTableName = '';

  cacheTableName = '';

  smscodeTableName = '';

  transactionTotalsTableName = '';

  smscodeTtlInSeconds = 600;

  sessionCacheTtlInSeconds = 86400;

  cacheModelSerialGsi = '';

  deviceSerialModelGsi = '';

  cqrsCommandHandler = '';

  cqrsCmds: DomainURI = DomainURImap;

  pgsRootCaSsmArn = '';

  pgsApiHandler = '';

  pgsApiInitiateTxnPath = '';

  pgsApiEndpoint = '';

  pgsCnpApiEndpoint = '';

  pgsCpocApiEndpoint = '';

  shortIdGsi = '';

  entityGsi = '';

  typeGsi = '';

  sortKeyGsi = '';

  entityTransactionTotalGsi = '';

  entityCacheGsi = '';

  depositsPendingGsi = '';

  depositGsi = '';

  accessTokenGsi = '';

  siteGsi = '';

  siteNameGsi = '';

  cardholderGsi = '';

  deviceGsi = '';

  secondaryGsiV1 = '';

  originalTransactionGsi = '';

  zellerAppAuth0ClientKey = '';

  zellerAppFirebaseCredentials = {} as { projectId: string; adminClientEmail: string; privateKey: string };

  auth0ClientKey = '';

  auth0ClientSecret = '';

  auth0Audience = '';

  auth0Tenant = '';

  auth0JwtMaxAge = '';

  auth0ApiUrls: Auth0Urls = {} as Auth0Urls;

  awsRegion = '';

  stage = '';

  iamUserKey = '';

  iamUserSecret = '';

  isInLambda!: boolean;

  amsEndpoint = '';

  amsEndpointVersion = '';

  amsCustomerEndpointPath = '';

  amsEntityEndpointPath = '';

  amsOnboardingEndpointPath = '';

  amsDeviceEndpointPath = '';

  amsSiteEndpointPath = '';

  amsCardholderEndpointPath = '';

  amsDeviceMgnEndpointPath = '';

  amsTransactionEndpointPath = '';

  crmsEndpoint = '';

  crmsEndpointVersion = '';

  ersEndpoint = '';

  ersEndpointVersion = '';

  crmsCustomerEndpointPath = '';

  httpTimeout = 30000;

  lambdaHttpTimeout = 5000;

  lambdaConnectTimeout = 2000;

  maxRetries = 3;

  dynamodbTimeout = 5000;

  dynamodbConnectTimeout = 2000;

  estimatedAnnualRevenueThreshold = 500000;

  riskRuleEndpoint = '';

  riskRuleEndpointRegulatedMCCEndpoint = '';

  riskRuleEndpointCategoryPath = '';

  onboardingRiskRuleEndpoint = '';

  onboardingRiskRuleEndpointCategoryPath = '';

  riskRuleEndpointProhibitedMCCPath = '';

  allowRefundRiskRuleEndpoint = '';

  cmsEndpoint = '';

  cmsEndpointVersion = '';

  bankingWrapperEnabled = false;

  bankingWrapperEndpoint = '';

  eCommerceApiUrl = '';

  eCommerceApiToken = '';

  eCommerceChannelId = '';

  deviceKeysTable = '';

  receiptLogoUploadBucket = '';

  cardLogoUploadBucket = '';

  receiptLogoProcessedBucket = '';

  cardLogoProcessedBucket = '';

  transactionDepositExportBucket = '';

  debitCardTransactionSummaryBucket = '';

  printedReceiptLogoSize = { width: 0, height: 0 };

  elecronicReceipLogoSize = { width: 1024, height: 1024 };

  cardLogoSize = { width: 100, height: 100 };

  cardLogoMonoPrintSize = { width: 400, height: 168 };

  receiptLogoBaseUrl = '';

  cardLogoBaseUrl = '';

  entityDocumentUploadBucket = '';

  documentUploadBucket = '';

  usePgsRki = false;

  pgsRKIEndpoint = '';

  smsEndpoint = '';

  smsEndpointVersion = '';

  cmsProjectionSqsUrl = '';

  componentName = '';

  /**
   * The stand in parameter name in parameter store
   */
  allowStandInSsmName = '';

  fontRegularPath = '';

  fontMediumPath = '';

  fontBoldPath = '';

  merchantTableName = '';

  billingServiceEndpoint = '';

  /** images---> */

  receiptDocProcessedBucket = '';

  receiptDocumentSize = { width: 2048, height: 2048 };

  maxReceiptDocumentUploadsUrls = 30;

  maxReceiptDocumentFileNameLength = 256;

  rawReceiptDocUploadsBucket = '';

  customerIconTempBucket = '';

  customerIconBaseUrl = '';

  customerIconProcessedBucket = '';

  screensaverLogoBaseUrl = '';

  /** <---images */

  /**
   * Validation rules debit card transaction annotations
   */
  maxNoteLengthChars = 10000;

  maxTagsPerTransaction = 10;

  maxTagsCharLength = 20;

  mfaEnrolmentEnabled = false;

  sensitiveAccessTokenMaxAge = 300;

  transactionImagesProcessedBucket = '';

  maxTransactionImagesUploadsUrls = 30;

  maxTransactionImagesFileNameLength = 256;

  transactionImageSize = { width: 2048, height: 2048 };

  transactionImagesUploadsBucket = '';

  transactionAsyncBatchHandler = '';

  smsVerificationRateLimit: number = 60 * 60 * 1000; // 1 hour

  isAdminMiddlewareEnabled = false;

  isIdvStatusMiddlewareEnabled = false;

  projectionSqsUrl = '';

  messageMediaCredentials = { apiKeySsmName: '', apiSecretSsmName: '' };

  materialiseIncomingExternalNonApprovedTransaction = false;

  zellerSessionIdCheckEnabled = false;

  zellerSessionIdTtlInSeconds = 28800; // 8 hours

  selfieCheckVerificationEndpoint = '';

  selfieCheckVerificationEndpointPath = '';

  selfieCheckVerificationEndpointVersion = '';

  selfieCheckVerificationEnabled = false;

  catalogComponentTable = '';

  appSyncEndpoint = '';

  statementsToSendSqsUrl = '';

  businessIdentifierSearchEndpoint = '';

  businessIdentifierSearchEndpointVersion = '';

  businessIdentifierSearchEndpointPath = '';

  dcaTransactionLookupSqsUrl = '';

  transactionTotalsUpdateSqsUrl = '';

  recalculateEntityDayTotalsSqsUrl = '';

  recalculateFullSiteTotalsSqsUrl = '';

  oraclePosCertTrustStoreBucket = '';

  idvEnabled = false;

  fsEndpoint = '';

  fsEndpointVersion = '';

  refundOverdraftEndpoint = '';

  refundOverdraftEndpointPath = '';

  multiEntityEnabled = false;

  isCustomerSettingsSiteSubscriptionEnabled = false;

  auth0MetadataFromToken = false;

  protected readonly configService: ConfigService = new ConfigService();

  constructor() {
    this.setupComponent();
    this.setupAwsEnv();
    this.setupDbEnv();
    this.setupAuth0Env();
    this.setupCqrsEnv();
    this.setupAms();
    this.setupCrms();
    this.setupErs();
    this.setupRiskRule();
    this.setupAppConfig();
    this.setupCms();
    this.setupBankingWrapper();
    this.setupECommerce();
    this.setupReceiptLogoHandlerEnv();
    this.setupCardLogoHandlerEnv();
    this.setupDocumentUploadEnv();
    this.setupSmsEnv();
    this.setupBucket();
    this.setupFont();
    this.setupBilling();
    this.setupSqs();
    this.setupMfa();
    this.setupTransactionImages();
    this.setupMiddleware();
    this.setUpMaterialisation();
    this.setupFirebase();
    this.setUpSelfieCheckVerification();
    this.setupPublisherEnv();
    this.setupReceiptDocumentHandlerEnv();
    this.setupScreensaverLogoHandlerEnv();
    this.setupCustomerImages();
    this.setUpBusinessIdentifierSearch();
    this.setUpIdv();
    this.setupMultiEntity();
    this.transactionAsyncBatchHandler = this.configService.get('TRANSACTION_ASYNC_BATCH_HANDLER', '');
  }

  setupDbEnv(): void {
    this.componentTableName = this.configService.get('COMPONENT_TABLE', 'Entities');
    this.cacheTableName = this.configService.get('SESSION_CACHE_TABLE', 'SessionCache');
    this.shortIdGsi = this.configService.get('SHORT_ID_GSI', 'shortIdGsi');
    this.cacheModelSerialGsi = this.configService.get('CACHE_MODELSERIAL_GSI', 'modelSerialGsi');
    this.deviceSerialModelGsi = this.configService.get('DEVICE_SERIALMODEL_GSI', 'deviceSerialModelGsi');
    this.entityGsi = this.configService.get('ENTITY_GSI', 'entityGsi');
    this.typeGsi = this.configService.get('TYPE_GSI', 'typeGsi');
    this.sortKeyGsi = this.configService.get('SORT_KEY_GSI', 'sortKeyGsi');
    this.entityTransactionTotalGsi = this.configService.get(
      'ENTITY_TRANSACTION_TOTAL_GSI',
      'entityTransactionTotal200Gsi',
    );
    this.siteGsi = this.configService.get('SITE_GSI', 'siteGsi');
    this.siteNameGsi = this.configService.get('SITE_NAME_GSI', 'siteNameGsi');
    this.depositsPendingGsi = this.configService.get('DEPOSITS_PENDING_GSI', 'depositsPendingGsi');
    this.depositGsi = this.configService.get('DEPOSIT_GSI', 'depositGsi');
    this.accessTokenGsi = this.configService.get('ACCESS_TOKEN_GSI', 'accessTokenGsiv2');
    this.entityCacheGsi = this.configService.get('ENTITY_CACHE_GSI', 'entityCacheGsi');
    this.originalTransactionGsi = this.configService.get('ORIGINAL_TRANSACTION_GSI', 'originalTransactionGsi');
    this.smscodeTableName = this.configService.get('SMSCODE_TABLE', 'Smscodes');
    this.deviceKeysTable = this.configService.get('TMP_DEVICE_KEYS_TABLE', 'DeviceKeys');
    this.cardholderGsi = this.configService.get('CARDHOLDER_GSI', 'cardholderGsi');
    this.deviceGsi = this.configService.get('DEVICE_GSI', 'deviceGsi');
    this.secondaryGsiV1 = this.configService.get('SECONDARY_GSI_V1', 'secondaryGsiV1');
    this.allowStandInSsmName = this.configService.get('DBS_STAND_IN_SSM', '');
    this.merchantTableName = this.configService.get('MERCHANT_TABLE', 'Merchant');
    this.transactionTotalsTableName = this.configService.get('TRANSACTION_TOTALS_TABLE', 'Totals');
  }

  setupReceiptLogoHandlerEnv(): void {
    this.receiptLogoUploadBucket = this.configService.get('S3_RECEIPT_LOGO_UPLOADS', '');
    this.receiptLogoProcessedBucket = this.configService.get('S3_RECEIPT_LOGO_PROCESSED', '');
    this.receiptLogoBaseUrl = this.configService.get('RECEIPT_LOGO_BASE_URL', 'https://dashboard.myzeller.dev/assets');
    const monoSize = this.configService.get('PRINTED_RECEIPT_LOGO_SIZE', '334x500');
    this.printedReceiptLogoSize = this.parseLogoSize(monoSize);
  }

  setupCardLogoHandlerEnv(): void {
    this.cardLogoUploadBucket = this.configService.get('S3_CARD_LOGO_UPLOADS', '');
    this.cardLogoProcessedBucket = this.configService.get('S3_CARD_LOGO_PROCESSED', '');
    this.cardLogoBaseUrl = this.configService.get('CARD_LOGO_BASE_URL', 'https://dashboard.myzeller.dev/assets');
    const cardLogoSize = this.configService.get('CARD_LOGO_SIZE', '100x100');
    this.cardLogoSize = this.parseLogoSize(cardLogoSize);
    const cardLogoMonoPrintSize = this.configService.get('CARD_LOGO_MONO_PRINT_SIZE', '400x168');
    this.cardLogoMonoPrintSize = this.parseLogoSize(cardLogoMonoPrintSize);
  }

  setupCqrsEnv(): void {
    this.cqrsCommandHandler = this.configService.get('CQRS_COMMAND_HANDLER', '');
    this.isInLambda = !!this.configService.get('LAMBDA_TASK_ROOT', '');
    this.pgsRootCaSsmArn = this.configService.get('PGS_ROOT_CA_SSM_ARN', '');
    this.pgsApiHandler = this.configService.get('PGS_API_HANDLER', '');
    this.pgsApiEndpoint = this.configService.get('PGS_API_HTTP_ENDPOINT', '');
    this.pgsCnpApiEndpoint = this.configService.get('PGS_CNP_API_HTTP_ENDPOINT', '');
    this.pgsCpocApiEndpoint = this.configService.get('PGS_CPOC_API_HTTP_ENDPOINT', '');
    this.pgsApiInitiateTxnPath = this.configService.get('PGS_API_INITIATE_TXN_PATH', '');
    this.usePgsRki = this.configService.get('USE_PGS_RKI') === 'true';
    this.pgsRKIEndpoint = this.configService.get('PGS_RKI_ENDPOINT', '');
    this.httpTimeout = +this.configService.get('HTTP_TIMEOUT', 30000);
    this.projectionSqsUrl = this.configService.get('PROJECTION_SQS_URL', '');
  }

  setupAwsEnv(): void {
    this.stage = this.configService.get('STAGE', '');
    this.awsRegion = this.configService.get('AWS_REGION', '');
    this.iamUserKey = this.configService.get('IAM_USER_KEY', '');
    this.iamUserSecret = this.configService.get('IAM_USER_SECRET', '');
  }

  setupFirebase(): void {
    this.zellerAppFirebaseCredentials.privateKey = this.configService.get(
      'ZELLER_APP_FIREBASE_ADMIN_PRIVATE_KEY_SSM_NAME',
      '',
    );
    this.zellerAppFirebaseCredentials.adminClientEmail = this.configService.get(
      'ZELLER_APP_FIREBASE_ADMIN_EMAIL_SSM_NAME',
      '',
    );
    this.zellerAppFirebaseCredentials.projectId = this.configService.get('ZELLER_APP_FIREBASE_PROJECT_ID', '');
  }

  setupAuth0Env(): void {
    this.zellerAppAuth0ClientKey = this.configService.get('ZELLER_APP_AUTH0_CLIENT_ID', '');
    this.auth0ClientKey = this.configService.get('AUTH0_CLIENT_ID', '');
    this.auth0ClientSecret = this.configService.get('AUTH0_CLIENT_SECRET', '');
    this.auth0Tenant = this.configService.get('AUTH0_TENANT', '');
    this.auth0Audience = this.configService.get('AUTH0_AUDIENCE', '');
    this.auth0JwtMaxAge = this.configService.get('AUTH0_JWT_MAX_AGE', '86400');
    this.smscodeTtlInSeconds = this.configService.get('SMSCODE_TTL', 3600); // 60 minutes
    this.sessionCacheTtlInSeconds = this.configService.get('SESSION_TTL', 86400); // 24 hrs
    this.auth0ApiUrls = new Auth0Urls(this.auth0Tenant);
    this.auth0ApiUrls.jwtIssuer = this.configService.get('OPENID_ISSUER_URL', '');
    this.sensitiveAccessTokenMaxAge = this.configService.get('SENSITIVE_AUTH0_JWT_MAX_AGE', 300);
    this.smsVerificationRateLimit = this.configService.get('SMS_VERIFICATION_RATE_LIMIT', 60 * 60 * 1000);
    this.messageMediaCredentials.apiKeySsmName = this.configService.get('MESSAGE_MEDIA_API_KEY_SSM_NAME', '');
    this.messageMediaCredentials.apiSecretSsmName = this.configService.get('MESSAGE_MEDIA_API_SECRET_SSM_NAME', '');
    this.auth0MetadataFromToken = this.configService.get('AUTH0_METADATA_FROM_TOKEN', false) === 'true';
  }

  setupAms(): void {
    this.amsEndpoint = this.configService.get('AMS_API_ENDPOINT', '');
    this.amsEndpointVersion = this.configService.get('AMS_API_ENDPOINT_VERSION', 'v1');
    this.amsCustomerEndpointPath = this.configService.get('AMS_API_ENDPOINT_CUSTOMER_PATH', '/customer');
    this.amsEntityEndpointPath = this.configService.get('AMS_API_ENDPOINT_ENTITY_PATH', '/entity');
    this.amsOnboardingEndpointPath = this.configService.get('AMS_API_ENDPOINT_ONBOARDING_PATH', '/onboarding');
    this.amsDeviceEndpointPath = this.configService.get('AMS_API_ENDPOINT_DEVICE_PATH', '/device');
    this.amsSiteEndpointPath = this.configService.get('AMS_API_ENDPOINT_SITE_PATH', '/site');
    this.amsCardholderEndpointPath = this.configService.get('AMS_API_ENDPOINT_CARDHOLDER_PATH', '/cardholder');
    this.amsDeviceMgnEndpointPath = this.configService.get('AMS_API_ENDPOINT_DEVICE_MGN_PATH', '/devicemgn');
    this.amsTransactionEndpointPath = this.configService.get('AMS_API_ENDPOINT_TRANSACTION_PATH', '/transaction');
  }

  setupCrms(): void {
    this.crmsEndpoint = this.configService.get('CRMS_API_ENDPOINT', '');
    this.crmsEndpointVersion = this.configService.get('CRMS_API_ENDPOINT_VERSION', 'v1');
    this.crmsCustomerEndpointPath = this.configService.get('CRMS_API_ENDPOINT_CUSTOMER_PATH', '/customer');
    this.fsEndpoint = this.configService.get('FS_API_ENDPOINT', '');
    this.fsEndpointVersion = this.configService.get('FS_API_ENDPOINT_VERSION', 'v1');
    this.refundOverdraftEndpoint = this.configService.get('REFUND_OVERDRAFT_ENDPOINT', '');
    this.refundOverdraftEndpointPath = this.configService.get('REFUND_OVERDRAFT_ENDPOINT_PATH', '');
  }

  setupErs(): void {
    this.ersEndpoint = this.configService.get('ERS_API_ENDPOINT', '');
    this.ersEndpointVersion = this.configService.get('ERS_API_ENDPOINT_VERSION', 'v1');
  }

  setupRiskRule(): void {
    this.riskRuleEndpoint = this.configService.get('RISK_RULE_API_ENDPOINT', '');
    this.riskRuleEndpointRegulatedMCCEndpoint = this.configService.get('RISK_RULE_ENDPOINT_REGULATED_MCC_ENDPOINT', '');
    this.riskRuleEndpointCategoryPath = this.configService.get('RISK_RULE_ENDPOINT_CATEGORY_PATH', '/risk/rules/5');
    this.onboardingRiskRuleEndpoint = this.configService.get('ONBOARDING_RISK_RULE_API_ENDPOINT', '');
    this.onboardingRiskRuleEndpointCategoryPath = this.configService.get(
      'ONBOARDING_RISK_RULE_ENDPOINT_CATEGORY_PATH',
      '/risk/rules22',
    );
    this.riskRuleEndpointProhibitedMCCPath = this.configService.get(
      'RISK_RULE_ENDPOINT_PROHIBITED_MCC_PATH',
      '/risk/engine/5a',
    );
    this.allowRefundRiskRuleEndpoint = this.configService.get('ALLOW_REFUND_RISK_RULE_ENDPOINT', '');
  }

  setupAppConfig(): void {
    this.estimatedAnnualRevenueThreshold = +this.configService.get('ESTIMATED_ANNUAL_REVENUE_THRESHOLD', 50000);
  }

  setupCms(): void {
    this.cmsEndpoint = this.configService.get('CMS_API_ENDPOINT', '');
    this.cmsEndpointVersion = this.configService.get('CMS_API_ENDPOINT_VERSION', 'v1');
  }

  setupBankingWrapper(): void {
    this.bankingWrapperEnabled = this.configService.get('BANKING_WRAPPER_ENABLED', 'false') === 'true';
    this.bankingWrapperEndpoint = this.configService.get('BANKING_WRAPPER_API_ENDPOINT', '');
  }

  setupECommerce(): void {
    this.eCommerceApiUrl = this.configService.get('ECOMMERCE_API_URL', '');
    this.eCommerceApiToken = this.configService.get('ECOMMERCE_API_TOKEN', '');
    this.eCommerceChannelId = this.configService.get('ECOMMERCE_CHANNEL_ID', '');
  }

  setupDocumentUploadEnv(): void {
    this.entityDocumentUploadBucket = this.configService.get('S3_ENTITY_DOCUMENT_UPLOADS', '');
    this.documentUploadBucket = this.configService.get('S3_DOCUMENT_UPLOADS', '');
  }

  setupSmsEnv(): void {
    this.smsEndpoint = this.configService.get('SMS_API_ENDPOINT', '');
    this.smsEndpointVersion = this.configService.get('SMS_API_ENDPOINT_VERSION', 'v1');
  }

  setupBucket(): void {
    this.transactionDepositExportBucket = this.configService.get('TRANSACTION_EXPORT_BUCKET', '');
    this.debitCardTransactionSummaryBucket = this.configService.get('DEBIT_CARD_TRANSACTION_SUMMARY_BUCKET', '');
    this.oraclePosCertTrustStoreBucket = this.configService.get('ORACLE_POS_CERT_TRUST_STORE_BUCKET', '');
  }

  setupFont(): void {
    this.fontRegularPath = this.configService.get('FONT_REGULAR_PATH', '');
    this.fontMediumPath = this.configService.get('FONT_MEDIUM_PATH', '');
    this.fontBoldPath = this.configService.get('FONT_BOLD_PATH', '');
  }

  setupBilling(): void {
    this.billingServiceEndpoint = this.configService.get('BILLING_API_ENDPOINT', '');
  }

  setupSqs(): void {
    this.cmsProjectionSqsUrl = this.configService.get('CMS_PROJECTION_SQS_URL', '');
    this.dcaTransactionLookupSqsUrl = this.configService.get('DCA_TRANSACTION_LOOKUP_SQS_URL', '');
    this.transactionTotalsUpdateSqsUrl = this.configService.get('TRANSACTION_TOTALS_UPDATE_SQS_URL', '');
    this.recalculateEntityDayTotalsSqsUrl = this.configService.get('RECALCULATE_ENTITY_DAY_TOTALS_SQS_URL', '');
    this.recalculateFullSiteTotalsSqsUrl = this.configService.get('RECALCULATE_FULL_SITE_TOTALS_SQS_URL', '');
  }

  setupComponent(): void {
    this.componentName = this.configService.get('COMPONENT_NAME', '');
  }

  setupMfa(): void {
    this.mfaEnrolmentEnabled = this.configService.get('MFA_ENROLMENT_ENABLED', false) === 'true';
  }

  setupTransactionImages(): void {
    this.transactionImagesUploadsBucket = this.configService.get('S3_RECEIPT_DOCUMENT_UPLOADS', '');
    this.transactionImagesProcessedBucket = this.configService.get('S3_RECEIPT_DOCUMENT_PROCESSED', '');
  }

  setupMiddleware(): void {
    this.isAdminMiddlewareEnabled = this.configService.get('IS_ADMIN_MIDDLEWARE_ENABLED', false) === 'true';
    this.isIdvStatusMiddlewareEnabled = this.configService.get('IS_IDV_STATUS_MIDDLEWARE_ENABLED', false) === 'true';
    this.zellerSessionIdCheckEnabled = this.configService.get('IS_ZELLER_SESSION_ID_ENABLED', false) === 'true';
  }

  setupPublisherEnv(): void {
    this.catalogComponentTable = this.configService.get('CATALOG_COMPONENT_TABLE', 'Catalogs');
    this.appSyncEndpoint = this.configService.get('APP_SYNC_ENDPOINT', '');
    this.statementsToSendSqsUrl = this.configService.get('STATEMENTS_TO_SEND_SQS_URL', '');
    this.isCustomerSettingsSiteSubscriptionEnabled =
      this.configService.get('CUSTOMER_SETTINGS_SITE_SUBSCRIPTION_ENABLED', false) === 'true';
  }

  setUpMaterialisation(): void {
    this.materialiseIncomingExternalNonApprovedTransaction =
      this.configService.get('MATERIALISE_INCOMING_EXTERNAL_NON_APPROVED_TRANSACTION', false) === 'true';
  }

  setUpSelfieCheckVerification() {
    this.selfieCheckVerificationEndpoint = this.configService.get('SELFIE_CHECK_VERIFICATION_ENDPOINT', '');
    this.selfieCheckVerificationEndpointPath = this.configService.get(
      'SELFIE_CHECK_VERIFICATION_ENDPOINT_PATH',
      '/onfido',
    );
    this.selfieCheckVerificationEndpointVersion = this.configService.get(
      'SELFIE_CHECK_VERIFICATION_ENDPOINT_VERSION',
      'v1',
    );
    this.selfieCheckVerificationEnabled = this.configService.get('SELFIE_CHECK_VERIFICATION_ENABLED', false) === 'true';
  }

  setupReceiptDocumentHandlerEnv(): void {
    this.rawReceiptDocUploadsBucket = this.configService.get('S3_RECEIPT_DOCUMENT_UPLOADS', '');
    this.receiptDocProcessedBucket = this.configService.get('S3_RECEIPT_DOCUMENT_PROCESSED', '');
  }

  setupScreensaverLogoHandlerEnv(): void {
    this.screensaverLogoBaseUrl = this.configService.get(
      'SCREENSAVER_LOGO_BASE_URL',
      'https://dashboard.myzeller.dev/screensaver/assets/',
    );
  }

  setupCustomerImages(): void {
    this.customerIconBaseUrl = this.configService.get(
      'CUSTOMER_ICON_BASE_URL',
      'https://dashboard.myzeller.dev/assets/customer/icon/',
    );
    this.customerIconTempBucket = this.configService.get('CUSTOMER_ICON_TEMP_BUCKET', '');
    this.customerIconProcessedBucket = this.configService.get('CUSTOMER_ICON_PROCESSED_BUCKET', '');
  }

  setUpBusinessIdentifierSearch() {
    this.businessIdentifierSearchEndpoint = this.configService.get('BUSINESS_IDENTIFIER_SEARCH_ENDPOINT', '');
    this.businessIdentifierSearchEndpointPath = this.configService.get(
      'BUSINESS_IDENTIFIER_SEARCH_ENDPOINT_PATH',
      '/businessIdentifier/search',
    );
    this.businessIdentifierSearchEndpointVersion = this.configService.get(
      'BUSINESS_IDENTIFIER_SEARCH_ENDPOINT_VERSION',
      'v1',
    );
  }

  setUpIdv() {
    this.idvEnabled = this.configService.get('IDV_ENABLED', false) === 'true';
  }

  setupMultiEntity() {
    this.multiEntityEnabled = this.configService.get('MULTI_ENTITY_ENABLED', false) === 'true';
  }

  private readonly parseLogoSize = (sizeWidhtHeight: string) => {
    const sizeRaw = sizeWidhtHeight.split('x');
    return { width: Number(sizeRaw[0]), height: Number(sizeRaw[1]) };
  };
}
