import type { EntityUserContext } from '@npco/component-bff-core/dist/types';
import type {
  AddressState,
  DbRecordType,
  Money as MoneyString,
  TaxAmount as DtoTaxAmount,
  EntityType,
  ISO4217,
  StandInRule,
} from '@npco/component-dto-core';
import type {
  Customer,
  CustomerAddress,
  CustomerMedicareCardColours,
  PassportResponseDetails,
} from '@npco/component-dto-customer';
import type { DeviceCreatedEventDto } from '@npco/component-dto-device';
import type {
  Category,
  CategoryGroup,
  EntityAddress,
  EntityDailyLimits,
  EntityFullSearchCompletedEventDto,
  OnboardingStatus,
  OutstandingTransactionConfig,
  RegulatorBodyType,
  RiskReviewType,
  TermsOfServiceAcceptance,
} from '@npco/component-dto-entity';
import type { DocumentUploadedType } from '@npco/component-dto-ticket';
import type {
  IisNotAcquirerApprovedDetails,
  SendReceiptMode,
  StandInStatus,
  Transaction,
} from '@npco/component-dto-transaction';

import type { INestApplicationContext } from '@nestjs/common';
import type { Context, SQSRecord } from 'aws-lambda';

export { DbFilterInput } from '@npco/component-bff-core/dist/dynamodb';

/**
 * App
 * Customer
 * Entity
 * Site
 * Device
 * Deposit
 * Transaction
 * Identity
 * MISC
 */

/**
 * Sonar has an issue with types aliasing e.g
 * export type SettlementInput = ClawbackInput;
 * export type MyString = string;
 *
 * https://sonarsource.github.io/rspec/#/rspec/S6564/javascript
 *
 * The Duplicate type simply creates a new duplicated type with the same definition.
 */
export type Duplicate<Type> = { [Key in keyof Type]: Type[Key] };

/**
 * Sonar has Cognitive Complexity error when using unions
 * required to use 'field' in param;
 *
 * https://stackoverflow.com/questions/69218241/property-does-not-exist-on-type-union
 *
 * StrictUnion allows union of types without the 'in' operator being required
 */
type UnionKeys<T> = T extends T ? keyof T : never;
type StrictUnionHelper<T, TAll> = T extends any ? T & Partial<Record<Exclude<UnionKeys<TAll>, keyof T>, never>> : never;
export type StrictUnion<T> = StrictUnionHelper<T, T>;

export interface SqsReadEvent {
  Records: SQSRecord[];
}

export interface NestAppContext extends Context {
  app: INestApplicationContext;
}

/* App */
export interface NestAppEntityContext extends EntityUserContext {
  app: INestApplicationContext;
}

export type DeviceDbItem = Omit<
  DeviceCreatedEventDto & { deviceName: string; id: string; type: DbRecordType.DEVICE_SETTINGS },
  'name'
>;

export interface NestAppDeviceContext extends NestAppEntityContext {
  device: DeviceDbItem;
  siteUuid: string;
}

export interface JsonObject<T> {
  [key: string]: T;
}

export interface IdType {
  id: string;
  type: string;
}

export type QueryIndex = {
  indexName: string;
  keyName: string;
};

export interface EntityGsiKeySchema extends IdType {
  entityUuid: string;
}

export interface DeviceGsiKeySchema extends IdType {
  deviceUuid: string;
}
export interface CardholderGsiKeySchema extends IdType {
  cardholderUuid: string;
}

export type S3PresignedDownloadLink = {
  downloadLink: string;
  expire: Date;
};

/* Entity */

export interface Entity extends EntityDetails {
  id: string;
  shortId?: string;
  manualEntry: boolean;
  tradingName?: string;
  registeredAddress: EntityAddress;
  businessAddress: EntityAddress;
  categoryGroup?: CategoryGroup;
  category?: Category;
  estimatedAnnualRevenue: number;
  remitToCard: boolean;
  debitCardAccountUuid: string;
  goodsServicesProvide?: string;
  customerDiscovery?: string;
  website?: string;
  instagram?: string;
  facebook?: string;
  twitter?: string;
  regulatorBody?: {
    name?: string;
    referenceNumber?: string;
    type?: RegulatorBodyType;
  };
  hubspotCompanyId?: string;
  onboardingStatus?: OnboardingStatus;
  canAcquire?: boolean;
  canAcquireMoto?: boolean;
  canAcquireCnp?: boolean;
  canAcquireMobile?: boolean;
  canCreateAccount?: boolean;
  canCreateCard?: boolean;
  canPayByCard?: boolean;
  canRefund?: boolean;
  canSettle?: boolean;
  canStandIn?: boolean;
  canTransferIn?: boolean;
  canTransferOut?: boolean;
  hasChargeback?: boolean;
  hadForcedRefund?: boolean;
  hasDirectDebitRequest?: boolean;
  hadDirectDebitFailure?: boolean;
  fullSearchResult?: EntityFullSearchCompletedEventDto;
  riskReview?: RiskReviewType;
  metrics?: JsonObject<any>;
  standInRules?: StandInRule[];
  feeRateSettings?: FeeRateSettings;
  referralCode?: string;
  termsOfService?: {
    ezta?: TermsOfServiceAcceptance;
  };
  dailyLimits?: EntityDailyLimits;
  paymentSettings?: PaymentSettings;
  outstandingTransactionRequirementConfig?: OutstandingTransactionConfig;
  primaryAccountHolder?: string;
  cohort?: CohortType[] | string[];
  currency?: ISO4217;
}

export type AmsCustomer = Omit<Customer, 'id' | 'passportCountry' | 'passportVisaSubClass'> & {
  customerUuid: string;
  documents?: {
    passport?: {
      country: PassportResponseDetails['country'];
      visaSubClass: PassportResponseDetails['visaSubClass'];
    };
  };
};

export interface FeeRateSettings {
  feePercent: number;
  feeFixed: number;
  feePercentMoto: number;
  feeFixedMoto: number;
  feePercentVt: number;
  feeFixedVt: number;
  feePercentCpoc: number;
  feeFixedCpoc: number;
  feePercentXinv: number;
  feePercentIntlXinv: number;
  feeFixedIntlXinv: number;
  feeFixedXinv: number;
  feePercentZinv: number;
  feeFixedZinv: number;
  feePercentIntlZinv: number;
  feeFixedIntlZinv: number;
  feePercentPbl: number;
  feeFixedPbl: number;
  feePercentIntlPbl: number;
  feeFixedIntlPbl: number;
}

export interface MinMaxLimit {
  maximum?: number;
  minimum?: number;
}

export interface PaymentSettings {
  cnpPaymentLimits?: MinMaxLimit;
  paymentLimits?: MinMaxLimit;
  motoPaymentLimits?: MinMaxLimit;
  cpocPaymentLimits?: MinMaxLimit;
}

export interface EntityDetails {
  name: string;
  acn?: string;
  abn?: string;
  type: EntityType;
}

export interface RegisteredBusinessInput {
  name: string;
  type: EntityType;
  tradingName?: string;
  businessDetails: BusinessDetailsInput;
  categoryGroup: CategoryGroup;
  category: Category;
  estimatedAnnualRevenue: number;
  cohort: [string];
  manualEntry?: boolean;
  country: string;
}

export interface AusBusinessInput {
  acn?: string;
  abn?: string;
}

export interface GbrBusinessInput {
  crn: string;
}

export interface BusinessDetailsInput {
  ausBusiness?: AusBusinessInput;
  gbrBusiness?: GbrBusinessInput;
}

export interface UnregSoleTradBusinessInput {
  tradingName?: string;
  categoryGroup: CategoryGroup;
  category: Category;
  estimatedAnnualRevenue: number;
  establishingBusiness: boolean;
  cohort: [string];
  country: string;
}

export interface UnregBusinessInput {
  name: string;
  type: EntityType;
  tradingName?: string;
  categoryGroup: CategoryGroup;
  category: Category;
  estimatedAnnualRevenue: number;
  establishingBusiness: boolean;
  cohort: [string];
  country: string;
}

export enum EntityOnboardingResultStatus {
  COMPLETED = 'COMPLETED',
  IN_REVIEW = 'IN_REVIEW',
  MANUAL_ACTIVATION = 'MANUAL_ACTIVATION',
  MORE_INFO_REQUIRED = 'MORE_INFO_REQUIRED',
}

export interface DocumentUploadUrl {
  uploadUrl: string;
  fileName: string;
}

export type EntityOnboardingResult = {
  entityUuid: string;
  bsb?: string; // deprecated
  account?: string; // deprecated
  result: EntityOnboardingResultStatus;
};

export type ValidateFinaliseEntityOnboardingResult = {
  missingIndividualName: boolean;
  missingDob: boolean;
  missingIndividualAddress: boolean;
  missingPhone: boolean;
  missingEmail: boolean;
  missingEntityName: boolean;
  missingEntityAddress: boolean;
  invalidOnboardingStatus: boolean;
  onboardingStatus?: OnboardingStatus;
};

/* Tag */
export type Tag = {
  id: string;
  tags: string[];
  timestamp: string;
};

export interface CustomerCreateResponse {
  customerUuid: string;
  entityUuid: string;
}

/* Identity */

export interface IdentityInput {
  email: string;
  password: string;
  deviceUuid: string;
}

export interface CheckAccessTokenInput {
  accessToken: string;
  refreshToken: string;
  deviceUuid: string;
}

export interface Identity {
  valid: boolean;
  accessToken: string;
  refreshToken: string;
  idToken?: string;
}

export const enum AuthCredentialType {
  password = 'password',
  refreshToken = 'refresh_token',
}

export interface AuthCredential {
  type: AuthCredentialType;
}

export interface CreateAuth0UserInput {
  email: string;
  password: string;
  email_verified?: boolean;
  verify_email?: boolean;
  user_metadata?: JsonObject<any>;
  given_name?: string;
  family_name?: string;
  name?: string;
  nickname?: string;
}

export interface IdentityUserInfo {
  sub?: string;
  nickname?: string;
  given_name?: string;
  family_name?: string;
  name?: string;
  picture?: string;
  updated_at?: string;
  email?: string;
  email_verified?: boolean;
  phone_number?: string;
  phone_verified?: boolean;
  verify_phone_number?: boolean;
}

export interface ChangePasswordInput {
  password: string;
  userId: string;
}

export interface PinInput {
  deviceUuid: string;
  pin: string;
}

export declare type MoneyNumber = {
  /**
   * Amount in whole dollar in string e.g. $50.35 is repersented as "5035"
   */
  value: number;
  /**
   * ISO4217 currecny code
   */
  currency: ISO4217;
};

export interface TaxAmount {
  name: string;
  amount: MoneyNumber;
}

export enum ExportType {
  CSV = 'CSV',
  XLSX = 'XLSX',
  PDF = 'PDF',
}

export declare type SendReceiptInput = {
  transactionUuid: string;
  mode: SendReceiptMode;
  newEmail?: string;
  newPhone?: string;
};

export interface CompleteOptOutReceiptsResult {
  message?: string;
  status: string;
}

export declare type CustomerVerificationInput = {
  customerUuid: string;
  verifySafeHarbour?: boolean;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  address?: CustomerAddress;
  dob?: string;
  verifyDriversLicence: boolean;
  driversLicenceState?: AddressState;
  driversLicenceNumber?: string;
  driversLicenceFirstName?: string;
  driversLicenceMiddleName?: string;
  driversLicenceLastName?: string;
  verifyPassport: boolean;
  passportCountry?: string;
  passportNumber?: string;
  passportFirstName?: string;
  passportMiddleName?: string;
  passportLastName?: string;
  verifyMedicareCard: boolean;
  medicareCardColour?: CustomerMedicareCardColours;
  medicareFirstName?: string;
  medicareMiddleName?: string;
  medicareLastName?: string;
  medicareCardPosition?: number;
  medicareCardNumber?: string;
  medicareCardExpiry?: string;
};

export const enum AccessScope {
  MFA_SENSITIVE = 'access:sensitive',
}

export type FuncDictionary<T extends string, U> = {
  [K in T]: U;
};

export enum MfaEnrollmentType {
  SMS = 'SMS',
}

export interface MfaEnrollment {
  id: string;
  type: MfaEnrollmentType;
  name: string;
  createdAt: string;
}

export interface DocUploadedRecord {
  bucket: string;
  key: string;
}

export interface DocUploadedMetaData {
  customerUuid?: string;
  entityUuid?: string;
  componentName?: string;
  fileName?: string;
  subject?: string;
  uniqueFileUploadReference?: string;
  documentType?: DocumentUploadedType;
  consentToViewDocument?: boolean;
}

export type MediaTypeDictionary<T extends string, U> = {
  [K in T]: U;
};

export enum ActivateDebitCardErrorEnum {
  DOES_NOT_OWN_CARD = 'DOES_NOT_OWN_CARD',
  ALREADY_ACTIVATED = 'ALREADY_ACTIVATED',
  ACCOUNT_REQUIRED = 'ACCOUNT_REQUIRED',
  USER_IS_NOT_VERIFIED = 'USER_IS_NOT_VERIFIED',
}

export type ActivateDebitCardError = {
  error: ActivateDebitCardErrorEnum;
  __typename: string;
};

export interface CommonAmountParams {
  amount?: MoneyString;
  taxAmounts?: DtoTaxAmount[];
  lineItemAmounts?: MoneyString[];
  surchargeAmount?: MoneyString;
  tipAmount?: MoneyString;
  cashoutAmount?: MoneyString;
  adjustAmount?: MoneyString;
}

export interface DateRange {
  startDate: string;
  endDate: string;
}

export enum PosInterfacePairStatus {
  ACTIVE = 'ACTIVE',
  DELETED = 'DELETED',
}

export enum BankingSortKeyType {
  ISSUING_DEBIT_CARD_ACCOUNT = 'issuing.account.dca',
  ISSUING_SAVINGS_ACCOUNT = 'issuing.account.savings',
}

export interface CrmsTransaction extends Transaction {
  customerUuid?: string;
  isoMessageType?: string;
  bin?: string;
  approvalCode?: string;
  standInStatus?: StandInStatus;
  isNotAcquirerApprovedDetails?: IisNotAcquirerApprovedDetails;
  offlineMetricSaved?: boolean;
  offlineApprovedMetricSaved?: boolean;
}

export interface AppCrmsContext extends NestAppEntityContext {
  requestId: string;
}

export type WithRequiredProperty<Type, Key extends keyof Type> = Type & {
  [Property in Key]-?: Type[Property];
};

export interface AttestationDetails {
  token: string;
  signature: string;
  deviceId: string;
  instanceId: string;
}

export const COHORT_CODE = { STARTUP: 'STARTUP' } as const;

export type CohortCode = (typeof COHORT_CODE)[keyof typeof COHORT_CODE];

export interface CohortType {
  code: CohortCode;
}

export interface ConfirmBusinessInitDetailsInput {
  entityUuid?: string;
  registeringIndividual: string;
  sourceIp: string;
}
