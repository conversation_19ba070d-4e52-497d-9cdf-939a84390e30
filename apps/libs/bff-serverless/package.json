{"name": "@npco/component-bff-serverless", "version": "1.0.109", "description": "Common serverless for bff component", "main": "dist/index.js", "types": "dist/index.d.ts", "compilerOptions": {"module": "commonjs", "declaration": false, "noImplicitAny": false, "noUnusedLocals": true, "removeComments": true, "noLib": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "ES2021", "sourceMap": true, "allowJs": true, "outDir": "./dist"}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"], "scripts": {"isPublished": "bash ./bin/is-published.sh", "test": "jest --no-cache --maxWorkers=4", "lint": "eslint src --quiet ", "run-audit": "yarn npm audit --environment production", "deploy": "yarn npm publish", "build": "yarn tsc --build tsconfig.json", "clean": "rm -rf dist"}, "repository": {"type": "git", "url": "git+https://bitbucket.org/npco_dev/component-bff-serverless.git"}, "keywords": ["component", "bff", "serverless"], "license": "ISC", "homepage": "https://bitbucket.org/npco_dev/component-bff-serverless#readme", "dependencies": {"@aws-appsync/utils": "^1.2.5", "@graphql-tools/load-files": "^7.0.0", "dotenv": "^16.3.1", "esbuild": "^0.25.0", "glob": "^10.4.1", "graphql": "^16.7.1", "serverless": "^3.39.0"}, "devDependencies": {"@npco/eslint-config-backend": "^1.0.12", "@nx/jest": "20.7.0", "@rushstack/eslint-patch": "^1.6.0", "@swc/core": "^1.3.102", "@swc/jest": "^0.2.29", "@types/glob": "^8.1.0", "@types/jest": "^29.5.11", "@types/node": "18.18.12", "@types/serverless": "^3.12.22", "@types/uuid": "^9.0.2", "eslint": "^8.54.0", "jest": "^29.7.0", "jest-html-reporter": "^3.10.2", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "serverless": "^3.39.0", "serverless-domain-manager": "^7.1.2", "serverless-dotenv-plugin": "^6.0.0", "serverless-esbuild": "^1.52.1", "serverless-plugin-resource-tagging": "^1.2.0", "serverless-plugin-tracing": "^2.0.0", "serverless-prune-plugin": "^2.0.2", "ts-node": "^10.9.2", "typescript": "^5.4.5", "yaml-cfn": "^0.3.2"}, "prettier": "@npco/eslint-config-backend/prettier", "jestSonar": {"reportPath": "dist"}}