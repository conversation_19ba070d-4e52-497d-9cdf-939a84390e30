{"name": "@npco/component-bff-serverless", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "tags": ["library", "typescript", "typescript-library", "batch-build-capable"], "targets": {"version": {"executor": "@jscutlery/semver:version", "options": {}}, "ci:build": {"executor": "nx:noop"}, "lib:build": {"executor": "nx:noop"}, "build:part1": {"executor": "@nx/js:tsc", "options": {"outputPath": "{projectRoot}/dist", "main": "{projectRoot}/src/index.ts", "tsConfig": "{projectRoot}/tsconfig.json", "rootDir": "{projectRoot}/src"}, "cache": true}, "prepublish": {"executor": "nx:noop", "dependsOn": ["build"]}, "build:resolvers": {"executor": "nx:run-commands", "options": {"commands": [{"command": "chmod +x ./bin/buildResolvers.sh && ./bin/buildResolvers.sh"}], "cwd": "{projectRoot}"}, "cache": true, "dependsOn": ["build:part1"]}, "build": {"executor": "nx:noop", "dependsOn": ["build:resolvers"], "cache": true}}}