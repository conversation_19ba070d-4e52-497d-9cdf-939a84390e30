import { createResolver, ManagedPolicy } from '../../aws';
import { AwsRegions } from '../../param';
import { defaultTemplate } from '../../testcases/loadTemplate';
import { CanaryDeploymentStrategy } from '../common';
import { ApiAppEnvConfig } from '../config/appEnvConfig';

import { ApiAppServerlessStack } from './apiAppServerlessStack';

describe('test ApiAppServerlessStack', () => {
  let region: string | undefined;

  beforeEach(() => {
    region = process.env.AWS_REGION;
    process.env.AWS_REGION = AwsRegions.SYDNEY;
  });

  afterEach(() => {
    process.env.AWS_REGION = region;
  });

  it('should pass', () => {
    expect(new ApiAppServerlessStack('stack', {} as any, {} as any)).toEqual({
      _functions: undefined,
      _resources: undefined,
      _outputs: undefined,
      _resourceConditions: undefined,
      build: expect.anything(),
      config: {},
      envConfig: {},
      getFunctions: expect.anything(),
      getResources: expect.anything(),
      getOutputs: expect.anything(),
      getConditions: expect.anything(),
      validateFunctions: expect.anything(),
      stackName: 'stack',
    });
    expect(new ApiAppServerlessStack('stack', {} as any, {} as any).build()).toEqual({
      custom: {
        accountId: undefined,
        bucketStackName: undefined,
        dotenv: undefined,
        serviceName: undefined,
        service: undefined,
        vpcImport: {
          securityGroupIds: {
            'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg',
          },
          subnet01: {
            'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01',
          },
          subnet02: {
            'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02',
          },
          subnet03: {
            'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03',
          },
        },
      },
      frameworkVersion: undefined,
      useDotenv: true,
      package: {},
      plugins: [],
      provider: {
        stackName: 'undefined-stack',
        deploymentBucket: {
          blockPublicAccess: true,
          maxPreviousDeploymentArtifacts: 100,
          name: '${cf:${self:custom.bucketStackName}.deploymentBucket}',
        },
        environment: {
          AWS_NODEJS_CONNECTION_REUSE_ENABLED: '1',
          COMPONENT_NAME: '${env:COMPONENT_NAME}',
          LOG_LEVEL: '${env:LOG_LEVEL}',
          PART_NAME: '${env:PART_NAME}',
          STAGE: '${opt:stage}',
        },
        vpc: {
          securityGroupIds: ['${self:custom.vpcImport.securityGroupIds}'],
          subnetIds: [
            '${self:custom.vpcImport.subnet01}',
            '${self:custom.vpcImport.subnet02}',
            '${self:custom.vpcImport.subnet03}',
          ],
        },

        memorySize: '${env:LAMBDA_MEMORY_DEFAULT, 1024}',
        name: 'aws',
        region: '${opt:region}',
        runtime: '${env:NODE_JS_RUNTIME}',
        stackTags: {
          COMPONENT_NAME: '${env:COMPONENT_NAME}',
          PART_NAME: '${env:PART_NAME}',
          STAGE: '${opt:stage}',
          env: '${opt:stage}',
          service: '${env:COMPONENT_NAME}-${env:PART_NAME}',
        },
        tags: {
          COMPONENT_NAME: '${env:COMPONENT_NAME}',
          PART_NAME: '${env:PART_NAME}',
          STAGE: '${opt:stage}',
          env: '${opt:stage}',
          service: '${env:COMPONENT_NAME}-${env:PART_NAME}',
        },
        timeout: '${env:LAMBDA_TIMEOUT_IN_SECONDS}',
      },
      service: 'undefined-stack',
    });
  });

  it('should build with config', () => {
    const config = new ApiAppServerlessStack('mystack', new ApiAppEnvConfig(), {
      plugins: ['myplugin'],
      resources: { myResolver: createResolver('test', 'test', 'test') },
      custom: {
        bucketStackName: 'mybucket',
      },
      package: {
        individually: true,
      },
      functions: {
        myFunction: {
          handler: 'handler',
          name: 'lambddaName',
          appsync: {
            fieldName: 'myQuery',
            typeName: 'Query',
            dataResolverName: {
              useFieldNamePrefix: true,
            },
          },
          useLogicalArnName: false,
          policy: {
            useXray: false,
            useVpc: false,
          },
          deploymentSettings: {
            type: CanaryDeploymentStrategy.AllAtOnce,
            alias: 'dev',
          },
        },
      },
      managedPolicies: [ManagedPolicy.xrayPolicy],
      outputs: {
        myOutput: {
          Value: 'outputValue',
          Export: {
            Name: 'outputExport',
          },
        },
      },
      conditions: {
        PRIMARY_REGION: {
          'Fn::Equals': [
            {
              Ref: 'AWS::Region',
            },
            '${self:custom.primaryRegion}',
          ],
        },
      },
    });
    expect(config.build()).toEqual({
      custom: {
        accountId: '${aws:accountId}',
        bucketStackName: 'mybucket',
        dotenv: {
          path: 'config/',
        },
        serviceName: '${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}',
        service: '${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}',
        vpcImport: {
          securityGroupIds: {
            'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg',
          },
          subnet01: {
            'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01',
          },
          subnet02: {
            'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02',
          },
          subnet03: {
            'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03',
          },
        },
        prune: {
          automatic: '${strToBool(${env:PRUNE_ENABLED, "true"})}',
          includeLayers: '${strToBool(${env:PRUNE_INCLUDE_LAYERS, "true"})}',
          number: '${env:PRUNE_KEEP, 5}',
        },
      },
      frameworkVersion: '^3.39.0',
      functions: {
        myFunction: {
          handler: 'handler',
          name: '${self:provider.stackName}-lambddaName',
          role: 'lambddaNameRole',
          deploymentSettings: {
            type: CanaryDeploymentStrategy.AllAtOnce,
            alias: 'dev',
          },
        },
      },
      package: {
        patterns: ['!node_modules/**'],
        individually: true,
      },
      plugins: ['serverless-prune-plugin', 'myplugin'],
      provider: {
        deploymentBucket: {
          blockPublicAccess: true,
          maxPreviousDeploymentArtifacts: 100,
          name: '${cf:${self:custom.bucketStackName}.deploymentBucket}',
        },
        environment: {
          AWS_NODEJS_CONNECTION_REUSE_ENABLED: '1',
          COMPONENT_NAME: '${env:COMPONENT_NAME}',
          LOG_LEVEL: '${env:LOG_LEVEL}',
          PART_NAME: '${env:PART_NAME}',
          STAGE: '${opt:stage}',
        },
        memorySize: '${env:LAMBDA_MEMORY_DEFAULT, 1024}',
        name: 'aws',
        region: '${opt:region}',
        runtime: '${env:NODE_JS_RUNTIME}',
        stackName: '${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-mystack',
        stackTags: {
          COMPONENT_NAME: '${env:COMPONENT_NAME}',
          PART_NAME: '${env:PART_NAME}',
          STAGE: '${opt:stage}',
          env: '${opt:stage}',
          service: '${env:COMPONENT_NAME}-${env:PART_NAME}',
        },
        vpc: {
          securityGroupIds: ['${self:custom.vpcImport.securityGroupIds}'],
          subnetIds: [
            '${self:custom.vpcImport.subnet01}',
            '${self:custom.vpcImport.subnet02}',
            '${self:custom.vpcImport.subnet03}',
          ],
        },
        tags: {
          COMPONENT_NAME: '${env:COMPONENT_NAME}',
          PART_NAME: '${env:PART_NAME}',
          STAGE: '${opt:stage}',
          env: '${opt:stage}',
          service: '${env:COMPONENT_NAME}-${env:PART_NAME}',
        },
        timeout: '${env:LAMBDA_TIMEOUT_IN_SECONDS}',
      },
      resources: {
        Resources: {
          myQueryDataSource: {
            Properties: {
              ApiId: '${self:custom.appSyncApiId}',
              LambdaConfig: {
                LambdaFunctionArn:
                  'arn:aws:lambda:${self:provider.region}:${self:custom.accountId}:function:${self:provider.stackName}-lambddaName',
              },
              Name: 'myQueryDataSource',
              ServiceRoleArn: '${self:custom.appsyncDataSourceRoleArn}',
              Type: 'AWS_LAMBDA',
            },
            Type: 'AWS::AppSync::DataSource',
          },
          myQueryResolver: {
            Properties: {
              ApiId: '${self:custom.appSyncApiId}',
              DataSourceName: {
                'Fn::GetAtt': ['myQueryDataSource', 'Name'],
              },
              FieldName: 'myQuery',
              Runtime: {
                Name: 'APPSYNC_JS',
                RuntimeVersion: '1.0.0',
              },
              Kind: 'UNIT',
              Code: defaultTemplate,
              TypeName: 'Query',
            },
            Type: 'AWS::AppSync::Resolver',
          },
          lambddaNameRole: {
            Properties: {
              AssumeRolePolicyDocument: {
                Statement: [
                  {
                    Action: ['sts:AssumeRole'],
                    Effect: 'Allow',
                    Principal: {
                      Service: ['lambda.amazonaws.com'],
                    },
                  },
                ],
                Version: '2012-10-17',
              },
              Policies: [
                {
                  PolicyDocument: {
                    Statement: [
                      {
                        Action: ['logs:CreateLogStream', 'logs:PutLogEvents', 'logs:CreateLogGroup'],
                        Effect: 'Allow',
                        Resource: [
                          'arn:aws:logs:${self:provider.region}:${self:custom.accountId}:log-group:/aws/lambda/${self:provider.stackName}-lambddaName:*',
                        ],
                      },
                    ],
                    Version: '2012-10-17',
                  },
                  PolicyName: '${self:provider.stackName}-lambddaNameLogPolicy',
                },
              ],
            },
            Type: 'AWS::IAM::Role',
          },
          myResolver: {
            Properties: {
              ApiId: '${self:custom.appSyncApiId}',
              DataSourceName: {
                'Fn::GetAtt': ['test', 'Name'],
              },
              FieldName: 'test',
              RequestMappingTemplate: `{
  "version": "2018-05-29",
  "operation": "Invoke",
  "payload": {
    "args": $util.toJson($context.args),
    "identity": $util.toJson($context.identity),
    "request": $util.toJson($context.request),
    "authType": $util.toJson($util.authType()),
    "source": $util.toJson($context.source), 
    "info": $util.toJson($context.info)
  }
}`,
              ResponseMappingTemplate: `#if($context.result && $context.result.errorMessage)
$utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
#elseif($context.error)
$utils.error($context.error.message, $context.error.type, $context.result)
#end
$util.toJson($context.result)
`,
              TypeName: 'test',
            },
            Type: 'AWS::AppSync::Resolver',
          },
          xrayPolicy: {
            Properties: {
              PolicyDocument: {
                Statement: [
                  {
                    Action: [
                      'xray:PutTraceSegments',
                      'xray:PutTelemetryRecords',
                      'xray:GetSamplingRules',
                      'xray:GetSamplingTargets',
                      'xray:GetSamplingStatisticSummaries',
                    ],
                    Effect: 'Allow',
                    Resource: '*',
                  },
                ],
                Version: '2012-10-17',
              },
            },
            Type: 'AWS::IAM::ManagedPolicy',
          },
        },
        Outputs: {
          myOutput: {
            Value: 'outputValue',
            Export: {
              Name: 'outputExport',
            },
          },
        },
        Conditions: {
          PRIMARY_REGION: {
            'Fn::Equals': [
              {
                Ref: 'AWS::Region',
              },
              '${self:custom.primaryRegion}',
            ],
          },
        },
      },
      service: '${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-mystack',
      useDotenv: true,
    });
  });
});
