import { ManagedPolicy } from '../../aws/iam';
import { Action, Arn, AwsRegions } from '../../param';
import { defaultTemplate } from '../../testcases/loadTemplate';
import type { ServerlessFunctions } from '../types';

import { buildFunctionResources } from './buildFunctionResources';

describe('test functions', () => {
  let region: string | undefined;

  beforeEach(() => {
    region = process.env.AWS_REGION;
    process.env.AWS_REGION = AwsRegions.SYDNEY;
  });

  afterEach(() => {
    process.env.AWS_REGION = region;
  });

  const roleProp = {
    AssumeRolePolicyDocument: {
      Statement: [
        {
          Action: ['sts:AssumeRole'],
          Effect: 'Allow',
          Principal: {
            Service: ['lambda.amazonaws.com'],
          },
        },
      ],
      Version: '2012-10-17',
    },
  };
  it('should build serverless functions and resources', () => {
    const lambdas: ServerlessFunctions = {
      myFunction: {
        handler: 'handler',
        name: 'test',
      },
      myFunction2: {
        handler: 'handler',
        name: 'test2',
        appsync: {
          fieldName: 'fieldName',
          typeName: 'typeName',
          dataResolverName: {
            useFieldNamePrefix: true,
          },
        },
        policy: {
          managed: [ManagedPolicy.deviceTableQueryRolePolicy, ManagedPolicy.entityTableQueryRolePolicy],
          inline: {
            policy1: [{ actions: [Action.dynamodb.ALL], resources: [Arn.dynamodb.table('entities')] }],
            policy2: [{ actions: [Action.logs.ALL], resources: ['*'] }],
          },
        },
      },
    };
    const { functions, resources } = buildFunctionResources(lambdas, { staticEnv: true } as any);
    expect(functions).toEqual({
      myFunction: {
        handler: 'handler',
        name: '${self:provider.stackName}-test',
        role: 'testRole',
      },
      myFunction2: {
        handler: 'handler',
        name: '${self:provider.stackName}-test2',
        role: 'test2Role',
      },
    });
    expect(resources.fieldNameDataSource).toEqual({
      Properties: {
        ApiId: '${self:custom.appSyncApiId}',
        LambdaConfig: {
          LambdaFunctionArn: {
            'Fn::GetAtt': ['MyFunction2LambdaFunction', 'Arn'],
          },
        },
        Name: 'fieldNameDataSource',
        ServiceRoleArn: '${self:custom.appsyncDataSourceRoleArn}',
        Type: 'AWS_LAMBDA',
      },
      Type: 'AWS::AppSync::DataSource',
    });
    expect(resources.fieldNameResolver).toEqual({
      Properties: {
        ApiId: '${self:custom.appSyncApiId}',
        DataSourceName: {
          'Fn::GetAtt': ['fieldNameDataSource', 'Name'],
        },
        FieldName: 'fieldName',
        Runtime: {
          Name: 'APPSYNC_JS',
          RuntimeVersion: '1.0.0',
        },
        Kind: 'UNIT',
        Code: defaultTemplate,
        TypeName: 'typeName',
      },
      Type: 'AWS::AppSync::Resolver',
    });

    expect(resources.test2Role).toEqual({
      Properties: {
        ...roleProp,
        ManagedPolicyArns: [
          {
            'Fn::ImportValue': '${self:custom.serviceName}-lambdaVpcPolicyArn',
          },
          {
            'Fn::ImportValue': '${self:custom.serviceName}-xrayPolicyArn',
          },
          {
            'Fn::ImportValue': '${self:custom.serviceName}-deviceTableQueryRolePolicyArn',
          },
          {
            'Fn::ImportValue': '${self:custom.serviceName}-entityTableQueryRolePolicyArn',
          },
        ],
        Policies: [
          {
            PolicyDocument: {
              Statement: [
                {
                  Action: ['dynamodb:*'],
                  Effect: 'Allow',
                  Resource: ['arn:aws:dynamodb:${self:provider.region}:${self:custom.accountId}:table/entities'],
                },
              ],
              Version: '2012-10-17',
            },
            PolicyName: '${self:provider.stackName}-policy1',
          },

          {
            PolicyDocument: {
              Statement: [
                {
                  Action: ['logs:*'],
                  Effect: 'Allow',
                  Resource: ['*'],
                },
              ],
              Version: '2012-10-17',
            },
            PolicyName: '${self:provider.stackName}-policy2',
          },
          {
            PolicyDocument: {
              Statement: [
                {
                  Action: ['logs:CreateLogStream', 'logs:PutLogEvents', 'logs:CreateLogGroup'],
                  Effect: 'Allow',
                  Resource: [
                    'arn:aws:logs:${self:provider.region}:${self:custom.accountId}:log-group:/aws/lambda/${self:provider.stackName}-test2:*',
                  ],
                },
              ],
              Version: '2012-10-17',
            },
            PolicyName: '${self:provider.stackName}-test2LogPolicy',
          },
        ],
      },
      Type: 'AWS::IAM::Role',
    });
    expect(resources.testRole).toEqual({
      Properties: {
        ...roleProp,
        ManagedPolicyArns: [
          {
            'Fn::ImportValue': '${self:custom.serviceName}-lambdaVpcPolicyArn',
          },
          {
            'Fn::ImportValue': '${self:custom.serviceName}-xrayPolicyArn',
          },
        ],
        Policies: [
          {
            PolicyDocument: {
              Statement: [
                {
                  Action: ['logs:CreateLogStream', 'logs:PutLogEvents', 'logs:CreateLogGroup'],
                  Effect: 'Allow',
                  Resource: [
                    'arn:aws:logs:${self:provider.region}:${self:custom.accountId}:log-group:/aws/lambda/${self:provider.stackName}-test:*',
                  ],
                },
              ],
              Version: '2012-10-17',
            },
            PolicyName: '${self:provider.stackName}-testLogPolicy',
          },
        ],
      },
      Type: 'AWS::IAM::Role',
    });
  });

  it('should build serverless functions and resources for bff stacks', () => {
    const lambdas: ServerlessFunctions = {
      myFunction: {
        handler: 'handler',
        name: 'test',
      },
      myFunction2: {
        handler: 'handler',
        name: 'test2',
        appsync: {
          fieldName: 'fieldName',
          typeName: 'typeName',
          dataResolverName: {
            useFieldNamePrefix: true,
          },
        },
        policy: {
          managed: [ManagedPolicy.deviceTableQueryRolePolicy, ManagedPolicy.entityTableQueryRolePolicy],
          inline: {
            policy1: [{ actions: [Action.dynamodb.ALL], resources: [Arn.dynamodb.table('entities')] }],
            policy2: [{ actions: [Action.logs.ALL], resources: ['*'] }],
          },
        },
      },
    };
    const { resources } = buildFunctionResources(lambdas, { staticEnv: false } as any);
    expect(resources.testRole).toEqual({
      Properties: {
        ...roleProp,
        ManagedPolicyArns: [
          {
            'Fn::ImportValue': '${self:custom.service}-lambdaVpcPolicyArn',
          },
          {
            'Fn::ImportValue': '${self:custom.service}-xrayPolicyArn',
          },
        ],
        Policies: [
          {
            PolicyDocument: {
              Statement: [
                {
                  Action: ['logs:CreateLogStream', 'logs:PutLogEvents', 'logs:CreateLogGroup'],
                  Effect: 'Allow',
                  Resource: [
                    'arn:aws:logs:${self:provider.region}:${self:custom.accountId}:log-group:/aws/lambda/${self:provider.stackName}-test:*',
                  ],
                },
              ],
              Version: '2012-10-17',
            },
            PolicyName: '${self:provider.stackName}-testLogPolicy',
          },
        ],
      },
      Type: 'AWS::IAM::Role',
    });
  });

  it('should build serverless functions and resources for pipeline resolvers', () => {
    const lambdas: ServerlessFunctions = {
      myFunction: {
        handler: 'handler',
        name: 'test',
      },
      myFunction2: {
        handler: 'handler',
        name: 'test2',
        appsync: {
          fieldName: 'fieldName',
          typeName: 'typeName',
          templateCodePath: 'dist/templates/default.mjs',
        },
      },
      myFunction3: {
        handler: 'handler',
        name: 'test3',
        appsync: {
          kind: 'PIPELINE',
          fieldName: 'fieldName',
          typeName: 'typeName',
          templateCodePath: 'dist/templates/default.mjs',
        },
      },
    };
    const { resources } = buildFunctionResources(lambdas, { staticEnv: false } as any);
    expect(resources.test2DataSource).toEqual({
      Properties: {
        ApiId: '${self:custom.appSyncApiId}',
        LambdaConfig: {
          LambdaFunctionArn: {
            'Fn::GetAtt': ['MyFunction2LambdaFunction', 'Arn'],
          },
        },
        Name: 'test2DataSource',
        ServiceRoleArn: '${self:custom.appsyncDataSourceRoleArn}',
        Type: 'AWS_LAMBDA',
      },
      Type: 'AWS::AppSync::DataSource',
    });
    expect(resources.test2Resolver).toEqual({
      Properties: {
        ApiId: '${self:custom.appSyncApiId}',
        FieldName: 'fieldName',
        Kind: 'UNIT',
        TypeName: 'typeName',
        Code: defaultTemplate,
        Runtime: {
          Name: 'APPSYNC_JS',
          RuntimeVersion: '1.0.0',
        },
        DataSourceName: {
          'Fn::GetAtt': ['test2DataSource', 'Name'],
        },
      },
      Type: 'AWS::AppSync::Resolver',
    });
    expect(resources.test3FunctionConfiguration).toEqual({
      Properties: {
        ApiId: '${self:custom.appSyncApiId}',
        DataSourceName: {
          'Fn::GetAtt': ['test3DataSource', 'Name'],
        },
        FunctionVersion: '2018-05-29',
        Name: 'test3FunctionConfiguration',
        Runtime: {
          Name: 'APPSYNC_JS',
          RuntimeVersion: '1.0.0',
        },
        Code: defaultTemplate,
      },
      Type: 'AWS::AppSync::FunctionConfiguration',
    });
    expect(resources.test3Resolver).toEqual({
      Properties: {
        ApiId: '${self:custom.appSyncApiId}',
        FieldName: 'fieldName',
        Kind: 'PIPELINE',
        PipelineConfig: {
          Functions: [
            {
              'Fn::GetAtt': ['test3FunctionConfiguration', 'FunctionId'],
            },
          ],
        },
        TypeName: 'typeName',
        Code: defaultTemplate,
        Runtime: {
          Name: 'APPSYNC_JS',
          RuntimeVersion: '1.0.0',
        },
      },
      Type: 'AWS::AppSync::Resolver',
    });
  });

  it('should build serverless functions and resources for bff stacks but logs disallowed', () => {
    const lambdas: ServerlessFunctions = {
      myFunction: {
        handler: 'handler',
        name: 'test',
        disableLogs: true,
      },
    };
    const { functions, resources } = buildFunctionResources(lambdas, { staticEnv: false } as any);
    expect(functions.myFunction.disableLogs).toBeTruthy();
    expect(resources.testRole).toEqual({
      Properties: {
        ...roleProp,
        ManagedPolicyArns: [
          {
            'Fn::ImportValue': '${self:custom.service}-lambdaVpcPolicyArn',
          },
          {
            'Fn::ImportValue': '${self:custom.service}-xrayPolicyArn',
          },
        ],
        Policies: [],
      },
      Type: 'AWS::IAM::Role',
    });
  });
});
