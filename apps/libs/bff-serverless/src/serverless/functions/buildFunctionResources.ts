import type { AwsFunctionHand<PERSON>, CloudFormationResource } from 'serverless/aws';

import { createLambdaDataResolver } from '../../aws/appsync/createLambdaDataResolver';
import { createLambdaIamRole } from '../../aws/iam/lambdaRole';
import { ManagedPolicy } from '../../aws/iam/managedPolicy';
import type { ManagedPolicies } from '../../aws/types';
import { self } from '../../param';
import type { BaseEnvConfig } from '../config/baseEnvConfig';
import type { ServerlessFunctions, FunctionResources, ServerlessFunction } from '../types';

const getManagedPolicies = (policy?: {
  managed?: ManagedPolicies[];
  useXray?: boolean;
  useVpc?: boolean;
}): ManagedPolicies[] => {
  let managed: ManagedPolicies[] = [];
  if (!policy || policy.useVpc !== false) {
    managed.push(ManagedPolicy.lambdaVpcPolicy);
  }
  if (!policy || policy.useXray !== false) {
    managed.push(ManagedPolicy.xrayPolicy);
  }
  if (policy?.managed?.length) {
    managed = managed.concat(policy.managed);
  }
  return managed;
};

const getDefaultRole = (
  slsFunction: ServerlessFunction,
  staticEnv?: boolean,
): { roleName: string; role: CloudFormationResource } => {
  const roleName = `${slsFunction.name}Role`;
  const role = createLambdaIamRole(
    slsFunction.name,
    {
      inlinePolicies: slsFunction.policy?.inline,
      managedPolicy: getManagedPolicies(slsFunction.policy),
    },
    staticEnv,
    slsFunction.disableLogs ?? false,
  );

  return {
    role,
    roleName,
  };
};

const createAwsFunctionConfig = (slsFunction: ServerlessFunction, physicalFunctionName: string): AwsFunctionHandler => {
  const awsFunction: AwsFunctionHandler = {
    ...slsFunction,
    name: physicalFunctionName,
  };
  delete (awsFunction as ServerlessFunction).appsync;
  delete (awsFunction as ServerlessFunction).policy;
  delete (awsFunction as ServerlessFunction).useLogicalArnName;
  return awsFunction;
};

const buildFunctionResource = (
  slsFunction: ServerlessFunction,
  functionResources: FunctionResources,
  functionKey: string,
  staticEnv?: boolean,
): FunctionResources => {
  const funcsInstance = { ...functionResources };
  const physicalFunctionName = `${self.provider.stackName}-${slsFunction.name}`;
  const lambdaName = slsFunction.useLogicalArnName === false ? physicalFunctionName : functionKey;
  if (slsFunction.appsync) {
    funcsInstance.resources = {
      ...funcsInstance.resources,
      ...createLambdaDataResolver(slsFunction.name, lambdaName, slsFunction.appsync, slsFunction.useLogicalArnName),
    };
  }
  const awsFunction: AwsFunctionHandler = createAwsFunctionConfig(slsFunction, physicalFunctionName);
  if (!slsFunction.useDefaultRoleOnly) {
    const { role, roleName } = getDefaultRole(slsFunction, staticEnv);
    funcsInstance.resources[roleName] = role;
    awsFunction.role = roleName;
  }
  return {
    ...funcsInstance,
    functions: {
      ...funcsInstance.functions,
      [functionKey]: awsFunction,
    },
  };
};

/**
 * Builds Serverless functions and resources created. Automatically creates
 * iamRoles for the functions with logging, vpc and xray enabled by default
 * Also creates appsync lambda dataSource and resolvers
 * @param functions the serverless extended functions
 * @returns { resources, functions }
 */
export const buildFunctionResources = (functions: ServerlessFunctions, envConfig: BaseEnvConfig): FunctionResources => {
  return Object.keys(functions).reduce(
    (functionResources: FunctionResources, key: string) => {
      const slsFunction: ServerlessFunction = functions[key];
      return buildFunctionResource(slsFunction, functionResources, key, envConfig.staticEnv);
    },
    { functions: {}, resources: {} },
  );
};
