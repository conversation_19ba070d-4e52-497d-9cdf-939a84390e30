import { AwsRegions } from './actions';

export type SlsStr<TString extends string> = `\${${TString}}`;
export type StrToBool<TString extends string> = `\${strToBool(${TString})}`;

export const slsStr = <TString extends string>(str: TString): SlsStr<TString> => `\${${str}}`;

export const strToBool = <TString extends string>(str: TString): StrToBool<TString> => `\${strToBool(${str})}`;

export const getRemoteRegion = (r?: string) => {
  switch (r) {
    case AwsRegions.LONDON:
      return AwsRegions.SYDNEY;
    case AwsRegions.SYDNEY:
      return AwsRegions.LONDON;
    default:
      throw new Error(`Unknown region: ${r}`);
  }
};

export const getRemoteAccountId = (r?: string) => {
  switch (r) {
    case AwsRegions.LONDON:
      return process.env.SYDNEY_ACCOUNT_ID;
    case AwsRegions.SYDNEY:
      return process.env.LONDON_ACCOUNT_ID;
    default:
      throw new Error(`Unknown region: ${r}`);
  }
};

export const getAccountId = (region?: string) => {
  switch (region) {
    case AwsRegions.LONDON:
      return process.env.LONDON_ACCOUNT_ID;
    case AwsRegions.SYDNEY:
      return process.env.SYDNEY_ACCOUNT_ID;
    default:
      throw new Error(`Unknown region: ${region}`);
  }
};
