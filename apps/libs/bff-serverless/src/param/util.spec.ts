import { AwsRegions } from './actions';
import { slsStr, strToBool, getRemoteRegion, getRemoteAccountId, getAccountId } from './util';

describe('util.ts', () => {
  describe('slsStr', () => {
    it('should wrap a string in the Serverless variable syntax', () => {
      const result = slsStr('MY_VARIABLE');
      expect(result).toBe('${MY_VARIABLE}');
    });
  });

  describe('strToBool', () => {
    it('should wrap a string in the Serverless strToBool syntax', () => {
      const result = strToBool('MY_VARIABLE');
      expect(result).toBe('${strToBool(MY_VARIABLE)}');
    });
  });

  describe('getRemoteRegion', () => {
    it('should return SYDNEY for LONDON region', () => {
      const result = getRemoteRegion(AwsRegions.LONDON);
      expect(result).toBe(AwsRegions.SYDNEY);
    });

    it('should return LONDON for SYDNEY region', () => {
      const result = getRemoteRegion(AwsRegions.SYDNEY);
      expect(result).toBe(AwsRegions.LONDON);
    });

    it('should throw an error for an unknown region', () => {
      expect(() => getRemoteRegion('UNKNOWN_REGION')).toThrowError('Unknown region: UNKNOWN_REGION');
    });
  });

  describe('getRemoteAccountId', () => {
    const originalSydneyAccountEnv = process.env.SYDNEY_ACCOUNT_ID;
    const originalLondonAccountEnv = process.env.LONDON_ACCOUNT_ID;

    afterAll(() => {
      process.env.SYDNEY_ACCOUNT_ID = originalSydneyAccountEnv;
      process.env.LONDON_ACCOUNT_ID = originalLondonAccountEnv;
    });

    it('should return SYDNEY_ACCOUNT_ID for LONDON region', () => {
      process.env.SYDNEY_ACCOUNT_ID = '************';
      const result = getRemoteAccountId(AwsRegions.LONDON);
      expect(result).toBe(process.env.SYDNEY_ACCOUNT_ID);
    });

    it('should return LONDON_ACCOUNT_ID for SYDNEY region', () => {
      process.env.LONDON_ACCOUNT_ID = '************';
      const result = getRemoteAccountId(AwsRegions.SYDNEY);
      expect(result).toBe(process.env.LONDON_ACCOUNT_ID);
    });

    it('should throw an error for an unknown region', () => {
      expect(() => getRemoteAccountId('UNKNOWN_REGION')).toThrowError('Unknown region: UNKNOWN_REGION');
    });
  });

  describe('getAccountId', () => {
    const originalSydneyAccountEnv = process.env.SYDNEY_ACCOUNT_ID;
    const originalLondonAccountEnv = process.env.LONDON_ACCOUNT_ID;

    afterAll(() => {
      process.env.SYDNEY_ACCOUNT_ID = originalSydneyAccountEnv;
      process.env.LONDON_ACCOUNT_ID = originalLondonAccountEnv;
    });

    it('should return LONDON_ACCOUNT_ID for LONDON region', () => {
      process.env.LONDON_ACCOUNT_ID = '************';
      const result = getAccountId(AwsRegions.LONDON);
      expect(result).toBe(process.env.LONDON_ACCOUNT_ID);
    });

    it('should return SYDNEY_ACCOUNT_ID for SYDNEY region', () => {
      process.env.SYDNEY_ACCOUNT_ID = '************';
      const result = getAccountId(AwsRegions.SYDNEY);
      expect(result).toBe(process.env.SYDNEY_ACCOUNT_ID);
    });

    it('should throw an error for an unknown region', () => {
      expect(() => getAccountId('UNKNOWN_REGION')).toThrow('Unknown region: UNKNOWN_REGION');
    });
  });
});
