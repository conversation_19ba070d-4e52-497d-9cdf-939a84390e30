import { DiscountConfig, Item, Order, ServiceChargeConfig } from '../types';
import { OrderAmountCalculator } from './calculator';
import { convertAmountToAUD, getAmountInAUDString } from './currencyConverter';
// verifying that calculations work according the excel sheet provided by product team
// https://docs.google.com/spreadsheets/d/1sKBFdTcyXrHActj27pDDUipcjG8iDY6Yzl-h-5MdyKg/edit#gid=*********

describe('Order calculation tests', () => {
  const calculator = new OrderAmountCalculator();

  const createOrderWithItems = (inputItems?: Item[], itemsTaxInclusive = true) => {
    const order = {} as Order;
    order.catalogSettings = { itemsTaxInclusive };
    order.discounts = [
      {
        value: 200000,
        config: DiscountConfig.AMOUNT,
      },
    ];
    order.items = inputItems || [
      {
        name: 'notebook',
        price: 545500,
        discounts: [
          {
            value: 100000,
            config: DiscountConfig.AMOUNT,
          },
        ],
        taxes: [
          {
            name: 'GST',
            enabled: true,
          } as any,
        ],
      } as any,
      {
        name: 'pens',
        price: 181800,
        discounts: [
          {
            value: 100000,
            config: DiscountConfig.AMOUNT,
          },
        ],
        taxes: [
          {
            name: 'GST',
            enabled: true,
          } as any,
        ],
      } as any,
    ];
    return order;
  };

  const createOrderWithItemsWithServiceCharges = (inputItems?: Item[], itemsTaxInclusive = true) => {
    const order = {} as Order;
    order.catalogSettings = { itemsTaxInclusive };
    order.serviceCharges = [
      {
        value: 200000,
        config: ServiceChargeConfig.AMOUNT,
      },
    ];
    order.items = inputItems || [
      {
        name: 'notebook',
        price: 545500,
        serviceCharges: [
          {
            value: 100000,
            config: ServiceChargeConfig.AMOUNT,
          },
        ],
        taxes: [
          {
            name: 'GST',
            enabled: true,
          } as any,
        ],
      } as any,
      {
        name: 'pens',
        price: 181800,
        serviceCharges: [
          {
            value: 100000,
            config: ServiceChargeConfig.AMOUNT,
          },
        ],
        taxes: [
          {
            name: 'GST',
            enabled: true,
          } as any,
        ],
      } as any,
    ];
    return order;
  };

  describe('before surcharge calculations', () => {
    describe('gst included tests', () => {
      it('should match the values as in the FE #1 for GST excluded', async () => {
        const items = [
          {
            name: 'test1',
            price: 917727,
            quantity: 1,
            taxes: [{ enabled: true, name: 'GST', percent: 10 }],
            discounts: [{ config: 'PERCENTAGE', value: 10 }],
          },
          {
            name: 'test2',
            price: 1009800,
            quantity: 1,
            taxes: [{ enabled: false, name: 'GST', percent: null }],
            discounts: [{ config: 'AMOUNT', value: 100000 }],
          },
          {
            name: 'test3',
            price: 3636273,
            quantity: 1,
            taxes: [{ enabled: true, name: 'GST', percent: 10 }],
          },
          {
            name: 'test4',
            price: 5999900,
            quantity: 1,
            taxes: [{ enabled: false, name: 'GST', percent: null }],
          },
        ];
        const order = createOrderWithItems(items as any, true);
        order.discounts = [{ config: 'AMOUNT', value: 9999 * 100 } as any];

        const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
        expect(convertAmountToAUD(subtotalAmount)).toEqual(1181.81);
        expect(convertAmountToAUD(totalGst)).toEqual(40.85);
        expect(convertAmountToAUD(totalAmount)).toEqual(1081.82);
      }, 9999999);

      it('should match the values as in the FE #1 for GST excluded with service charges', async () => {
        const items = [
          {
            name: 'test1',
            price: 917727,
            quantity: 1,
            taxes: [{ enabled: true, name: 'GST', percent: 10 }],
            serviceCharges: [{ config: 'PERCENTAGE', value: 10 }],
          },
          {
            name: 'test2',
            price: 1009800,
            quantity: 1,
            taxes: [{ enabled: false, name: 'GST', percent: null }],
            serviceCharges: [{ config: 'AMOUNT', value: 100000 }],
          },
          {
            name: 'test3',
            price: 3636273,
            quantity: 1,
            taxes: [{ enabled: true, name: 'GST', percent: 10 }],
          },
          {
            name: 'test4',
            price: 5999900,
            quantity: 1,
            taxes: [{ enabled: false, name: 'GST', percent: null }],
          },
        ];
        const order = createOrderWithItemsWithServiceCharges(items as any, true);
        order.serviceCharges = [{ config: 'AMOUNT', value: 9999 * 100 } as any];

        const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
        expect(convertAmountToAUD(subtotalAmount)).toEqual(1222.01);
        expect(convertAmountToAUD(totalGst)).toEqual(46.46);
        expect(convertAmountToAUD(totalAmount)).toEqual(1322);
      }, 9999999);

      it('should calculate totalAmount, gst, subtotal when all items tax applicable', async () => {
        // setup
        /*
            GST Incl.				
            Item Name	Description	Price	Item Discount	GST Applicable (Y/N)
            Notebook	-	         $60.00	$10.00	            Y
            Pens	-	             $20.00	$10.00	            Y
                           
               Subtotal	$60.00		
               Invoice Level Discount	$20.00		
               GST Incl	$3.64		
               Total	$40.00		
            */
        const order = createOrderWithItems();
        // act
        const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
        // verify
        expect(totalAmount).toEqual(400030);
        expect(totalGst).toEqual(36366);
        expect(getAmountInAUDString(totalGst)).toEqual('$3.64');
        expect(subtotalAmount).toEqual(600030);
      });

      it('should calculate totalAmount, gst, subtotal when all items tax applicable with service charges', async () => {
        const order = createOrderWithItemsWithServiceCharges();
        const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
        expect(totalAmount).toEqual(1200030);
        expect(totalGst).toEqual(90912);
        expect(getAmountInAUDString(totalGst)).toEqual('$9.09');
        expect(subtotalAmount).toEqual(1000030);
      });

      it('should calculate totalAmount, gst, subtotal when all items tax applicable with modifiers', async () => {
        // setup
        /*
            GST Incl.				
            Item Name	Description	Price	Item Discount	GST Applicable (Y/N) Modifiers
            Notebook	-	         $60.00	$10.00	            Y               $5.00
            Pens	-	             $20.00	$10.00	            Y               $1.00
                           
               Subtotal	$66.00		
               Invoice Level Discount	$20.00		
               GST Incl	$4.18		
               Total	$46.00		
            */
        const order = createOrderWithItems();
        order.items![0].modifiers = [{ price: 50000 } as any];
        order.items![1].modifiers = [{ price: 10000 } as any];
        // act
        const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
        // verify
        expect(totalAmount).toEqual(460030);
        expect(totalGst).toEqual(41821);
        expect(getAmountInAUDString(totalGst)).toEqual('$4.18');
        expect(subtotalAmount).toEqual(660030);
      });

      it('should calculate totalAmount, gst, subtotal when: first item tax applicable, seconds is not, items has no discounts', async () => {
        // setup
        /*
          GST Incl.				
          Item Name	Description	Price	Item Discount	GST Applicable (Y/N)
          Notebook	-	        $60.00	$0.00	            Y
          Pens	-	            $20.00	$0.00	            N
                         
             Subtotal	$80.00		
             Invoice Level Discount	$20.00		
             GST Incl	$4.09		
             Total	$60.00		
          */
        const order = createOrderWithItems();
        order.items![0].discounts = undefined;
        order.items![1].discounts = undefined;
        order.items![1].taxes = [
          {
            name: 'GST',
            enabled: false,
          } as any,
        ];
        order.items![1].price = 20 * 10000;

        // act
        const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
        // verify
        expect(totalAmount).toEqual(600050);
        expect(totalGst).toEqual(40913);
        expect(getAmountInAUDString(totalGst)).toEqual('$4.09');
        expect(subtotalAmount).toEqual(800050);
      }, 999999);

      it('should calculate totalAmount, gst, subtotal when items not tax applicable and no item discounts', async () => {
        // setup
        /*
          GST Incl.				
          Item Name	Description	Price	Item Discount	GST Applicable (Y/N)
          Notebook	-	        $60.00	$0.00	            N
          Pens	-	            $20.00	$0.00	            N
                         
             Subtotal	$80.00		
             Invoice Level Discount	$20.00		
             GST Incl	$0.00		
             Total	$60.00		
          */
        const order = createOrderWithItems();
        order.items![0].discounts = undefined;
        order.items![1].discounts = undefined;
        order.items![0].taxes = undefined;
        order.items![0].price = 60 * 10000; // setting item price to 60$
        order.items![1].taxes = undefined;
        order.items![1].price = 20 * 10000; // setting item price to 20$

        // act
        const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
        // verify
        expect(totalAmount).toEqual(600000);
        expect(totalGst).toEqual(0);
        expect(subtotalAmount).toEqual(800000);
      }, 999999);
    });

    describe('gst excluded test', () => {
      it('should match the values as in the FE #1', async () => {
        const items = [
          {
            name: 'test1',
            price: 1009500,
            priceWithGst: 1009500,
            quantity: 1,
            taxes: [{ enabled: true, name: 'GST', percent: 10 }],
            discounts: [{ config: 'PERCENTAGE', value: 10 }],
          },
          {
            name: 'test2',
            price: 1009800,
            priceWithGst: 1009800,

            quantity: 1,
            taxes: [{ enabled: false, name: 'GST', percent: null }],
            discounts: [{ config: 'AMOUNT', value: 100000 }],
          },
          {
            name: 'test3',
            price: 3999900,
            priceWithGst: 3999900,
            quantity: 1,
            taxes: [{ enabled: true, name: 'GST', percent: 10 }],
          },
          {
            name: 'test4',
            price: 5999900,
            priceWithGst: 5999900,
            quantity: 1,
            taxes: [{ enabled: false, name: 'GST', percent: null }],
          },
        ];
        const order = createOrderWithItems(items as any, false);
        order.catalogSettings = { itemsTaxInclusive: false };
        order.discounts = [{ config: 'AMOUNT', value: 999900 }] as any;

        const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
        expect(convertAmountToAUD(totalAmount)).toEqual(1126.75);
        expect(convertAmountToAUD(totalGst)).toEqual(44.93);
        expect(convertAmountToAUD(subtotalAmount)).toEqual(1181.81);
      });

      it('should calculate totalAmount, gst, subtotal excluded when all items applicable', async () => {
        /*
       GST Incl.				
       Item Name	Description	Price	Item Discount	GST Applicable (Y/N)
       Notebook	-	            $60.00	$10.00	            Y
       Pens	-	                $20.00	$10.00	            Y
                   	
           Subtotal	$60.00		
           Invoice Level Discount	$20.00		
           GST Excl	$4		
           Total	$44.00		
       */
        // setup
        const order = createOrderWithItems();
        order.catalogSettings = { itemsTaxInclusive: false };
        order.items![0].price = 60 * 10000;
        order.items![1].price = 20 * 10000;
        // act
        const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
        // verify
        expect(totalAmount).toEqual(440000);
        expect(totalGst).toEqual(40000);
        expect(getAmountInAUDString(totalGst)).toEqual('$4.00');
        expect(subtotalAmount).toEqual(600000);
      }, 99999999);

      it('should calculate totalAmount, gst, subtotal excluded when all items tax applicable with modifiers', async () => {
        // setup
        /*
            GST Excl.				
            Item Name	Description	Price	Item Discount	GST Applicable (Y/N) Modifiers
            Notebook	-	         $60.00	$10.00	            Y               $5.00
            Pens	-	             $20.00	$10.00	            Y               $1.00
                           
               Subtotal	$66.00		
               Invoice Level Discount	$20.00		
               GST Excl	$4.18		
               Total	$46.00		
            */
        const order = createOrderWithItems();
        order.catalogSettings = { itemsTaxInclusive: false };
        order.items![0].price = 60 * 10000;
        order.items![1].price = 20 * 10000;
        order.items![0].modifiers = [{ price: 50000 } as any];
        order.items![1].modifiers = [{ price: 10000 } as any];
        // act
        const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
        // verify
        expect(totalAmount).toEqual(506000);
        expect(totalGst).toEqual(46000);
        expect(getAmountInAUDString(totalGst)).toEqual('$4.60');
        expect(subtotalAmount).toEqual(660000);
      });

      it('should calculate totalAmount, gst, subtotal excluded when only one item applicable', async () => {
        /*
       GST Incl.				
       Item Name	Description	Price	Item Discount	GST Applicable (Y/N)
       Notebook	-	            $60.00	$10.00	            Y
       Pens	-	                $20.00	$10.00	            N
                   	
           Subtotal	$60.00		
           Invoice Level Discount	$20.00		
           GST Excl	$3.33		
           Total	$43.33		
       */
        // setup
        const order = createOrderWithItems();
        order.catalogSettings = { itemsTaxInclusive: false };
        order.items![0].price = 60 * 10000;

        order.items![1].taxes = [{ name: 'GST', enabled: false }];
        order.items![1].price = 20 * 10000;

        // act
        const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
        // verify
        expect(totalAmount).toEqual(433333);
        expect(totalGst).toEqual(33333);
        expect(getAmountInAUDString(totalGst)).toEqual('$3.33');
        expect(subtotalAmount).toEqual(600000);
      });

      it('should calculate totalAmount, gst, subtotal excluded when no item applicable', async () => {
        /*
       GST Incl.				
       Item Name	Description	Price	Item Discount	GST Applicable (Y/N)
       Notebook	-	            $60.00	 	            N
       Pens	-	                $20.00	   	            N
                   	
           Subtotal	$80.00		
           Invoice Level Discount	$20.00		
           GST Excl	$0.00		
           Total	$60.00	
       */
        // setup
        const order = createOrderWithItems();
        order.catalogSettings = { itemsTaxInclusive: false };
        order.items![0].discounts = undefined;
        order.items![1].discounts = undefined;
        order.items![0].taxes = undefined;
        order.items![0].price = 60 * 10000;

        order.items![1].taxes = undefined;
        order.items![1].price = 20 * 10000;

        // act
        const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
        // verify
        expect(totalAmount).toEqual(600000);
        expect(totalGst).toEqual(0);
        expect(subtotalAmount).toEqual(800000);
      });
    });
  });

  describe('gst exclusive rounding tests', () => {
    it('case 1', async () => {
      /**
          * CASE 1
         Item 1 price = $1.25
         GST = $0.125 = $0.12 carry the .5
         
         Item 2 price = $1.25
         GST = $0.125 = $0.12 carry the .5
         
         Total = Round(( 1.25 + 0.12 ) + ( 1.25 + 0.12 ) + ( 0.005 + 0.005 ))
         = Round(2.75)
         = $2.75
    */
      // setup
      const items = [
        {
          name: 'notebook',
          price: 12500,
          taxes: [
            {
              name: 'GST',
              enabled: true,
            } as any,
          ],
        } as any,
        {
          name: 'pen',
          price: 12500,
          taxes: [
            {
              name: 'GST',
              enabled: true,
            } as any,
          ],
        } as any,
      ];

      const order = createOrderWithItems(items, false);
      order.discounts = undefined;
      order.catalogSettings = { itemsTaxInclusive: false };

      // act
      const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
      // verify
      expect(totalAmount).toEqual(27500);
      expect(totalGst).toEqual(2500);
      expect(subtotalAmount).toEqual(25000);
    });
    it('case 2', async () => {
      /**
          CASE 2
          Item 1 price = $1.87
          GST = $0.187 = $0.18 carry the .7
          
          Item 2 price = $2.46
          GST = $0.246 = $0.24 carry the .6
          
          Total = Round(( 1.87+0.18 ) + ( 2.46+0.24 ) + ( 0.007+0.006 ))
          = Round(4.763)
          = $4.76
  */
      // setup
      const items = [
        {
          name: 'notebook',
          price: 18700,
          taxes: [
            {
              name: 'GST',
              enabled: true,
            } as any,
          ],
        } as any,
        {
          name: 'pen',
          price: 24600,
          taxes: [
            {
              name: 'GST',
              enabled: true,
            } as any,
          ],
        } as any,
      ];
      const order = createOrderWithItems(items, false);
      order.discounts = undefined;
      // act
      const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
      // verify
      expect(totalAmount).toEqual(47630);
      expect(totalGst).toEqual(4330);
      expect(subtotalAmount).toEqual(43300);
    });
    it('case 3', async () => {
      /**
          CASE 3
          Item 1 price = $0.03
          
          Item 2 price = $0.06
          
      */
      // setup
      const items = [
        {
          name: 'notebook',
          price: 300,
          taxes: [
            {
              name: 'GST',
              enabled: true,
            } as any,
          ],
        } as any,
        {
          name: 'pen',
          price: 600,
          taxes: [
            {
              name: 'GST',
              enabled: true,
            } as any,
          ],
        } as any,
      ];
      const order = createOrderWithItems(items, false);
      order.discounts = [
        {
          value: 0,
          config: DiscountConfig.PERCENTAGE,
        },
      ];
      // act
      const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
      // verify
      expect(totalAmount).toEqual(990);
      expect(totalGst).toEqual(90);
      expect(subtotalAmount).toEqual(900);
    });
  });

  describe('item quantity with discounts calculations', () => {
    it('should match the values as in the FE #1 for item discounts', async () => {
      const items = [
        {
          name: 'test1',
          price: 909091,
          quantity: 2,
          taxes: [{ enabled: true, name: 'GST', percent: 10 }],
          discounts: [{ config: 'PERCENTAGE', value: 12 }],
        },
        {
          name: 'test2',
          price: 1020000,
          quantity: 3,
          taxes: [{ enabled: false, name: 'GST', percent: null }],
          discounts: [{ config: 'AMOUNT', value: 1011000 }],
        },
      ];
      const order = createOrderWithItems(items as any, true);
      order.discounts = undefined;

      const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
      expect(convertAmountToAUD(subtotalAmount)).toEqual(380.9);
      expect(convertAmountToAUD(totalGst)).toEqual(16.0);
      expect(convertAmountToAUD(totalAmount)).toEqual(380.9);
    }, 999999);
  });

  describe('items with modifiers, item discounts, and order level discounts', () => {
    // https://docs.google.com/spreadsheets/d/1b8iemB5YjCDeSMy1jTY75NQ-2ckRNg4VwD5LPODIyJY/edit?gid=*********#gid=*********
    const items = [
      {
        name: 'Almond croissant',
        price: 67000,
        quantity: 1,
        taxes: [{ enabled: true, name: 'GST', percent: 10 }],
        discounts: [
          { config: DiscountConfig.PERCENTAGE, value: 2 },
          { config: DiscountConfig.AMOUNT, value: 310900 },
          { config: DiscountConfig.PERCENTAGE, value: 1 },
          { config: DiscountConfig.AMOUNT, value: 123800 },
        ],
        modifiers: [{ price: 50000 }],
      },
      {
        name: 'Green tea',
        price: 864600,
        quantity: 1,
        taxes: [{ enabled: true, name: 'GST', percent: 10 }],
        discounts: [
          { config: DiscountConfig.AMOUNT, value: 310900 },
          { config: DiscountConfig.AMOUNT, value: 192000 },
        ],
        modifiers: [{ price: 35000 }],
      },
      {
        name: 'Chai',
        price: 200000,
        quantity: 1,
        taxes: [{ enabled: false, name: 'GST', percent: null }],
        discounts: [],
        modifiers: [{ price: 0 }],
      },
      {
        name: 'Kale',
        price: 746800,
        quantity: 1,
        taxes: [{ enabled: true, name: 'GST', percent: 10 }],
        discounts: [
          { config: DiscountConfig.AMOUNT, value: 452000 },
          { config: DiscountConfig.PERCENTAGE, value: 12 },
          { config: DiscountConfig.AMOUNT, value: 82300 },
        ],
        modifiers: [{ price: 2500000 }],
      },
      {
        name: 'Ice Latte',
        price: 419300,
        quantity: 1,
        taxes: [{ enabled: true, name: 'GST', percent: 10 }],
        discounts: [],
        modifiers: [{ price: 35000 }],
      },
      {
        name: 'Almond croissant',
        price: 67000,
        quantity: 1,
        taxes: [{ enabled: true, name: 'GST', percent: 10 }],
        discounts: [],
      },
    ];
    const orderDiscounts = [
      { config: DiscountConfig.PERCENTAGE, value: 12 },
      { config: DiscountConfig.AMOUNT, value: 452000 },
      { config: DiscountConfig.PERCENTAGE, value: 10 },
      { config: DiscountConfig.AMOUNT, value: 134500 },
    ];

    it('should match the values as in the calculation master sheet with GST inclusive', async () => {
      const order = createOrderWithItems(items as any, true);
      order.discounts = orderDiscounts;
      const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
      expect(convertAmountToAUD(subtotalAmount)).toEqual(364.17);
      expect(convertAmountToAUD(totalGst)).toEqual(19.74);
      expect(convertAmountToAUD(totalAmount)).toEqual(229.77);
    });

    it('should match the values as in the calculation master sheet with GST exclusive', async () => {
      const order = createOrderWithItems(items as any, false);
      order.discounts = orderDiscounts;
      const { totalAmount, totalGst, subtotalAmount } = calculator.calculateTaxes(order);
      expect(convertAmountToAUD(subtotalAmount)).toEqual(344.09);
      expect(convertAmountToAUD(totalGst)).toEqual(20.14);
      expect(convertAmountToAUD(totalAmount)).toEqual(234.01);
    });
  });
});
