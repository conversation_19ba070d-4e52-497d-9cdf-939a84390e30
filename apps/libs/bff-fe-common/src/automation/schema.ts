import { z } from 'zod';

const validWeekdays = ['MO', 'TU', 'WE', 'TH', 'FR', 'SA', 'SU'] as const;

const POSITIONAL_DAY_REGEX = /^(?:-1|[1-5])(?:MO|TU|WE|TH|FR|SA|SU)$/;

export const POSITIONAL_DAY_VALIDATION_ERROR =
  'Expected format: {number}{weekday}, where {weekday} is one of: MO, TU, WE, TH, FR, SA, SU.';
export const INVALID_DATE_STRING_VALIDATION_ERROR = 'Must be a valid datetime string';
export const INVALID_BYDAYS_VALIDATION_ERROR = 'Must be a valid weekday (MO–SU)';
export const EMPTY_BYDAYS_VALIDATION_ERROR = 'At least one weekday must be selected';

function isValidDateString(input: string): boolean {
  const date = new Date(input);
  return !Number.isNaN(date.getTime());
}

const positionalDay = z.string().regex(POSITIONAL_DAY_REGEX, {
  message: POSITIONAL_DAY_VALIDATION_ERROR,
});

const baseRecurrenceSchema = z.object({
  start: z.string().refine(isValidDateString, { message: INVALID_DATE_STRING_VALIDATION_ERROR }),
  end: z.string().refine(isValidDateString, { message: INVALID_DATE_STRING_VALIDATION_ERROR }),
});

const dailyRecurrence = baseRecurrenceSchema.extend({
  type: z.literal('daily'),
  until: z.string().refine(isValidDateString, { message: INVALID_DATE_STRING_VALIDATION_ERROR }).optional(),
});

const weeklyRecurrence = baseRecurrenceSchema.extend({
  type: z.literal('weekly'),
  byDays: z
    .array(z.enum(validWeekdays, { errorMap: () => ({ message: INVALID_BYDAYS_VALIDATION_ERROR }) }))
    .nonempty({ message: EMPTY_BYDAYS_VALIDATION_ERROR }),
  until: z.string().refine(isValidDateString, { message: INVALID_DATE_STRING_VALIDATION_ERROR }).optional(),
});

const monthlyRecurrence = baseRecurrenceSchema.extend({
  type: z.literal('monthly'),
  byPositionalDay: positionalDay.optional(),
  until: z.string().refine(isValidDateString, { message: INVALID_DATE_STRING_VALIDATION_ERROR }).optional(),
});

const yearlyRecurrence = baseRecurrenceSchema.extend({
  type: z.literal('yearly'),
  until: z.string().refine(isValidDateString, { message: INVALID_DATE_STRING_VALIDATION_ERROR }).optional(),
});

// No until
const oneOff = baseRecurrenceSchema.extend({
  type: z.literal('oneOff'),
});

export const recurrenceSchema = z.union([
  dailyRecurrence,
  weeklyRecurrence,
  monthlyRecurrence,
  yearlyRecurrence,
  oneOff,
]);

export type Day = (typeof validWeekdays)[number];
export type PositionalDay = z.infer<typeof positionalDay>[];
export type Recurrence = z.infer<typeof recurrenceSchema>;
