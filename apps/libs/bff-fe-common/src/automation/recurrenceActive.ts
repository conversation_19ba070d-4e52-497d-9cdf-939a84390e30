import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import ICAL from 'ical.js';

import type { HolidayRule, RuleInput } from './types';

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

/**
 * Evaluates whether a recurring or one-off calendar event defined by an iCal string
 * is currently active based on the current system time.
 *
 * @param icalString - A valid RFC5545-compliant iCal string representing a calendar event.
 * @returns `true` if the event is currently active; otherwise, `false`.
 */
function isRecurringEventActive(icalString: string): boolean {
  const now = dayjs();
  const calendarData = ICAL.parse(icalString);
  const component = new ICAL.Component(calendarData);
  const eventData = component.getFirstSubcomponent('vevent');

  if (!eventData) {
    return false;
  }

  const dtstart = eventData.getFirstPropertyValue('dtstart');
  const dtend = eventData.getFirstPropertyValue('dtend');

  if (!(dtstart instanceof ICAL.Time) || !(dtend instanceof ICAL.Time)) {
    return false;
  }

  const recurRaw = eventData.getFirstPropertyValue('rrule');
  const recur = recurRaw instanceof ICAL.Recur ? recurRaw : null;

  // One off rule, no recurrence
  if (!recur) {
    const from = dayjs(dtstart.toJSDate());
    const to = dayjs(dtend.toJSDate());
    return now.isSameOrAfter(from) && now.isSameOrBefore(to);
  }

  const expiry = recur.until?.toString();

  if (expiry && dayjs(expiry).isSameOrBefore(now)) {
    return false;
  }

  const startIterator = recur.iterator(dtstart);
  const endIterator = recur.iterator(dtend);

  let start = startIterator.next();
  let end = endIterator.next();

  while (start && end) {
    const from = dayjs(start.toJSDate());
    const to = dayjs(end.toJSDate());

    if (now.isSameOrAfter(from) && now.isSameOrBefore(to)) {
      return true;
    }

    if (to.isAfter(now)) {
      return false;
    }

    start = startIterator.next();
    end = endIterator.next();
  }

  return false;
}

/**
 * Checks whether any of the holidays listed in a CSV string of IDs are currently active.
 *
 * This function looks up each holiday ID in the provided `icalDataForHolidays` record,
 * evaluates whether its corresponding event is active using `isRecurringEventActive`,
 * and returns true if any of them are currently active.
 *
 * @param holidayData - An object containing:
 *   - `holidayIdsCsv`: A comma-separated list of holiday IDs to check.
 *   - `icalDataForHolidays`: A record mapping each holiday ID to its iCal string.
 * @returns `true` if at least one of the holidays is active; otherwise, `false`.
 */
function isAnyHolidayActive({ holidayIdsCsv, icalDataForHolidays: holidayData }: Omit<HolidayRule, 'type'>): boolean {
  const holidays = holidayIdsCsv
    .trim()
    .split(',')
    .map((string) => string.trim())
    .filter((string) => Boolean(string));
  return Boolean(holidays.length) && holidays.some((holiday) => isRecurringEventActive(holidayData[holiday]));
}

/**
 * Determines whether a scheduling rule is currently active.
 *
 * This function supports two types of rules:
 *
 * - `RFC5545`: A rule defined by an RFC5545-compliant iCal string. The function checks
 *    whether the current system time falls within an active event or recurrence window.
 *
 * - `HOLIDAY`: A rule that references a comma-separated list of holiday IDs (`holidayIdsCsv`)
 *   and a corresponding map of iCal strings (`icalDataForHolidays`) keyed by holiday ID.
 *   It returns `true` if at least one of the referenced holidays is currently active.
 *
 * @param rule - The rule input to evaluate. Must be one of:
 *   - `Rfc5545Rule`: `{ type: 'RFC5545'; icalString: string }`
 *   - `HolidayRule`: `{ type: 'HOLIDAY'; holidayIdsCsv: string; icalDataForHolidays: Record<string, string> }`
 * @returns `true` if the rule is active at the current system time; otherwise, `false`.
 */
export function isRuleActive(rule: RuleInput) {
  switch (rule.type) {
    case 'RFC5545':
      return isRecurringEventActive(rule.icalString);
    case 'HOLIDAY':
      return isAnyHolidayActive(rule);
    default:
      return false;
  }
}
