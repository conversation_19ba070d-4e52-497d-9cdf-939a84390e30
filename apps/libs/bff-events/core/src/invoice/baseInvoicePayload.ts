import type {
  InvoiceActivity,
  InvoiceCustomer,
  InvoiceDiscount,
  InvoiceEmail,
  InvoiceItem,
  InvoiceSendSchedule,
  InvoiceSite,
  InvoiceStatus,
  InvoiceSMS,
  InvoiceDiscountDetail,
  InvoiceServiceChargeDetail,
  InvoiceAmounts,
} from './types';

export class InvoiceCorePayload {
  entityUuid: string;

  referenceNumber: string;

  status: InvoiceStatus;

  title?: string;

  message?: string;

  siteSettings?: InvoiceSite;

  startDate?: string;

  dueDate?: string;

  discount?: InvoiceDiscount;

  email?: InvoiceEmail;

  includeCalculation?: boolean;

  totalGst?: number; // deprecated due to uk currency refactor

  subtotalAmount?: number; // deprecated due to uk currency refactor

  totalAmount?: number; // deprecated due to uk currency refactor

  totalSurcharge?: number; // deprecated due to uk currency refactor

  totalDiscount?: number; // deprecated due to uk currency refactor

  totalServiceCharge?: number; // deprecated due to uk currency refactor

  itemsApplyTax?: boolean;

  itemsTaxInclusive?: boolean;

  sentTime?: number;

  dueAmount?: number; // deprecated due to uk currency refactor

  paidAmount?: number; // deprecated due to uk currency refactor

  amounts?: InvoiceAmounts;

  paymentLink?: string;

  paidTime?: number;

  updatedTime?: number;

  createdTime?: number;

  notes?: string;

  payerContactName?: string;

  payerContactUuid?: string;

  sendEmailCopyTo?: string;

  senderCustomerUuid?: string;

  sendSchedule?: InvoiceSendSchedule;

  sms?: InvoiceSMS;

  requiredEmailUpdateBeforeSend?: string[];

  requiredPhoneUpdateBeforeSend?: string[];

  discounts?: InvoiceDiscountDetail[];

  serviceCharges?: InvoiceServiceChargeDetail[];

  constructor(params: InvoiceCorePayload) {
    this.entityUuid = params.entityUuid;
    this.status = params.status;
    this.referenceNumber = params.referenceNumber;
    this.title = params.title;
    this.message = params.message;
    this.siteSettings = params.siteSettings;
    this.startDate = params.startDate;
    this.dueDate = params.dueDate;
    this.discount = params.discount;
    this.email = params.email;
    this.subtotalAmount = params.subtotalAmount;
    this.totalAmount = params.totalAmount;
    this.totalSurcharge = params.totalSurcharge;
    this.totalDiscount = params.totalDiscount;
    this.totalServiceCharge = params.totalServiceCharge;
    this.totalGst = params.totalGst;
    this.amounts = params.amounts;
    this.includeCalculation = params.includeCalculation;
    this.itemsApplyTax = params.itemsApplyTax;
    this.itemsTaxInclusive = params.itemsTaxInclusive;
    this.sentTime = params.sentTime;
    this.dueAmount = params.dueAmount;
    this.paidAmount = params.paidAmount;
    this.paymentLink = params.paymentLink;
    this.createdTime = params.createdTime;
    this.updatedTime = params.updatedTime;
    this.paidTime = params.paidTime;
    this.notes = params.notes;
    this.payerContactName = params.payerContactName;
    this.payerContactUuid = params.payerContactUuid;
    this.sendEmailCopyTo = params.sendEmailCopyTo;
    this.senderCustomerUuid = params.senderCustomerUuid;
    this.sendSchedule = params.sendSchedule;
    this.sms = params.sms;
    this.requiredEmailUpdateBeforeSend = params.requiredEmailUpdateBeforeSend;
    this.requiredPhoneUpdateBeforeSend = params.requiredPhoneUpdateBeforeSend;
    this.discounts = params.discounts;
    this.serviceCharges = params.serviceCharges;
  }
}

export class BaseInvoicePayload extends InvoiceCorePayload {
  customer?: InvoiceCustomer;

  items?: InvoiceItem[];

  activities?: InvoiceActivity[];

  constructor(params: BaseInvoicePayload) {
    super(params);
    this.items = params.items;
    this.customer = params.customer;
    this.activities = params.activities;
  }
}
