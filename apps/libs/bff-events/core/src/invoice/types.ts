import type { ReceiptSettings, SurchargesTaxesSettings } from '@npco/component-domain-events/dist/commonTypes/site';
import type { SiteCreatedBaseEventPayload } from '@npco/component-domain-events/dist/site';

import type { ContactBaseEvent } from '../addressbook';
import type { CatalogItemCreatedBaseEventPayload } from '../catalog';

export enum InvoiceStatus {
  DRAFT = 'DRAFT',
  SENT = 'SENT',
  PART_PAID = 'PART_PAID',
  PAID = 'PAID',
  OVERDUE = 'OVERDUE',
  CANCELLED = 'CANCELLED',
  ACTIVE = 'ACTIVE',
  ENDED = 'ENDED',
  DELETED = 'DELETED',
  SCHEDULED = 'SCHEDULED',
  SEND_FAILED = 'SEND_FAILED', // deprecated, use ERROR
  ERROR = 'ERROR',
}

export enum InvoiceMilestoneStatus {
  NULL = 'NULL',
  PAID = 'PAID',
  OVERDUE = 'OVERDUE',
  CANCELLED = 'CANCELLED',
}

export enum InvoiceDiscountConfig {
  PERCENTAGE = 'PERCENTAGE',
  AMOUNT = 'AMOUNT',
}

export enum InvoiceServiceChargeConfig {
  PERCENTAGE = 'PERCENTAGE',
  AMOUNT = 'AMOUNT',
}

export enum InvoiceItemUnit {
  QUANTITY = 'QUANTITY',
  HOUR = 'HOUR',
  DAY = 'DAY',
}

export enum InvoiceMilestoneType {
  DEPOSIT = 'DEPOSIT',
  MILESTONE = 'MILESTONE',
  FINAL = 'FINAL',
}

export enum InvoiceItemType {
  SINGLE = 'SINGLE',
  VARIANT = 'VARIANT',
  MODIFIER = 'MODIFIER',
  ONE_TIME = 'ONE_TIME',
}

export type InvoiceEmailRecipients = {
  recipient: string;
  cc?: string[];
  bcc?: string[];
  sendMeCopy?: boolean;
};

export type InvoiceEmail = {
  subject?: string;
  body?: string;
  recipients?: InvoiceEmailRecipients;
  enabled?: boolean;
};

export type InvoiceSMS = {
  enabled: boolean;
  payerContactPhoneNumber: string;
};

export enum InvoiceDiscountType {
  BASIC = 'BASIC',
  AUTOMATIC = 'AUTOMATIC',
}

export enum InvoiceServiceChargeType {
  BASIC = 'BASIC',
  AUTOMATIC = 'AUTOMATIC',
}

export type InvoiceDiscountDetail = {
  id: string;
  catalogDiscountUuid?: string;
  name?: string;
  type?: InvoiceDiscountType;
  discountedAmount: string;
  ordinal: number;
  config: InvoiceDiscountConfig;
  value: string;
  catalogDiscount: any;
  quantity?: number;
};

export type InvoiceServiceChargeDetail = {
  id: string;
  catalogServiceChargeUuid?: string;
  name?: string;
  type?: InvoiceServiceChargeType;
  serviceChargeAmount: string;
  ordinal: number;
  config: InvoiceServiceChargeConfig;
  value: string;
  catalogServiceCharge: any;
  quantity?: number;
};

export type InvoiceDiscount = {
  config: InvoiceDiscountConfig;
  value: number;
};

export type InvoiceSiteSettings = {
  discountsEnabled: boolean;
  shippingInformationEnabled: boolean;
  surchargesTaxes: SurchargesTaxesSettings;
  receipt: ReceiptSettings;
};

export enum EntityChangeStatus {
  UPDATED = 'UPDATED',
  DELETED = 'DELETED',
  NO_CHANGE = 'NO_CHANGE',
}

export type EntityChangeTracker = {
  id: string;
  lastRevisionTime?: string;
  updatedTime?: string;
  status: EntityChangeStatus;
};

export type CatalogItemReference = Partial<CatalogItemCreatedBaseEventPayload> & EntityChangeTracker;

export type ContactReference = Partial<ContactBaseEvent> & EntityChangeTracker;

export type ItemTax = {
  enabled: boolean;
  name: string;
  percent?: number;
};

export type InvoiceItemModifier = {
  id: string;
  catalogModifierUuid?: string;
  catalogModifierSetUuid?: string;
  catalogModifierSet?: InvoiceModifierSet;
  name: string;
  price: string;
  ordinal: number;
  description?: string;
  quantity: number;
  subtotalAmount?: string;
};

export type InvoiceModifierSet = {
  id: string;
  entityUuid: string;
  invoicesEnabled: boolean;
  selectionRequired: boolean;
  name: string;
  modifiers: InvoiceCatalogModifier[];
  status: EntityChangeStatus;
  lastRevisionTime: string;
};

export type InvoiceCatalogModifier = {
  id: string;
  name: string;
  price: string;
  description?: string;
  ordinal: number;
};

export type InvoiceItem = {
  id: string;
  entityUuid: string;
  name: string;
  orderIndex: number;
  price?: number;
  type?: InvoiceItemType;
  description?: string;
  unit: InvoiceItemUnit;
  quantity: number;
  discountAmount?: number;
  serviceChargeAmount?: number;
  discount?: InvoiceDiscount;
  discounts?: InvoiceDiscountDetail[];
  serviceCharges?: InvoiceServiceChargeDetail[];
  modifiers?: InvoiceItemModifier[];
  reportingCategoryUuid?: string;
  taxes?: ItemTax[];
  updatedTime?: string;
  catalogItem?: CatalogItemReference;
};

export type InvoiceSite = SiteCreatedBaseEventPayload;

export type InvoiceCustomer = {
  /**
   * customer id
   */
  id: string;

  payerContact: ContactReference;

  attentionContact?: ContactReference;

  payerEmail?: string;
};

export enum InvoicePaymentType {
  MANUAL = 'MANUAL',
  CNP = 'CNP',
}

export type InvoicePaymentAmounts = {
  amount: number;
  surchargedAmount?: number;
  surchargedGst?: number;
};

export type InvoicePayment = {
  id: string;

  entityUuid: string;

  invoiceId: string;

  amount: number; // deprecated due to uk currency refactor

  type: InvoicePaymentType;

  paymentTimeISO: string;

  notes?: string;

  localPaymentTime?: Date;

  surchargedAmount?: number; // deprecated due to uk currency refactor

  surchargedGst?: number; // deprecated due to uk currency refactor

  rrn?: string;

  transactionUuid?: string;

  entityShortId?: string;

  amounts?: InvoicePaymentAmounts;
};

export enum InvoiceActivityStatus {
  COMPLETED = 'COMPLETED',
  SCHEDULED = 'SCHEDULED',
  SKIPPED = 'SKIPPED',
  FAILED = 'FAILED',
}
export enum InvoiceAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  CANCELLED = 'CANCELLED',
  SEND_INVOICE = 'SEND_INVOICE',
  RE_SEND_INVOICE = 'RE_SEND_INVOICE',
  SEND_AUTO_REMINDER = 'SEND_AUTO_REMINDER',
  SEND_MANUAL_REMINDER = 'SEND_MANUAL_REMINDER',
  MANUAL_PAYMENT = 'MANUAL_PAYMENT',
  CNP_PAYMENT = 'CNP_PAYMENT',
  SEND_SCHEDULED_INVOICE = 'SEND_SCHEDULED_INVOICE',
  SEND_SMS_INVOICE = 'SEND_SMS_INVOICE',
  SEND_EMAIL_INVOICE = 'SEND_EMAIL_INVOICE',
}

export type InvoiceDeliveryResult = {
  failed: boolean;
  reason?: string;
  confirmationDateISO?: string;
};

export type InvoiceActivity = {
  id: string;

  entityUuid: string;

  invoiceReferenceNumber: string;

  contactUuid?: string;

  editorCustomerUuid?: string;

  contactName?: string;

  status: InvoiceActivityStatus;

  type: InvoiceAction;

  title: string;

  completedTime?: string;

  balance?: number;

  paidAmount?: number;

  cnpTxnRefNum?: string;

  failureReason?: string;

  dueDate?: string;

  lastRetryTime?: string;

  executionAttempts?: number;

  reminderIndex?: string;

  invoiceEmailDeliveryResult?: InvoiceDeliveryResult;

  invoiceSmsDeliveryResult?: InvoiceDeliveryResult;

  invoiceEmailTrackingId?: string;

  invoiceSmsTrackingId?: string;
};

export type InvoiceAmounts = {
  paidAmount?: number;
  subtotalAmount?: number;
  dueAmount?: number;
  totalAmount?: number;
  totalSurcharge?: number;
  totalDiscount?: number;
  totalServiceCharge?: number;
  totalGst?: number;
};

export type BaseInvoice = {
  entityUuid: string;

  referenceNumber: string;

  status: InvoiceStatus;

  title?: string;

  message?: string;

  siteSettings?: InvoiceSite;

  startDate?: string;

  dueDate?: string;

  discount?: InvoiceDiscount;

  email?: InvoiceEmail;

  includeCalculation?: boolean;

  totalGst?: number; // deprecated due to uk currency refactor

  subtotalAmount?: number; // deprecated due to uk currency refactor

  totalAmount?: number; // deprecated due to uk currency refactor

  totalSurcharge?: number; // deprecated due to uk currency refactor

  totalDiscount?: number; // deprecated due to uk currency refactor

  totalServiceCharge?: number; // deprecated due to uk currency refactor

  amounts?: InvoiceAmounts;

  itemsApplyTax?: boolean;

  itemsTaxInclusive?: boolean;

  customer?: InvoiceCustomer;

  items?: InvoiceItem[];

  activities?: InvoiceActivity[];

  paidTime?: number;

  sentTime?: number;

  paidAmount?: number; // deprecated due to uk currency refactor

  dueAmount?: number; // deprecated due to uk currency refactor

  updatedTime?: number;

  createdTime?: number;

  totalSurchargedGst?: number; // deprecated due to uk currency refactor

  notes?: string;

  sendEmailCopyTo?: string;

  senderCustomerUuid?: string;

  sendSchedule?: InvoiceSendSchedule;
};

export type InvoiceSendSchedule = {
  enabled: boolean;
  sendDate: string;
};
