{"name": "@npco/component-events-ces", "version": "1.0.4", "description": "Domain events for connected entities service", "main": "dist/index.js", "types": "dist/index.d.ts", "compilerOptions": {"module": "commonjs", "declaration": false, "noImplicitAny": false, "noUnusedLocals": true, "removeComments": true, "noLib": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es6", "sourceMap": true, "allowJs": true, "outDir": "./dist"}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"], "scripts": {"clean": "rm -rf dist", "test": "jest --config $INIT_CWD/jest.config.ts", "deploy": "yarn npm publish", "run-audit": "yarn npm audit --environment production"}, "husky": {"hooks": {"pre-push": "yarn lint && yarn run-audit && yarn test"}}, "keywords": ["domain", "events", "domain-events"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@npco/component-domain-events": "15.3.27", "@npco/component-events-core": "workspace:*", "uuid": "^9.0.0"}, "devDependencies": {"@npco/eslint-config-backend": "^1.0.12", "@nx/jest": "20.7.0", "@rushstack/eslint-patch": "^1.6.0", "@swc/core": "^1.3.85", "@swc/jest": "^0.2.29", "@types/jest": "^29.5.11", "@types/node": "18.19.14", "@types/uuid": "^9.0.0", "@typescript-eslint/parser": "^5.38.0", "eslint": "^8.56.0", "jest": "^29.7.0", "jest-html-reporter": "^3.10.2", "jest-junit": "^16.0.0", "jest-sonar": "^0.2.16", "jest-sonar-reporter": "^2.0.0", "typescript": "^5.4.5"}, "jestSonar": {"reportPath": "dist", "reportFile": "test-reporter.xml", "indent": 2}, "prettier": "@npco/eslint-config-backend/prettier"}