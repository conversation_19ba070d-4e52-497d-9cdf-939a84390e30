import { address } from '@npco/component-dto-core/dist/mockTestData/mockdata.address';

import type { SiteCreatedEventDto } from '../siteCreatedEventDto';
import { SiteType } from '../types';

import { moto } from './mockdata.moto';
import { receipt } from './mockdata.receipt';
import { schemes } from './mockdata.schemes';
import { schemesMoto } from './mockdata.schemesMoto';
import { surchargesTaxes } from './mockdata.surchargesTaxes';
import { timezone } from './mockdata.timezone';
import { tipping } from './mockdata.tipping';

export const mockSiteCreatedEventDto: SiteCreatedEventDto = {
  name: 'name',
  pin: 'pin',
  entityUuid: 'entityUuid',
  siteUuid: 'siteUuid',
  type: SiteType.FIXED,
  address,
  refundRequiresPin: false,
  surchargesTaxes,
  moto,
  tipping,
  receipt,
  schemes,
  schemesMoto,
  domicile: 'AU',
  currency: 'AUD',
  timezone,
};
