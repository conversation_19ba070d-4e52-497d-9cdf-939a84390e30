import { Region } from '@npco/component-dto-test-utils/dist/regionMockProvider';

import { timezone, getTimezoneForRegion } from './mockdata.timezone';

describe('mockdata.timezone', () => {
  describe('timezone export', () => {
    it('should be defined', () => {
      expect(timezone).toBeDefined();
    });

    it('should be a string', () => {
      expect(typeof timezone).toBe('string');
    });

    it('should have a valid timezone format', () => {
      expect(timezone).toMatch(/^[A-Za-z_]+\/[A-Za-z_]+$/);
    });
  });

  describe('getTimezoneForRegion function', () => {
    it('should be defined', () => {
      expect(getTimezoneForRegion).toBeDefined();
    });

    it('should return Australia/Melbourne for Australia region', () => {
      const australiaTimezone = getTimezoneForRegion(Region.AUSTRALIA);
      expect(australiaTimezone).toBe('Australia/Melbourne');
    });

    it('should return Europe/London for UK region', () => {
      const ukTimezone = getTimezoneForRegion(Region.UK);
      expect(ukTimezone).toBe('Europe/London');
    });

    it('should return default timezone when no region specified', () => {
      const defaultTimezone = getTimezoneForRegion();
      expect(defaultTimezone).toBeDefined();
      expect(typeof defaultTimezone).toBe('string');
    });
  });

  describe('regional configuration differences', () => {
    it('should have different timezone values between Australia and UK regions', () => {
      const australiaTimezone = getTimezoneForRegion(Region.AUSTRALIA);
      const ukTimezone = getTimezoneForRegion(Region.UK);

      expect(australiaTimezone).toBe('Australia/Melbourne');
      expect(ukTimezone).toBe('Europe/London');
      expect(australiaTimezone).not.toBe(ukTimezone);
    });
  });

  describe('data immutability', () => {
    it('should return the same value for each call to the same region', () => {
      const timezone1 = getTimezoneForRegion(Region.AUSTRALIA);
      const timezone2 = getTimezoneForRegion(Region.AUSTRALIA);

      expect(timezone1).toBe(timezone2);
    });

    it('should return consistent values across multiple calls', () => {
      const ukTimezone1 = getTimezoneForRegion(Region.UK);
      const ukTimezone2 = getTimezoneForRegion(Region.UK);
      const ukTimezone3 = getTimezoneForRegion(Region.UK);

      expect(ukTimezone1).toBe('Europe/London');
      expect(ukTimezone2).toBe('Europe/London');
      expect(ukTimezone3).toBe('Europe/London');
    });
  });

  describe('environment-based behavior', () => {
    const originalAwsRegion = process.env.AWS_REGION;

    afterEach(() => {
      if (originalAwsRegion) {
        process.env.AWS_REGION = originalAwsRegion;
      } else {
        delete process.env.AWS_REGION;
      }
    });

    it('should use current region when AWS_REGION is set to UK', () => {
      process.env.AWS_REGION = 'eu-west-2';
      const currentTimezone = getTimezoneForRegion();
      expect(currentTimezone).toBe('Europe/London');
    });

    it('should use current region when AWS_REGION is set to Australia', () => {
      process.env.AWS_REGION = 'ap-southeast-2';
      const currentTimezone = getTimezoneForRegion();
      expect(currentTimezone).toBe('Australia/Melbourne');
    });
  });
});
