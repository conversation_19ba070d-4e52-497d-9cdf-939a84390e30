import type { RegionalOverride } from '@npco/component-dto-test-utils/dist/regionMockProvider';
import { Region, RegionalMockProvider } from '@npco/component-dto-test-utils/dist/regionMockProvider';

interface TimezoneConfig {
  value: string;
}

const defaultConfig: TimezoneConfig = {
  value: 'Australia/Melbourne',
};

const regionOverrides: RegionalOverride<TimezoneConfig> = {
  [Region.UK]: {
    value: 'Europe/London',
  },
};

class TimezoneProvider extends RegionalMockProvider<TimezoneConfig> {
  constructor() {
    super(defaultConfig, regionOverrides);
  }
}

const timezoneProvider = new TimezoneProvider();
export const timezone: string = timezoneProvider.getConfig().value;

export const getTimezoneForRegion = (region?: Region): string => timezoneProvider.getConfig(region).value;
