import type { RegionalOverride } from '@npco/component-dto-test-utils/dist/regionMockProvider';
import { Region, RegionalMockProvider } from '@npco/component-dto-test-utils/dist/regionMockProvider';

import type { SurchargesTaxesSettings } from '../types';

import { taxes } from './mockdata.taxes';

const defaultConfig: SurchargesTaxesSettings = {
  surchargeAllowed: true,
  surchargeEnabled: false,
  surchargeEnabledMoto: false,
  surchargePercent: 0,
  feePercent: 140,
  gstEnabled: false,
  gstPercent: 1000,
  surchargePercentMoto: 0,
  feePercentMoto: 170,
  surchargeFullFees: false,
  surchargeFullFeesMoto: false,
  feesSurchargeCp: {
    surchargePercent: 0,
    feePercent: 140,
    surchargeFullFees: false,
    surchargeEnabled: false,
    feeFixed: 0,
  },
  feesSurchargeMoto: {
    surchargePercent: 0,
    feePercent: 170,
    surchargeFullFees: false,
    surchargeEnabled: false,
    feeFixed: 0,
  },
  feesSurchargeCpoc: {
    surchargePercent: 0,
    feePercent: 140,
    surchargeFullFees: false,
    surchargeEnabled: false,
    feeFixed: 0,
  },
  feesSurchargeXinv: {
    surchargePercent: 0,
    feePercent: 175,
    surchargeFullFees: false,
    surchargeEnabled: false,
    feeFixed: 30,
    surchargePercentIntl: 0,
    feePercentIntl: 290,
    feeFixedIntl: 30,
  },
  feesSurchargeZinv: {
    surchargePercent: 0,
    feePercent: 170,
    surchargeFullFees: false,
    surchargeEnabled: false,
    feeFixed: 25,
    surchargePercentIntl: 0,
    feePercentIntl: 290,
    feeFixedIntl: 25,
  },
  feesSurchargePbl: {
    feeFixed: 25,
    feeFixedIntl: 25,
    feePercent: 175,
    feePercentIntl: 290,
    surchargeEnabled: false,
    surchargeFullFees: false,
    surchargePercent: 0,
    surchargePercentIntl: 0,
  },
  feesSurchargeVt: {
    feePercent: 175,
    feeFixed: 25,
    surchargeEnabled: false,
    surchargeFullFees: false,
    surchargePercent: 0,
  },
  taxes,
};

const regionOverrides: RegionalOverride<SurchargesTaxesSettings> = {
  [Region.UK]: {
    surchargeAllowed: false,
    gstPercent: 2000,
  },
};
class SurchargesTaxesProvider extends RegionalMockProvider<SurchargesTaxesSettings> {
  constructor() {
    super(defaultConfig, regionOverrides);
  }
}

const surchargesTaxesProvider = new SurchargesTaxesProvider();
export const surchargesTaxes: SurchargesTaxesSettings = surchargesTaxesProvider.getConfig();

export const getSurchargesTaxesForRegion = (region?: Region): SurchargesTaxesSettings =>
  surchargesTaxesProvider.getConfig(region);
