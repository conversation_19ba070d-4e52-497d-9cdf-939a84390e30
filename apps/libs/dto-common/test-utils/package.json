{"name": "@npco/component-dto-test-utils", "version": "1.0.1", "description": "Test utils for dto libraries", "main": "dist/index.js", "types": "dist/index.d.ts", "compilerOptions": {"module": "commonjs", "declaration": false, "noImplicitAny": false, "noUnusedLocals": true, "removeComments": true, "noLib": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es6", "sourceMap": true, "allowJs": true, "outDir": "./dist"}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"], "scripts": {"clean": "rm -rf dist", "test": "JEST_DYNAMODB_CONFIG=\"$INIT_CWD/jest-dynamodb-config.js\" yarn run dto:jest --config $INIT_CWD/jest.config.ts", "deploy": "yarn npm publish", "run-audit": "yarn npm audit --environment production", "dto:tsc": "tsc", "dto:jest": "jest", "dto:eslint": "eslint"}, "husky": {"hooks": {"pre-push": "yarn lint && yarn run-audit && yarn test"}}, "repository": {"type": "git", "url": "git+https://bitbucket.org/zeller-dev/component-dto-common.git"}, "keywords": ["dto", "common"], "author": "<PERSON><PERSON>", "license": "ISC", "homepage": "https://bitbucket.org/npco_dev/component-dto-common#readme", "dependencies": {"lodash": "^4.17.21", "uuid": "^9.0.0"}, "devDependencies": {"@npco/eslint-config-backend": "^1.0.8", "@nx/jest": "20.7.0", "@rushstack/eslint-patch": "^1.6.0", "@swc/core": "^1.3.99", "@swc/jest": "^0.2.29", "@types/jest": "^29.5.11", "@types/lodash": "^4.17.5", "@types/node": "18.19.14", "@types/uuid": "^9.0.2", "eslint": "^8.54.0", "jest": "^29.7.0", "jest-html-reporter": "^3.10.2", "jest-junit": "^16.0.0", "jest-sonar": "^0.2.16", "jest-sonar-reporter": "^2.0.0", "typescript": "^5.4.5"}, "jestSonar": {"reportPath": "dist", "reportFile": "test-reporter.xml", "indent": 2}, "prettier": "@npco/eslint-config-backend/prettier"}