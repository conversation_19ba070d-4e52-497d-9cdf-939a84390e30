import type { Address, Icon, ScreeningResult, EntityType, CustomerRole, AddressState } from '@npco/component-dto-core';

import type { Report } from '@onfido/api';

interface CustomerBase {
  id: string;
  defaultEntityUuid?: string;
  email?: string;
  firstname?: string;
  middlename?: string;
  lastname?: string;
  phone?: string;
  address?: CustomerAddress;
  dob?: string;
  type?: EntityType;
  companyTrustName?: string;
  companyProfileData?: CompanyProfileData;
  abn?: string;
  acn?: string;
}

interface CustomerEntityBase extends CustomerBase {
  entityUuid?: string;
  nickname?: string;
  role?: CustomerRole | null;
  director?: boolean;
  secretary?: boolean;
  ceo?: boolean;
  shareholder?: boolean;
  beneficialOwner?: boolean;
  beneficialOwnerAlt?: boolean;
  beneficiary?: boolean;
  partner?: boolean;
  trustee?: boolean;
  settlor?: boolean;
  generalContact?: boolean;
  financialContact?: boolean;
  chair?: boolean;
  treasurer?: boolean;
  governmentRole?: string;
  permissions?: CustomerPermissions;
  sites?: string[]; // Uuids for resolver
  isVisible?: boolean;
}

export interface CustomerSite {
  id: string;
  name: string;
  pin?: string;
  address?: Address;
  refundRequiresPin?: boolean;
  devices?: Device[];
  customers?: Customer[];
}

// 'Customer' specific gql non-input fields
export interface CustomerCore extends CustomerBase {
  icon?: Icon;
  emailVerified?: boolean;
  phoneVerified?: boolean;
  createdTime?: string;
  createdAt?: string;
  productTourStatus?: ProductTourStatus;
  kyc?: KYC;
  kycStatus?: KYCStatus;
  kycCheckPoints?: KycCheckpoint[];
  passportCountry?: string;
  selfieVerificationCheck?: SelfieVerificationCheck;
  passportVisaSubClass?: string;
  hubspotContactId?: string;
  idvAttempts?: DocumentIdvSafeHarbourAttempts;
  idv?: DocumentVerificationStatusResult;
  safeharbour?: SafeHarbourVerificationStatusResult;
  referralCode?: string;
  hasLogin?: boolean;
  screening?: ScreeningResult;
  documents?: DocumentVerificationDetails;
  identityUserId?: string | null;
  mfaEnrolments?: MfaEnrolment[] | null;
}

// 'CustomerEntity' specific gql non-input fields
export interface CustomerEntity extends CustomerCore, CustomerEntityBase {
  marketingModalSeenList?: string;
  banner?: string;
  modal?: string;
  siteCount?: number;
  registeringIndividual?: boolean;
  primaryAccountHolder?: boolean;
  isInvitationPending?: boolean;
  invitedBy?: CustomerInvitedBy;
}

export type Customer = CustomerEntity;

export interface CustomerUpdateInput extends CustomerEntityBase, ProductTourStatus {
  // updateModalSeenListResolver
  marketingModalName?: string;
  assignSites?: string[];
  unassignSites?: string[];
  // 'IdentityService' calls 'updateCustomer()' to update 'phoneVerified' - refactoring is required before removing 'phoneVerified' from the input type
  phoneVerified?: boolean;
  // 'CustomerIconService' calls 'updateCustomer()' to update 'icon' - refactoring is required before removing 'icon' from the input type
  icon?: Icon;
}

export interface CreateCustomerInput extends Omit<CustomerEntityBase, 'id' | 'sites'> {
  createIdentity?: boolean;
  assignSites?: string[];
  invitedBy?: CustomerInvitedBy;
  invitedByZeller?: boolean;
}

export enum MpKycStatus {
  VERIFIED = 'VERIFIED',
  NOT_REQUIRED = 'NOT_REQUIRED',
  REQUIRED = 'REQUIRED',
  RC_REJECTED = 'RC_REJECTED',
  RC_ABANDONED = 'RC_ABANDONED',
  IN_REVIEW = 'IN_REVIEW',
}

export declare type CustomerVerificationResponse = {
  customerUuid: string;
  resultDriversLicence?: CustomerDocumentVerificationResult;
  resultPassport?: CustomerDocumentVerificationResult;
  resultMedicareCard?: CustomerDocumentVerificationResult;
  error?: string;
};

export interface Device {
  id: string;
  name: string;
}

export interface CustomerPermissions {
  allowZellerInvoices?: boolean;
  allowXeroPaymentServices?: boolean;
  allowItemManagement?: boolean;
  allowDiscountManagement?: boolean;
}

export interface PhoneVerifyResponse {
  codeVerified: boolean;
}

export interface PhoneRegisterResponse {
  codeSent: boolean;
  validUntil: number;
}

export interface PhoneRegisterInput {
  deviceUuid?: string;
  phone: string;
  auth0UserId: string;
}

export interface PhoneVerifyInput {
  code: string;
  auth0UserId: string;
}

export interface CustomerPermissionsOutput {
  allowItemManagement?: boolean;
  allowDiscountManagement?: boolean;
}

export enum CustomerMedicareCardColours {
  BLUE = 'BLUE',
  GREEN = 'GREEN',
  YELLOW = 'YELLOW',
}

export enum CustomerDocumentVerificationResult {
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  ERROR = 'ERROR',
  NOT_VERIFIED = 'NOT_VERIFIED',
}

export enum SafeHarbourResult {
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  ERROR = 'ERROR',
}

export enum SafeHarbourEngineType {
  ZELLER = 'ZELLER',
  EQUIFAX = 'EQUIFAX',
}

export enum OnfidoCheckCompletedResult {
  CLEAR = 'CLEAR',
  CONSIDER = 'CONSIDER',
}

export enum OnfidoReportType {
  DOCUMENT = 'DOCUMENT',
  FACIAL_SIMILARITY_PHOTO = 'FACIAL_SIMILARITY_PHOTO',
  FACIAL_SIMILARITY_MOTION = 'FACIAL_SIMILARITY_MOTION',
  KNOWN_FACES = 'KNOWN_FACES',
}

export enum OnfidoReportResult {
  CLEAR = 'CLEAR',
  CONSIDER = 'CONSIDER',
  UNIDENTIFIED = 'UNIDENTIFIED',
  NULL = 'NULL',
}

export enum OnfidoReportDocumentSubResult {
  CLEAR = 'CLEAR',
  CAUTION = 'CAUTION',
  SUSPECTED = 'SUSPECTED',
  REJECTED = 'REJECTED',
  NULL = 'NULL',
}

export enum KycCheckpoint {
  IDV = 'IDV',
  SELFIE_VERIFICATION = 'SELFIE_VERIFICATION',
}

export type OnfidoReportDocument = {
  subResult: OnfidoReportDocumentSubResult;
};

export type OnfidoReportFacialSimilarity = {
  faceComparison: OnfidoReportResult;
  imageIntegrity: OnfidoReportResult;
  visualAuthenticity: OnfidoReportResult;
};

export type OnfidoReportKnownFaces = {
  previouslySeenFaces: OnfidoReportResult;
  imageIntegrity: OnfidoReportResult;
};

export type SelfieVerificationCheck = {
  check?: CustomerUpdatedOnfidoCheck;
  report?: CustomerUpdatedOnfidoReport;
};

export type CustomerUpdatedOnfidoReportDocument = OnfidoReportDocument & {
  reportId: string;
  result: OnfidoReportResult;
  payload: Report;
};

export type CustomerUpdatedOnfidoFacialSimilarity = OnfidoReportFacialSimilarity & {
  reportType: OnfidoReportType;
  reportId: string;
  result: OnfidoReportResult;
  payload: Report;
};

export type CustomerUpdatedOnfidoKnownFaces = OnfidoReportKnownFaces & {
  reportId: string;
  result: OnfidoReportResult;
  payload: Report;
};

export type CustomerUpdatedOnfidoCheck = {
  checkId: string;
  applicantId: string;
  resultsUri: string;
  result: OnfidoCheckCompletedResult;
  createdAt: string;
};

export type CustomerUpdatedOnfidoReport = {
  document?: CustomerUpdatedOnfidoReportDocument;
  facialSimilarity?: CustomerUpdatedOnfidoFacialSimilarity;
  knownFaces?: CustomerUpdatedOnfidoKnownFaces;
};

export enum IdvDocumentType {
  PASSPORT = 'PASSPORT',
  DRIVERS_LICENCE = 'DRIVERS_LICENCE',
  MEDICARE_CARD = 'MEDICARE_CARD',
  SAFE_HARBOUR = 'SAFE_HARBOUR',
}

export enum CompanyProfileData {
  MODIFIED = 'MODIFIED',
  MANUALLY_CREATED = 'MANUALLY_CREATED',
  PRISTINE = 'PRISTINE',
}

export interface CustomerAddress {
  street: string;
  suburb: string;
  state: string;
  postcode: string;
  country: string;
}

export type CustomerInvitedBy = {
  customerUuid: string;
  firstName?: string;
  lastName?: string;
  middleName?: string;
  email?: string;
};
export interface CustomerMarketingDetails {
  bannerPriority1?: string;
  bannerPriority2?: string;
  bannerPriority3?: string;
  modalPriority1?: string;
  modalPriority2?: string;
  modalPriority3?: string;
}

export type DocumentVerificationReferenceType = {
  enquiryId: string;
  type: IdvDocumentType;
  url: string;
};

export type DocumentVerificationRequestedPayload = {
  firstName: string;
  middleName?: string;
  lastName: string;
  address?: Address;
  dob?: string;
  driversLicenceState?: AddressState;
  driversLicenceFirstName?: string;
  driversLicenceMiddleName?: string;
  driversLicenceLastName?: string;
  passportCountry?: string;
  passportFirstName?: string;
  passportMiddleName?: string;
  passportLastName?: string;
  medicareCardColour?: CustomerMedicareCardColours;
  medicareFirstName?: string;
  medicareMiddleName?: string;
  medicareLastName?: string;
  medicareCardPosition?: number;
  medicareCardExpiry?: string;
};

export interface DocumentVerificationDetails extends DocumentVerificationRequestedPayload {
  tokenisedDriversLicenseNumber?: string;
  tokenisedPassportNumber?: string;
  tokenisedMedicareCard?: string;
  resultDriversLicence?: CustomerDocumentVerificationResult;
  resultPassport?: CustomerDocumentVerificationResult;
  resultMedicareCard?: CustomerDocumentVerificationResult;
  driversLicence?: DriversLicenceResponseDetails;
  passport?: PassportResponseDetails;
  medicare?: MedicareResponseDetails;
}

export enum DocumentVerificationResult {
  ACCEPTED = 'ACCEPTED',
  ACCEPTED_ONE_ONLY = 'ACCEPTED_ONE_ONLY',
  REJECTED = 'REJECTED',
  ERROR = 'ERROR',
  ACCEPTED_ONE_PHOTO_ID_ONLY = 'ACCEPTED_ONE_PHOTO_ID_ONLY',
}

export enum DocumentVerificationStatus {
  NOT_REQUIRED = 'NOT_REQUIRED',
  REQUIRED = 'REQUIRED',
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR',
}

export type DocumentVerificationStatusResult = {
  status: DocumentVerificationStatus;
  result?: DocumentVerificationResult;
};

export enum KYCStatus {
  VERIFIED = 'VERIFIED',
  RC_VERIFIED = 'RC_VERIFIED',
  RC_REJECTED = 'RC_REJECTED',
  RC_ABANDONED = 'RC_ABANDONED',
  REVIEW = 'REVIEW',
  REQUIRED = 'REQUIRED',
  NOT_REQUIRED = 'NOT_REQUIRED',
}

export enum KYCDecision {
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  ABANDONED = 'ABANDONED',
  FORCE_REVIEW = 'FORCE_REVIEW',
  FORCE_REQUIRED = 'FORCE_REQUIRED',
  FORCE_NOT_REQUIRED = 'FORCE_NOT_REQUIRED',
}

export type KYC = {
  status: KYCStatus;
  decision?: KYCDecision;
};

export enum CustomerKYCResult {
  VERIFIED = 'VERIFIED',
  IN_REVIEW = 'IN_REVIEW',
}

export type FinaliseCustomerKYCResult = {
  result: CustomerKYCResult;
  uploadDocument: OnboardingUploadDocumentType[];
};

export enum OnboardingUploadDocumentType {
  IDENTITY_DOCUMENT = 'IDENTITY_DOCUMENT',
  PROOF_OF_ADDRESS = 'PROOF_OF_ADDRESS',
  VISA_LABEL = 'VISA_LABEL',
}

export type SafeHarbourReferenceType = {
  enquiryId: string;
  url: string;
};

export enum SafeHarbourVerificationResult {
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  ERROR = 'ERROR',
}

export enum SafeHarbourVerificationStatus {
  NOT_REQUIRED = 'NOT_REQUIRED',
  REQUIRED = 'REQUIRED',
  COMPLETED = 'COMPLETED',
}

export type SafeHarbourVerificationStatusResult = {
  status: SafeHarbourVerificationStatus;
  result?: SafeHarbourVerificationResult;
};

export type ProductTourStatus = {
  showOnboardingShop?: boolean;
  showAdminMerchantPortalWelcome?: boolean;
  showInvoiceInstructions?: boolean;
  showInvoicesWelcome?: boolean;
  showItemsWelcome?: boolean;
  showItemInstructions?: boolean;
  showInvoicesCustomisationWelcome?: boolean;
  showInvoicesScheduleSendWelcome?: boolean;
  showInvoicesSendBySmsWelcome?: boolean;
  showInvoiceSendViaInfo?: boolean;
  showInvoicingCustomisationSettingsWelcome?: boolean;
  showTapToPayInstructions?: boolean;
  showTapToPayMayJune?: boolean;
  showCustomScreensaverPromo?: boolean;
  showSavingsAccountWelcome?: boolean;
  showSavingsAccountMarch?: boolean;
  showSavingsAccountMay?: boolean;
  showCorporateCardsMayOffer?: boolean;
  showCorporateCardsWalkthrough?: boolean;
  showCorporateCardsSettingsWalkthrough?: boolean;
  showNotificationsWelcome?: boolean;
  showCorporateCardsAdminWelcome?: boolean;
  showInvoiceApril?: boolean;
  showCatalogueItemsWelcome?: boolean;
  profileAvatarWalkthrough?: boolean;
  showServiceChargesWelcome?: boolean;
};

export enum MfaEnrolmentType {
  'SMS' = 'SMS',
}

export type MfaEnrolment = {
  type: MfaEnrolmentType;
  name: string;
  createdAt: string;
};

export type DriversLicenceDetails = {
  /**
   * State which issued the Drivers Licence
   */
  state?: AddressState;

  /**
   * Drivers Licence number
   */
  number?: string;

  /**
   * A card number for the drivers licence that gets issued for every new licence
   */
  cardNumber: string;
};

export type PassportDetails = {
  /**
   * Country which issued the Passport
   */
  country?: string;

  /**
   * Passport number
   */
  number?: string;
};

export type MedicareDetails = {
  /**
   * Position of name on Medicare Card
   */
  position?: number;

  /**
   * Medicare Card number
   */
  number?: string;

  /**
   * Middle name on Medicare - in case different to main name
   */
  middleName?: string;

  /**
   * Colour of Medicare Card
   */
  colour?: CustomerMedicareCardColours;

  /**
   * Medicare Card expiry date
   */
  expiry?: string;
};

export enum IdvSafeHarbourDocumentType {
  DRIVING_LICENCE = 'DRIVING_LICENCE',
  PASSPORT = 'PASSPORT',
  MEDICARE_CARD = 'MEDICARE_CARD',
  NO_SECOND_ID = 'NO_SECOND_ID',
}

export enum IdvAndSafeHarbourResultType {
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  ERROR = 'ERROR',
}

export type IdvSafeHarbourAddress = {
  street?: string;
  suburb?: string;
  state?: string;
  postcode?: string;
};

export type DriversLicenceResponseDetails = {
  firstName?: string;
  middleName?: string;
  lastName?: string;
  dob?: string;
  tokenisedNumber?: string;
  state?: string;
  cardNumber?: string;
  result?: IdvAndSafeHarbourResultType;
};

export type PassportResponseDetails = {
  firstName?: string;
  middleName?: string;
  lastName?: string;
  dob?: string;
  tokenisedNumber?: string;
  country?: string;
  visaClass?: string;
  visaSubClass?: string;
  visaDateOfExpiry?: string;
  residenceStatus?: string;
  result?: IdvAndSafeHarbourResultType;
};

export type MedicareResponseDetails = {
  firstName?: string;
  middleName?: string;
  lastName?: string;
  dob?: string;
  tokenisedNumber?: string;
  position?: number;
  colour?: CustomerMedicareCardColours;
  expiry?: string;
  result?: IdvAndSafeHarbourResultType;
};

export type SafeHarbourResponseDetails = {
  overallResult?: IdvAndSafeHarbourResultType;
  addressVerification?: boolean;
  dobVerification?: boolean;
};

export type DocumentVerificationAndSafeHarbourResponse = {
  documentVerification?: IdvAndSafeHarbourResultType;
  safeHabour?: SafeHarbourResponseDetails;
  safeHarbour?: SafeHarbourResponseDetails;
  error?: string;
};

export type DocumentVerificationResponse = {
  documentVerification?: IdvAndSafeHarbourResultType;
  error?: string;
};

export type DocumentIdvSafeHarbourAttempts = {
  passport?: number;
  driversLicence?: number;
  medicareCard?: number;
};

export type DocumentIdvSafeHarbourVerificationResponse = {
  addressVerification?: boolean;
  dobVerification?: boolean;
};

export type SessionDeviceInformationAttributes = {
  /* For Web, it will be extracted from the UserAgent header. eg "iOS", "Mac OS X", "Android", "Windows" or "Linux".
  For mobile SDKs it's constant "Android" or "iOS" */
  OS: string[];

  /* Model of the mobile device. For Web, it will be extracted as it is from UserAgent header (in android only). e.g. SM-G900P (Samsung S5) */
  Model: string[];

  /* Value of "App" for Native Apps. For web, detected browser. eg Chrome Mobile, Chrome, Mobile Safari, Firefox, Safari */
  Browser: string[];
};

export type SessionDeviceInformationIpLocation = {
  /* City from IP */
  city?: string;

  /* Region/State from IP */
  region?: string;

  /* Country from IP */
  country?: string;

  /* latitude from IP */
  latitude?: string;

  /* longitude from IP */
  longitude?: string;
};

export type SessionDeviceInformationGpsLocation = {
  /* City from Gps */
  city?: string;

  /* Region/State from Gps */
  region?: string;

  /* Country from Gps */
  country?: string;

  /* latitude from Gps */
  latitude?: string;

  /* longitude from Gps */
  longitude?: string;
};

export type SessionDeviceInformationHesitationPercentile = {
  /* Fields like Amount which are not in long term memory.
  Fields like Amount, Quantity and sometimes credit card number are not fields that folks normally memorize and hence are put in non long term memory. */
  nonLtm?: number;
  /* Fields like Name, address which are in long term memory. Long term memory are fields like name, address which is usually memorized by the users. */
  ltm?: number;
};

export type SessionDeviceInformationBehaviorBiometricsFormInfo = {
  /* name of the Web form field */
  name?: string;

  /* Number of copy paste events */
  numCopyPasteEvents?: number;

  /* Number of copy paste events */
  numClipboardEvents?: number;

  /* Number of Autfill events */
  numAutoFillEvents?: number;

  /* Number of Expert Key Events */
  numExpertKeyEvents?: number;

  /* hesitation percentage in fields */
  hesitationPercentage?: number;

  /* is field in long term memory */
  isLTM?: boolean;

  /* time spent in MS in that field */
  timeSpendInMsEvents: number[];
};

export type SessionDeviceInformationBehaviorBiometrics = {
  /* hesitation percentile Object */
  hesitationPercentile?: SessionDeviceInformationHesitationPercentile;
  /* Number of Distractions Events */
  numDistractionEvents?: number;
  /* Form Field or App fields like textField */
  fields?: SessionDeviceInformationBehaviorBiometricsFormInfo[];
  /** @beta numContextSwitchEvents is currently in beta and not recommended to be in use */
  numContextSwitchEvents: any;
  /** @beta typingSpeed is currently in beta and not recommended to be in use */
  typingSpeed: any;
};

/** @beta RemoteSoftware is currently in beta and not recommended to be in use */
export type SessionDeviceInformationRemoteSoftware = {
  key?: 'RemoteSoftware';
  value?: any;
};

/** @beta TamperedApp is currently in beta and not recommended to be in use */
export type SessionDeviceInformationTamperedApp = {
  key?: 'TamperedApp';
  value?: any;
};

/** @beta SessionIpCount is currently in beta and not recommended to be in use */
export type SessionDeviceInformationSessionIpCount = {
  key?: 'SessionIpCount';
  value?: any;
};

/** @beta SessionIpCountryCount is currently in beta and not recommended to be in use */
export type SessionDeviceInformationSessionIpCountryCount = {
  key?: 'SessionIpCountryCount';
  value?: any;
};

/** @beta RemoteSessionLevel is currently in beta and not recommended to be in use */
export type SessionDeviceInformationRemoteSessionLevel = {
  key?: 'RemoteSessionLevel';
  value?: any;
};

/* True OS of device e.g. BlueStacks Android Emulator running on Mac will get TrueOS of Mac instead of Android.	*/
export type SessionDeviceInformationSignalsTrueOS = {
  key?: 'TrueOS';
  value?: 'Windows' | 'Linux/Android' | 'Mac/iOS' | 'Unknown';
};

/* Likelihood of OS anomaly between the TrueOS vs OS sent by the device	 */
export type SessionDeviceInformationSignalsOSAnomaly = {
  key?: 'OSAnomaly';
  value?: 'high' | 'medium' | 'low';
};

/* Hours since we have first seen the device.	*/
export type SessionDeviceInformationSignalsOSDeviceAgeHours = {
  key?: 'DeviceAgeHours';
  value?: string;
};

/* True IP Address behind IP masking tools.	*/
export type SessionDeviceInformationSignalsTrueIP = {
  key?: 'TrueIP';
  value?: string;
};

/* The likelihood of the network connection being a VPN	*/
export type SessionDeviceInformationSignalsVPN = {
  key?: 'VPN';
  value?: 'high' | 'medium' | 'low';
};

/* The likelihood of network connection being a Proxy	*/
export type SessionDeviceInformationSignalsProxy = {
  key?: 'Proxy';
  value?: 'high' | 'medium' | 'low';
};

/* Is the device an emulator eg Bluestacks - Android, Selenium - Web */
export type SessionDeviceInformationSignalsEmulator = {
  key?: 'Emulator';
  value?: 'true' | 'false';
};

/* Is the device rooted? (mobile SDK only) */
export type SessionDeviceInformationSignalsRooted = {
  key?: 'Rooted';
  value?: 'true' | 'false';
};

/* Is the device controlled via Remote Software like TeamViewer, AnyDesk, etc */
export type SessionDeviceInformationSignalsRemoteSoftwareLevel = {
  key?: 'RemoteSoftwareLevel';
  value?: 'high' | 'medium' | 'low';
};

/* Ip address type */
export type SessionDeviceInformationSignalsIpType = {
  key?: 'IpType';
  value?:
    | 'Commercial'
    | 'Organization'
    | 'Government'
    | 'Military'
    | 'Education'
    | 'Library'
    | 'Fixed Line ISP'
    | 'Mobile ISP'
    | 'Data Center'
    | 'Fixed Line ISP / Mobile ISP'
    | 'Invalid IP'
    | 'Unknown';
};

/* Same User or Different User from Initial Session, after model is trained by at least 3 sessions from the user */
export type SessionDeviceInformationSignalsSameUserScore = {
  key?: 'SameUserScore';
  value?: string;
};

/* Account level device ID that is resistant to minor changes; fingerprint at the user account level to help with login anomalies */
export type SessionDeviceInformationSignalsAccountDeviceId = {
  key?: 'AccountDeviceId';
  value?: string;
};

/* Riskiness from behavior */
export type SessionDeviceInformationSignalsBehaviorBiometricLevel = {
  key?: 'behaviorBiometricLevel';
  value?: 'high' | 'medium' | 'low';
};

export type SessionDeviceInformation = {
  /* Device ID. Also known as HardID or ExactID. This is a cookie that is placed on the customer's browser. */
  id?: string;

  /* Riskiness of the session: */
  level?: 'very_high' | 'high' | 'medium' | 'medium_low' | 'low';

  /* Fingerprint of the device at a global level, also known as SoftID or FuzzyID
  based on a hash of different attributes on the device. There can be overlap, hence the related fingerprintConfidenceScore. */
  fingerprint?: string;

  /*   confidence of fingerprint being unique from 0 to 100 */
  fingerprintConfidenceScore?: number;

  /* Reputation of the device across our network (eg if we have seen any fraud report for this device) */
  deviceReputation?: 'very_high_risk' | 'high_risk' | 'medium_risk' | 'low_medium_risk' | 'low_risk' | 'unknown';

  /* Attributes of the device like OS, Model & Browser */
  attributes?: SessionDeviceInformationAttributes;

  signals?: Array<
    | SessionDeviceInformationSignalsTrueOS
    | SessionDeviceInformationSignalsOSAnomaly
    | SessionDeviceInformationSignalsOSDeviceAgeHours
    | SessionDeviceInformationSignalsTrueIP
    | SessionDeviceInformationSignalsVPN
    | SessionDeviceInformationSignalsProxy
    | SessionDeviceInformationSignalsEmulator
    | SessionDeviceInformationSignalsRooted
    | SessionDeviceInformationSignalsRemoteSoftwareLevel
    | SessionDeviceInformationSignalsIpType
    | SessionDeviceInformationSignalsSameUserScore
    | SessionDeviceInformationSignalsAccountDeviceId
    | SessionDeviceInformationSignalsBehaviorBiometricLevel
    | SessionDeviceInformationRemoteSoftware
    | SessionDeviceInformationTamperedApp
    | SessionDeviceInformationSessionIpCount
    | SessionDeviceInformationSessionIpCountryCount
    | SessionDeviceInformationRemoteSessionLevel
  >;

  /* Session Key */
  sessionKey?: string;

  /* Location data got from IP Address */
  ipLocation?: SessionDeviceInformationIpLocation;

  /* Location data got from GPS coordinate (available only if your application has user's permission to collect user's location) */
  gpsLocation?: SessionDeviceInformationGpsLocation;

  /* Behavior biometric data Signals */
  behaviorBiometrics?: SessionDeviceInformationBehaviorBiometrics;

  /* Result of rule evaluations for each checkpoint. device checkpoint is executed by default. */
  checkpoints?: any;

  /** @beta ipAddresses is currently in beta and not recommended to be in use */
  ipAddresses?: any;

  /** @beta pageFieldLTM is currently in beta and not recommended to be in use */
  pageFieldLTM?: any;

  /** @beta pageFieldNonLTM is currently in beta and not recommended to be in use */
  pageFieldNonLTM?: any;

  /* This can be shown if there are any errors from the response e.g. no device found for the given session xyz */
  message?: any;
};

export type CustomerDocumentVerificationV2RequestDto = {
  customerUuid: string;
  /**
   * UUIDv4 for the Entity the Customer belongs to
   * (now it's optional due to multi-entity requirement)
   */
  entityUuid?: string;
  /**
   * First name
   */
  firstName: string;
  /**
   * Optional middle name
   */
  middleName?: string;
  /**
   * Last name
   */
  lastName: string;
  /**
   * Address
   */
  address?: IdvSafeHarbourAddress;
  /**
   * Date of Birth
   */
  dob?: string;
  /**
   * The Document type being passed through as well as safe harbour
   */
  documentType?: IdvSafeHarbourDocumentType;
  /**
   * Empty String for NO_SECOND_ID
   */
  document?: string;

  /**
   * MAC to check the integrity of document being passed
   */
  documentMac?: string;
};

export enum BioMetricsReviewStatus {
  TRUE_POSITIVE_FACE_ID_FACE_MATCH = 'True Positive(Face ID, Face Match)',
  TRUE_POSITIVE_FAKE_ID_SPOOFED_SELFIE = 'True Positive (Fake ID, Spoofed selfie)',
  TRUE_POSITIVE_FAKE_ID_FACE_MISMATCH = 'True Positive (Fake ID, Face mismatch)',
  TRUE_POSITIVE_STOLEN_ID_FACE_MISMATCH = 'True Positive (Stolen ID, Face Mismatch)',
  TRUE_POSITIVE_FACE_MISMATCH_GENUINE_ID = 'True Positive (Face mismatch, Genuine ID of Related Party)',
}
