import type { CustomerRole, Status } from '@npco/component-dto-core';

import type { CompanyProfileData, CustomerInvitedBy, CustomerPermissions } from './types';

export abstract class CustomerEntityBaseEventDto {
  customerUuid: string;

  entityUuid: string;

  /**
   * Hints at the actions a user took while editing company data during on-boarding
   * MODIFIED = User changed an existing record
   * MANUALLY_CREATED = User added a new record
   * PRISTINE = User did not modify the company record
   */
  companyProfileData?: CompanyProfileData;

  role?: CustomerRole;

  director?: boolean;

  secretary?: boolean;

  ceo?: boolean;

  beneficialOwner?: boolean;

  beneficialOwnerAlt?: boolean;

  beneficiary?: boolean;

  partner?: boolean;

  trustee?: boolean;

  settlor?: boolean;

  generalContact?: boolean;

  financialContact?: boolean;

  registeringIndividual?: boolean;

  shareholder?: boolean;

  isInvitationPending?: boolean;

  invitedBy?: CustomerInvitedBy;

  chair?: boolean;

  treasurer?: boolean;

  governmentRole?: string;

  primaryAccountHolder?: boolean;

  status?: Status;

  permissions?: CustomerPermissions;

  isVisible?: boolean;

  sites?: string[];

  isPartOfBulkInvite?: boolean;

  constructor(params: CustomerEntityBaseEventDto) {
    this.customerUuid = params.customerUuid;
    this.entityUuid = params.entityUuid;
    this.role = params.role;
    this.registeringIndividual = params.registeringIndividual;
    this.isInvitationPending = params.isInvitationPending;
    this.invitedBy = params.invitedBy;
    this.status = params.status;
    this.director = params.director;
    this.secretary = params.secretary;
    this.ceo = params.ceo;
    this.shareholder = params.shareholder;
    this.beneficialOwner = params.beneficialOwner;
    this.beneficialOwnerAlt = params.beneficialOwnerAlt;
    this.beneficiary = params.beneficiary;
    this.partner = params.partner;
    this.trustee = params.trustee;
    this.settlor = params.settlor;
    this.generalContact = params.generalContact;
    this.financialContact = params.financialContact;
    this.chair = params.chair;
    this.treasurer = params.treasurer;
    this.governmentRole = params.governmentRole;
    this.companyProfileData = params.companyProfileData;
    this.primaryAccountHolder = params.primaryAccountHolder;
    this.permissions = params.permissions;
    this.isVisible = params.isVisible;
    this.sites = params.sites;
    this.isPartOfBulkInvite = params.isPartOfBulkInvite;
  }
}
