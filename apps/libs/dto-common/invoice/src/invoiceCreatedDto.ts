import type {
  InvoiceActivity,
  InvoiceCustomer,
  InvoiceDiscount,
  InvoiceEmail,
  InvoiceItem,
  InvoiceSendSchedule,
  InvoiceSite,
  InvoiceStatus,
  InvoiceSMS,
  InvoiceDiscountDetail,
  InvoiceServiceChargeDetail,
  InvoiceAmounts,
} from './types';

export class InvoiceCreatedEventDto {
  id: string;

  entityUuid: string;

  entityShortId: string;

  referenceNumber: string;

  status: InvoiceStatus;

  title?: string;

  message?: string;

  siteSettings?: InvoiceSite;

  startDate?: string;

  dueDate?: string;

  discount?: InvoiceDiscount;

  email?: InvoiceEmail;

  includeCalculation?: boolean;

  subtotalAmount?: number;

  totalAmount?: number;

  totalSurcharge?: number;

  totalDiscount?: number;

  totalServiceCharge?: number;

  totalGst?: number;

  amounts?: InvoiceAmounts;

  items?: InvoiceItem[];

  customer?: InvoiceCustomer;

  itemsApplyTax?: boolean;

  itemsTaxInclusive?: boolean;

  activities?: InvoiceActivity[];

  dueAmount?: number;

  paidAmount?: number;

  paymentLink?: string;

  sentTime?: number;

  paidTime?: number;

  updatedTime?: number;

  createdTime?: number;

  notes?: string;

  payerContactName?: string;

  payerContactUuid?: string;

  sms?: InvoiceSMS;

  requiredEmailUpdateBeforeSend?: string[];

  requiredPhoneUpdateBeforeSend?: string[];

  sendSchedule?: InvoiceSendSchedule;

  senderCustomerUuid?: string;

  discounts?: InvoiceDiscountDetail[];

  serviceCharges?: InvoiceServiceChargeDetail[];

  constructor(params: InvoiceCreatedEventDto) {
    this.id = params.id;
    this.entityUuid = params.entityUuid;
    this.entityShortId = params.entityShortId;
    this.status = params.status;
    this.referenceNumber = params.referenceNumber;
    this.title = params.title;
    this.message = params.message;
    this.siteSettings = params.siteSettings;
    this.startDate = params.startDate;
    this.dueDate = params.dueDate;
    this.discount = params.discount;
    this.email = params.email;
    this.includeCalculation = params.includeCalculation;
    this.subtotalAmount = params.subtotalAmount;
    this.totalAmount = params.totalAmount;
    this.totalSurcharge = params.totalSurcharge;
    this.totalDiscount = params.totalDiscount;
    this.totalServiceCharge = params.totalServiceCharge;
    this.totalGst = params.totalGst;
    this.amounts = params.amounts;
    this.items = params.items;
    this.customer = params.customer;
    this.itemsApplyTax = params.itemsApplyTax;
    this.itemsTaxInclusive = params.itemsTaxInclusive;
    this.activities = params.activities;
    this.dueAmount = params.dueAmount;
    this.paidAmount = params.paidAmount;
    this.paymentLink = params.paymentLink;
    this.sentTime = params.sentTime;
    this.paidTime = params.paidTime;
    this.updatedTime = params.updatedTime;
    this.createdTime = params.createdTime;
    this.notes = params.notes;
    this.payerContactName = params.payerContactName;
    this.payerContactUuid = params.payerContactUuid;
    this.sendSchedule = params.sendSchedule;
    this.sms = params.sms;
    this.requiredEmailUpdateBeforeSend = params.requiredEmailUpdateBeforeSend;
    this.requiredPhoneUpdateBeforeSend = params.requiredPhoneUpdateBeforeSend;
    this.senderCustomerUuid = params.senderCustomerUuid;
    this.discounts = params.discounts;
    this.serviceCharges = params.serviceCharges;
  }
}
