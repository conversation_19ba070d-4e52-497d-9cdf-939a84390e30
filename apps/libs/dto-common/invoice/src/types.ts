import type { ContactBaseEventDto } from '@npco/component-dto-addressbook';
import type { CatalogUnit, Item, CatalogModifier as CatalogItemModifier } from '@npco/component-dto-catalog';
import type { SiteUpdatedEventDto, SurchargesTaxesSettings, ReceiptSettings } from '@npco/component-dto-site';

export enum InvoiceStatus {
  DRAFT = 'DRAFT',
  SENT = 'SENT',
  PART_PAID = 'PART_PAID',
  PAID = 'PAID',
  OVERDUE = 'OVERDUE',
  CANCELLED = 'CANCELLED',
  ACTIVE = 'ACTIVE',
  ENDED = 'ENDED',
  SCHEDULED = 'SCHEDULED',
  DELETED = 'DELETED',
  SEND_FAILED = 'SEND_FAILED', // deprecated, use ERROR
  ERROR = 'ERROR',
}

export enum InvoiceMilestoneStatus {
  NULL = 'NULL',
  PAID = 'PAID',
  OVERDUE = 'OVERDUE',
  CANCELLED = 'CANCELLED',
}

export enum InvoiceDiscountConfig {
  PERCENTAGE = 'PERCENTAGE',
  AMOUNT = 'AMOUNT',
}

export enum InvoiceItemUnit {
  QUANTITY = 'QUANTITY',
  HOUR = 'HOUR',
  DAY = 'DAY',
}

export enum InvoiceMilestoneType {
  DEPOSIT = 'DEPOSIT',
  MILESTONE = 'MILESTONE',
  FINAL = 'FINAL',
}

export type InvoiceEmailRecipients = {
  recipient: string;
  cc?: string[];
  bcc?: string[];
  sendMeCopy?: boolean;
};

export type InvoiceEmail = {
  subject?: string;
  body?: string;
  recipients?: InvoiceEmailRecipients;
  enabled?: boolean;
};

export type InvoiceSMS = {
  enabled: boolean;
  payerContactPhoneNumber: string;
};

export enum InvoiceDiscountType {
  BASIC = 'BASIC',
  AUTOMATIC = 'AUTOMATIC',
}

export type InvoiceDiscountDetail = {
  id: string;
  catalogDiscountUuid?: string;
  name?: string;
  type?: InvoiceDiscountType;
  discountedAmount: string;
  ordinal: number;
  config: InvoiceDiscountConfig;
  value: string;
  catalogDiscount: any;
  quantity?: number;
};

export type InvoiceDiscount = {
  config: InvoiceDiscountConfig;
  value: number;
  quantity?: number;
};

export type InvoiceTax = {
  enabled: boolean;
  name: string;
  percent: number;
};

export enum EntityChangeStatus {
  UPDATED = 'UPDATED',
  DELETED = 'DELETED',
  NO_CHANGE = 'NO_CHANGE',
  UNLINKED = 'UNLINKED',
}

export type EntityChangeTracker = {
  lastRevisionTime?: string;
  updatedTime?: string;
  status: EntityChangeStatus;
};

export type CatalogItemReference = Partial<Omit<Item, 'status'>> &
  EntityChangeTracker & { itemType?: InvoiceItemType; parentName?: string };

type Contact = ContactBaseEventDto;
export type ContactReference = Partial<Contact> & EntityChangeTracker;

export enum InvoiceItemType {
  SINGLE = 'SINGLE',
  VARIANT = 'VARIANT',
  MODIFIER = 'MODIFIER',
  ONE_TIME = 'ONE_TIME',
  PARENT = 'PARENT',
}

export type CreateInvoiceItemModifierInput = {
  id?: string;
  catalogModifierUuid: string; // undefined if one-time modifier
  catalogModifierSetUuid: string;
  ordinal: number;
  quantity: number;
};

export type InvoiceItemModifier = {
  id: string;
  catalogModifierUuid: string;
  catalogModifierSetUuid: string;
  catalogModifierSet: InvoiceCatalogModifierSet;
  name: string;
  price: string;
  ordinal: number;
  description?: string;
  unit: CatalogUnit;
  quantity: number;
  subtotalAmount?: string;
};

export type InvoiceCatalogModifierSet = {
  id: string;
  entityUuid: string;
  invoicesEnabled: boolean;
  selectionRequired: boolean;
  name: string;
  modifiers: InvoiceCatalogModifier[];
  status: EntityChangeStatus;
  lastRevisionTime: string;
};

export type InvoiceCatalogModifier = {
  id: string;
  name: string;
  price: string;
  ordinal: number;
  description?: string;
};

export type InvoiceCustomerInput = {
  payerContactUuid?: string;
  payerContact?: {
    contactUuid?: string;
  };
  attentionContact?: {
    contactUuid?: string;
  };
  attentionContactUuid: string;
  payerEmail: string;
  payerContactStatus?: EntityChangeStatus;
  attentionContactStatus?: EntityChangeStatus;
};

export type GetCatelogReferenceInput = CreateInvoiceInput | UpdateInvoiceInput | Invoice;

export type CreateInvoiceDiscountInput = {
  id?: string;
  catalogDiscountUuid?: string; // undefined if one-time discount
  catalogDiscount?: any;
  name?: string;
  type?: InvoiceDiscountType;
  config: InvoiceDiscountConfig;
  value: string;
  ordinal: number;
  discountedAmount?: string;
  quantity?: number;
};

export type InvoiceItemInput = InvoiceItem & {
  modifiers?: CreateInvoiceItemModifierInput[];
};

export type InvoiceMilestoneInput = {
  type: InvoiceMilestoneType;
  status: InvoiceMilestoneStatus;
  startDate: string;
  dueDate: string;
  amount: number;
  description?: string;
};

export type CreateInvoiceInput = {
  entityUuid: string;
  entityShortId: string;
  customer?: InvoiceCustomerInput;
  title?: string;
  message?: string;
  notes?: string;
  startDate?: string;
  dueDate?: string;
  items?: InvoiceItemInput[];
  discount?: InvoiceDiscount;
  discounts?: CreateInvoiceDiscountInput[];
  serviceCharges?: CreateInvoiceServiceChargeInput[];
  emailRecipients?: InvoiceEmailRecipients;
  itemsApplyTax?: boolean;
  itemsTaxInclusive?: boolean;
  milestones?: InvoiceMilestoneInput[];
  email?: InvoiceEmail;
  sendSchedule?: InvoiceSendSchedule;
  sms?: InvoiceSMS;
};

export type UpdateInvoiceInput = {
  id: string; // auto generated ID, not used for query
  entityUuid: string;
  customerUuid?: string;
  referenceNumber: string;
} & CreateInvoiceInput;

export type InvoiceItem = {
  id: string;
  catalogItemUuid?: string;
  entityUuid: string;
  name: string;
  orderIndex: number;
  price?: number;
  type?: InvoiceItemType;
  priceWithGst?: number;
  discountAmount?: number;
  serviceChargeAmount?: number;
  description?: string;
  unit: InvoiceItemUnit;
  quantity: number;
  discount?: InvoiceDiscount;
  discounts?: InvoiceDiscountDetail[];
  serviceCharges?: InvoiceServiceChargeDetail[];
  modifiers?: InvoiceItemModifier[];
  reportingCategoryUuid?: string;
  taxes?: InvoiceTax[];
  updatedTime?: string;
  catalogItem?: any;
  itemLastUsedTime?: string;
  parentId?: string;
};

export type InvoiceMilestone = {
  id: string;
  entityUuid: string;
  type: InvoiceMilestoneType;
  status: InvoiceMilestoneStatus;
  startDate: string;
  dueDate: string;
  amount: number;
  description: string;
};

export enum InvoiceActivityStatus {
  COMPLETED = 'COMPLETED',
  SCHEDULED = 'SCHEDULED',
  SKIPPED = 'SKIPPED',
  FAILED = 'FAILED',
}
export enum InvoiceAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  CANCELLED = 'CANCELLED',
  PAYMENT_DUE = 'PAYMENT_DUE',
  SEND_INVOICE = 'SEND_INVOICE',
  RE_SEND_INVOICE = 'RE_SEND_INVOICE',
  SEND_AUTO_REMINDER = 'SEND_AUTO_REMINDER',
  SEND_MANUAL_REMINDER = 'SEND_MANUAL_REMINDER',
  MANUAL_PAYMENT = 'MANUAL_PAYMENT',
  CNP_PAYMENT = 'CNP_PAYMENT',
  SEND_SCHEDULED_INVOICE = 'SEND_SCHEDULED_INVOICE',
  SEND_INVOICE_FAILED = 'SEND_INVOICE_FAILED',
  SEND_SMS_INVOICE = 'SEND_SMS_INVOICE',
  SEND_EMAIL_INVOICE = 'SEND_EMAIL_INVOICE',
}

export type InvoiceDeliveryResult = {
  failed: boolean;
  reason?: string;
  confirmationDateISO?: string;
};

export type InvoiceActivity = {
  id: string;

  entityUuid: string;

  invoiceReferenceNumber: string;

  contactUuid?: string;

  editorCustomerUuid?: string;

  contactName?: string;

  status: InvoiceActivityStatus;

  type: InvoiceAction;

  title: string;

  completedTime?: string;

  balance?: number;

  paidAmount?: number;

  cnpTxnRefNum?: string;

  failureReason?: string;

  dueDate?: string;

  lastRetryTime?: string;

  executionAttempts?: number;

  reminderIndex?: string;

  invoiceEmailDeliveryResult?: InvoiceDeliveryResult;

  invoiceSmsDeliveryResult?: InvoiceDeliveryResult;

  invoiceEmailTrackingId?: string;

  invoiceSmsTrackingId?: string;
};

export type InvoiceCustomer = {
  id: string;
  entityUuid: string;
  invoiceStatus: InvoiceStatus;
  payerContact: ContactReference;
  attentionContact?: ContactReference;
  payerEmail: string;
  // added for support of contact search
  payerContactUuid: string;
  attentionContactUuid?: string;
};

export type InvoiceSiteSettings = {
  name?: string;
  discountsEnabled: boolean;
  shippingInformationEnabled: boolean;
  surchargesTaxes: SurchargesTaxesSettings;
  receipt: ReceiptSettings;
};

export type InvoiceSite = SiteUpdatedEventDto;

export type InvoiceSendSchedule = {
  enabled: boolean;
  sendDate: string;
};

export type InvoiceAmounts = {
  paidAmount?: number;
  subtotalAmount?: number;
  dueAmount?: number;
  totalAmount?: number;
  totalSurcharge?: number;
  totalDiscount?: number;
  totalServiceCharge?: number;
  totalGst?: number;
};

export type Invoice = {
  id: string;
  entityUuid: string;
  entityShortId: string;
  status: InvoiceStatus;
  referenceNumber: string;
  customer?: InvoiceCustomer;
  title?: string;
  message?: string;
  siteSettings?: InvoiceSiteSettings;
  startDate?: string;
  dueDate?: string;
  items?: InvoiceItem[];
  milestones?: InvoiceMilestone[];
  discount?: InvoiceDiscount;
  discounts?: InvoiceDiscountDetail[];
  serviceCharges?: InvoiceServiceChargeDetail[];
  email?: InvoiceEmail;
  activities?: InvoiceActivity[];
  notes?: string;
  paidAmount?: number; // deprecated due to uk currency refactor
  subtotalAmount?: number; // deprecated due to uk currency refactor
  dueAmount?: number; // deprecated due to uk currency refactor
  totalAmount?: number; // deprecated due to uk currency refactor
  totalSurcharge?: number; // deprecated due to uk currency refactor
  totalDiscount?: number; // deprecated due to uk currency refactor
  totalServiceCharge?: number; // deprecated due to uk currency refactor
  totalGst?: number; // deprecated due to uk currency refactor
  amounts?: InvoiceAmounts;
  createdTime?: number;
  issuedTime?: number;
  sentTime?: number;
  paidTime?: number;
  updatedTime?: number;
  payerContactName?: string;
  itemsApplyTax: boolean;
  itemsTaxInclusive: boolean;
  paymentLink?: string;
  totalSurchargedGst?: number;
  sendEmailCopyTo?: string;
  downloadUrl?: string;
  sendSchedule?: InvoiceSendSchedule;
  sms?: InvoiceSMS;
  requiredEmailUpdateBeforeSend?: string[];
  requiredPhoneUpdateBeforeSend?: string[];
  senderCustomerUuid?: string;
};

export enum InvoicePaymentType {
  MANUAL = 'MANUAL',
  CNP = 'CNP',
}

export type InvoicePaymentAmounts = {
  amount: number;
  surchargedAmount?: number;
  surchargedGst?: number;
};

export type InvoicePayment = {
  id: string;

  entityUuid: string;

  invoiceId: string;

  amount: number; // deprecated due to uk currency refactor

  type: InvoicePaymentType;

  paymentTimeISO: string;

  notes?: string;

  localPaymentTime?: Date;

  surchargedAmount?: number; // deprecated due to uk currency refactor

  surchargedGst?: number; // deprecated due to uk currency refactor

  rrn?: string;

  transactionUuid?: string;

  entityShortId?: string;

  amounts?: InvoicePaymentAmounts;
};

export type InvoiceBatchObject = {
  invoice: Invoice;
  shouldUpdateInvoiceCore?: boolean;
  shouldUpdateInvoiceCustomer?: boolean;
  shouldUpdateInvoiceActivities?: boolean;
  shouldUpdateInvoiceItems?: boolean;
};

export enum ItemStatus {
  ACTIVE = 'ACTIVE',
  DELETED = 'DELETED',
}

export type CatalogModifier = CatalogItemModifier;

export interface ModifierSetSiteSettings {
  id: string;
  entityUuid: string;
  catalogModifierSetUuid: string;
  siteUuid: string;
}

// service charge
export enum InvoiceServiceChargeConfig {
  PERCENTAGE = 'PERCENTAGE',
  AMOUNT = 'AMOUNT',
}

export enum InvoiceServiceChargeType {
  BASIC = 'BASIC',
  AUTOMATIC = 'AUTOMATIC',
}

export type InvoiceServiceChargeDetail = {
  id: string;
  catalogServiceChargeUuid?: string;
  name?: string;
  type?: InvoiceServiceChargeType;
  serviceChargeAmount: string;
  ordinal: number;
  config: InvoiceServiceChargeConfig;
  value: string;
  catalogServiceCharge: any;
  quantity?: number;
};

export type InvoiceServiceCharge = {
  config: InvoiceServiceChargeConfig;
  value: number;
  quantity?: number;
};

export type CreateInvoiceServiceChargeInput = {
  id?: string;
  catalogServiceChargeUuid?: string;
  catalogServiceCharge?: any;
  name?: string;
  type?: InvoiceServiceChargeType;
  config: InvoiceServiceChargeConfig;
  value: string;
  ordinal: number;
  serviceChargeAmount?: string;
  quantity?: number;
};
