import type { EntityType, Address, Money } from '@npco/component-dto-core';

export enum LabelType {
  PERSON = 'PERSON',
  BUSINESS = 'BUSINESS',
}

export enum OnboardingStatus {
  NONE = 'NONE',
  PHONE_COMPLETE = 'PHONE_COMPLETE',
  ENTITY_ESTABLISHED = 'ENTITY_ESTABLISHED',
  ENTITY_ADDRESS1 = 'ENTITY_ADDRESS1',
  ENTITY_ADDRESS2 = 'ENTITY_ADDRESS2',
  ENTITY_REVENUE = 'ENTITY_REVENUE',
  ENTITY_CATEGORY = 'ENTITY_CATEGORY',
  TRADING_NAME_ESTABLISHED = 'TRADING_NAME_ESTABLISHED',
  IDV_REQUIRED = 'IDV_REQUIRED',
  IDV_COMPLETE = 'IDV_COMPLETE',
  DIRECTORS_ESTABLISHED = 'DIRECTORS_ESTABLISHED',
  BOS_ESTABLISHED = 'BOS_ESTABLISHED',
  ALT_BOS_ESTABLISHED = 'ALT_BOS_ESTABLISHED',
  MORE_INFO_COLLECTED = 'MORE_INFO_COLLECTED',
  BUSINESS_REG_COLLECTED = 'BUSINESS_REG_COLLECTED',
  PARTNERS_ESTABLISHED = 'PARTNERS_ESTABLISHED',
  DOC_UPLOADED = 'DOC_UPLOADED',
  SETTLORS_ESTABLISHED = 'SETTLORS_ESTABLISHED',
  BEN_ESTABLISHED = 'BEN_ESTABLISHED', // Beneficiaries established
  TRUSTEES_ESTABLISHED = 'TRUSTEES_ESTABLISHED',
  CHAIR_ESTABLISHED = 'CHAIR_ESTABLISHED',
  SECRETARY_ESTABLISHED = 'SECRETARY_ESTABLISHED',
  TREASURE_ESTABLISHED = 'TREASURE_ESTABLISHED',
  GOVERNMENT_ROLE_ESTABLISHED = 'GOVERNMENT_ROLE_ESTABLISHED',
  FINALISING_ONBOARDING = 'FINALISING_ONBOARDING',
  ONBOARDED = 'ONBOARDED',
  REVIEW = 'REVIEW',
  RC_ONBOARDED = 'RC_ONBOARDED',
  RC_REJECTED = 'RC_REJECTED',
  RC_ABANDONED = 'RC_ABANDONED',
  RC_DEPLATFORMED = 'RC_DEPLATFORMED',
  RC_REVIEW = 'RC_REVIEW',
  BV_COMPLETE = 'BV_COMPLETE',
  BV_ERROR = 'BV_ERROR',
}

export const preOnboardingStatus = [
  OnboardingStatus.NONE,
  OnboardingStatus.PHONE_COMPLETE,
  OnboardingStatus.ENTITY_ESTABLISHED,
  OnboardingStatus.ENTITY_ADDRESS1,
  OnboardingStatus.ENTITY_ADDRESS2,
  OnboardingStatus.ENTITY_REVENUE,
  OnboardingStatus.ENTITY_CATEGORY,
  OnboardingStatus.TRADING_NAME_ESTABLISHED,
  OnboardingStatus.IDV_REQUIRED,
  OnboardingStatus.IDV_COMPLETE,
  OnboardingStatus.DIRECTORS_ESTABLISHED,
  OnboardingStatus.BOS_ESTABLISHED,
  OnboardingStatus.ALT_BOS_ESTABLISHED,
  OnboardingStatus.MORE_INFO_COLLECTED,
  OnboardingStatus.BUSINESS_REG_COLLECTED,
  OnboardingStatus.PARTNERS_ESTABLISHED,
  OnboardingStatus.DOC_UPLOADED,
  OnboardingStatus.SETTLORS_ESTABLISHED,
  OnboardingStatus.BEN_ESTABLISHED,
  OnboardingStatus.TRUSTEES_ESTABLISHED,
  OnboardingStatus.CHAIR_ESTABLISHED,
  OnboardingStatus.SECRETARY_ESTABLISHED,
  OnboardingStatus.TREASURE_ESTABLISHED,
  OnboardingStatus.GOVERNMENT_ROLE_ESTABLISHED,
  OnboardingStatus.BV_COMPLETE,
  OnboardingStatus.BV_ERROR,
  OnboardingStatus.FINALISING_ONBOARDING,
];

export const postOnboardingStatus = [
  OnboardingStatus.ONBOARDED,
  OnboardingStatus.REVIEW,
  OnboardingStatus.RC_ONBOARDED,
  OnboardingStatus.RC_REJECTED,
  OnboardingStatus.RC_ABANDONED,
  OnboardingStatus.RC_DEPLATFORMED,
  OnboardingStatus.RC_REVIEW,
];

export type AccountStatus = {
  canAcquire: boolean;
  canAcquireMoto: boolean;
  canAcquireCnp: boolean;
  canAcquireMobile: boolean;
  canAcquireVt: boolean;
  canAcquireAmex: boolean;
  canCreateAccount: boolean;
  canCreateCard: boolean;
  canPayByCard: boolean;
  canRefund: boolean;
  canSettle: boolean;
  canUpdateSettlementAccount: boolean;
  canStandIn: boolean;
  canTransferIn: boolean;
  canTransferOut: boolean;
  hasChargeback: boolean;
  hadForcedRefund: boolean;
  hasDirectDebitRequest: boolean;
  hadDirectDebitFailure: boolean;
};

/* Entity category group / category */

export enum CategoryGroup {
  BEAUTY = 'BEAUTY',
  EDUCATION = 'EDUCATION',
  CHARITIES = 'CHARITIES',
  FOODDRINK = 'FOODDRINK',
  HEALTHCAREFITNESS = 'HEALTHCAREFITNESS',
  HOMEMAINTENANCE = 'HOMEMAINTENANCE',
  LEISUREENTERTAINMENT = 'LEISUREENTERTAINMENT',
  PROFESSIONALSERVICES = 'PROFESSIONALSERVICES',
  RETAIL = 'RETAIL',
  TRANSPORTATION = 'TRANSPORTATION',
  TRAVEL = 'TRAVEL',
  GOVERNMENTSERVICES = 'GOVERNMENTSERVICES',
  FINANCIALSERVICES = 'FINANCIALSERVICES',
}

export enum Category {
  OTHER = 'OTHER',
  BEAUTYSALON = 'BEAUTYSALON',
  HAIRSALON = 'HAIRSALON',
  BARBERSHOP = 'BARBERSHOP',
  MASSAGETHERAPIST = 'MASSAGETHERAPIST',
  NAILSALON = 'NAILSALON',
  TATTOOPIERCING = 'TATTOOPIERCING',
  HEALTHBEAUTYSPA = 'HEALTHBEAUTYSPA',
  MASSAGEPARLOUR = 'MASSAGEPARLOUR',
  CHILDCARE = 'CHILDCARE',
  TEACHER = 'TEACHER',
  TUTOR = 'TUTOR',
  SCHOOL = 'SCHOOL',
  UNIVERSITY = 'UNIVERSITY',
  CHARITY = 'CHARITY',
  MEMBERSHIPORG = 'MEMBERSHIPORG',
  POLITICALORG = 'POLITICALORG',
  RELIGIOUSORG = 'RELIGIOUSORG',
  BAKERY = 'BAKERY',
  BARCLUB = 'BARCLUB',
  CATERING = 'CATERING',
  COFFEE = 'COFFEE',
  FOODTRUCKCART = 'FOODTRUCKCART',
  GROCERY = 'GROCERY',
  MARKET = 'MARKET',
  PRIVATECHEF = 'PRIVATECHEF',
  TAKEAWAYRESTAURANT = 'TAKEAWAYRESTAURANT',
  TABLESERVICERESTAURANT = 'TABLESERVICERESTAURANT',
  WHOLESALEVENDOR = 'WHOLESALEVENDOR',
  ALCOHOLWHOLESALER = 'ALCOHOLWHOLESALER',
  ACUPUNCTURE = 'ACUPUNCTURE',
  CAREGIVER = 'CAREGIVER',
  CHIROPRACTOR = 'CHIROPRACTOR',
  DENTIST = 'DENTIST',
  GYM = 'GYM',
  MEDICALTPRACTITIONER = 'MEDICALTPRACTITIONER',
  OPTOMETRIST = 'OPTOMETRIST',
  PERSONALTRAINER = 'PERSONALTRAINER',
  PSYCHIATRIST = 'PSYCHIATRIST',
  COUNSELLOR = 'COUNSELLOR',
  VETERINARY = 'VETERINARY',
  PHYSIOTHERAPIST = 'PHYSIOTHERAPIST',
  DIETITIAN = 'DIETITIAN',
  PODIATRIST = 'PODIATRIST',
  OCCUPATIONALTHERAPIST = 'OCCUPATIONALTHERAPIST',
  HYPNOTHERAPIST = 'HYPNOTHERAPIST',
  PHYSICALTHERAPIST = 'PHYSICALTHERAPIST',
  DOCTOR = 'DOCTOR',
  ANESTHETIST = 'ANESTHETIST',
  MIDWIFE = 'MIDWIFE',
  NURSE = 'NURSE',
  PHARMACIST = 'PHARMACIST',
  AUTOMOTIVE = 'AUTOMOTIVE',
  CARPETCLEANING = 'CARPETCLEANING',
  CLEANING = 'CLEANING',
  CLOTHINGALTERATIONS = 'CLOTHINGALTERATIONS',
  DRYCLEANING = 'DRYCLEANING',
  ELECTRICAL = 'ELECTRICAL',
  FLOORING = 'FLOORING',
  GENERALCONTRACTING = 'GENERALCONTRACTING',
  HEATINGANDAC = 'HEATINGANDAC',
  INSTALLATIONSERVICES = 'INSTALLATIONSERVICES',
  RUBBISHREMOVAL = 'RUBBISHREMOVAL',
  LANDSCAPING = 'LANDSCAPING',
  LOCKSMITH = 'LOCKSMITH',
  PAINTING = 'PAINTING',
  PESTCONTROL = 'PESTCONTROL',
  PLUMBING = 'PLUMBING',
  CARPENTRY = 'CARPENTRY',
  PLASTERINGCEILING = 'PLASTERINGCEILING',
  TILINGCARPETING = 'TILINGCARPETING',
  BRICKLAYING = 'BRICKLAYING',
  CONCRETING = 'CONCRETING',
  GLAZING = 'GLAZING',
  CONSTRUCTIONMATERIALS = 'CONSTRUCTIONMATERIALS',
  CONSTRUCTION = 'CONSTRUCTION',
  ARCHITECTURE = 'ARCHITECTURE',
  FESTIVALS = 'FESTIVALS',
  CINEMA = 'CINEMA',
  MUSEUM = 'MUSEUM',
  MUSIC = 'MUSIC',
  PERFORMINGARTS = 'PERFORMINGARTS',
  SPORTINGEVENTS = 'SPORTINGEVENTS',
  SPORTSRECREATION = 'SPORTSRECREATION',
  ACCOUNTING = 'ACCOUNTING',
  CONSULTING = 'CONSULTING',
  DESIGN = 'DESIGN',
  INTERIORDESIGN = 'INTERIORDESIGN',
  LEGALSERVICES = 'LEGALSERVICES',
  MARKETING = 'MARKETING',
  PHOTOGRAPHY = 'PHOTOGRAPHY',
  PRINTINGSERVICES = 'PRINTINGSERVICES',
  REALESTATE = 'REALESTATE',
  SOFTWAREDEVELOPMENT = 'SOFTWAREDEVELOPMENT',
  DATINGSERVICES = 'DATINGSERVICES',
  EMPLOYMENTAGENCIES = 'EMPLOYMENTAGENCIES',
  MOTIVATIONALSERVICES = 'MOTIVATIONALSERVICES',
  ARTPHOTOFILM = 'ARTPHOTOFILM',
  BOOKSMUSICVIDEO = 'BOOKSMUSICVIDEO',
  CLOTHING = 'CLOTHING',
  COMPUTERAPPLICANCES = 'COMPUTERAPPLICANCES',
  ELECTRONICS = 'ELECTRONICS',
  EYEWEAR = 'EYEWEAR',
  EVENTS = 'EVENTS',
  FLOWERSGIFTS = 'FLOWERSGIFTS',
  FURNITURE = 'FURNITURE',
  HOMEGOODS = 'HOMEGOODS',
  HOBBYSHOP = 'HOBBYSHOP',
  JEWELLERYWATCHES = 'JEWELLERYWATCHES',
  OFFICESUPPLY = 'OFFICESUPPLY',
  PETSHOP = 'PETSHOP',
  SPECIALITYSHOP = 'SPECIALITYSHOP',
  SPORTINGGOODS = 'SPORTINGGOODS',
  BUS = 'BUS',
  DELIVERY = 'DELIVERY',
  REMOVALIST = 'REMOVALIST',
  PRIVATECARHIRE = 'PRIVATECARHIRE',
  TAXI = 'TAXI',
  AIRLINEANDAIRCARRIER = 'AIRLINEANDAIRCARRIER',
  COURIERSERVICES = 'COURIERSERVICES',
  MOVERS = 'MOVERS',
  BOATRENTALS = 'BOATRENTALS',
  DEALERS = 'DEALERS',
  BUSINESSSERVICES = 'BUSINESSSERVICES',
  CARTRUCKSERVICES = 'CARTRUCKSERVICES',
  CARRENTAL = 'CARRENTAL',
  TRAVELAGENCY = 'TRAVELAGENCY',
  LODGING = 'LODGING',
  LOCALCOUNCIL = 'LOCALCOUNCIL',
  LIBRARY = 'LIBRARY',
  PRIMARYSCHOOL = 'PRIMARYSCHOOL',
  PARKSRECREATION = 'PARKSRECREATION',
  ADULTSERVICES = 'ADULTSERVICES',
  ONLINETOBACCOVAPERETAILERS = 'ONLINETOBACCOVAPERETAILERS',
  WEAPONSAMMUNITIONS = 'WEAPONSAMMUNITIONS',
  GAMBLINGESTABLISHMENT = 'GAMBLINGESTABLISHMENT',
  FOREIGNEXCHANGESEVICES = 'FOREIGNEXCHANGESEVICES',
  CRYPTOCURRENCY = 'CRYPTOCURRENCY',
  PAYMENTPROCESSORS = 'PAYMENTPROCESSORS',
  INSURANCEPROVIDERS = 'INSURANCEPROVIDERS',
}

export enum TaxRate {
  BAS_EXCLUDED = 'BAS_EXCLUDED',
  GST_FREE_EXPENSE = 'GST_FREE_EXPENSE',
  GST_FREE_INCOME = 'GST_FREE_INCOME',
  GST_ON_EXPENSE = 'GST_ON_EXPENSE',
  GST_ON_IMPORTS = 'GST_ON_IMPORTS',
  GST_ON_INCOME = 'GST_ON_INCOME',
}

export type EntityAddress = {
  street1?: string;
  street2?: string;
  suburb?: string;
  state?: string;
  postcode?: string;
  country?: string;
};

export type EntityMember = {
  type: EntityType;
  firstname?: string;
  middlename?: string;
  lastname?: string;
  companyTrustName?: string;
  abn?: string;
  address?: Address;
  dob?: string;
  director?: boolean;
  secretary?: boolean;
  ceo?: boolean;
  beneficialOwner?: boolean;
  beneficialOwnerAlt?: boolean;
  beneficiary?: boolean;
  shareholder?: boolean;
  partner?: boolean;
  trustee?: boolean;
  settlor?: boolean;
  chair?: boolean;
  treasurer?: boolean;
  nonBeneficiallyHeldShareholdings?: boolean;
  ORDsharesHeldInPercent?: number;
};

export type EntityOnboardingMember = {
  isCurrentUser: boolean;
  temporaryId: string;
} & EntityMember;

export enum RiskReviewStatus {
  NOT_REQUIRED = 'NOT_REQUIRED',
  REQUIRED = 'REQUIRED',
  COMPLETED = 'COMPLETED',
}

export enum RiskReviewResult {
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  ABANDONED = 'ABANDONED',
  DEPLATFORMED = 'DEPLATFORMED',
  REVIEW = 'REVIEW',
}

export enum RiskRating {
  LOW_NEW = 'LOW_NEW',
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
}

export enum SettledSumValue {
  LessThan10k = 'lessThan10k',
  MoreOrEqual10k = 'moreOrEqual10k',
}
export enum OnboardingFlowType {
  COMPANY = 'COMPANY',
  COMPANY_NOT_FOUND = 'COMPANY_NOT_FOUND',
  INDIVIDUAL = 'INDIVIDUAL',
  INDIVIDUAL_NO_ABN = 'INDIVIDUAL_NO_ABN',
  TRUST = 'TRUST',
  PARTNERSHIP = 'PARTNERSHIP',
  ASSOCIATION = 'ASSOCIATION',
  GOVERNMENT = 'GOVERNMENT',
}
export type TrusteeType = EntityType.COMPANY | EntityType.INDIVIDUAL;

export interface HelperFields {
  isNotRegulated?: boolean;
  hasNoPlaceOfBusiness?: boolean;
  settledSum?: SettledSumValue;
  trustee?: TrusteeType;
}
export interface MembersFilters {
  director?: boolean;
  beneficialOwner?: boolean;
  settlor?: boolean;
  trustee?: boolean;
  beneficiary?: boolean;
  partner?: boolean;
  chair?: boolean;
  secretary?: boolean;
  treasurer?: boolean;
}

export enum DocumentType {
  DRIVING_LICENCE = 'DRIVING_LICENCE',
  PASSPORT = 'PASSPORT',
  MEDICARE_CARD = 'MEDICARE_CARD',
  NO_SECOND_ID = 'NO_SECOND_ID',
}

export interface KYCInitialData {
  documents?: DocumentType[];
  attempts?: {
    [DocumentType.DRIVING_LICENCE]?: number;
    [DocumentType.PASSPORT]?: number;
    [DocumentType.MEDICARE_CARD]?: number;
  };
  isAgreed?: boolean;
  personalData?: PersonalData;
}

export interface PersonalData {
  street?: string;
  suburb?: string;
  state?: string;
  postcode?: string | null;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  dob?: string;
}

export interface InitialCustomerData {
  street?: string;
  suburb?: string;
  state?: string;
  postcode?: string;
  country?: string;
  roles?: string[];
  companyTrustName?: string;
  abn?: string;
  firstName?: string;
  lastName?: string;
  middleName?: string;
  dob?: string;
  temporaryId?: string;
}

export enum RegulatorBodyType {
  COMMONWEALTH = 'COMMONWEALTH',
  ACT = 'ACT',
  NSW = 'NSW',
  NT = 'NT',
  SA = 'SA',
  TAS = 'TAS',
  VIC = 'VIC',
  WA = 'WA',
}

export enum RiskReviewRejectedReason {
  TOSV_PROHIBITE = 'TOSV_PROHIBITED',
  TOSV_FAKE = 'TOSV_FAKE',
  HIGH_RISK = 'HIGH_RISK',
  UNSUPPORTED = 'UNSUPPORTED',
  DUPLICATE = 'DUPLICATE',
  ADVERSE_NEWS = 'ADVERSE_NEWS',
  TEST_ACCOUNT = 'TEST_ACCOUNT',
  MERCHANT_REQUESTED_TO_OFFBOARD = 'MERCHANT_REQUESTED_TO_OFFBOARD',
  OTHER = 'OTHER',
}

export type RiskReviewType = {
  status?: RiskReviewStatus;
  result?: RiskReviewResult;
  reason?: string;
  rejectedReasons?: RiskReviewRejectedReason[];
  requiredRiskReviewReason?: string[];
  riskReviewDetail?: string[];
};

// Entity Migration State Removed

export type TermsOfServiceAcceptance = {
  accepted: boolean;
  customerUuid?: string;
  acceptedAt?: string;
};

export type EntityDailyLimitsRiskLastUpdated = {
  zellerUserId: string;
  lastUpdatedAt: string; // ISO8601
  reason: string;
};

export type EntityDailyLimits = {
  riskLimit: Money;
  merchantLimit: Money;
  appliedLimit: Money;
  riskLastUpdated?: EntityDailyLimitsRiskLastUpdated;
};

export type EntityTransactionMetaData = {
  yetToMakeTransaction: boolean;
  firstTransactionUuid?: string;
  firstTransactionTimestamp?: string;
};

export type OutstandingTransactionConfig = {
  note: boolean;
  attachments: boolean;
  category: boolean;
  accountingCategory: boolean;
};

export enum TmsThreeDSEntityCategory {
  BLACKLISTED = 'BLACKLISTED',
  WHITELISTED = 'WHITELISTED',
  LOW_THRESHOLD = 'LOW_THRESHOLD',
  HIGH_THRESHOLD = 'HIGH_THRESHOLD',
}

export interface FeeRates {
  // input as floats
  feePercent?: number;
  feeFixed?: number;
  feePercentMoto?: number;
  feeFixedMoto?: number;
  feePercentZinv?: number;
  feeFixedZinv?: number;
  feePercentIntlZinv?: number;
  feeFixedIntlZinv?: number;
  feePercentXinv?: number;
  feeFixedXinv?: number;
  feePercentIntlXinv?: number;
  feeFixedIntlXinv?: number;
  feePercentCpoc?: number;
  feeFixedCpoc?: number;
  feePercentVt?: number;
  feeFixedVt?: number;
  feePercentPbl?: number;
  feeFixedPbl?: number;
  feePercentIntlPbl?: number;
  feeFixedIntlPbl?: number;
}

export interface PaymentSettingsLastUpdated {
  updatedTime: string;
  updatedBy: string;
  effectiveFrom?: string;
}

export interface MinMaxInput {
  maximum?: number;
  minimum?: number;
}

export interface PaymentSettings {
  cnpPaymentLimits?: MinMaxInput;
  paymentLimits?: MinMaxInput;
  motoPaymentLimits?: MinMaxInput;
  cpocPaymentLimits?: MinMaxInput;
}

export type RefundOverdraftSettings = {
  limit: number | null;
  reason: string;
};

export type HsTicket = {
  id: string;
  fortiroResult?: FortiroResult;
  fundsHeld?: boolean;
  chargeBackAmount?: number;
  fortiroWebUrl?: string;
};

export enum FortiroResult {
  TRUE_POSITIVE = 'TP',
  FALSE_POSITIVE = 'False Positive',
}

export const COHORT_CODE = { STARTUP: 'STARTUP' } as const;

export type CohortCode = (typeof COHORT_CODE)[keyof typeof COHORT_CODE];

export interface CohortType {
  code: CohortCode;
}

export type AusBusinessDetails = {
  acn?: string;
  abn?: string;
};

export type GbrBusinessDetails = {
  crn: string;
};

export type BusinessDetails = {
  ausBusiness?: AusBusinessDetails;
  gbrBusiness?: GbrBusinessDetails;
};

export interface AusBusinessInput {
  acn?: string;
  abn?: string;
}

export interface GbrBusinessInput {
  crn: string;
}

export interface BusinessDetailsInput {
  ausBusiness?: AusBusinessInput;
  gbrBusiness?: GbrBusinessInput;
}

export type RegionalOptions = {
  surchargeAllowed?: boolean;
};
