import type { EntityType } from '@npco/component-dto-core';

import type { BusinessDetails } from './types';

export class EntityInitialSearchCompletedEventDto {
  /**
   * Unique identifier for the entity - backwards compatibility with the old registration flow
   */
  entityUuid?: string;

  /**
   * Unique identifier for the entity - used in the new registration flow
   *    */
  customerUuid?: string;

  /**
   * ABN or ACN used to identify the business or organisation
   */
  businessIdentifier: string;

  /**
   * Indicates whether the businessIdentifier was found or not
   */
  found: boolean;

  /**
   * Country of the business or organisation in ISO 3166-1 alpha-3
   */
  country?: string;

  /**
   * Business details based on the country of the business or organisation.
   */
  businessDetails?: BusinessDetails;

  /**
   * Business or organisation name
   */
  name?: string;

  /**
   * ACN if applicable
   * @deprecated Use businessDetails.ausBusiness.acn instead
   */
  acn?: string;

  /**
   * ABN
   * @deprecated Use businessDetails.ausBusiness.abn instead
   */
  abn?: string;

  /**
   * Entity type Enum: INDIVIDUAL, COMPANY, PARTNERSHIP, TRUST, ASSOCIATION
   */
  type?: EntityType;

  /**
   * Provided if there was an error associated with the inability to find the businessIdentifier
   */
  error?: string;

  constructor(params: EntityInitialSearchCompletedEventDto) {
    this.entityUuid = params.entityUuid;
    this.customerUuid = params.customerUuid;
    this.businessIdentifier = params.businessIdentifier;
    this.found = params.found;
    this.country = params.country;
    this.name = params.name;
    this.businessDetails = params.businessDetails;
    this.acn = params.acn; // NoSonar - Backwards compatibility
    this.abn = params.abn; // NoSonar - Backwards compatibility
    this.type = params.type;
    this.error = params.error;
  }
}
