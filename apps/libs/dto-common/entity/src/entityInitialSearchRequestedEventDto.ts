export class EntityInitialSearchRequestedEventDto {
  entityUuid?: string;

  customerUuid?: string;

  /**
   * ABN or ACN used to identify the business or organisation
   */
  businessIdentifier: string;

  constructor(params: EntityInitialSearchRequestedEventDto) {
    this.entityUuid = params.entityUuid;
    this.customerUuid = params.customerUuid;
    this.businessIdentifier = params.businessIdentifier;
  }
}
