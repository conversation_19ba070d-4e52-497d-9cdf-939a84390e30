import type { ScheduledTransferType, TransferDetails, ScheduleInput } from './types';

export class ScheduledTransferBaseDto {
  entityUuid: string;

  customerUuid: string;

  /**
   * Identifier of the account associated with the scheduled transfer
   */
  accountUuid: string;

  /**
   * Indicates the type of transfer.
   */
  transferType: ScheduledTransferType;

  /**
   * Indicates the type of schedule and also details related to the schedule type
   */
  schedule: ScheduleInput;

  /**
   * Transfer details depending on the transfer type.
   */
  transferDetails: TransferDetails;

  /**
   * Timestamp indicating when the  scheduled transfer was initiated
   */
  timestamp: number;

  /**
   * The next date when transfer is scheduled
   */
  nextTransferDate?: string;

  description?: string;

  contactUuid?: string;

  constructor(params: ScheduledTransferBaseDto) {
    this.entityUuid = params.entityUuid;
    this.customerUuid = params.customerUuid;
    this.accountUuid = params.accountUuid;
    this.transferType = params.transferType;
    this.schedule = params.schedule;
    this.transferDetails = params.transferDetails;
    this.timestamp = params.timestamp;
    this.description = params.description;
    this.nextTransferDate = params.nextTransferDate;
    this.contactUuid = params.contactUuid;
  }
}
