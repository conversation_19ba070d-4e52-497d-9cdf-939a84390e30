module.exports = {
    roots: ['<rootDir>/tests'],
    testMatch: [
        '**/__tests__/**/*.+(ts|tsx|js)',
        '**/?(*.)+(spec|test).+(ts|tsx|js)',
    ],
    coveragePathIgnorePatterns: [
        "testcases"
    ],
    transform: {
        '^.+\\.(t|j)sx?$': '@swc/jest',
    },
    testTimeout: 50000,
    reporters: [
        'default',
        ['summary', { summaryThreshold: 1 }],
        [
            'jest-junit',
            {
                outputDirectory: 'dist/',
                outputName: 'report.xml',
                uniqueOutputName: 'false',
                titleTemplate: '{classname}-{title}',
                ancestorSeparator: ' › ',
                usePathForSuiteName: 'true',
                includeConsoleOutput: 'true',
                suiteName: 'Test Report',
            },
        ],
        [
            'jest-html-reporter',
            {
                pageTitle: 'Test Report',
                'outputPath': 'dist/test-report.html'
            },
        ],
    ],
};
