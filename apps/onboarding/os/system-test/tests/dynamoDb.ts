import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DeleteCommand, DynamoDBDocumentClient, QueryCommand } from '@aws-sdk/lib-dynamodb';
import {
  getCustomerScreeningSearchTable,
  getEntityScreeningSearchTable,
  getFullCompanyProfileSearchTable,
  getIdvSafeHarbourSearchTable,
  getInitialCompanySearchTable, getInitialCompanySearchTableCustomerUuidGIS,
  getInitialCompanySearchTableEntityUuidGIS,
  getRegion,
  getSelfieVerificationSearchTable,
} from './utils';


const queryTestCasesInDynamoDB = async (
  key: string,
  value: string,
  tableName: string,
  documentClient?: DynamoDBDocumentClient,
  index?: string,
) => {
  const dynamodb = documentClient ?? DynamoDBDocumentClient.from(
    new DynamoDBClient({
      region: getRegion(),
    }),
    {
      marshallOptions: {
        removeUndefinedValues: true,
      },
    });

  const params = {
    TableName: tableName,
    ...(index ? {IndexName: index} : {}),
    KeyConditionExpression: `${key} = :id`,
    ExpressionAttributeValues: {
      ':id': value,
    },
    ScanIndexForward: false,
  };
  const command = new QueryCommand(params);
  return await dynamodb.send(command);
};

const queryTestCasesInDynamoDbByType = async (
  id: string,
  type: string,
  tableName: string,
  documentClient?: DynamoDBDocumentClient,
) => {
  const dynamodb = documentClient ?? DynamoDBDocumentClient.from(new DynamoDBClient({
    region: getRegion(),
  }));
  const params = {
    TableName: tableName,
    KeyConditionExpression: '#id = :id AND begins_with(#type, :type)',
    ExpressionAttributeValues: {
      ':id': id,
      ':type': type,
    },
    ExpressionAttributeNames: {
      '#type': 'type',
      '#id': 'id',
    },
    ScanIndexForward: false,
  };
  const command = new QueryCommand(params);
  return await dynamodb.send(command);
};

export const queryTestCasesInInitialSearchDynamoDB = async (
  id: string,
  tableName?: string,
  documentClient?: DynamoDBDocumentClient,
) =>
  queryTestCasesInDynamoDB('id', id, tableName ?? (await getInitialCompanySearchTable()), documentClient);

export const queryTestCasesByGISInInitialSearchDynamoDB = async (
  id: string,
  tableName?: string,
  documentClient?: DynamoDBDocumentClient,
) =>
  queryTestCasesInDynamoDB('entityUuid', id, tableName ?? (await getInitialCompanySearchTable()), documentClient, await getInitialCompanySearchTableEntityUuidGIS());

export const queryTestCasesByCustomerGISInInitialSearchDynamoDB = async (
  id: string,
  tableName?: string,
  documentClient?: DynamoDBDocumentClient,
) =>
  queryTestCasesInDynamoDB('customerUuid', id, tableName ?? (await getInitialCompanySearchTable()), documentClient, await getInitialCompanySearchTableCustomerUuidGIS());

export const queryTestCasesInFullCompanyProfileDynamoDB = async (
  id: string,
  tableName?: string,
  documentClient?: DynamoDBDocumentClient,
) =>
  queryTestCasesInDynamoDB('entityUuid', id, tableName ?? (await getFullCompanyProfileSearchTable()), documentClient);

export const queryTestCasesInCustomerScreeningSearchDynamoDB = async (
  id: string,
  tableName?: string,
  documentClient?: DynamoDBDocumentClient,
) =>
  queryTestCasesInDynamoDB('customerUuid', id, tableName ?? (await getCustomerScreeningSearchTable()), documentClient);

export const queryTestCasesInEntityScreeningSearchDynamoDB = async (
  id: string,
  tableName?: string,
  documentClient?: DynamoDBDocumentClient,
) =>
  queryTestCasesInDynamoDB('entityUuid', id, tableName ?? (await getEntityScreeningSearchTable()), documentClient);

export const queryTestCasesInIdvSafeHarbourSearchDynamoDB = async (
  id: string,
  tableName?: string,
  documentClient?: DynamoDBDocumentClient,
) =>
  queryTestCasesInDynamoDB('id', id, tableName ?? (await getIdvSafeHarbourSearchTable()), documentClient);

export const queryTestCasesInSelfieVerificationSearchDynamoDB = async (
  id: string,
  type: string,
  tableName?: string,
  documentClient?: DynamoDBDocumentClient,
) =>
  queryTestCasesInDynamoDbByType(id, type, tableName ?? (await getSelfieVerificationSearchTable()), documentClient);

const removeTestCasesInDynamoDB = async (
  primaryKey: string,
  value: string,
  sortKey: string,
  table: string,
  func: any,
) => {
  const dynamodb = DynamoDBDocumentClient.from(new DynamoDBClient({
    region: getRegion(),
  }));
  const result = await func(value, table, dynamodb);

  for (const item of result.Items) {
    const command = new DeleteCommand({
      TableName: table,
      Key: {
        [primaryKey]: item[primaryKey],
        [sortKey]: item[sortKey],
      },
    });
    await dynamodb.send(command);
  }
};

const removeTestCasesInDynamoDBWithType = async (
  primaryKey: string,
  value: string,
  type: string,
  table: string,
  func: any,
) => {
  const dynamodb = DynamoDBDocumentClient.from(new DynamoDBClient({
    region: getRegion(),
  }));
  const result = await func(value, type, table, dynamodb);

  for (const item of result.Items) {
    const command = new DeleteCommand({
      TableName: table,
      Key: {
        [primaryKey]: item[primaryKey],
        type,
      },
    });
    await dynamodb.send(command);
  }
};

export const removeTestCasesInInitialSearchDynamoDB = async (id: string) =>
  removeTestCasesInDynamoDB('id', id, 'expirationTime', await getInitialCompanySearchTable(), queryTestCasesInInitialSearchDynamoDB);

export const removeTestCasesInFullCompanyProfileSearchDynamoDB = async (id: string) =>
  removeTestCasesInDynamoDB('entityUuid', id, 'expirationTime', await getFullCompanyProfileSearchTable(), queryTestCasesInFullCompanyProfileDynamoDB);

export const removeTestCasesInCustomerScreeningSearchDynamoDB = async (id: string) =>
  removeTestCasesInDynamoDB('customerUuid', id, 'expirationTime', await getCustomerScreeningSearchTable(), queryTestCasesInCustomerScreeningSearchDynamoDB);

export const removeTestCasesInEntityScreeningSearchDynamoDB = async (id: string) =>
  removeTestCasesInDynamoDB('entityUuid', id, 'expirationTime', await getEntityScreeningSearchTable(), queryTestCasesInEntityScreeningSearchDynamoDB);

export const removeTestCasesInIdvSafeHarbourSearchDynamoDB = async (id: string) =>
  removeTestCasesInDynamoDB('id', id, 'expirationTime', await getIdvSafeHarbourSearchTable(), queryTestCasesInIdvSafeHarbourSearchDynamoDB);

export const removeTestCasesInSelfieVerificationSearchDynamoDB = async (id: string, type: string) =>
  removeTestCasesInDynamoDBWithType('id', id, type, await getSelfieVerificationSearchTable(), queryTestCasesInSelfieVerificationSearchDynamoDB);
