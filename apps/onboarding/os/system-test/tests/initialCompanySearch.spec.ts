import { getInitialCompanySearchLambda, getRegion, setAWSEnvironment, sleep } from './utils';
import {
  EntityInitialSearchCompletedEventDto,
  EntityInitialSearchRequestedEventDto,
} from '@npco/component-dto-entity';

import {
  EntityType
} from '@npco/component-dto-core';
import { v4 as uuidv4 } from 'uuid';
import { Lambda } from "@aws-sdk/client-lambda";
import { constants } from 'http2';
import {
  queryTestCasesByCustomerGISInInitialSearchDynamoDB,
  queryTestCasesByGISInInitialSearchDynamoDB,
  queryTestCasesInInitialSearchDynamoDB,
  removeTestCasesInInitialSearchDynamoDB,
} from './dynamoDb';
import { TextEncoder } from 'util';

const {HTTP_STATUS_OK} = constants;

const mockEntityUuid = uuidv4();
const mockCustomerUuid = uuidv4();
const validACN = '*********';
const validABN = '31*********';
const nonSupportedABN = '***********';
const expectName = 'WHITE CITY PTY LTD';

beforeAll(async () => {
  setAWSEnvironment();
});

describe('initial company search test suite', () => {
  let lambda: Lambda;
  let lambdaName: string;

  beforeAll(async () => {
    lambdaName = await getInitialCompanySearchLambda();
    lambda = new Lambda({region: getRegion()});
  });

  beforeEach(async () => {
    await removeAllTestCases();
  });
  afterAll(async () => {
    await removeAllTestCases();
  });

  it('Can search an ACN via Equifax API, create a cache and return correctly', async () => {
    const initialCompanySearchDto = {
      entityUuid: mockEntityUuid,
      customerUuid: mockCustomerUuid,
      businessIdentifier: validACN,
    } as EntityInitialSearchRequestedEventDto;

    // noinspection JSDeprecatedSymbols
    const expectedPayload = {
      entityUuid: initialCompanySearchDto.entityUuid,
      customerUuid: initialCompanySearchDto.customerUuid,
      businessIdentifier: initialCompanySearchDto.businessIdentifier,
      found: true,
      country: 'AUS',
      type: EntityType.COMPANY,
      businessDetails: {
        ausBusiness: {
          acn: validACN,
          abn: validABN,
        },
      },
      acn: validACN, //NoSonar - backward compatibility
      abn: validABN, //NoSonar - backward compatibility
      name: expectName,
    } as EntityInitialSearchCompletedEventDto;

    let dynamoDbRecords = await queryTestCasesInInitialSearchDynamoDB(validACN);
    expect(dynamoDbRecords.Count).toBe(0);
    const output = await lambda.invoke({
      FunctionName: lambdaName,
      InvocationType: 'RequestResponse',
      Payload: new TextEncoder().encode(JSON.stringify({detail: initialCompanySearchDto})),
    });
    expect(output.StatusCode).toBe(HTTP_STATUS_OK);
    // @ts-ignore
    expect(JSON.parse(new TextDecoder().decode(output.Payload))).toEqual(expectedPayload);
    const result = await queryTestCasesInInitialSearchDynamoDB(validACN);
    expect(result.Count).toBe(1);
    // @ts-ignore
    const {id, expirationTime, acn, abn, name, entityUuid, customerUuid, type, requestXml, responseXml} = result.Items[0];
    expect(id).toBe(initialCompanySearchDto.businessIdentifier);
    expect(acn).toBe(validACN);
    expect(abn).toBe(validABN);
    expect(name).toBe(expectName);
    expect(entityUuid).toBe(initialCompanySearchDto.entityUuid);
    expect(customerUuid).toBe(initialCompanySearchDto.customerUuid);
    expect(type).toBe(EntityType.COMPANY);

    expect(requestXml).toEqual(expect.stringMatching(/^<\?xml version="1\.0" encoding="UTF-8"\?>/));
    expect(requestXml).toEqual(expect.stringMatching(/<BCAaccess-code>\**<\/BCAaccess-code>/));
    expect(requestXml).toEqual(expect.stringMatching(/<BCAaccess-pwd>\**<\/BCAaccess-pwd>/));
    expect(responseXml).toEqual(
      expect.stringMatching(/^<\?xml version="1\.0"\?><BCAmessage service-request-id="\d*" type="RESPONSE">/)
    );

    const gsiRecord = await queryTestCasesByGISInInitialSearchDynamoDB(initialCompanySearchDto.entityUuid!);
    expect(gsiRecord.Count).toBe(1);
    // @ts-ignore
    const {id: gsiId, acn: gsiAcn, entityUuid: gsiEntityUuid, expirationTime: gsiExpirationTime} = gsiRecord.Items[0];
    expect(gsiId).toBe(id);
    expect(gsiAcn).toBe(acn);
    expect(gsiEntityUuid).toBe(entityUuid);
    expect(gsiExpirationTime).toBe(expirationTime);

    const customerGsiRecord = await queryTestCasesByCustomerGISInInitialSearchDynamoDB(initialCompanySearchDto.customerUuid!);
    expect(customerGsiRecord.Count).toBe(1);
    // @ts-ignore
    const {id: customerGsiId, acn: customerGsiAcn, customerUuid: gsiCustomerUuid, expirationTime: customerGsiExpirationTime} = customerGsiRecord.Items[0];
    expect(customerGsiId).toBe(id);
    expect(customerGsiAcn).toBe(acn);
    expect(gsiCustomerUuid).toBe(customerUuid);
    expect(customerGsiExpirationTime).toBe(expirationTime);
  });

  it('Can search by ABN, create cache and return correctly', async () => {
    const initialCompanySearchDto = {
      entityUuid: mockEntityUuid,
      businessIdentifier: validABN,
    } as EntityInitialSearchRequestedEventDto;

    // noinspection JSDeprecatedSymbols
    const expectedPayload = {
      entityUuid: initialCompanySearchDto.entityUuid,
      businessIdentifier: initialCompanySearchDto.businessIdentifier,
      found: true,
      type: EntityType.COMPANY,
      country: 'AUS',
      businessDetails: {
        ausBusiness: {
          acn: validACN,
          abn: validABN,
        },
      },
      acn: validACN, //NoSonar - backward compatibility
      abn: validABN, //NoSonar - backward compatibility
      name: expectName,
    } as EntityInitialSearchCompletedEventDto;

    let dynamoDbRecords = await queryTestCasesInInitialSearchDynamoDB(validABN);
    expect(dynamoDbRecords.Count).toBe(0);

    const output = await lambda.invoke({
      FunctionName: lambdaName,
      InvocationType: 'RequestResponse',
      Payload: new TextEncoder().encode(JSON.stringify({detail: initialCompanySearchDto})),
    });

    // @ts-ignore
    expect(JSON.parse(new TextDecoder().decode(output.Payload))).toEqual(expectedPayload);

    dynamoDbRecords = await queryTestCasesInInitialSearchDynamoDB(validABN);
    expect(dynamoDbRecords.Count).toBe(1);
  });

  it('Return the data from cache correctly and generate the new cache if the cache is available ', async () => {
    const firstInitialCompanySearchDto = {
      entityUuid: mockEntityUuid,
      businessIdentifier: validACN,
    } as EntityInitialSearchRequestedEventDto;

    const entityUuid = uuidv4();
    const secondInitialCompanySearchDto = {
      entityUuid,
      businessIdentifier: validACN,
    } as EntityInitialSearchRequestedEventDto;

    // noinspection JSDeprecatedSymbols
    const expectedPayload = {
      entityUuid,
      businessIdentifier: validACN,
      found: true,
      country: 'AUS',
      businessDetails: {
        ausBusiness: {
          acn: validACN,
          abn: validABN,
        },
      },
      type: EntityType.COMPANY,
      acn: validACN, //NoSonar - backward compatibility
      abn: validABN, //NoSonar - backward compatibility
      name: expectName,
    } as EntityInitialSearchCompletedEventDto;

    let dynamoDbRecords = await queryTestCasesInInitialSearchDynamoDB(validACN);

    expect(dynamoDbRecords.Count).toBe(0);

    await lambda.invoke({
      FunctionName: lambdaName,
      InvocationType: 'RequestResponse',
      Payload: new TextEncoder().encode(JSON.stringify({detail: firstInitialCompanySearchDto})),
    });
    await sleep(1000);
    const output = await lambda.invoke({
      FunctionName: lambdaName,
      InvocationType: 'RequestResponse',
      Payload: new TextEncoder().encode(JSON.stringify({detail: secondInitialCompanySearchDto})),
    });

    // @ts-ignore
    expect(JSON.parse(new TextDecoder().decode(output.Payload))).toEqual(expectedPayload);

    const result = await queryTestCasesInInitialSearchDynamoDB(validACN);
    expect(result.Count).toBe(2);
    // @ts-ignore
    const [latestResult, cache] = result.Items;
    expect(latestResult.id).toBe(cache.id);
    expect(latestResult.entityUuid).toBe(entityUuid);
    expect(latestResult.abn).toBe(cache.abn);
    expect(latestResult.acn).toBe(cache.acn);
    expect(latestResult.name).toBe(cache.name);
    expect(latestResult.type).toBe(cache.type);
    expect(latestResult.requestXml).toBe(cache.requestXml);
    expect(latestResult.respondXml).toBe(cache.respondXml);
    expect(latestResult.expirationTime).toBeGreaterThan(cache.expirationTime);
  });

  it('return correctly if there is no record found', async () => {
    const businessIdentifier = '*********';

    const initialCompanySearchDto = {
      entityUuid: mockEntityUuid,
      businessIdentifier,
    } as EntityInitialSearchRequestedEventDto;

    const expectedPayload = {
      entityUuid: initialCompanySearchDto.entityUuid,
      businessIdentifier: initialCompanySearchDto.businessIdentifier,
      country: 'AUS',
      found: false,
    } as EntityInitialSearchCompletedEventDto;

    let dynamoDbRecords = await queryTestCasesInInitialSearchDynamoDB(businessIdentifier);
    expect(dynamoDbRecords.Count).toBe(0);

    const output = await lambda.invoke({
      FunctionName: lambdaName,
      InvocationType: 'RequestResponse',
      Payload: new TextEncoder().encode(JSON.stringify({detail: initialCompanySearchDto})),
    });

    // @ts-ignore
    expect(JSON.parse(new TextDecoder().decode(output.Payload))).toEqual(expectedPayload);

    dynamoDbRecords = await queryTestCasesInInitialSearchDynamoDB(businessIdentifier);
    expect(dynamoDbRecords.Count).toBe(0);
  });

  it('return error if it is an invalid businessIdentify', async () => {
    const businessIdentifier = '123';

    const initialCompanySearchDto = {
      entityUuid: mockEntityUuid,
      businessIdentifier,
    } as EntityInitialSearchRequestedEventDto;

    const expectedPayload = {
      entityUuid: initialCompanySearchDto.entityUuid,
      businessIdentifier: initialCompanySearchDto.businessIdentifier,
      country: 'AUS',
      found: false,
      error: 'Error: Invalid Business Identifier',
    } as EntityInitialSearchCompletedEventDto;

    let dynamoDbRecords = await queryTestCasesInInitialSearchDynamoDB(businessIdentifier);
    expect(dynamoDbRecords.Count).toBe(0);

    const output = await lambda.invoke({
      FunctionName: lambdaName,
      InvocationType: 'RequestResponse',
      Payload: new TextEncoder().encode(JSON.stringify({detail: initialCompanySearchDto})),
    });

    // @ts-ignore
    expect(JSON.parse(new TextDecoder().decode(output.Payload))).toEqual(expectedPayload);

    dynamoDbRecords = await queryTestCasesInInitialSearchDynamoDB(businessIdentifier);
    expect(dynamoDbRecords.Count).toBe(0);
  });

  it('return error if the entity type is not support', async () => {
    const businessIdentifier = nonSupportedABN;

    const initialCompanySearchDto = {
      entityUuid: mockEntityUuid,
      businessIdentifier,
    } as EntityInitialSearchRequestedEventDto;

    const expectedPayload = {
      entityUuid: initialCompanySearchDto.entityUuid,
      businessIdentifier: initialCompanySearchDto.businessIdentifier,
      country: 'AUS',
      found: false,
      error: 'Entity Type is not supported',
    } as EntityInitialSearchCompletedEventDto;

    let dynamoDbRecords = await queryTestCasesInInitialSearchDynamoDB(businessIdentifier);
    expect(dynamoDbRecords.Count).toBe(0);

    const output = await lambda.invoke({
      FunctionName: lambdaName,
      InvocationType: 'RequestResponse',
      Payload: new TextEncoder().encode(JSON.stringify({detail: initialCompanySearchDto})),
    });

    // @ts-ignore
    expect(JSON.parse(new TextDecoder().decode(output.Payload))).toEqual(expectedPayload);

    dynamoDbRecords = await queryTestCasesInInitialSearchDynamoDB(businessIdentifier);
    //ZD-2282: Change the unsupported entity type will be also stored in cache
    expect(dynamoDbRecords.Count).toBe(1);
  });
});

const removeAllTestCases = async () => {
  await Promise.all([
    removeTestCasesInInitialSearchDynamoDB(validACN),
    removeTestCasesInInitialSearchDynamoDB(validABN),
    removeTestCasesInInitialSearchDynamoDB(nonSupportedABN),
  ]);
};
