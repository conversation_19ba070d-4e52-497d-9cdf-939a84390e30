import { CloudFormation } from '@aws-sdk/client-cloudformation';
import yargs from 'yargs';

const stackOutputCache = new Map<string, any>();

const componentName = 'os';
const partName = 'engine';
const region = process.env.AWS_REGION ?? 'ap-southeast-2';
let stage = process.env.STAGE;

const getEngineStackName = () => `${stage}-${componentName}-${partName}`;
const getDbStackName = () => `${stage}-${componentName}-${partName}-dynamodb`;
const getPrivateApiStackName = () => `${getEngineStackName()}-private-api`

const getLambdaNameFromArn = (arn: string) => {
  const splits = arn.split(':');
  return splits[6];
};

export const setAWSEnvironment = () => {
  const argv = yargs.options({
    stage: {type: 'string', default: false},
  }).parseSync();
  if (argv.stage) {
    stage = argv.stage as string;
  }
};

const getStackOutputs = async (stackName: any) => {
  if (stackOutputCache.has(stackName)) {
    return stackOutputCache.get(stackName);
  }
  const cloudformation = new CloudFormation({
    region,
  });
  return cloudformation
    .describeStacks({StackName: stackName})
    .then((data: any) => {
      stackOutputCache.set(stackName, data.Stacks[0].Outputs);
      return data.Stacks[0].Outputs;
    })
    .catch((e: any) => {
      console.error(e);
      throw e;
    });
};

const getLambdaName = async (keyName: string) => {
  const stackOutputs = await getStackOutputs(getEngineStackName());
  const arn = stackOutputs.find(
    (output: any) =>
      output.OutputKey === keyName,
  ).OutputValue;
  return getLambdaNameFromArn(arn);
};

const getPrivateApiLambdaName = async (keyName: string) => {
  const stackOutputs = await getStackOutputs(getPrivateApiStackName());
  const arn = stackOutputs.find(
    (output: any) =>
      output.OutputKey === keyName,
  ).OutputValue;
  return getLambdaNameFromArn(arn);
}

const getDynamoDbName = async (keyName: string) => {
  const stackOutputs = await getStackOutputs(`${getEngineStackName()}-dynamodb`);
  return stackOutputs.find(
    (output: any) =>
      output.OutputKey === keyName,
  ).OutputValue;
};

export const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

//Get Lambda Information
export const getInitialCompanySearchLambda = () => getLambdaName('InitialCompanySearchHandlerLambdaFunctionQualifiedArn');
export const getFullCompanyProfileSearchLambda = () => getLambdaName('FullCompanyProfileSearchHandlerLambdaFunctionQualifiedArn');
export const getCustomerScreeningSearchLambda = () => getLambdaName('CustomerScreeningHandlerLambdaFunctionQualifiedArn');
export const getEntityScreeningSearchLambda = () => getLambdaName('EntityScreeningHandlerLambdaFunctionQualifiedArn');
export const getCustomerSafeHarbourLambda = () => getLambdaName('SafeHarbourHandlerLambdaFunctionQualifiedArn');
export const getAmexMerchantSubmissionLambda = () => getLambdaName('AmexMerchantSubmissionHandlerLambdaFunctionQualifiedArn');
export const getIdvSafeHarbourSearchLambda = () => getLambdaName('IdvAndSafeHarbourHandlerLambdaFunctionQualifiedArn');

// Get Private API Stack lambda information
export const getSelfieVerificationRequestTokenLambda = () => getPrivateApiLambdaName('OnfidoTokenRequestHandlerLambdaFunctionQualifiedArn');
export const getDocumentKeyExchangeLambda = () => getPrivateApiLambdaName('DocumentsKeyExchangeHandlerLambdaFunctionQualifiedArn')

//Get DynamoDB Information
export const getInitialCompanySearchTable = () => getDynamoDbName('InitialCompanySearchTable');
export const getInitialCompanySearchTableEntityUuidGIS = () => getDynamoDbName('InitialCompanySearchTableGSI');
export const getInitialCompanySearchTableCustomerUuidGIS = () => getDynamoDbName('InitialCompanySearchTableCustomerGSI');
export const getFullCompanyProfileSearchTable = () => getDynamoDbName('FullCompanyProfileSearchTable');
export const getCustomerScreeningSearchTable = () => getDynamoDbName('CustomerScreeningSearchTable');
export const getEntityScreeningSearchTable = () => getDynamoDbName('EntityScreeningSearchTable');
export const getIdvSafeHarbourSearchTable = () => getDynamoDbName('IdvSafeHarbourSearchTable');
export const getSelfieVerificationSearchTable = () => getDynamoDbName('SelfieVerificationSearchTable');

export const getRegion = () => region;
