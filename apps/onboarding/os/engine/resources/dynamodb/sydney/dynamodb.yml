Resources:
  InitialCompanySearchTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:custom.provider.initialCompanySearchTableName}
      DeletionProtectionEnabled: ${self:custom.tableDeletionProtection.${opt:stage}, self:custom.tableDeletionProtection.st}
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: customerUuid
          AttributeType: S
        - AttributeName: entityUuid
          AttributeType: S
        - AttributeName: expirationTime
          AttributeType: N
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: expirationTime
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: ${self:custom.initialCompanySearchTable.entityUuidGSI}
          KeySchema:
            - AttributeName: entityUuid
              KeyType: HASH
            - AttributeName: expirationTime
              KeyType: RANGE
          Projection:
            NonKeyAttributes:
              - acn
            ProjectionType: INCLUDE
        - IndexName: ${self:custom.initialCompanySearchTable.customerUuidGSI}
          KeySchema:
            - AttributeName: customerUuid
              KeyType: HASH
            - AttributeName: expirationTime
              KeyType: RANGE
          Projection:
            NonKeyAttributes:
              - acn
            ProjectionType: INCLUDE
      TimeToLiveSpecification:
        AttributeName: expirationTime
        Enabled: true
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: COMPONENT_NAME
          Value: ${env:COMPONENT_NAME, 'os'}
        - Key: PART_NAME
          Value: ${env:PART_NAME, 'engine'}
        - Key: STAGE
          Value: ${opt:stage}
        - Key: service
          Value: ${self:service}
  FullCompanyProfileSearchTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:custom.provider.fullCompanyProfileSearchTableName}
      DeletionProtectionEnabled: ${self:custom.tableDeletionProtection.${opt:stage}, self:custom.tableDeletionProtection.st}
      AttributeDefinitions:
        - AttributeName: entityUuid
          AttributeType: S
        - AttributeName: expirationTime
          AttributeType: N
      KeySchema:
        - AttributeName: entityUuid
          KeyType: HASH
        - AttributeName: expirationTime
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: expirationTime
        Enabled: true
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: COMPONENT_NAME
          Value: ${env:COMPONENT_NAME, 'os'}
        - Key: PART_NAME
          Value: ${env:PART_NAME, 'engine'}
        - Key: STAGE
          Value: ${opt:stage}
        - Key: service
          Value: ${self:service}
        - Key: env
          Value: ${opt:stage}
  CustomerScreeningSearchTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:custom.provider.customerScreeningSearchTableName}
      DeletionProtectionEnabled: ${self:custom.tableDeletionProtection.${opt:stage}, self:custom.tableDeletionProtection.st}
      AttributeDefinitions:
        - AttributeName: customerUuid
          AttributeType: S
        - AttributeName: expirationTime
          AttributeType: N
      KeySchema:
        - AttributeName: customerUuid
          KeyType: HASH
        - AttributeName: expirationTime
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: expirationTime
        Enabled: true
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: COMPONENT_NAME
          Value: ${env:COMPONENT_NAME, 'os'}
        - Key: PART_NAME
          Value: ${env:PART_NAME, 'engine'}
        - Key: STAGE
          Value: ${opt:stage}
        - Key: service
          Value: ${self:service}
        - Key: env
          Value: ${opt:stage}
  EntityScreeningSearchTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:custom.provider.entityScreeningSearchTableName}
      DeletionProtectionEnabled: ${self:custom.tableDeletionProtection.${opt:stage}, self:custom.tableDeletionProtection.st}
      AttributeDefinitions:
        - AttributeName: entityUuid
          AttributeType: S
        - AttributeName: expirationTime
          AttributeType: N
      KeySchema:
        - AttributeName: entityUuid
          KeyType: HASH
        - AttributeName: expirationTime
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: expirationTime
        Enabled: true
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: COMPONENT_NAME
          Value: ${env:COMPONENT_NAME, 'os'}
        - Key: PART_NAME
          Value: ${env:PART_NAME, 'engine'}
        - Key: STAGE
          Value: ${opt:stage}
        - Key: service
          Value: ${self:service}
        - Key: env
          Value: ${opt:stage}
  IdvSafeHarbourSearchTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:custom.provider.idvSafeHarbourSearchTableName}
      DeletionProtectionEnabled: ${self:custom.tableDeletionProtection.${opt:stage}, self:custom.tableDeletionProtection.st}
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: expirationTime
          AttributeType: N
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: expirationTime
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: expirationTime
        Enabled: true
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: COMPONENT_NAME
          Value: ${env:COMPONENT_NAME, 'os'}
        - Key: PART_NAME
          Value: ${env:PART_NAME, 'engine'}
        - Key: STAGE
          Value: ${opt:stage}
        - Key: service
          Value: ${self:service}
        - Key: env
          Value: ${opt:stage}
  IdvSearchTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:custom.provider.idvSearchTableName}
      DeletionProtectionEnabled: ${self:custom.tableDeletionProtection.${opt:stage}, self:custom.tableDeletionProtection.st}
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: expirationTime
          AttributeType: N
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: expirationTime
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: expirationTime
        Enabled: true
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: COMPONENT_NAME
          Value: ${env:COMPONENT_NAME, 'os'}
        - Key: PART_NAME
          Value: ${env:PART_NAME, 'engine'}
        - Key: STAGE
          Value: ${opt:stage}
        - Key: service
          Value: ${self:service}
        - Key: env
          Value: ${opt:stage}
  SafeharbourSearchTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:custom.provider.safeharbourSearchTableName}
      DeletionProtectionEnabled: ${self:custom.tableDeletionProtection.${opt:stage}, self:custom.tableDeletionProtection.st}
      AttributeDefinitions:
        - AttributeName: customerUuid
          AttributeType: S
        - AttributeName: expirationTime
          AttributeType: N
      KeySchema:
        - AttributeName: customerUuid
          KeyType: HASH
        - AttributeName: expirationTime
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: expirationTime
        Enabled: true
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: COMPONENT_NAME
          Value: ${env:COMPONENT_NAME, 'os'}
        - Key: PART_NAME
          Value: ${env:PART_NAME, 'engine'}
        - Key: STAGE
          Value: ${opt:stage}
        - Key: service
          Value: ${self:service}
        - Key: env
          Value: ${opt:stage}
  SelfieVerificationSearchTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:custom.provider.selfieVerificationSearchTableName}
      DeletionProtectionEnabled: ${self:custom.tableDeletionProtection.${opt:stage}, self:custom.tableDeletionProtection.st}
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: type
          AttributeType: S
        - AttributeName: customerUuid
          AttributeType: S
        - AttributeName: entityUuid
          AttributeType: S
        - AttributeName: applicantId
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
        - AttributeName: type
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: expirationTime
        Enabled: true
      GlobalSecondaryIndexes:
        - IndexName: EntityUuid
          KeySchema:
            - AttributeName: entityUuid
              KeyType: HASH
            - AttributeName: type
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: CustomerUuid
          KeySchema:
            - AttributeName: customerUuid
              KeyType: HASH
            - AttributeName: type
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: ApplicantId
          KeySchema:
            - AttributeName: applicantId
              KeyType: HASH
            - AttributeName: type
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: COMPONENT_NAME
          Value: ${env:COMPONENT_NAME, 'os'}
        - Key: PART_NAME
          Value: ${env:PART_NAME, 'engine'}
        - Key: STAGE
          Value: ${opt:stage}
        - Key: service
          Value: ${self:service}
        - Key: env
          Value: ${opt:stage}
  DocumentsVerificationSessionTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: ${self:custom.provider.documentsVerificationSessionTableName}
      DeletionProtectionEnabled: ${self:custom.tableDeletionProtection.${opt:stage}, self:custom.tableDeletionProtection.st}
      AttributeDefinitions:
        - AttributeName: customerUuid
          AttributeType: S
        - AttributeName: sessionId
          AttributeType: S
      KeySchema:
        - AttributeName: customerUuid
          KeyType: HASH
        - AttributeName: sessionId
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: expirationTime
        Enabled: true
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: COMPONENT_NAME
          Value: ${env:COMPONENT_NAME, 'os'}
        - Key: PART_NAME
          Value: ${env:PART_NAME, 'engine'}
        - Key: STAGE
          Value: ${opt:stage}
        - Key: service
          Value: ${self:service}
        - Key: env
          Value: ${opt:stage}
Outputs:
  InitialCompanySearchTable:
    Description: The initial company search table
    Value: ${self:custom.initialCompanySearchTable.name}
    Export:
      Name: ${self:provider.stackName}:InitialCompanySearchTable
  InitialCompanySearchTableGSI:
    Description: The initial company search table GSI
    Value: ${self:custom.initialCompanySearchTable.entityUuidGSI}
    Export:
      Name: ${self:provider.stackName}:InitialCompanySearchTableGSI
  InitialCompanySearchTableCustomerGSI:
    Description: The initial company search table Customer UUID GSI
    Value: ${self:custom.initialCompanySearchTable.customerUuidGSI}
    Export:
      Name: ${self:provider.stackName}:InitialCompanySearchTableCustomerGSI
  FullCompanyProfileSearchTable:
    Description: The full company profile search table
    Value: ${self:custom.fullCompanyProfileSearchTable.name}
    Export:
      Name: ${self:provider.stackName}:FullCompanyProfileSearchTable
  CustomerScreeningSearchTable:
    Description: The customer screening search table
    Value: ${self:custom.customerScreeningSearchTable.name}
    Export:
      Name: ${self:provider.stackName}:CustomerScreeningSearchTable
  EntityScreeningSearchTable:
    Description: The entity screening search table
    Value: ${self:custom.entityScreeningSearchTable.name}
    Export:
      Name: ${self:provider.stackName}:EntityScreeningSearchTable
  IdvSafeHarbourSearchTable:
    Description: The identity document verification search table
    Value: ${self:custom.idvSafeHarbourSearchTable.name}
    Export:
      Name: ${self:provider.stackName}:IdvSafeHarbourSearchTable
  IdvSearchTable:
    Description: The identity document verification search table
    Value: ${self:custom.idvSearchTable.name}
    Export:
      Name: ${self:provider.stackName}:idvSearchTable
  SafeharbourSearchTable:
    Description: Search Harbour Search Table
    Value: ${self:custom.safeharbourSearchTable.name}
    Export:
      Name: ${self:provider.stackName}:SafeharbourSearchTable
  SelfieVerificationSearchTable:
    Description: Search Harbour Search Table
    Value: ${self:custom.selfieVerificationSearchTable.name}
    Export:
      Name: ${self:provider.stackName}:SelfieVerificationSearchTable
  DocumentsVerificationSessionTable:
    Description: Document verification session Table
    Value: ${self:custom.documentsVerificationSessionTable.name}
    Export:
      Name: ${self:provider.stackName}:DocumentsVerificationSessionTable
