import { EntityType } from '@npco/component-dto-core';

import { jest } from '@jest/globals';
import AWSXray from 'aws-xray-sdk';
import fs from 'fs';
import nock from 'nock';
import path from 'path';
import * as url from 'url';

import { EnvironmentService } from '../../config/envService.js';
import { BusinessIdentifierType } from '../../types.js';
import { maskEquifaxCredential } from '../lib/index.js';
import type { AXIOSResponse, EquifaxInitialCompanyResult } from '../types.js';

import {
  detectBusinessIdentifierType,
  generateRequestXML,
  initialCompanySearch,
  isActive,
  maskEquifaxInitialSearchAXOISResponse,
} from './initialCompanySearch.js';

// eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle
const __dirname = url.fileURLToPath(new URL('.', import.meta.url));

jest.mock('aws-xray-sdk');

const BASE_URL = 'https://fake_url/sys1';
const HOST = 'https://fake_url';
const PATH = '/sys1';

function setTestEnvironmentVariables() {
  process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_URL = BASE_URL;
  process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_MODE = 'test';
  process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_USERNAME = 'equifax_username';
  process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_PASSWORD = 'equifax_password';
  process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_SUBSCRIBER_IDENTIFIER = 'equifax_subscriber';
  process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_SECURITY_CODE = 'equifax_security_code';
}

const nock200ResponseWithData = (data: string) => nock(HOST).post(PATH).reply(200, data);

const nock200ResponseWithFile = (relativeFilePath: string) =>
  nock(HOST).post(PATH).replyWithFile(200, path.join(__dirname, 'mockTestResponse', relativeFilePath));

const nockResponseWithError = (error: string) => nock(HOST).post(PATH).replyWithError(error);
describe('initial search test suite', () => {
  beforeAll(() => {
    jest.spyOn(AWSXray, 'getSegment').mockReturnValue(null as never);
  });

  afterAll(nock.restore);
  afterEach(nock.cleanAll);

  describe('detectBusinessIdentifierType', () => {
    it('returns ABN if the input business Identifier is ABN', () => {
      const validABNWithoutSpace = '95*********';
      expect(detectBusinessIdentifierType(validABNWithoutSpace)).toBe(BusinessIdentifierType.ABN);
      const validABNWithSpace = '95 ***********';
      expect(detectBusinessIdentifierType(validABNWithSpace)).toBe(BusinessIdentifierType.ABN);
    });

    it('returns ACN if the input business identifier is ACN', () => {
      const validACNWithoutSpace = '*********';
      expect(detectBusinessIdentifierType(validACNWithoutSpace)).toBe(BusinessIdentifierType.ACN);

      const validACNWithSpace = '***********';
      expect(detectBusinessIdentifierType(validACNWithSpace)).toBe(BusinessIdentifierType.ACN);
    });

    it('returns UNKNOWN if the input business identifier is invalid', async () => {
      const invalidLengthID = '9563732114';
      expect(detectBusinessIdentifierType(invalidLengthID)).toBe(BusinessIdentifierType.INVALID);
      const invalidElevenDigitsID = '***********';
      expect(detectBusinessIdentifierType(invalidElevenDigitsID)).toBe(BusinessIdentifierType.INVALID);
      const invalidElevenDigitsIDWithSpace = '95 ***********';
      expect(detectBusinessIdentifierType(invalidElevenDigitsIDWithSpace)).toBe(BusinessIdentifierType.INVALID);
      const invalidNineDigitsID = '*********';
      expect(detectBusinessIdentifierType(invalidNineDigitsID)).toBe(BusinessIdentifierType.INVALID);
      const invalidNineDigitsIDWithSpace = '***********';
      expect(detectBusinessIdentifierType(invalidNineDigitsIDWithSpace)).toBe(BusinessIdentifierType.INVALID);
    });
  });

  describe('initialCompanySearch - Request and Response XML', () => {
    setTestEnvironmentVariables();
    const envService = new EnvironmentService();
    const aggregateId = 'abc';

    it('Throw an error if invalid input', () => {
      const invalidID = '9563732114';
      expect(initialCompanySearch(invalidID, envService, aggregateId)).rejects.toThrow('Invalid Business Identifier');
    });

    test.each(['79*********', '79 ***********'])('generates a valid Request XML for ABN: %s', async (validABN) => {
      const validRequestXML = `<?xml version="1.0" encoding="UTF-8"?>
<BCAmessage type="REQUEST">
  <BCAaccess>
    <BCAaccess-code>${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_USERNAME}</BCAaccess-code>
    <BCAaccess-pwd>${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_PASSWORD}</BCAaccess-pwd>
  </BCAaccess>
  <BCAservice>
    <BCAservice-client-ref>${aggregateId}</BCAservice-client-ref>
    <BCAservice-code>BCA020</BCAservice-code>
    <BCAservice-code-version>V00</BCAservice-code-version>
    <BCAservice-data>
      <request version="2.0" mode="${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_MODE}" transaction-reference="String">
        <subscriber-details>
          <subscriber-identifier>${
            process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_SUBSCRIBER_IDENTIFIER
          }</subscriber-identifier>
          <security>${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_SECURITY_CODE}</security>
        </subscriber-details>
        <product name="organisation-identification-v2"/>
        <working-with-organisation search-type="registered-companies-businesses">
          <australian-business-number>${validABN.replace(/ /g, '')}</australian-business-number>
        </working-with-organisation>
      </request>
    </BCAservice-data>
  </BCAservice>
</BCAmessage>`;

      const validRequestReturnXML = `<?xml version="1.0" encoding="UTF-8"?>
<BCAmessage type="REQUEST">
  <BCAaccess>
    <BCAaccess-code>${'*'.repeat(process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_USERNAME?.length || 0)}</BCAaccess-code>
    <BCAaccess-pwd>${'*'.repeat(process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_PASSWORD?.length || 0)}</BCAaccess-pwd>
  </BCAaccess>
  <BCAservice>
    <BCAservice-client-ref>${aggregateId}</BCAservice-client-ref>
    <BCAservice-code>BCA020</BCAservice-code>
    <BCAservice-code-version>V00</BCAservice-code-version>
    <BCAservice-data>
      <request version="2.0" mode="${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_MODE}" transaction-reference="String">
        <subscriber-details>
          <subscriber-identifier>${
            process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_SUBSCRIBER_IDENTIFIER
          }</subscriber-identifier>
          <security>${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_SECURITY_CODE}</security>
        </subscriber-details>
        <product name="organisation-identification-v2"/>
        <working-with-organisation search-type="registered-companies-businesses">
          <australian-business-number>${validABN.replace(/ /g, '')}</australian-business-number>
        </working-with-organisation>
      </request>
    </BCAservice-data>
  </BCAservice>
</BCAmessage>`;

      nock200ResponseWithData('');

      const requestXml = generateRequestXML(validABN, BusinessIdentifierType.ABN, envService, aggregateId);
      const requestReturnXml = maskEquifaxCredential(requestXml, envService);
      expect(requestXml).toBe(validRequestXML);
      expect(requestReturnXml).toBe(validRequestReturnXML);

      const { requestXml: initialCompanySearchRequestReturnXml } = await initialCompanySearch(
        validABN,
        envService,
        aggregateId,
      );
      expect(initialCompanySearchRequestReturnXml).toBe(validRequestReturnXML);
    });

    test.each(['*********', '***********'])('generate a valid Request XML for ACN', async (validACN) => {
      const validRequestXML = `<?xml version="1.0" encoding="UTF-8"?>
<BCAmessage type="REQUEST">
  <BCAaccess>
    <BCAaccess-code>${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_USERNAME}</BCAaccess-code>
    <BCAaccess-pwd>${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_PASSWORD}</BCAaccess-pwd>
  </BCAaccess>
  <BCAservice>
    <BCAservice-client-ref>${aggregateId}</BCAservice-client-ref>
    <BCAservice-code>BCA020</BCAservice-code>
    <BCAservice-code-version>V00</BCAservice-code-version>
    <BCAservice-data>
      <request version="2.0" mode="${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_MODE}" transaction-reference="String">
        <subscriber-details>
          <subscriber-identifier>${
            process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_SUBSCRIBER_IDENTIFIER
          }</subscriber-identifier>
          <security>${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_SECURITY_CODE}</security>
        </subscriber-details>
        <product name="organisation-identification-v2"/>
        <working-with-organisation search-type="registered-companies-businesses">
          <organisation-number>${validACN.replace(/ /g, '')}</organisation-number>
        </working-with-organisation>
      </request>
    </BCAservice-data>
  </BCAservice>
</BCAmessage>`;

      const validRequestReturnXML = `<?xml version="1.0" encoding="UTF-8"?>
<BCAmessage type="REQUEST">
  <BCAaccess>
    <BCAaccess-code>${'*'.repeat(process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_USERNAME?.length || 0)}</BCAaccess-code>
    <BCAaccess-pwd>${'*'.repeat(process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_PASSWORD?.length || 0)}</BCAaccess-pwd>
  </BCAaccess>
  <BCAservice>
    <BCAservice-client-ref>${aggregateId}</BCAservice-client-ref>
    <BCAservice-code>BCA020</BCAservice-code>
    <BCAservice-code-version>V00</BCAservice-code-version>
    <BCAservice-data>
      <request version="2.0" mode="${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_MODE}" transaction-reference="String">
        <subscriber-details>
          <subscriber-identifier>${
            process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_SUBSCRIBER_IDENTIFIER
          }</subscriber-identifier>
          <security>${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_SECURITY_CODE}</security>
        </subscriber-details>
        <product name="organisation-identification-v2"/>
        <working-with-organisation search-type="registered-companies-businesses">
          <organisation-number>${validACN.replace(/ /g, '')}</organisation-number>
        </working-with-organisation>
      </request>
    </BCAservice-data>
  </BCAservice>
</BCAmessage>`;

      nock200ResponseWithData('');

      const requestXml = generateRequestXML(validACN, BusinessIdentifierType.ACN, envService, aggregateId);
      const requestReturnXml = maskEquifaxCredential(requestXml, envService);
      expect(requestXml).toBe(validRequestXML);
      expect(requestReturnXml).toBe(validRequestReturnXML);

      const { requestXml: initialCompanySearchRequestReturnXml } = await initialCompanySearch(
        validACN,
        envService,
        aggregateId,
      );
      expect(initialCompanySearchRequestReturnXml).toBe(validRequestReturnXML);
    });

    it('generate the responseXML in the response', async () => {
      const mockResponseXml = `<?xml version="1.0"?>`;
      nock200ResponseWithData(mockResponseXml);

      const businessIdentifier = '*********';

      const { responseXml } = await initialCompanySearch(businessIdentifier, envService, aggregateId);
      expect(responseXml).toBe(mockResponseXml);
    });

    it('can handle the responseXML is too large - get the first 200000 characters', async () => {
      nock200ResponseWithFile('initial_search_response_too_large.xml');
      const businessIdentifier = '***********';
      const { responseXml } = await initialCompanySearch(businessIdentifier, envService, aggregateId);
      expect(responseXml).toBeDefined();
      expect(responseXml?.length).toEqual(200000);
      expect(responseXml).toBe(
        fs.readFileSync(path.join(__dirname, 'mockTestResponse', 'initial_search_response_too_large_cache')).toString(),
      );
    });
  });

  describe('initialCompanySearch - Testing Company Data', () => {
    const BRAMBLES_AUSTRALIA_BUSINESS_NAME = 'BRAMBLES AUSTRALIA PTY LTD';

    setTestEnvironmentVariables();
    const envService = new EnvironmentService();
    const aggregateId = 'abc';

    it('return detail with ABN only (Individual) - Single Record', async () => {
      nock200ResponseWithFile('ABN_***********_Response_single_record_with_no_acn.xml');

      const businessIdentifier = '***********';
      const { abn, acn, name, active } = await initialCompanySearch(businessIdentifier, envService, aggregateId);

      expect(abn).toBe(businessIdentifier);
      expect(acn).toBeUndefined();
      expect(active).toBe(true);
      expect(name).toBe('NICHOLAS MICHAEL RUTLAND');
    });

    it('return detail with ABN only (Individual) - Multiple Records', async () => {
      nock200ResponseWithFile('ABN_***********_Response_multiple_records_with_no_acn.xml');

      const businessIdentifier = '***********';
      const businessName = 'NICHOLAS MICHAEL RUTLAND';
      const { abn, acn, name, active } = await initialCompanySearch(businessIdentifier, envService, aggregateId);

      expect(abn).toBe(businessIdentifier);
      expect(acn).toBeUndefined();
      expect(name).toBe(businessName);
      expect(active).toBe(true);
    });

    it('return detail with ABN and ACN - Multiple records with a single record + ACN header', async () => {
      nock200ResponseWithFile('ABN_79*********_Response_with_multiple_records_and_acn_header.xml');

      const businessIdentifier = '79*********';
      const expertAcn = '*********';
      const businessName = BRAMBLES_AUSTRALIA_BUSINESS_NAME;
      const { abn, acn, name, active } = await initialCompanySearch(businessIdentifier, envService, aggregateId);

      expect(abn).toBe(businessIdentifier);
      expect(acn).toBe(expertAcn);
      expect(name).toBe(businessName);
      expect(active).toBe(false);
    });

    it('return detail with ABN and ACN - Multiple records with ACN in asicBnrReport', async () => {
      nock200ResponseWithFile('ABN_79*********_Response_with_multiple_records_asicBnrReport_acn_header.xml');

      const businessIdentifier = '79*********';
      const expertAcn = '*********';
      const businessName = BRAMBLES_AUSTRALIA_BUSINESS_NAME;
      const { abn, acn, name, active } = await initialCompanySearch(businessIdentifier, envService, aggregateId);

      expect(abn).toBe(businessIdentifier);
      expect(acn).toBe(expertAcn);
      expect(name).toBe(businessName);
      expect(active).toBe(true);
    });

    it('return detail with ABN and ACN - Single record with ACN in australianBusinessRegisterReport', async () => {
      nock200ResponseWithFile(
        'ABN_79*********_Response_with_single_records_australianBusinessRegisterReport_acn_header.xml',
      );

      const businessIdentifier = '79*********';
      const expertAcn = '*********';
      const businessName = BRAMBLES_AUSTRALIA_BUSINESS_NAME;
      const { abn, acn, name, active } = await initialCompanySearch(businessIdentifier, envService, aggregateId);

      expect(abn).toBe(businessIdentifier);
      expect(acn).toBe(expertAcn);
      expect(name).toBe(businessName);
      expect(active).toBe(true);
    });

    it('return detail with ABN and ACN - Single record with ACN in asicReport', async () => {
      nock200ResponseWithFile('ACN_*********_Response_asic_report_only.xml');

      const businessIdentifier = '79*********';
      const expertAcn = '*********';
      const businessName = 'BRAMBLES INTERNATIONAL PTY LTD';
      const { abn, acn, name, active } = await initialCompanySearch(businessIdentifier, envService, aggregateId);

      expect(abn).toBe(businessIdentifier);
      expect(acn).toBe(expertAcn);
      expect(name).toBe(businessName);
      expect(active).toBe(false);
    });

    it('return detail with ABN - sole trader record that is cancelled', async () => {
      nock200ResponseWithFile('abn_cancelled.xml');

      const businessIdentifier = '***********';
      const businessName = 'YICK HON JOSEPH SO';
      const { abn, acn, name, active } = await initialCompanySearch(businessIdentifier, envService, aggregateId);

      expect(abn).toBe(businessIdentifier);
      expect(acn).toBeUndefined();
      expect(name).toBe(businessName);
      expect(active).toBe(false);
    });

    it('return inactive - real data example', async () => {
      nock200ResponseWithFile('exad_real_data.xml');

      const businessIdentifier = '55*********';
      const { abn, acn, name, active } = await initialCompanySearch(businessIdentifier, envService, aggregateId);

      expect(abn).toBe(businessIdentifier);
      expect(acn).toBe('*********');
      expect(name).toBe('PRECISION AUTO GROUP PTY LTD');
      expect(active).toBe(false);
    });

    it('return detail with ABN and ACN - Match Data Only', async () => {
      nock200ResponseWithFile('ACN_*********_Response_match_data_only.xml');

      const businessIdentifier = '79*********';
      const expertAcn = '*********';
      const businessName = 'BRAMBLES INTERNATIONAL PTY LTD';
      const { abn, acn, name, active } = await initialCompanySearch(businessIdentifier, envService, aggregateId);

      expect(abn).toBe(businessIdentifier);
      expect(acn).toBe(expertAcn);
      expect(name).toBe(businessName);
      expect(active).toBe(true);
    });

    it('return detail with ACN only', async () => {
      nock200ResponseWithFile('ACN_*********_Response_with_no_abn.xml');

      const businessIdentifier = '*********';
      const businessName = 'H J CARTER & ASSOCIATES PTY LTD';
      const { abn, acn, name, active } = await initialCompanySearch(businessIdentifier, envService, aggregateId);
      expect(abn).toBeUndefined();
      expect(acn).toBe(businessIdentifier);
      expect(name).toBe(businessName);
      expect(active).toBe(false);
    });

    it('return undefined if the no report existed (theory only, for branch test)', async () => {
      nock200ResponseWithFile('mock_return_testing_xml_without_reports.xml');

      const businessIdentifier = '*********';
      const businessName = 'H J CARTER & ASSOCIATES PTY LTD';
      const { abn, acn, name, active } = await initialCompanySearch(businessIdentifier, envService, aggregateId);
      expect(abn).toBeUndefined();
      expect(acn).toBeUndefined();
      expect(name).toBe(businessName);
      expect(active).toBe(true);
    });
  });

  describe('initialCompanySearch - Testing Company Type', () => {
    setTestEnvironmentVariables();
    const envService = new EnvironmentService();
    const aggregateId = 'abc';
    const businessIdentifier = '79*********';

    test.each([
      ['CCL', EntityType.PARTNERSHIP],
      ['CCN', EntityType.ASSOCIATION_UNINCORPORATED],
      ['CGE', EntityType.GOVERNMENT],
      ['CGP', EntityType.PARTNERSHIP],
      ['COP', EntityType.ASSOCIATION],
      ['CUT', EntityType.TRUST],
      ['DIT', EntityType.TRUST],
      ['DST', EntityType.TRUST],
      ['DTT', EntityType.TRUST],
      ['FPT', EntityType.PARTNERSHIP],
      ['FUT', EntityType.TRUST],
      ['FXT', EntityType.TRUST],
      ['HYT', EntityType.TRUST],
      ['IND', EntityType.INDIVIDUAL],
      ['LCB', EntityType.PARTNERSHIP],
      ['LCL', EntityType.PARTNERSHIP],
      ['LCN', EntityType.ASSOCIATION_UNINCORPORATED],
      ['LCR', EntityType.COMPANY],
      ['LGC', EntityType.COMPANY],
      ['LGE', EntityType.GOVERNMENT],
      ['LGP', EntityType.PARTNERSHIP],
      ['LPT', EntityType.PARTNERSHIP],
      ['OIE', EntityType.ASSOCIATION],
      ['PQT', EntityType.TRUST],
      ['PRV', EntityType.COMPANY],
      ['PTR', EntityType.PARTNERSHIP],
      ['PTT', EntityType.TRUST],
      ['PUB', EntityType.COMPANY],
      ['PUT', EntityType.TRUST],
      ['SCB', EntityType.COMPANY],
      ['SCL', EntityType.PARTNERSHIP],
      ['SCN', EntityType.ASSOCIATION_UNINCORPORATED],
      ['SCR', EntityType.COMPANY],
      ['SGC', EntityType.COMPANY],
      ['SGE', EntityType.GOVERNMENT],
      ['SGP', EntityType.PARTNERSHIP],
      ['SMF', EntityType.TRUST],
      ['TCB', EntityType.PARTNERSHIP],
      ['TCL', EntityType.PARTNERSHIP],
      ['TCN', EntityType.ASSOCIATION_UNINCORPORATED],
      ['TCR', EntityType.PARTNERSHIP],
      ['TGE', EntityType.GOVERNMENT],
      ['TGP', EntityType.PARTNERSHIP],
      ['TRT', EntityType.TRUST],
      ['UIE', EntityType.ASSOCIATION_UNINCORPORATED],
      ['XXX', EntityType.OTHER],
    ])('generate correct customer type for ABR Entity Type - %s', async (entityTypeCode, expectedType) => {
      const mockResponseXml = `<?xml version="1.0"?>
<BCAmessage service-request-id="***********" type="RESPONSE">
    <BCAservices>
        <BCAservice>
            <BCAservice-code>BCA020</BCAservice-code>
            <BCAservice-code-version>V00</BCAservice-code-version>
            <BCAservice-client-ref>178bd6c4-a18d-4117-8e72-748a5a0a69f2</BCAservice-client-ref>
            <BCAservice-data>
                <response version="1-36-1">
                    <org-id-result>
                        <match-name>THE WASTE PEOPLE </match-name>
                        <organisation-number>BN98360928</organisation-number>
                        <organisation-type>*</organisation-type>
                        <australian-business-number>79*********</australian-business-number>
                        <organisation-number-heading>number</organisation-number-heading>
                        <state>NSW</state>
                        <australian-business-register-report>
                            <ASICNumber>*********</ASICNumber>
                            <ASICNumberType>ACN</ASICNumberType>
                            <ABN>79*********</ABN>
                            <ABNStatus>ACT</ABNStatus>
                            <ABNStatusFromDate>2000-03-04</ABNStatusFromDate>
                            <EntityTypeInd>${entityTypeCode}</EntityTypeInd>
                            <Non-IndividualName>BRAMBLES AUSTRALIA PTY LTD</Non-IndividualName>
                        </australian-business-register-report>
                    </org-id-result>
                </response>
            </BCAservice-data>
        </BCAservice>
    </BCAservices>
</BCAmessage>`;
      nock200ResponseWithData(mockResponseXml);

      const { type, abrEntityType, asicType } = await initialCompanySearch(businessIdentifier, envService, aggregateId);
      expect(type).toBe(expectedType);
      expect(abrEntityType).toBe(entityTypeCode);
      expect(asicType).toBeUndefined();
    });

    test.each([
      ['APTY', EntityType.COMPANY],
      ['APUB', EntityType.COMPANY],
      ['ASSN', EntityType.ASSOCIATION],
      ['BUSN', EntityType.COMPANY],
      ['CHAR', EntityType.OTHER],
      ['COMP', EntityType.OTHER],
      ['COOP', EntityType.ASSOCIATION],
      ['FNOS', EntityType.COMPANY],
      ['LTDP', EntityType.PARTNERSHIP],
      ['MISM', EntityType.OTHER],
      ['NONC', EntityType.OTHER],
      ['NRET', EntityType.OTHER],
      ['RACN', EntityType.OTHER],
      ['REBD', EntityType.OTHER],
      ['RSVN', EntityType.OTHER],
      ['SOLS', EntityType.COMPANY],
      ['TRST', EntityType.TRUST],
      ['XXXX', EntityType.OTHER],
    ])('generate correct customer type for ASIC Organisation Type - %s', async (organisationTypeCode, expectedType) => {
      const mockResponseXml = `<?xml version="1.0"?>
<BCAmessage service-request-id="***********" type="RESPONSE">
    <BCAservices>
        <BCAservice>
            <BCAservice-code>BCA020</BCAservice-code>
            <BCAservice-code-version>V00</BCAservice-code-version>
            <BCAservice-client-ref>178bd6c4-a18d-4117-8e72-748a5a0a69f2</BCAservice-client-ref>
            <BCAservice-data>
                <response version="1-36-1">
                    <org-id-result>
                        <match-name>THE WASTE PEOPLE </match-name>
                        <organisation-number>BN98360928</organisation-number>
                        <organisation-type>*</organisation-type>
                        <australian-business-number>79*********</australian-business-number>
                        <organisation-number-heading>number</organisation-number-heading>
                        <state>NSW</state>
                        <asic-report>
                            <asic-name>BRAMBLES AUSTRALIA PTY LTD</asic-name>
                            <asic-organisation-number>*********</asic-organisation-number>
                            <asic-organisation-number-heading>ACN</asic-organisation-number-heading>
                            <asic-type>${organisationTypeCode}</asic-type>
                            <asic-status>DRGD</asic-status>
                            <asic-state>NSW</asic-state>
                            <asic-jurisdiction>ASIC</asic-jurisdiction>
                        </asic-report>
                     </org-id-result>
                </response>
            </BCAservice-data>
        </BCAservice>
    </BCAservices>
</BCAmessage>`;
      nock200ResponseWithData(mockResponseXml);

      const { type, asicType, abrEntityType } = await initialCompanySearch(businessIdentifier, envService, aggregateId);
      expect(type).toBe(expectedType);
      expect(asicType).toBe(organisationTypeCode);
      expect(abrEntityType).toBeUndefined();
    });

    it('return NOT_SUPPORT if no valid report', async () => {
      const mockResponseXml = `<?xml version="1.0"?>
<BCAmessage service-request-id="***********" type="RESPONSE">
    <BCAservices>
        <BCAservice>
            <BCAservice-code>BCA020</BCAservice-code>
            <BCAservice-code-version>V00</BCAservice-code-version>
            <BCAservice-client-ref>178bd6c4-a18d-4117-8e72-748a5a0a69f2</BCAservice-client-ref>
            <BCAservice-data>
                <response version="1-36-1">
                    <org-id-result>
                        <match-name>THE WASTE PEOPLE </match-name>
                        <organisation-number>BN98360928</organisation-number>
                        <organisation-type>*</organisation-type>
                        <australian-business-number>79*********</australian-business-number>
                        <organisation-number-heading>number</organisation-number-heading>
                        <state>NSW</state>
                        <australian-business-register-report>
                            <ErrorMsg>Subject of your enquiry was not found.</ErrorMsg>
                        </australian-business-register-report>
                        <asic-report>
                            <ErrorMsg>Subject of your enquiry was not found.</ErrorMsg>
                        </asic-report>
                        <asic-bnr-report>
                          <ErrorMsg>Subject of your enquiry was not found.</ErrorMsg>
                        </asic-bnr-report>
                    </org-id-result>
                </response>
            </BCAservice-data>
        </BCAservice>
    </BCAservices>
</BCAmessage>`;
      nock200ResponseWithData(mockResponseXml);

      const { type } = await initialCompanySearch(businessIdentifier, envService, aggregateId);
      expect(type).toBe(EntityType.OTHER);
    });
  });

  describe('record not found', () => {
    setTestEnvironmentVariables();
    const envService = new EnvironmentService();
    const aggregateId = 'abc';
    const businessIdentifier = '79*********';

    it('return request and response xml only for not found case', async () => {
      nock200ResponseWithFile('Record_Not_Found.xml');
      const { name, abn, acn, type, requestXml, responseXml } = await initialCompanySearch(
        businessIdentifier,
        envService,
        aggregateId,
      );
      expect(name).toBeUndefined();
      expect(abn).toBeUndefined();
      expect(acn).toBeUndefined();
      expect(type).toBeUndefined();
      expect(requestXml).toBeDefined();
      expect(responseXml).toBeDefined();
    });
  });

  describe('Equifax API Error', () => {
    setTestEnvironmentVariables();
    const envService = new EnvironmentService();
    const aggregateId = 'abc';
    const businessIdentifier = '79*********';

    it('return request and response xml only Equifax API Error - Style 1', async () => {
      const errorType = 'AUTHENTICATION';
      const errorCode = 'ABC';
      const errorDescription = 'this is a string';
      const mockResponseXml = `<?xml version="1.0"?>
<BCAmessage service-request-id="***********" type="RESPONSE">
    <BCAservices>
        <BCAservice>
            <BCAservice-code>BCA020</BCAservice-code>
            <BCAservice-code-version>V00</BCAservice-code-version>
            <BCAservice-client-ref>abc</BCAservice-client-ref>
            <BCAservice-data>
                <BCAerror type="${errorType}">
                    <BCAerror-code>${errorCode}</BCAerror-code>
                    <BCAerror-description>${errorDescription}</BCAerror-description>
                </BCAerror>
            </BCAservice-data>
        </BCAservice>
    </BCAservices>
</BCAmessage>`;
      nock200ResponseWithData(mockResponseXml);

      await expect(initialCompanySearch(businessIdentifier, envService, aggregateId)).rejects.toThrow(
        `Equifax API ${errorType} Error`,
      );
    });

    it('return request and response xml only Equifax API Error - Style 2', async () => {
      const errorType = 'SYS';
      nock200ResponseWithFile('Equifax_error.xml');

      await expect(initialCompanySearch(businessIdentifier, envService, aggregateId)).rejects.toThrow(
        `Equifax API ${errorType} Error`,
      );
    });

    it('network error', async () => {
      const errorReason = 'Network Error';
      nockResponseWithError(errorReason);

      await expect(initialCompanySearch(businessIdentifier, envService, aggregateId)).rejects.toThrow(
        `Equifax Network Error - Error: ${errorReason}`,
      );
    });
  });

  describe('Masking the Equifax Initial Search Response', () => {
    setTestEnvironmentVariables();
    const envService = new EnvironmentService();
    const statusTest = 'This is a status Text';

    it('mask the personal information and the equifax credential correctly', () => {
      const mockAXIOSResponse: AXIOSResponse = {
        status: 200,
        statusText: statusTest,
        config: {
          data: `<xmlData>
  <username>
    ${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_USERNAME}
  </username>
  <password>
    ${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_PASSWORD}
  </password>
</xmlData>`,
        },
        data: `<xmlData>
    <match_name>ABCD EFGH</match_name>
    <IndividualName>ABCD EFGH</IndividualName>
    <OtherField>abcd efgh</OtherField>
</xmlData>`,
      };

      const expectedAXIOSResponse: AXIOSResponse = {
        status: 200,
        statusText: statusTest,
        config: {
          data: `<xmlData>
  <username>
    ${'*'.repeat(process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_USERNAME?.length || 0)}
  </username>
  <password>
    ${'*'.repeat(process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_PASSWORD?.length || 0)}
  </password>
</xmlData>`,
        },
        data: `<xmlData>
    <match_name>A********</match_name>
    <IndividualName>A********</IndividualName>
    <OtherField>abcd efgh</OtherField>
</xmlData>`,
      };

      expect(maskEquifaxInitialSearchAXOISResponse(mockAXIOSResponse, envService)).toEqual(expectedAXIOSResponse);
    });

    it('mask the personal information (empty) and the equifax credential correctly', () => {
      const mockAXIOSResponse: AXIOSResponse = {
        status: 200,
        statusText: statusTest,
        config: {
          data: `<xmlData>
  <username>
    ${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_USERNAME}
  </username>
  <password>
    ${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_PASSWORD}
  </password>
</xmlData>`,
        },
        data: `<xmlData>
    <match_name>ABCD EFGH</match_name>
    <IndividualName></IndividualName>
    <OtherField>abcd efgh</OtherField>
</xmlData>`,
      };

      const expectedAXIOSResponse: AXIOSResponse = {
        status: 200,
        statusText: statusTest,
        config: {
          data: `<xmlData>
  <username>
    ${'*'.repeat(process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_USERNAME?.length || 0)}
  </username>
  <password>
    ${'*'.repeat(process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_PASSWORD?.length || 0)}
  </password>
</xmlData>`,
        },
        data: `<xmlData>
    <match_name>ABCD EFGH</match_name>
    <IndividualName></IndividualName>
    <OtherField>abcd efgh</OtherField>
</xmlData>`,
      };

      expect(maskEquifaxInitialSearchAXOISResponse(mockAXIOSResponse, envService)).toEqual(expectedAXIOSResponse);
    });

    it('mask the equifax credential only for non-Individual data', () => {
      const mockAXIOSResponse: AXIOSResponse = {
        status: 200,
        statusText: statusTest,
        config: {
          data: `<xmlData>
  <username>
    ${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_USERNAME}
  </username>
  <password>
    ${process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_PASSWORD}
  </password>
</xmlData>`,
        },
        data: `<xmlData>
    <match_name>ABCD EFGH</match_name>
    <NonIndividualName>ABCD EFGH</NonIndividualName>
    <OtherField>abcd efgh</OtherField>
</xmlData>`,
      };

      const expectedAXIOSResponse: AXIOSResponse = {
        status: 200,
        statusText: statusTest,
        config: {
          data: `<xmlData>
  <username>
    ${'*'.repeat(process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_USERNAME?.length || 0)}
  </username>
  <password>
    ${'*'.repeat(process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_PASSWORD?.length || 0)}
  </password>
</xmlData>`,
        },
        data: `<xmlData>
    <match_name>ABCD EFGH</match_name>
    <NonIndividualName>ABCD EFGH</NonIndividualName>
    <OtherField>abcd efgh</OtherField>
</xmlData>`,
      };

      expect(maskEquifaxInitialSearchAXOISResponse(mockAXIOSResponse, envService)).toEqual(expectedAXIOSResponse);
    });
  });

  describe('Is an active business', () => {
    describe('ASIC report', () => {
      test.each([
        ['REGD', true],
        ['DRGD', false],
        ['EXAD', false],
      ])('return ASIC report status: %s as %s', async (status: string, result: boolean) => {
        const equifaxInitialCompanyResult: EquifaxInitialCompanyResult = {
          matchName: 'string',
          asicReport: {
            asicStatus: status,
          },
        };

        expect(isActive(equifaxInitialCompanyResult)).toBe(result);
      });
    });

    describe('ABR Report', () => {
      test.each([
        ['ACT', true],
        ['CAN', false],
      ])('return ABR report status: %s as %s', async (status: string, result: boolean) => {
        const equifaxInitialCompanyResult: EquifaxInitialCompanyResult = {
          matchName: 'string',
          australianBusinessRegisterReport: {
            ABNStatus: status,
          },
        };
        expect(isActive(equifaxInitialCompanyResult)).toBe(result);
      });
    });

    it('else case', () => {
      const equifaxInitialCompanyResult: EquifaxInitialCompanyResult = {
        matchName: 'string',
      };
      expect(isActive(equifaxInitialCompanyResult)).toBe(true);
    });
  });
});
