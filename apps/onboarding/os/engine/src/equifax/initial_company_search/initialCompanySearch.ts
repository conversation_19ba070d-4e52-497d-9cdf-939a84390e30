import { debug, error, info, warn } from '@npco/component-bff-core/dist/utils/logger.js';
import { EntityType } from '@npco/component-dto-core';

import axios from 'axios';
import camelcaseKeys from 'camelcase-keys';
import { decode } from 'html-entities';
import { convert, create } from 'xmlbuilder2';

import type { EnvironmentService } from '../../config/envService.js';
import { closeAwsXraySubSegment, createAwsXraySubSegment, isValidAbn, isValidAcn } from '../../lib/index.js';
import { maskPersonalInformation } from '../../lib/maskingData.js';
import type { InitialCompanySearchResult } from '../../types.js';
import { BusinessIdentifierType } from '../../types.js';
import { maskEquifaxCredential } from '../lib/index.js';
import { ABRSupportEntityTypeMapping, ASICSupportOrganisationTypeMapping } from '../mappings.js';
import type { AXIOSResponse, EquifaxInitialCompanyResult } from '../types.js';

/**
 * Detect the type of Business Identifier
 * @param {string} value - the input string
 * @returns {BusinessIdentifierType} - The ENUM of the Business Identifier type
 */
const detectBusinessIdentifierType = (value: string): BusinessIdentifierType => {
  switch (value.length) {
    case 9:
      return isValidAcn(value) ? BusinessIdentifierType.ACN : BusinessIdentifierType.INVALID;
    case 11:
      if (value.includes(' ')) {
        return isValidAcn(value) ? BusinessIdentifierType.ACN : BusinessIdentifierType.INVALID;
      }
      return isValidAbn(value) ? BusinessIdentifierType.ABN : BusinessIdentifierType.INVALID;
    case 14:
      return isValidAbn(value) ? BusinessIdentifierType.ABN : BusinessIdentifierType.INVALID;
    default:
      return BusinessIdentifierType.INVALID;
  }
};

/**
 * Generate the Equifax Operation XML Tag for ABN / ACN
 * @param {BusinessIdentifierType.ACN|BusinessIdentifierType.ABN} value - Enum BusinessIdentifier - ACN or ABN
 * @returns {string} - The corresponding type of Equifax Operation XML tag name
 */
const equifaxInitialSearchOperationType = (value: BusinessIdentifierType.ACN | BusinessIdentifierType.ABN): string => {
  if (value === BusinessIdentifierType.ABN) {
    return 'australian-business-number';
  }
  return 'organisation-number';
};

/**
 * Get the ACN from the Equifax Initial Company Search
 * @param {EquifaxInitialCompanyResult} equifaxResult - Equifax Initial Company Search result
 * @returns {string | undefined } ACN number or undefined
 */
const getAcn = (equifaxResult: EquifaxInitialCompanyResult): string | undefined => {
  const { organisationNumber, organisationNumberHeading } = equifaxResult;

  if (organisationNumberHeading === 'ACN') {
    debug(`ACN: ${organisationNumber} - based on the Organisation Number tag`);
    return organisationNumber;
  }

  if (equifaxResult.asicReport) {
    const { asicOrganisationNumber, asicOrganisationNumberHeading } = equifaxResult.asicReport;
    if (asicOrganisationNumberHeading === 'ACN') {
      debug(`ACN: ${asicOrganisationNumber} - based on ASIC report`);
      return asicOrganisationNumber;
    }
  }

  if (equifaxResult.asicBnrReport?.businessNameHolder?.organisation?.ACN) {
    debug(`ACN: ${equifaxResult.asicBnrReport.businessNameHolder.organisation.ACN} - based on ASIC BNR Report`);
    return equifaxResult.asicBnrReport.businessNameHolder.organisation.ACN;
  }

  if (equifaxResult.australianBusinessRegisterReport) {
    const { ASICNumber: asicNumber, ASICNumberType: asicNumberType } = equifaxResult.australianBusinessRegisterReport;
    if (asicNumberType === 'ACN') {
      debug(`ACN: ${asicNumber} - based on Australian Business Register Report`);
      return asicNumber;
    }
  }

  debug('ACN: Not found');
  return undefined;
};

/**
 * Process the Equifax Initial Company search results and returns the record with ACN number. If there is no record with
 * ACN number, the first result will be returned
 * @param {Array<EquifaxInitialCompanyResult>} results - the array of Equifax Initial Company Search result
 * @returns {EquifaxInitialCompanyResult} - the single record with contains ACN number or the first result of the search
 */
const prefilterRecords = (results: Array<EquifaxInitialCompanyResult>): EquifaxInitialCompanyResult => {
  debug('Received more than one record, filtering... ');
  const resultsWithASICReport = results.filter((record) => typeof record.asicReport !== 'undefined');
  if (resultsWithASICReport.length > 0) {
    return resultsWithASICReport[0];
  }
  const resultsWithACN = results.filter(
    (result) =>
      typeof result.asicBnrReport?.businessNameHolder?.organisation?.ACN !== 'undefined' ||
      result.organisationNumberHeading === 'ACN',
  );
  if (resultsWithACN.length > 0) {
    debug('Using the record with ACN');
    return resultsWithACN.find((record) => typeof record.asicReport !== 'undefined') ?? resultsWithACN[0];
  }
  debug('Using the first record as no ACN is found');
  return results[0];
};

/**
 * Decode the XML from the AXOIS response
 * @param str
 */
const decodeXMLResponse = (str: string): string => decode(decode(str, { level: 'xml' }), { level: 'xml' });

/**
 * Retrieve the Business name of the company
 * @param {EquifaxInitialCompanyResult} equifaxResult - the Equifax Initial Company result
 * @returns {string} - the business name
 */
const getBusinessName = (equifaxResult: EquifaxInitialCompanyResult): string => {
  if (equifaxResult.australianBusinessRegisterReport?.individualName) {
    debug(`Business Name: 
    ${maskPersonalInformation(equifaxResult.australianBusinessRegisterReport.individualName)} 
    - based on ABR Report Individual`);
    return decodeXMLResponse(equifaxResult.australianBusinessRegisterReport.individualName);
  }

  if (equifaxResult.australianBusinessRegisterReport?.nonIndividualName) {
    debug(`Business Name: ${equifaxResult.australianBusinessRegisterReport.nonIndividualName} 
    - based on ABR Report Non-Individual`);
    return decodeXMLResponse(equifaxResult.australianBusinessRegisterReport.nonIndividualName);
  }

  if (equifaxResult.asicReport?.asicOrganisationNumberHeading === 'ACN' && equifaxResult.asicReport.asicName) {
    debug(`Business Name: ${equifaxResult.asicReport.asicName} - based on ASIC Report`);
    return decodeXMLResponse(equifaxResult.asicReport.asicName);
  }

  debug(`Business Name: ${equifaxResult.matchName} - based on match name`);
  return decodeXMLResponse(equifaxResult.matchName);
};

/**
 * Generate the Request XML for the Equifax and for the function return
 * @param {string} businessIdentifier - Business Identifier (BI)
 * @param {BusinessIdentifierType.ABN|BusinessIdentifierType.ACN} operation - indicate the BI is a ACN/ABN
 * @param {EnvironmentService} envService - Environment Service
 * @param {string} [aggregateId] - Aggregate ID
 * @returns {{requestXml: string, requestReturnXml: string}} - Response contains RequestXml (with credential) and
 * RequestReturnXML (without credential)
 */
const generateRequestXML = (
  businessIdentifier: string,
  operation: BusinessIdentifierType.ABN | BusinessIdentifierType.ACN,
  envService: EnvironmentService,
  aggregateId?: string,
): string => {
  info(`Generating the Request XML with ${operation}: ${businessIdentifier}`);
  const {
    equifaxInitialCompanySearchUsername,
    equifaxInitialCompanySearchPassword,
    equifaxInitialCompanySearchSubscriberIdentifier,
    equifaxInitialCompanySearchSecurityCode,
    equifaxInitialCompanySearchMode,
  } = envService;

  const xmlObjectDoc = {
    BCAmessage: {
      '@type': 'REQUEST',
      BCAaccess: {
        'BCAaccess-code': equifaxInitialCompanySearchUsername,
        'BCAaccess-pwd': equifaxInitialCompanySearchPassword,
      },
      BCAservice: {
        'BCAservice-client-ref': aggregateId,
        'BCAservice-code': 'BCA020',
        'BCAservice-code-version': 'V00',
        'BCAservice-data': {
          request: {
            '@version': '2.0',
            '@mode': equifaxInitialCompanySearchMode,
            '@transaction-reference': 'String',
            'subscriber-details': {
              'subscriber-identifier': equifaxInitialCompanySearchSubscriberIdentifier,
              security: equifaxInitialCompanySearchSecurityCode,
            },
            product: {
              '@name': 'organisation-identification-v2',
            },
            'working-with-organisation': {
              '@search-type': 'registered-companies-businesses',
              [equifaxInitialSearchOperationType(operation)]: businessIdentifier.replace(/ /g, ''),
            },
          },
        },
      },
    },
  };

  const xmlRequestObj = create(
    {
      version: '1.0',
      encoding: 'UTF-8',
    },
    xmlObjectDoc,
  );

  info(`Generated the Request XML with ${operation}: ${businessIdentifier}`);
  return xmlRequestObj.end({ prettyPrint: true });
};

/**
 * Mask the Equifax Initial Search sensitive Information.
 * @param {AXIOSResponse} message - AXIOS Response message
 * @param {EnvironmentService} envService - Environment Service
 * @returns {AXIOSResponse} - return with masked Equifax credential and individual information (if have)
 */
const maskEquifaxInitialSearchAXOISResponse = (
  message: AXIOSResponse,
  envService: EnvironmentService,
): AXIOSResponse => {
  const response = { ...message };
  response.config.data = maskEquifaxCredential(response.config.data, envService);
  if (response.data.includes('<IndividualName>')) {
    const personalInformation = /<IndividualName>(.*)<\/IndividualName>/.exec(response.data);
    /* istanbul ignore next */
    if (personalInformation?.[1]) {
      response.data = maskPersonalInformation(response.data, personalInformation[1]);
    }
  }
  return response;
};

/**
 * Get the Entity Type base on the Equifax Initial Company Search Result
 * @param {EquifaxInitialCompanyResult} equifaxResult - Equifax Initial Search Result
 * @returns {EntityType} - Return the corresponding Zeller Entity Type
 */
const entityType = (equifaxResult: EquifaxInitialCompanyResult): EntityType => {
  if (equifaxResult.australianBusinessRegisterReport?.EntityTypeInd) {
    const result =
      ABRSupportEntityTypeMapping[equifaxResult.australianBusinessRegisterReport.EntityTypeInd] ?? EntityType.OTHER;
    debug(`Entity Type: ${result} is based on ABR Report 
    - ${equifaxResult.australianBusinessRegisterReport.EntityTypeInd}`);
    return result;
  }

  if (equifaxResult.asicReport?.asicType) {
    const result = ASICSupportOrganisationTypeMapping[equifaxResult.asicReport.asicType] ?? EntityType.OTHER;
    debug(`Entity Type: ${result} is based on ASIC Report - ${equifaxResult.asicReport.asicType}`);
    return result;
  }

  debug('Entity Type is not existed in the ABR and ASIC report');
  return EntityType.OTHER;
};

/**
 * Is the current ABN/ACN in active status?
 * ASIC Report will be higher priority than ABR Report
 * @param equifaxResult
 * @returns boolean
 */
const isActive = (equifaxResult: EquifaxInitialCompanyResult): boolean => {
  if (equifaxResult.asicReport?.asicStatus) {
    return equifaxResult.asicReport.asicStatus === 'REGD';
  }

  if (equifaxResult.australianBusinessRegisterReport?.ABNStatus) {
    return equifaxResult.australianBusinessRegisterReport.ABNStatus === 'ACT';
  }

  return true;
};

/**
 * Make an API call to the Equifax Initial Company Search
 * @param {string} businessIdentifier - Business Identifier
 * @param {EnvironmentService} envService - Environment Service
 * @param {string} [aggregateId] - Aggregate ID
 * @returns {Promise<InitialCompanySearchResult>} - Promise of the Initial Company Search
 * @throws {Error} - Invalid Business Identifier
 * @throws {Error} - Network Error
 * @throws {Error} - Equifax API Error
 */
const initialCompanySearch = async (
  businessIdentifier: string,
  envService: EnvironmentService,
  aggregateId?: string,
): Promise<InitialCompanySearchResult> => {
  info('Start: equifaxInitialCompanySearch');
  const operation = detectBusinessIdentifierType(businessIdentifier);
  if (operation === BusinessIdentifierType.INVALID) {
    warn(`${businessIdentifier} is an invalid business Identifier`);
    info('End: equifaxInitialCompanySearch with throw');
    throw new Error('Invalid Business Identifier');
  }

  const { equifaxInitialCompanySearchUrl } = envService;

  const requestXml = generateRequestXML(businessIdentifier, operation, envService, aggregateId);
  const requestMaskedXml = maskEquifaxCredential(requestXml, envService);
  const config = {
    headers: { 'Content-Type': 'text/xml' },
  };
  info(`Calling Equifax Initial Company Search API with ${operation}: ${businessIdentifier}`);
  debug(`Request XML ${requestMaskedXml}`);
  let response;
  const xraySubSegment = createAwsXraySubSegment('equifax-initial-company-search');
  try {
    response = await axios.post(equifaxInitialCompanySearchUrl, requestXml, config);
    closeAwsXraySubSegment(xraySubSegment);
    debug(maskEquifaxInitialSearchAXOISResponse(response as AXIOSResponse, envService));
  } catch (httpError: any) {
    closeAwsXraySubSegment(xraySubSegment);
    error('HTTP Service Error:');
    error(httpError);
    throw new Error(`Equifax Network Error - ${httpError.toString()}`);
  }

  info(`Received response from Equifax Initial Company Search API with ${operation}: ${businessIdentifier}`);
  let responseXml = response.data as string;
  debug(`Response XML: ${responseXml}`);
  const responseObj = camelcaseKeys(JSON.parse(convert(responseXml, { format: 'json' })), {
    deep: true,
    exclude: [
      'ACN',
      'ASICNumber',
      'ASICNumberType',
      'ABNStatus',
      'BCAmessage',
      'BCAservice',
      'BCAservices',
      'EntityTypeInd',
      'BCAerror',
    ],
  });
  responseXml = responseXml.substring(0, 200000);
  if (responseObj.BCAmessage?.BCAservices?.BCAservice?.bcAserviceData?.BCAerror) {
    const {
      '@type': errorType,
      bcAerrorCode: errorCode,
      bcAerrorDescription: errorDescription,
    } = responseObj.BCAmessage.BCAservices.BCAservice.bcAserviceData.BCAerror;
    error(`Equifax API ${errorType} Error -  ${errorCode}: ${errorDescription}`);
    info('End: equifaxInitialCompanySearch with error throw');
    throw new Error(`Equifax API ${errorType} Error`);
  }

  if (responseObj.BCAmessage?.BCAservices?.BCAservice?.bcAserviceData?.response?.error) {
    const { errorType, errorDescription } = responseObj.BCAmessage.BCAservices.BCAservice.bcAserviceData.response.error;
    if (errorDescription['@code'] === 'BCA.0002') {
      return {
        businessIdentifier,
        aggregateId,
        requestXml: requestMaskedXml,
        responseXml,
      };
    }
    error(`Equifax API ${errorType['#']} Error -  ${errorDescription['@code']}: ${errorDescription['#']}`);
    info('End: equifaxInitialCompanySearch with error throw');
    throw new Error(`Equifax API ${errorType['#']} Error`);
  }

  if (responseObj.BCAmessage?.BCAservices?.BCAservice?.bcAserviceData?.response?.orgIdResult) {
    const orgIdResult = responseObj.BCAmessage.BCAservices.BCAservice.bcAserviceData.response.orgIdResult;
    const result = Array.isArray(orgIdResult) ? prefilterRecords(orgIdResult) : orgIdResult;

    const { australianBusinessNumber: abn } = result;
    const name = getBusinessName(result);
    const type = entityType(result);

    info('End: equifaxInitialCompanySearch with a record');
    return {
      businessIdentifier,
      aggregateId,
      abn,
      acn: getAcn(result),
      name,
      type,
      active: isActive(result),
      abrEntityType: result.australianBusinessRegisterReport?.EntityTypeInd,
      asicType: result.asicReport?.asicType,
      requestXml: requestMaskedXml,
      responseXml: type === EntityType.INDIVIDUAL ? maskPersonalInformation(responseXml, name) : responseXml,
    };
  }

  info('End: equifaxInitialCompanySearch without founding a record');
  return {
    businessIdentifier,
    aggregateId,
    requestXml: requestMaskedXml,
    responseXml,
  };
};

export {
  generateRequestXML,
  detectBusinessIdentifierType,
  initialCompanySearch,
  maskEquifaxInitialSearchAXOISResponse,
  isActive,
};
