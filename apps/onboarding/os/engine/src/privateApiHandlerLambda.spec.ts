import type { CustomerDocumentVerificationV2RequestDto } from '@npco/component-dto-customer';

import { jest } from '@jest/globals';
import AWSXray from 'aws-xray-sdk';

jest.mock('aws-xray-sdk');

const mockRequestSdkToken = jest.fn();
jest.unstable_mockModule('./service/selfie_verification_check/selfieVerificationCheckService', () => ({
  __esModule: true,
  createApplicationCheck: jest.fn(),
  requestSdkToken: mockRequestSdkToken,
}));

const mockInitialCompanySearch = jest.fn();
jest.unstable_mockModule('./service/search/aus/companySearchService', () => ({
  __esModule: true,
  fullCompanyProfileSearch: jest.fn(),
  initialCompanySearch: mockInitialCompanySearch,
}));

const mockStartDocVerificationKeyExchange = jest.fn();
jest.unstable_mockModule('./service/document_encryption/index.js', () => ({
  __esModule: true,
  startDocVerificationKeyExchange: mockStartDocVerificationKeyExchange,
  decrypt: jest.fn(),
}));

const mockIdentityVerificationSafeHarbourSearchV2 = jest.fn();
const mockIdentityVerificationSearchV2 = jest.fn();
jest.unstable_mockModule('./service/idv_and_safe_harbour/index.js', () => ({
  __esModule: true,
  identityVerificationSafeHarbourSearch: jest.fn(),
  identityVerificationSafeHarbourSearchV2: mockIdentityVerificationSafeHarbourSearchV2,
  identityVerificationSearch: jest.fn(),
  identityVerificationSearchV2: mockIdentityVerificationSearchV2,
}));

jest.mock('@npco/component-bff-core/dist/middleware', () => {
  // Require the original module to not be mocked...
  const originalModule = jest.requireActual('@npco/component-bff-core/dist/middleware');
  return {
    __esModule: true, // Use it when dealing with esModules
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    ...originalModule,
    xrayAggregateMiddleware: jest.fn(),
  };
});

describe('private api handler Lambda test suite', () => {
  let context: any;
  let onfidoTokenRequestHandler: any;
  let businessIdentifierSearchHandler: any;
  let documentsKeyExchangeHandler: any;
  let idvV2Handler: any;
  let idvWithSafeHarbourV2Handler: any;
  const requestBody: CustomerDocumentVerificationV2RequestDto = {
    customerUuid: 'uuid',
    entityUuid: 'uuid',
    firstName: 'mockFirstName',
    lastName: 'lastName',
  };

  beforeAll(async () => {
    const privateApiHandlerLambda = await import('./privateApiHandlerLambda.js');
    onfidoTokenRequestHandler = privateApiHandlerLambda.onfidoTokenRequestHandler;
    businessIdentifierSearchHandler = privateApiHandlerLambda.businessIdentifierSearchHandler;
    documentsKeyExchangeHandler = privateApiHandlerLambda.documentsKeyExchangeHandler;
    idvV2Handler = privateApiHandlerLambda.idvV2Handler;
    idvWithSafeHarbourV2Handler = privateApiHandlerLambda.idvWithSafeHarbourV2Handler;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(AWSXray, 'getSegment').mockReturnValue(null as never);
  });

  it('should be able to handle request onfido sdk token', (done) => {
    onfidoTokenRequestHandler(
      {
        body: JSON.stringify({
          entityUuid: 'entityUuid-01',
          customerUuid: 'customerUuid-01',
          firstName: 'firstName',
          lastName: 'lastName',
        }),
        request: { headers: { authorization: 'accessToken' } },
      },
      context,
      () => {
        expect(mockRequestSdkToken).toHaveBeenCalledTimes(1);
        done();
      },
    );
  });

  it('should be able to handle business identifierSearch', (done) => {
    businessIdentifierSearchHandler(
      {
        body: JSON.stringify({
          entityUuid: 'entityUuid-01',
          businessIdentifier: 'test',
        }),
      },
      context,
      () => {
        expect(mockInitialCompanySearch).toHaveBeenCalledTimes(1);
        done();
      },
    );
  });

  it('should be able to handle document verification key exchange', (done) => {
    documentsKeyExchangeHandler(
      {
        body: JSON.stringify({
          clientPublicKey: 'mockPublicKey',
        }),
        pathParameters: { customerUuid: 'uuid' },
        headers: { 'zeller-session-id': 'sessionId' },
      },
      context,
      () => {
        expect(mockStartDocVerificationKeyExchange).toHaveBeenCalledTimes(1);
        done();
      },
    );
  });

  it('should be able to handle errors in document verification key exchange', (done) => {
    mockStartDocVerificationKeyExchange.mockImplementationOnce(() => {
      throw new Error('error');
    });

    documentsKeyExchangeHandler(
      {
        body: JSON.stringify({
          clientPublicKey: 'mockPublicKey',
        }),
        pathParameters: { customerUuid: 'uuid' },
        headers: { 'zeller-session-id': 'sessionId' },
      },
      context,
      () => {
        expect(mockStartDocVerificationKeyExchange).toHaveBeenCalledTimes(1);
        done();
      },
    );
  });

  it('should be able to handle idv request', (done) => {
    idvV2Handler(
      {
        body: JSON.stringify(requestBody),
        path: { customerUuid: 'uuid' },
        headers: { 'zeller-session-id': 'sessionId' },
      },
      context,
      () => {
        expect(mockIdentityVerificationSearchV2).toHaveBeenCalledTimes(1);
        done();
      },
    );
  });

  it('should be able to handle idv with safeharbour request', (done) => {
    idvWithSafeHarbourV2Handler(
      {
        body: JSON.stringify(requestBody),
        path: { customerUuid: 'uuid' },
        headers: { 'zeller-session-id': 'sessionId' },
      },
      context,
      () => {
        expect(mockIdentityVerificationSafeHarbourSearchV2).toHaveBeenCalledTimes(1);
        done();
      },
    );
  });
});
