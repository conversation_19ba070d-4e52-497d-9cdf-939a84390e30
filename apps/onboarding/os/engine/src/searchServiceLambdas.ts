import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware/index.js';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2.js';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource.js';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent.js';
import type {
  EntityFullSearchCompletedEventDto,
  EntityFullSearchRequestedEventDto,
  EntityInitialSearchCompletedEventDto,
  EntityInitialSearchRequestedEventDto,
} from '@npco/component-dto-entity';

import type { Handler } from 'aws-lambda';

import { LambdaErrorHandler } from './lambda_failed_handler/lambdaErrorHandler.js';
import { fullCompanyProfileSearch, initialCompanySearch } from './service/search/aus/index.js';
import type { LambdaDestinationErrorEvent } from './types.js';

const SYSTEM_ERROR = 'System Error';

export const initialCompanySearchHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.OS, eventType: LambdaEventSource.EVENT_BRIDGE },
  async (event: { detail: EntityInitialSearchRequestedEventDto }) => {
    return initialCompanySearch(event.detail);
  },
  [xrayAggregateMiddleware(/* istanbul ignore next */ (event: any) => event.detail.entityUuid)],
);

export const initialCompanySearchErrorHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.OS, eventType: LambdaEventSource.LAMBDA_ON_ERROR },
  async (event: LambdaDestinationErrorEvent) => {
    LambdaErrorHandler.captureError(event);
    return {
      entityUuid: event.requestPayload.detail.entityUuid,
      businessIdentifier: event.requestPayload.detail.businessIdentifier,
      found: false,
      error: SYSTEM_ERROR,
    } as EntityInitialSearchCompletedEventDto;
  },
);

export const fullCompanyProfileSearchHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.OS, eventType: LambdaEventSource.EVENT_BRIDGE },
  async (event: { detail: EntityFullSearchRequestedEventDto }) => {
    return fullCompanyProfileSearch(event.detail);
  },
  [xrayAggregateMiddleware(/* istanbul ignore next */ (event: any) => event.detail.entityUuid)],
);

export const fullCompanyProfileSearchErrorHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.OS, eventType: LambdaEventSource.LAMBDA_ON_ERROR },
  async (event: LambdaDestinationErrorEvent) => {
    LambdaErrorHandler.captureError(event);
    return {
      entityUuid: event.requestPayload.detail.entityUuid,
      found: false,
      error: SYSTEM_ERROR,
    } as EntityFullSearchCompletedEventDto;
  },
);
