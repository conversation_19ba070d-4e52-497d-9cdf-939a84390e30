import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2.js';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource.js';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent.js';
import { info, debug } from '@npco/component-bff-core/dist/utils/logger.js';
import type { CustomerDocumentVerificationV2RequestDto } from '@npco/component-dto-customer';
import type { EntityInitialSearchRequestedEventDto } from '@npco/component-dto-entity';

import type { APIGatewayProxyEvent, Handler } from 'aws-lambda';

import { startDocVerificationKeyExchange } from './service/document_encryption/index.js';
import {
  identityVerificationSafeHarbourSearchV2,
  identityVerificationSearchV2,
} from './service/idv_and_safe_harbour/index.js';
import { initialCompanySearch } from './service/search/aus/index.js';
import { requestSdkToken } from './service/selfie_verification_check/index.js';
import type { RequestSDKTokenPayload } from './service/selfie_verification_check/types.js';

export const onfidoTokenRequestHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.OS, eventType: LambdaEventSource.API_GATEWAY_PROXY },
  async (event: { body: string } & APIGatewayProxyEvent) => {
    info(`onfidoTokenRequestHandler event is -> ${JSON.stringify(event)}`);
    const payload = JSON.parse(event.body);
    return requestSdkToken(payload as RequestSDKTokenPayload);
  },
  [
    xrayAggregateMiddleware(
      /* istanbul ignore next */ (event: any) => {
        const parsedBody = JSON.parse(event.body);
        return parsedBody.customerUuid;
      },
    ),
  ],
);

export const businessIdentifierSearchHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.OS, eventType: LambdaEventSource.API_GATEWAY_PROXY },
  async (event: { body: string } & APIGatewayProxyEvent) => {
    info(`businessVerificationRequestBody is -> ${JSON.stringify(event)}`);
    const payload = JSON.parse(event.body);
    const result = await initialCompanySearch(payload as EntityInitialSearchRequestedEventDto);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      isBase64Encoded: false,
    };
  },
  [
    xrayAggregateMiddleware(
      /* istanbul ignore next */ (event: any) => {
        const parsedBody = JSON.parse(event.body);
        return parsedBody.entityUuid;
      },
    ),
  ],
);

export const documentsKeyExchangeHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.OS, eventType: LambdaEventSource.API_GATEWAY_PROXY },
  async (
    event: {
      body: string;
      pathParameters: { customerUuid: string };
      headers: { 'zeller-session-id': string };
    } & APIGatewayProxyEvent,
  ) => {
    debug(`documentsVerificationKeyExchange event is -> ${JSON.stringify(event)}`, event.pathParameters.customerUuid);

    const paylaod = JSON.parse(event.body);

    if (!paylaod?.clientPublicKey) {
      return {
        statusCode: 400,
        body: 'client public key required',
      };
    }

    try {
      const result = await startDocVerificationKeyExchange(
        event.pathParameters.customerUuid,
        event.headers['zeller-session-id'],
        paylaod.clientPublicKey,
      );

      return {
        statusCode: 200,
        isBase64Encoded: false,
        body: JSON.stringify(result),
      };
    } catch (e: any) {
      if (e.message === 'INVALID_PUBLIC_KEY_FORMAT') {
        return {
          statusCode: 400,
          isBase64Encoded: false,
          body: e.message,
        };
      }

      return {
        statusCode: 500,
        isBase64Encoded: false,
        body: 'INTERNAL_SERVER_ERROR',
      };
    }
  },
  [
    xrayAggregateMiddleware(
      /* istanbul ignore next */ (event: any) => {
        return event.pathParameters.customerUuid;
      },
    ),
  ],
);

export const idvV2Handler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.OS, eventType: LambdaEventSource.API_GATEWAY_PROXY },
  async (
    event: {
      body: CustomerDocumentVerificationV2RequestDto;
      path: { customerUuid: string };
      headers: { 'zeller-session-id': string };
    } & APIGatewayProxyEvent,
  ) => {
    info(`idvV2 event is -> ${event}`);

    await identityVerificationSearchV2(event.path.customerUuid, event.headers['zeller-session-id'], event.body);

    return {
      statusCode: 200,
      isBase64Encoded: false,
    };
  },
  [
    xrayAggregateMiddleware(
      /* istanbul ignore next */ (event: any) => {
        return event.path.customerUuid;
      },
    ),
  ],
);

export const idvWithSafeHarbourV2Handler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.OS, eventType: LambdaEventSource.API_GATEWAY_PROXY },
  async (
    event: {
      body: CustomerDocumentVerificationV2RequestDto;
      path: { customerUuid: string };
      headers: { 'zeller-session-id': string };
    } & APIGatewayProxyEvent,
  ) => {
    info(`idvWithSafeHarbourV2Handler event is -> ${JSON.stringify(event)}`);

    await identityVerificationSafeHarbourSearchV2(
      event.path.customerUuid,
      event.headers['zeller-session-id'],
      event.body,
    );

    return {
      statusCode: 202,
      isBase64Encoded: false,
    };
  },
  [
    xrayAggregateMiddleware(
      /* istanbul ignore next */ (event: any) => {
        return event.path.customerUuid;
      },
    ),
  ],
);
