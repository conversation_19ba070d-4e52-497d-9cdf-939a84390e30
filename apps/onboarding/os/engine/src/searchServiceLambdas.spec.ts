import { jest } from '@jest/globals';
import AWSXray from 'aws-xray-sdk';

import { LambdaErrorHandler } from './lambda_failed_handler/lambdaErrorHandler.js';
import type { LambdaDestinationErrorEvent } from './types.js';

jest.mock('aws-xray-sdk');
jest.mock('@npco/component-bff-core/dist/middleware', () => {
  // Require the original module to not be mocked...
  const originalModule = jest.requireActual('@npco/component-bff-core/dist/middleware');
  return {
    __esModule: true, // Use it when dealing with esModules
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    ...originalModule,
    xrayAggregateMiddleware: jest.fn(),
  };
});

const initialCompanySearch = jest.fn();
const fullCompanyProfileSearch = jest.fn();
jest.unstable_mockModule('./service/search/aus/companySearchService', () => ({
  __esModule: true,
  initialCompanySearch,
  fullCompanyProfileSearch,
}));

describe('company search test suite', () => {
  let context: any;
  let fullCompanyProfileSearchErrorHandler: any;
  let fullCompanyProfileSearchHandler: any;
  let initialCompanySearchErrorHandler: any;
  let initialCompanySearchHandler: any;

  beforeAll(async () => {
    const CompanySearchLambdas = await import('./searchServiceLambdas.js');
    fullCompanyProfileSearchErrorHandler = CompanySearchLambdas.fullCompanyProfileSearchErrorHandler;
    fullCompanyProfileSearchHandler = CompanySearchLambdas.fullCompanyProfileSearchHandler;
    initialCompanySearchHandler = CompanySearchLambdas.initialCompanySearchHandler;
    initialCompanySearchErrorHandler = CompanySearchLambdas.initialCompanySearchErrorHandler;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(AWSXray, 'getSegment').mockReturnValue(null as never);
  });

  it('should be able to handle lambda initial search event', (done) => {
    initialCompanySearchHandler(
      {
        detail: { entityUuid: '01' },
        request: { headers: { authorization: 'accessToken' } },
      },
      context,
      () => {
        expect(initialCompanySearch).toHaveBeenCalledTimes(1);
        done();
      },
    );
  });
  it('should be able to handle lambda full company profile search event', (done) => {
    fullCompanyProfileSearchHandler(
      {
        detail: { entityUuid: 'uuidv4' },
        request: { headers: { authorization: 'accessToken' } },
      },
      context,
      () => {
        expect(fullCompanyProfileSearch).toHaveBeenCalledTimes(1);
        done();
      },
    );
  });

  it('should be able to handle error event in the initial search', (done) => {
    const mockEvent: LambdaDestinationErrorEvent = {
      version: '',
      timestamp: '',
      requestContext: {
        requestId: '',
        functionArn: '',
        condition: '',
        approximateInvokeCount: 0,
      },
      requestPayload: {
        detail: {
          entityUuid: 'uuid',
          businessIdentifier: '1',
        },
      },
      responseContext: {
        statusCode: 200,
        executedVersion: '',
        functionError: '',
      },
      responsePayload: {
        errorType: 'error_type',
        errorMessage: 'error_message',
        trace: [],
      },
    };
    const spy = jest.spyOn(LambdaErrorHandler, 'captureError');
    initialCompanySearchErrorHandler(mockEvent, context, () => {
      expect(LambdaErrorHandler.captureError).toHaveBeenCalledTimes(1);
      spy.mockRestore();
      done();
    });
  });

  it('should be able to handle error event in the full company profile search', (done) => {
    const mockEvent: LambdaDestinationErrorEvent = {
      version: '',
      timestamp: '',
      requestContext: {
        requestId: '',
        functionArn: '',
        condition: '',
        approximateInvokeCount: 0,
      },
      requestPayload: {
        detail: {
          entityUuid: 'uuid',
        },
      },
      responseContext: {
        statusCode: 200,
        executedVersion: '',
        functionError: '',
      },
      responsePayload: {
        errorType: 'error_type',
        errorMessage: 'error_message',
        trace: [],
      },
    };
    const spy = jest.spyOn(LambdaErrorHandler, 'captureError');
    fullCompanyProfileSearchErrorHandler(mockEvent, context, () => {
      expect(LambdaErrorHandler.captureError).toHaveBeenCalledTimes(1);
      spy.mockRestore();
      done();
    });
  });
});
