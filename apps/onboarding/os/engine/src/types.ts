import type { AddressState, EntityType } from '@npco/component-dto-core';
import type {
  CustomerDocumentVerificationAndSafeHarbourRequestedEventDto,
  CustomerScreeningRequestedEventDto,
  IdvDocumentType,
  IdvSafeHarbourDocumentType,
} from '@npco/component-dto-customer';
import type { EntityAddress, EntityMember } from '@npco/component-dto-entity';

const individualNameField = 'individual-name';
const dateOfBirthField = 'date-of-birth';

export class IdvEquifaxResponse {
  public response: IDVSearchResult | undefined;

  public idvType: IdvDocumentType | IdvSafeHarbourDocumentType;

  public requestXml: string;

  public responseXml?: string;

  constructor(
    idvType: IdvDocumentType | IdvSafeHarbourDocumentType,
    requestXml: string,
    responseXml?: string,
    response?: IDVSearchResult,
  ) {
    this.response = response;
    this.idvType = idvType;
    this.requestXml = requestXml;
    this.responseXml = responseXml;
  }
}

export const enum BusinessIdentifierType {
  ABN = 'ABN',
  ACN = 'ACN',
  INVALID = 'INVALID',
}

export type IdvEnquiryIdMapping = {
  idvType: IdvDocumentType;
  enquiryId?: string;
};

export type IdvSafeHarbourEnquiryIdMapping = {
  idvType: IdvSafeHarbourDocumentType;
  enquiryId?: string;
};

export interface InitialCompanySearchResult {
  businessIdentifier: string;
  aggregateId?: string;
  name?: string;
  abn?: string;
  acn?: string;
  type?: EntityType;
  active?: boolean;
  asicType?: string;
  abrEntityType?: string;
  requestXml: string;
  responseXml?: string;
}

export type FullCompanyProfileSearchResult = {
  aggregateId: string;
  acn: string;
  found: boolean;
  registeredAddress?: EntityAddress;
  businessAddress?: EntityAddress;
  members?: EntityMember[];
  requestXml?: string;
  responseXml?: string;
  companyFile?: Record<string, unknown>;
  error?: string;
};

export type IDVErrorFault = {
  faultcode: string;
  faultstring: string;
  faultactor: string;
  detail: string;
};

export type IDVSearchResult = {
  attributes: IDVSearchAttributesResult;
  responseOutcome: IDVSearchResponseOutcomeResult;
  componentResponses: IDVSearchComponentResponseResult;
  error: IDVErrorFault;
};

export type IDVSearchAttributesResult = {
  clientReference: string;
  enquiryId: string;
  profileName: string;
  profileVersion: string;
};

export type IDVSearchResponseOutcomeResult = {
  overallOutcome: string;
};

export type IDVSearchComponentResponseResult = {
  verificationResponse: ComponentVerificationResponse;
};

export type VisaDetailsRecord = {
  familyName: string;
  givenName: string;
  travelDocumentNumber: string;
  visaClass: string;
  visaClassSubclass: string;
  visaApplicant: string;
  visaDateOfExpiry: string;
  systemDate: string;
  residenceStatus: string;
  location: string;
};

export type ComponentVerificationResponse = {
  verificationOutcome: ComponentVerificationResponseOutcome;
  rulesResults: ComponentVerificationRuleResults;
  analysisResults: ComponentAnalysisResults;
  searchResults: ComponentSearchResults;
  records: { visaDetailsRecord: VisaDetailsRecord };
};

export type ComponentSearchResults = {
  searchResult: Array<ComponentSearchResultDetail>;
};

export type ComponentSearchResultDetail = {
  attributes: SearchResultDetailAttributes;
  individualName: SearchResultDetailIndividualName;
  dateOfBirth: SearchResultDetailDateOfBirth;
  currentAddress: SearchResultDetailCurrentAddress;
  streetNumber: SearchResultsDetailStreetNumber;
  streetName: SearchResultsDetailStreetName;
  streetType: SearchResultsDetailStreetType;
  suburb: SearchResultsDetailSuburb;
  state: SearchResultsDetailState;
  postcode: SearchResultsDetailPostcode;
  unformattedAddress: SearchResultsDetailUnformattedAddress;
};

export type SearchResultsDetailUnformattedAddress = {
  attributes: SearchResultDetailAreaAttributes;
};

export type SearchResultsDetailPostcode = {
  attributes: SearchResultDetailAreaAttributes;
};

export type SearchResultsDetailState = {
  attributes: SearchResultDetailAreaAttributes;
};

export type SearchResultsDetailSuburb = {
  attributes: SearchResultDetailAreaAttributes;
};

export type SearchResultsDetailStreetType = {
  attributes: SearchResultDetailAreaAttributes;
};

export type SearchResultsDetailStreetName = {
  attributes: SearchResultDetailAreaAttributes;
};

export type SearchResultsDetailStreetNumber = {
  attributes: SearchResultDetailAreaAttributes;
};

export type SearchResultDetailCurrentAddress = {
  unitNumber: SearchResultDetailAreaAttributes;
};

export type SearchResultDetailDateOfBirth = {
  attributes: SearchResultDetailAreaAttributes;
};

export type SearchResultDetailIndividualName = {
  familyName: SearchResultDetailIndividualFamilyName;
};

export type SearchResultDetailIndividualFamilyName = {
  attributes: SearchResultDetailAreaAttributes;
};

export type SearchResultDetailAreaAttributes = {
  searchValue: string;
};

export type SearchResultDetailAttributes = {
  matchIndicator: string;
  matchScore: string;
  searchName: string;
  searchType: string;
  serviceResultCode: string;
  serviceResultDetail: string;
  serviceResultString: string;
};

export type ComponentAnalysisResults = {
  analysisResult: Array<ComponentAnalysisResultDetail>;
};

export type ComponentAnalysisResultDetail = {
  category: any;
  searchName: string;
  rawScore: number;
  minimumValue: number;
  filteredScore: number;
  weight: number;
  points: number;
  contributingFactors: any;
};

export type ComponentVerificationRuleResults = {
  ruleResult: Array<RuleResultDetail>;
};

export type RuleResultDetail = {
  attributes: RuleResultAttributes;
  indicator: string;
  reason: Array<string>;
};

export type RuleResultAttributes = {
  name: string;
};

export type ComponentVerificationResponseOutcome = {
  indicator: string;
  totalPoints: string;
  selfVerificationUrl: string;
};

export interface LambdaDestinationSuccessEvent {
  version: string;
  timestamp: string;
  requestContext: {
    requestId: string;
    functionArn: string;
    condition: string;
    approximateInvokeCount: number;
  };
  requestPayload: {
    detail: any;
    'detail-type': string;
    id: string;
    region: string;
    source: string;
    time: string;
    version: string;
    requestPayload: any;
  };
  responseContext: {
    statusCode: number;
    executedVersion: string;
    functionError?: string;
  };
  responsePayload: any;
}

export interface LambdaDestinationErrorEvent {
  version: string;
  timestamp: string;
  requestContext: {
    requestId: string;
    functionArn: string;
    condition: string;
    approximateInvokeCount: number;
  };
  requestPayload: {
    detail: {
      entityUuid?: string;
      customerUuid?: string;
      businessIdentifier?: string;
    };
  };
  responseContext: {
    statusCode: number;
    executedVersion: string;
    functionError?: string;
  };
  responsePayload: {
    errorType: string;
    errorMessage: string;
    trace: string[];
  };
}

export interface CustomerScreeningLambdaDestinationErrorEvent {
  version: string;
  timestamp: string;
  requestContext: {
    requestId: string;
    functionArn: string;
    condition: string;
    approximateInvokeCount: number;
  };
  requestPayload: {
    detail: CustomerScreeningRequestedEventDto;
  };
  responseContext: {
    statusCode: number;
    executedVersion: string;
    functionError?: string;
  };
  responsePayload: {
    errorType: string;
    errorMessage: string;
    trace: string[];
  };
}

export interface IdvLambdaDestinationErrorEvent {
  version: string;
  timestamp: string;
  requestContext: {
    requestId: string;
    functionArn: string;
    condition: string;
    approximateInvokeCount: number;
  };
  requestPayload: {
    detail: CustomerDocumentVerificationAndSafeHarbourRequestedEventDto;
  };
  responseContext: {
    statusCode: number;
    executedVersion: string;
    functionError?: string;
  };
  responsePayload: {
    errorType: string;
    errorMessage: string;
    trace: string[];
  };
}

export interface IdvSafeHarbourLambdaDestinationErrorEvent {
  version: string;
  timestamp: string;
  requestContext: {
    requestId: string;
    functionArn: string;
    condition: string;
    approximateInvokeCount: number;
  };
  requestPayload: {
    detail: CustomerDocumentVerificationAndSafeHarbourRequestedEventDto;
  };
  responseContext: {
    statusCode: number;
    executedVersion: string;
    functionError?: string;
  };
  responsePayload: {
    errorType: string;
    errorMessage: string;
    trace: string[];
  };
}

export interface OnfidoWebhookLambdaDestinationErrorEvent {
  version: string;
  timestamp: string;
  requestContext: {
    requestId: string;
    functionArn: string;
    condition: string;
    approximateInvokeCount: number;
  };
  requestPayload: {
    detail: any;
  };
  responseContext: {
    statusCode: number;
    executedVersion: string;
    functionError?: string;
  };
  responsePayload: {
    errorType: string;
    errorMessage: string;
    trace: string[];
  };
}

export interface MedicareRequestArguments {
  attributes: IdvRequestAttributes;
  [individualNameField]: IdvRequestIndividualName;
  [dateOfBirthField]: string;
  medicare: MedicareCardArgumentsDetails;
  'current-address'?: SafeHarbourCurrentAddressDetails;
}

export interface IdvRequestAttributes {
  'client-reference': string;
  'enquiry-id'?: string;
}

export interface IdvRequestIndividualName {
  'family-name'?: string;
  'first-given-name'?: string;
  'other-given-name'?: string;
}

export interface MedicareCardArgumentsDetails {
  'card-number': string;
  'reference-number'?: number;
  'middle-name-on-card'?: string;
  'date-of-expiry': string;
  'card-colour': string;
}

export interface PassportRequestArguments {
  attributes: IdvRequestAttributes;
  [individualNameField]: IdvRequestIndividualName;
  [dateOfBirthField]: string;
  'passport-details': PassportArgumentsDetails;
  'current-address'?: SafeHarbourCurrentAddressDetails;
}

export interface PassportArgumentsDetails {
  'country-code': string;
  number: string;
}

export interface DriversLicenceRequestArguments {
  attributes: IdvRequestAttributes;
  [individualNameField]: IdvRequestIndividualName;
  [dateOfBirthField]: string;
  'drivers-licence-details': DriversLicenceArgumentsDetails;
  'current-address'?: SafeHarbourCurrentAddressDetails;
}

export interface DriversLicenceArgumentsDetails {
  'state-code'?: AddressState;
  number: string;
  'card-number'?: string;
}

export interface SafeHarbourRequestArguments {
  attributes: IdvRequestAttributes;
  [individualNameField]: IdvRequestIndividualName;
  [dateOfBirthField]?: string;
  'current-address'?: SafeHarbourCurrentAddressDetails;
}

export interface SafeHarbourCurrentAddressDetails {
  'unformatted-address': string;
}
