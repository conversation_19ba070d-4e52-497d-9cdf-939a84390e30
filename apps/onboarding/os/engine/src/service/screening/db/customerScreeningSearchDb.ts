import { debug, error, info } from '@npco/component-bff-core/dist/utils/logger.js';
import type { CustomerScreeningRequestedEventDto } from '@npco/component-dto-customer';

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand } from '@aws-sdk/lib-dynamodb';
import AWSXray from 'aws-xray-sdk';

import type { EnvironmentService } from '../../../config/envService.js';
import type { CustomerScreeningResults } from '../../../screening/types.js';
import type { CustomerScreeningSearchDbRecord } from '../../search/aus/types.js';

const createCustomerScreeningSearchCache = async (
  aggregateId: string,
  screeningResponses: CustomerScreeningResults,
  envService: EnvironmentService,
  screeningRequest: CustomerScreeningRequestedEventDto,
): Promise<void> => {
  info('createCustomerScreeningSearchCache: Creating record - ', aggregateId);
  const dynamodb = DynamoDBDocumentClient.from(
    new DynamoDBClient({
      region: envService.region,
    }),
    {
      marshallOptions: {
        removeUndefinedValues: true,
      },
    },
  );

  if (envService.isInLambda) {
    AWSXray.captureAWSv3Client(dynamodb);
  }

  const { request, validResponseFromScreening, screeningError, screeningResponse } = screeningResponses;

  const stringifyResponse = JSON.stringify(screeningResponse);
  const response = stringifyResponse ? stringifyResponse.substring(0, 200000) : '';

  const now = new Date();
  const expiredTime = new Date(now);
  expiredTime.setDate(now.getDate() + 1);
  const cache: CustomerScreeningSearchDbRecord = {
    customerUuid: aggregateId,
    screeningResponse: response,
    request,
    screeningType: screeningRequest.screeningType,
    validResponseFromScreening,
    expirationTime: Math.round(expiredTime.getTime() / 1e3),
    ...(screeningError ? { screeningError } : {}),
  };
  debug('Cache data:', aggregateId);
  debug(cache, aggregateId);
  const params = {
    TableName: envService.customerScreeningSearchTable,
    Item: cache,
  };

  try {
    debug('Put the records into Screening Search DynamoDB Table with following parameters', aggregateId);
    debug(params, aggregateId);
    const command = new PutCommand(params);
    await dynamodb.send(command);
  } catch (dynamodbError: any) {
    error(dynamodbError.toString(), aggregateId);
    throw new Error(dynamodbError.toString());
  }
  info('createCustomerScreeningSearchCache: Screening Search DynamoDB Table - record created: ', aggregateId);
};

export { createCustomerScreeningSearchCache };
