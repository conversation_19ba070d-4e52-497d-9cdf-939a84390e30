import { debug, error, info } from '@npco/component-bff-core/dist/utils/logger.js';

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand } from '@aws-sdk/lib-dynamodb';
import AWSXray from 'aws-xray-sdk';

import type { EnvironmentService } from '../../../config/envService.js';
import type { EntityScreeningResults } from '../../../screening/types.js';
import type { EntityScreeningSearchDbRecord } from '../../search/aus/types.js';

const createEntityScreeningSearchCache = async (
  aggregateId: string,
  screeningResponses: EntityScreeningResults,
  envService: EnvironmentService,
): Promise<void> => {
  info('createEntityScreeningSearchCache: creating record - ', aggregateId);
  const { request, validResponseFromScreening, screeningError } = screeningResponses;
  const stringifyResponse = JSON.stringify(screeningResponses.screeningResponse);
  const response = stringifyResponse ? stringifyResponse.substring(0, 200000) : '';

  const now = new Date();
  const expiredTime = new Date(now);
  expiredTime.setDate(now.getDate() + 1);
  const cache: EntityScreeningSearchDbRecord = {
    entityUuid: aggregateId,
    screeningResponse: response,
    request,
    validResponseFromScreening,
    expirationTime: Math.round(expiredTime.getTime() / 1e3),
    ...(screeningError ? { screeningError } : {}),
  };
  debug('Cache data:', aggregateId);
  debug(cache, aggregateId);

  const dynamodb = DynamoDBDocumentClient.from(
    new DynamoDBClient({
      region: envService.region,
    }),
    {
      marshallOptions: {
        removeUndefinedValues: true,
      },
    },
  );

  if (envService.isInLambda) {
    AWSXray.captureAWSv3Client(dynamodb);
  }

  const params = {
    TableName: envService.entityScreeningSearchTable,
    Item: cache,
  };

  try {
    debug('Put the records into Screening Search DynamoDB Table with following parameters', aggregateId);
    debug(params, aggregateId);
    const command = new PutCommand(params);
    await dynamodb.send(command);
  } catch (dynamodbError: any) {
    error(dynamodbError.toString(), aggregateId);
    throw new Error(dynamodbError.toString());
  }
  info('createEntityScreeningSearchCache: Screening Search DynamoDB Table - record created: ', aggregateId);
};

export { createEntityScreeningSearchCache };
