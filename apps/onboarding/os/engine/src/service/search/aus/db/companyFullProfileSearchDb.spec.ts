import type { Address } from '@npco/component-dto-core';
import { AddressState } from '@npco/component-dto-core';
import type { EntityAddress, EntityMember } from '@npco/component-dto-entity';

import { DynamoDBDocumentClient, PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { jest } from '@jest/globals';
import { mockClient } from 'aws-sdk-client-mock';
import AWSXray from 'aws-xray-sdk';
import { instance, mock, when } from 'ts-mockito';

import { EnvironmentService } from '../../../../config/envService.js';
import type { FullCompanyProfileSearchResult } from '../../../../types.js';
import { getCommandInputs } from '../../../../utils/test/getCommandInputs.spec.js';

import {
  createFullCompanyProfileCache,
  lookupFullCompanyProfileCacheByEntityUuid,
} from './companyFullProfileSearchDb.js';
import 'aws-sdk-client-mock-jest';

jest.mock('aws-xray-sdk');

const DYNAMODB_ERROR_STRING = 'Dynamodb error';
const AGGREGATE_ID = 'AGGREGATE_ID';
const dynamoDBDocumentClientMock = mockClient(DynamoDBDocumentClient);

const mockBusinessAddress = {
  street1: 'Level 5',
  street2: '103 James Street',
  suburb: 'Sydney',
  state: AddressState.NSW,
  postcode: '2000',
} as EntityAddress;
const mockRegisteredAddress = {
  street1: 'Level 4',
  street2: '103 James Street',
  suburb: 'Sydney',
  state: AddressState.NSW,
  postcode: '2000',
} as EntityAddress;
const mockAddress = {
  street: '123 Spring Street',
  suburb: 'Melbourne',
  state: AddressState.VIC,
  postcode: '3000',
} as Address;
const mockMembers = [
  {
    firstname: 'firstname',
    lastname: 'lastname',
    address: mockAddress,
    director: true,
  },
  {
    firstname: 'firstname',
    middlename: 'middlename',
    lastname: 'lastname',
    address: mockAddress,
    secretary: true,
  },
] as EntityMember[];

describe('look up the cache test suite', () => {
  const mockEnvironmentService = mock(EnvironmentService);

  beforeEach(() => {
    dynamoDBDocumentClientMock.reset();
  });

  it('returns the cache if the entity ID existed', async () => {
    const mockDynamodbItem = {
      entityUuid: 'uuid',
      expirationTime: 123,
      acn: 'acn',
      registeredAddress: mockRegisteredAddress,
      businessAddress: mockBusinessAddress,
      members: mockMembers,
      requestXml: 'request_xml',
      responseXml: 'response_xml',
    };

    dynamoDBDocumentClientMock.on(QueryCommand).resolves({
      Count: 2,
      Items: [
        mockDynamodbItem,
        {
          entityUuid: 'uuid',
          expirationTime: 1231,
          acn: 'acn_1',
        },
      ],
    });

    expect(dynamoDBDocumentClientMock).not.toHaveReceivedCommand(QueryCommand);
    const cache = await lookupFullCompanyProfileCacheByEntityUuid('uuid', instance(mockEnvironmentService));
    expect(dynamoDBDocumentClientMock).toHaveReceivedCommandTimes(QueryCommand, 1);

    expect(cache).not.toBeNull();
    expect(cache).toEqual(mockDynamodbItem);
  });

  it('returns the cache if the entity ID existed - without address and the members', async () => {
    const mockDynamodbItem = {
      entityUuid: 'uuid',
      expirationTime: 123,
      acn: 'acn',
    };
    dynamoDBDocumentClientMock.on(QueryCommand).resolves({
      Count: 1,
      Items: [mockDynamodbItem],
    });

    expect(dynamoDBDocumentClientMock).not.toHaveReceivedCommand(QueryCommand);
    const cache = await lookupFullCompanyProfileCacheByEntityUuid('uuid', instance(mockEnvironmentService));
    expect(dynamoDBDocumentClientMock).toHaveReceivedCommandTimes(QueryCommand, 1);

    expect(cache).not.toBeNull();
    expect(cache).toEqual(mockDynamodbItem);
  });

  it('return null if the cache is not existed', async () => {
    dynamoDBDocumentClientMock.on(QueryCommand).resolves({
      Count: 0,
      Items: [],
    });

    expect(dynamoDBDocumentClientMock).not.toHaveReceivedCommand(QueryCommand);
    const cache = await lookupFullCompanyProfileCacheByEntityUuid('uuid', instance(mockEnvironmentService));
    expect(dynamoDBDocumentClientMock).toHaveReceivedCommandTimes(QueryCommand, 1);

    expect(cache).toBeNull();
  });

  it('throw when Dynamodb is failed', async () => {
    dynamoDBDocumentClientMock.on(QueryCommand).rejects(new Error(DYNAMODB_ERROR_STRING));
    await expect(
      lookupFullCompanyProfileCacheByEntityUuid('uuid', instance(mockEnvironmentService), AGGREGATE_ID),
    ).rejects.toThrow(DYNAMODB_ERROR_STRING);
  });

  it('should be able to xray aws xray on dynamodb client', async () => {
    when(mockEnvironmentService.isInLambda).thenReturn(true);
    const spy = jest.spyOn(AWSXray, 'captureAWSv3Client').mockReturnValue({
      middlewareStack: {
        remove: '',
        use: '',
      },
      config: '',
    });

    dynamoDBDocumentClientMock.on(QueryCommand).resolves({
      Count: 0,
      Items: [],
    });

    expect(spy).not.toHaveBeenCalled();
    await lookupFullCompanyProfileCacheByEntityUuid('uuid', instance(mockEnvironmentService));
    expect(spy).toHaveBeenCalledTimes(1);
    spy.mockRestore();
  });
});

describe('create full company profile search cache', () => {
  const mockEnvironmentService = mock(EnvironmentService);
  const mockRequestXml = 'request_xml';
  const mockResponseXml = 'response_xml';
  const mockInput = {
    aggregateId: 'uuid',
    acn: '*********',
    found: true,
    registeredAddress: mockRegisteredAddress,
    businessAddress: mockBusinessAddress,
    members: mockMembers,
    requestXml: mockRequestXml,
    responseXml: mockResponseXml,
  } as FullCompanyProfileSearchResult;

  beforeEach(() => {
    dynamoDBDocumentClientMock.reset();
  });

  it('can create cache', async () => {
    dynamoDBDocumentClientMock.on(PutCommand).resolves({});

    expect(dynamoDBDocumentClientMock).not.toHaveReceivedCommand(PutCommand);
    const timestamp = new Date().getTime();
    await createFullCompanyProfileCache(mockInput, instance(mockEnvironmentService), AGGREGATE_ID);
    expect(dynamoDBDocumentClientMock).toHaveReceivedCommandTimes(PutCommand, 1);

    const { Item } = getCommandInputs(dynamoDBDocumentClientMock, PutCommand)[0];
    const { entityUuid, expirationTime, acn, registeredAddress, businessAddress, members, requestXml, responseXml } =
      Item as any;

    const ttl = new Date();
    ttl.setTime(timestamp);
    ttl.setDate(ttl.getDate() + 1);

    expect(entityUuid).toBe(mockInput.aggregateId);
    expect(acn).toBe(mockInput.acn);
    expect(expirationTime).toBeLessThanOrEqual(Math.ceil(ttl.getTime() / 1e3));
    expect(expirationTime).toBeGreaterThanOrEqual(Math.floor(timestamp / 1e3));
    expect(registeredAddress).toBe(mockInput.registeredAddress);
    expect(businessAddress).toBe(mockInput.businessAddress);
    expect(members).toEqual(expect.arrayContaining(mockMembers));
    expect(responseXml).toEqual(mockResponseXml);
    expect(requestXml).toEqual(mockRequestXml);
  });

  it('can create cache without the optional data', async () => {
    dynamoDBDocumentClientMock.on(PutCommand).resolves({});

    const mockMinInput = {
      aggregateId: 'uuid',
      acn: '*********',
      found: true,
      members: [],
      requestXml: mockRequestXml,
      responseXml: mockResponseXml,
    } as FullCompanyProfileSearchResult;

    expect(dynamoDBDocumentClientMock).not.toHaveReceivedCommand(PutCommand);
    const timestamp = new Date().getTime();
    await createFullCompanyProfileCache(mockMinInput, instance(mockEnvironmentService), AGGREGATE_ID);
    expect(dynamoDBDocumentClientMock).toHaveReceivedCommandTimes(PutCommand, 1);

    const { Item } = getCommandInputs(dynamoDBDocumentClientMock, PutCommand)[0];
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const { entityUuid, acn, registeredAddress, businessAddress, members, requestXml, responseXml, expirationTime } =
      Item;

    const ttl = new Date();
    ttl.setTime(timestamp);
    ttl.setDate(ttl.getDate() + 1);

    expect(expirationTime).toBeLessThanOrEqual(Math.ceil(ttl.getTime() / 1e3));
    expect(expirationTime).toBeGreaterThanOrEqual(Math.floor(timestamp / 1e3));
    expect(entityUuid).toBe(mockInput.aggregateId);
    expect(acn).toBe(mockInput.acn);
    expect(registeredAddress).toBeUndefined();
    expect(businessAddress).toBeUndefined();
    expect(members).toBeUndefined();
    expect(responseXml).toEqual(mockResponseXml);
    expect(requestXml).toEqual(mockRequestXml);
  });

  it('throw when Dynamodb is failed', async () => {
    dynamoDBDocumentClientMock.on(PutCommand).rejects(new Error(DYNAMODB_ERROR_STRING));

    await expect(
      createFullCompanyProfileCache(mockInput, instance(mockEnvironmentService), AGGREGATE_ID),
    ).rejects.toThrow(DYNAMODB_ERROR_STRING);
  });

  it('should be able to xray aws xray on dynamodb client', async () => {
    when(mockEnvironmentService.isInLambda).thenReturn(true);
    const spy = jest.spyOn(AWSXray, 'captureAWSv3Client').mockReturnValue({
      middlewareStack: {
        remove: '',
        use: '',
      },
      config: '',
    });

    dynamoDBDocumentClientMock.on(PutCommand).resolves({});

    expect(spy).not.toHaveBeenCalled();
    await createFullCompanyProfileCache(mockInput, instance(mockEnvironmentService), AGGREGATE_ID);
    expect(spy).toHaveBeenCalledTimes(1);
    spy.mockRestore();
  });
});
