import { debug, error, info } from '@npco/component-bff-core/dist/utils/logger.js';

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import AWSXray from 'aws-xray-sdk';

import type { EnvironmentService } from '../../../../config/envService.js';
import type { FullCompanyProfileSearchResult } from '../../../../types.js';
import type { CompanyFullProfileSearchDbRecord } from '../types.js';

export const lookupFullCompanyProfileCacheByEntityUuid = async (
  entityUuid: string,
  envService: EnvironmentService,
  aggregateId?: string,
): Promise<CompanyFullProfileSearchDbRecord | null> => {
  const dynamodb = DynamoDBDocumentClient.from(
    new DynamoDBClient({
      region: envService.region,
    }),
  );

  if (envService.isInLambda) {
    AWSXray.captureAWSv3Client(dynamodb);
  }

  const params = {
    TableName: envService.fullCompanyProfileSearchTable,
    KeyConditionExpression: 'entityUuid = :entityUuid',
    ExpressionAttributeValues: {
      ':entityUuid': entityUuid,
    },
    ScanIndexForward: false,
    Limit: 1,
  };

  try {
    debug(`Query full company profile search with ${params}`, aggregateId);
    const command = new QueryCommand(params);
    const result = await dynamodb.send(command);
    debug(result, aggregateId);
    if (result.Count && result.Count > 0 && result.Items) {
      info(`Full Company Profile Search DynamoDB Table - record found: ${entityUuid}`, aggregateId);
      return result.Items[0] as CompanyFullProfileSearchDbRecord;
    }
    info(`Full Company Profile Search DynamoDB Table - record not found: ${entityUuid}`, aggregateId);
    return null;
  } catch (dynamodbError: any) {
    error(dynamodbError.toString(), aggregateId);
    throw dynamodbError;
  }
};

export const createFullCompanyProfileCache = async (
  companyRecord: FullCompanyProfileSearchResult,
  envService: EnvironmentService,
  aggregateId?: string,
): Promise<void> => {
  info(`Creating Full Company Profile record - ${companyRecord.aggregateId}`, aggregateId);
  const dynamodb = DynamoDBDocumentClient.from(
    new DynamoDBClient({
      region: envService.region,
    }),
    {
      marshallOptions: {
        removeUndefinedValues: true,
      },
    },
  );

  if (envService.isInLambda) {
    AWSXray.captureAWSv3Client(dynamodb);
  }

  const now = new Date();
  const expiredTime = new Date(now);
  expiredTime.setDate(now.getDate() + 1);
  const cache = {
    entityUuid: companyRecord.aggregateId,
    expirationTime: Math.round(expiredTime.getTime() / 1e3),
    acn: companyRecord.acn,
    ...(companyRecord.registeredAddress ? { registeredAddress: companyRecord.registeredAddress } : {}),
    ...(companyRecord.businessAddress ? { businessAddress: companyRecord.businessAddress } : {}),
    ...(companyRecord.members && companyRecord.members.length > 0 ? { members: companyRecord.members } : {}),
    requestXml: companyRecord.requestXml,
    responseXml: companyRecord.responseXml,
  } as CompanyFullProfileSearchDbRecord;
  debug('Cache data:', aggregateId);
  debug(cache, aggregateId);
  const params = {
    TableName: envService.fullCompanyProfileSearchTable,
    Item: cache,
  };

  try {
    debug('Put the records into Initial Search DynamoDB Table with following parameters', aggregateId);
    debug(params, aggregateId);
    const command = new PutCommand(params);
    await dynamodb.send(command);
  } catch (dynamodbError: any) {
    error(dynamodbError.toString(), aggregateId);
    throw new Error(dynamodbError.toString());
  }
  info(`Initial Search DynamoDB Table - record created: ${companyRecord.aggregateId}`, aggregateId);
};
