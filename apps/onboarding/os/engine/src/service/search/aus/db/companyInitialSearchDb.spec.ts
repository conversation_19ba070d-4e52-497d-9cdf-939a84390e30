import { DynamoDBDocumentClient, PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { jest } from '@jest/globals';
import { mockClient } from 'aws-sdk-client-mock';
import AWSXray from 'aws-xray-sdk';
import { instance, mock, when } from 'ts-mockito';
import { v4 } from 'uuid';

import { EnvironmentService } from '../../../../config/envService.js';
import type { InitialCompanySearchResult } from '../../../../types.js';
import { getCommandInputs } from '../../../../utils/test/getCommandInputs.spec.js';

import {
  createInitialSearchCache,
  lookupInitialSearchCacheByBusinessIdentifier,
  lookupInitialSearchCacheByEntityUuid,
} from './companyInitialSearchDb.js';
import 'aws-sdk-client-mock-jest';

jest.mock('aws-xray-sdk');

const DYNAMODB_ERROR_STRING = 'Dynamodb error';
const AGGREGATE_ID = 'AGGREGATE_ID';
const dynamoDBDocumentClientMock = mockClient(DynamoDBDocumentClient);

describe('create cache', () => {
  const mockEnvironmentService = mock(EnvironmentService);
  const mockInput = {
    aggregateId: 'uuid',
    businessIdentifier: '*********',
    name: 'BRAMBLES AUSTRALIA PTY LTD',
    acn: '*********',
    abn: '***********',
    type: 'COMPANY',
    active: true,
    abrEntityType: 'PRV',
    asicType: 'APTY',
    requestXml: 'REQUEST_XML',
    responseXml: 'RESPONSE_XML',
  } as InitialCompanySearchResult;

  beforeEach(() => {
    dynamoDBDocumentClientMock.reset();
  });

  it('can create cache with entityUuid', async () => {
    dynamoDBDocumentClientMock.on(PutCommand).resolves({});

    expect(dynamoDBDocumentClientMock).not.toHaveReceivedCommand(PutCommand);
    const timestampBefore = new Date().getTime();
    const mockEntityUuid = v4();
    when(mockEnvironmentService.initialCompanySearchCacheLifeTime).thenReturn(1);
    await createInitialSearchCache(mockInput, instance(mockEnvironmentService), mockInput.aggregateId, mockEntityUuid);
    const timestampAfter = new Date().getTime();
    expect(dynamoDBDocumentClientMock).toHaveReceivedCommandTimes(PutCommand, 1);

    const { Item } = getCommandInputs(dynamoDBDocumentClientMock, PutCommand)[0];
    const {
      id,
      entityUuid,
      customerUuid,
      expirationTime,
      name,
      acn,
      abn,
      type,
      active,
      abrEntityType,
      asicType,
      requestXml,
      responseXml,
    } = Item as any;

    const ttlBefore = new Date();
    ttlBefore.setTime(timestampBefore);
    ttlBefore.setDate(ttlBefore.getDate() + 1);

    const ttlAfter = new Date();
    ttlAfter.setTime(timestampAfter);
    ttlAfter.setDate(ttlAfter.getDate() + 1);

    expect(id).toBe(mockInput.businessIdentifier);
    expect(customerUuid).toBe(mockInput.aggregateId);
    expect(entityUuid).toBe(mockEntityUuid);
    expect(expirationTime).toBeGreaterThanOrEqual(Math.trunc(ttlBefore.getTime() / 1e3));
    expect(expirationTime).toBeLessThanOrEqual(Math.ceil(ttlAfter.getTime() / 1e3));
    expect(name).toBe(mockInput.name);
    expect(acn).toBe(mockInput.acn);
    expect(abn).toBe(mockInput.abn);
    expect(type).toBe(mockInput.type);
    expect(active).toBe(mockInput.active);
    expect(abrEntityType).toBe(mockInput.abrEntityType);
    expect(asicType).toBe(mockInput.asicType);
    expect(requestXml).toBe(mockInput.requestXml);
    expect(responseXml).toBe(mockInput.responseXml);
  });

  it('can create cache without entityUuid', async () => {
    dynamoDBDocumentClientMock.on(PutCommand).resolves({});

    expect(dynamoDBDocumentClientMock).not.toHaveReceivedCommand(PutCommand);
    const timestampBefore = new Date().getTime();
    when(mockEnvironmentService.initialCompanySearchCacheLifeTime).thenReturn(1);
    await createInitialSearchCache(mockInput, instance(mockEnvironmentService), mockInput.aggregateId);
    const timestampAfter = new Date().getTime();
    expect(dynamoDBDocumentClientMock).toHaveReceivedCommandTimes(PutCommand, 1);

    const { Item } = getCommandInputs(dynamoDBDocumentClientMock, PutCommand)[0];
    const {
      id,
      entityUuid,
      customerUuid,
      expirationTime,
      name,
      acn,
      abn,
      type,
      active,
      abrEntityType,
      asicType,
      requestXml,
      responseXml,
    } = Item as any;

    const ttlBefore = new Date();
    ttlBefore.setTime(timestampBefore);
    ttlBefore.setDate(ttlBefore.getDate() + 1);

    const ttlAfter = new Date();
    ttlAfter.setTime(timestampAfter);
    ttlAfter.setDate(ttlAfter.getDate() + 1);

    expect(id).toBe(mockInput.businessIdentifier);
    expect(customerUuid).toBe(mockInput.aggregateId);
    expect(entityUuid).toBeUndefined();
    expect(expirationTime).toBeGreaterThanOrEqual(Math.trunc(ttlBefore.getTime() / 1e3));
    expect(expirationTime).toBeLessThanOrEqual(Math.ceil(ttlAfter.getTime() / 1e3));
    expect(name).toBe(mockInput.name);
    expect(acn).toBe(mockInput.acn);
    expect(abn).toBe(mockInput.abn);
    expect(type).toBe(mockInput.type);
    expect(active).toBe(mockInput.active);
    expect(abrEntityType).toBe(mockInput.abrEntityType);
    expect(asicType).toBe(mockInput.asicType);
    expect(requestXml).toBe(mockInput.requestXml);
    expect(responseXml).toBe(mockInput.responseXml);
  });

  it('can create cache based on the environment service parameters', async () => {
    dynamoDBDocumentClientMock.on(PutCommand).resolves({});

    expect(dynamoDBDocumentClientMock).not.toHaveReceivedCommand(PutCommand);
    const timestampBefore = new Date().getTime();
    when(mockEnvironmentService.initialCompanySearchCacheLifeTime).thenReturn(10);
    await createInitialSearchCache(mockInput, instance(mockEnvironmentService));
    const timestampAfter = new Date().getTime();
    expect(dynamoDBDocumentClientMock).toHaveReceivedCommandTimes(PutCommand, 1);

    const { Item } = getCommandInputs(dynamoDBDocumentClientMock, PutCommand)[0];
    const { expirationTime } = Item as any;

    const ttlBefore = new Date();
    ttlBefore.setTime(timestampBefore);
    ttlBefore.setDate(ttlBefore.getDate() + 10);

    const ttlAfter = new Date();
    ttlAfter.setTime(timestampAfter);
    ttlAfter.setDate(ttlAfter.getDate() + 10);

    expect(expirationTime).toBeGreaterThanOrEqual(Math.trunc(ttlBefore.getTime() / 1e3));
    expect(expirationTime).toBeLessThanOrEqual(Math.ceil(ttlAfter.getTime() / 1e3));
  });

  it('throw when Dynamodb is failed', async () => {
    dynamoDBDocumentClientMock.on(PutCommand).rejects(new Error(DYNAMODB_ERROR_STRING));

    await expect(createInitialSearchCache(mockInput, instance(mockEnvironmentService), AGGREGATE_ID)).rejects.toThrow(
      DYNAMODB_ERROR_STRING,
    );
  });

  it('should be able to xray aws xray on dynamodb client', async () => {
    when(mockEnvironmentService.isInLambda).thenReturn(true);
    const spy = jest.spyOn(AWSXray, 'captureAWSv3Client').mockReturnValue({
      middlewareStack: {
        remove: '',
        use: '',
      },
      config: '',
    }); // () => jest.fn());

    dynamoDBDocumentClientMock.on(PutCommand).resolves({});

    expect(spy).not.toHaveBeenCalled();
    await createInitialSearchCache(mockInput, instance(mockEnvironmentService));
    expect(spy).toHaveBeenCalledTimes(1);
    spy.mockRestore();
  });
});

describe('Lookup Cache', () => {
  const mockEnvironmentService = mock(EnvironmentService);

  beforeEach(() => {
    dynamoDBDocumentClientMock.reset();
  });

  it('returns the cache if it is in the Dynamodb', async () => {
    const mockDynamodbItem = {
      id: '*********',
      entityUuid: 'uuid',
      expirationTime: 123,
      name: 'name',
      acn: 'acn',
      abn: 'abn',
      type: 'type',
      active: true,
      abrEntityType: 'PRV',
      asicType: 'APTY',
      requestXml: 'requestXml',
      responseXml: 'responseXml',
    };
    dynamoDBDocumentClientMock.on(QueryCommand).resolves({
      Count: 2,
      Items: [
        mockDynamodbItem,
        {
          id: 'id_1',
          entityUuid: 'uuid',
          expirationTime: 1231,
          name: 'name_1',
          acn: 'acn_1',
          abn: 'abn_1',
          type: 'type_!',
          active: false,
          requestXml: 'requestXml_1',
          responseXml: 'responseXml_1',
        },
      ],
    });

    expect(dynamoDBDocumentClientMock).not.toHaveReceivedCommand(QueryCommand);
    const cache = await lookupInitialSearchCacheByBusinessIdentifier('*********', instance(mockEnvironmentService));
    expect(dynamoDBDocumentClientMock).toHaveReceivedCommandTimes(QueryCommand, 1);

    expect(cache).not.toBeNull();
    expect(cache).toEqual(mockDynamodbItem);
  });

  it('return null if the cache is not existed', async () => {
    dynamoDBDocumentClientMock.on(QueryCommand).resolves({
      Count: 0,
      Items: [],
    });

    expect(dynamoDBDocumentClientMock).not.toHaveReceivedCommand(QueryCommand);
    const cache = await lookupInitialSearchCacheByBusinessIdentifier('*********', instance(mockEnvironmentService));
    expect(dynamoDBDocumentClientMock).toHaveReceivedCommandTimes(QueryCommand, 1);

    expect(cache).toBeNull();
  });

  it('throw when Dynamodb is failed', async () => {
    dynamoDBDocumentClientMock.on(QueryCommand).rejects(new Error(DYNAMODB_ERROR_STRING));
    await expect(
      lookupInitialSearchCacheByBusinessIdentifier('*********', instance(mockEnvironmentService), AGGREGATE_ID),
    ).rejects.toThrow(DYNAMODB_ERROR_STRING);
  });

  it('should be able to xray aws xray on dynamodb client', async () => {
    when(mockEnvironmentService.isInLambda).thenReturn(true);
    const spy = jest.spyOn(AWSXray, 'captureAWSv3Client').mockReturnValue({
      middlewareStack: {
        remove: '',
        use: '',
      },
      config: '',
    });

    dynamoDBDocumentClientMock.on(QueryCommand).resolves({});

    expect(spy).not.toHaveBeenCalled();
    await lookupInitialSearchCacheByBusinessIdentifier('*********', instance(mockEnvironmentService));
    expect(spy).toHaveBeenCalledTimes(1);
    spy.mockRestore();
  });
});

describe('Look up in GIS', () => {
  const mockEnvironmentService = mock(EnvironmentService);

  beforeEach(() => {
    dynamoDBDocumentClientMock.reset();
  });

  it('returns the cache if the entity ID existed', async () => {
    const mockDynamodbItem = {
      id: '*********',
      entityUuid: 'uuid',
      expirationTime: 123,
      name: 'name',
      acn: 'acn',
      abn: 'abn',
      type: 'type',
      active: true,
    };
    dynamoDBDocumentClientMock.on(QueryCommand).resolves({
      Count: 2,
      Items: [
        mockDynamodbItem,
        {
          id: 'id_1',
          entityUuid: 'uuid',
          expirationTime: 1231,
          name: 'name_1',
          acn: 'acn_1',
          abn: 'abn_1',
          type: 'type_!',
        },
      ],
    });

    expect(dynamoDBDocumentClientMock).not.toHaveReceivedCommand(QueryCommand);
    const cache = await lookupInitialSearchCacheByEntityUuid('uuid', instance(mockEnvironmentService));
    expect(dynamoDBDocumentClientMock).toHaveReceivedCommandTimes(QueryCommand, 1);

    expect(cache).not.toBeNull();
    expect(cache).toEqual(mockDynamodbItem);
  });

  it('return null if the cache is not existed', async () => {
    dynamoDBDocumentClientMock.on(QueryCommand).resolves({
      Count: 0,
      Items: [],
    });

    expect(dynamoDBDocumentClientMock).not.toHaveReceivedCommand(QueryCommand);
    const cache = await lookupInitialSearchCacheByEntityUuid('uuid', instance(mockEnvironmentService));
    expect(dynamoDBDocumentClientMock).toHaveReceivedCommandTimes(QueryCommand, 1);

    expect(cache).toBeNull();
  });

  it('throw when Dynamodb is failed', async () => {
    dynamoDBDocumentClientMock.on(QueryCommand).rejects(new Error(DYNAMODB_ERROR_STRING));
    await expect(
      lookupInitialSearchCacheByEntityUuid('uuid', instance(mockEnvironmentService), AGGREGATE_ID),
    ).rejects.toThrow(DYNAMODB_ERROR_STRING);
  });

  it('should be able to xray aws xray on dynamodb client', async () => {
    when(mockEnvironmentService.isInLambda).thenReturn(true);
    const spy = jest.spyOn(AWSXray, 'captureAWSv3Client').mockReturnValue({
      middlewareStack: {
        remove: '',
        use: '',
      },
      config: '',
    });

    dynamoDBDocumentClientMock.on(QueryCommand).resolves({
      Count: 0,
      Items: [],
    });

    expect(spy).not.toHaveBeenCalled();
    await lookupInitialSearchCacheByEntityUuid('uuid', instance(mockEnvironmentService));
    expect(spy).toHaveBeenCalledTimes(1);
    spy.mockRestore();
  });
});
