import type { EntityType, ScreeningRequestedType } from '@npco/component-dto-core';
import type { EntityAddress, EntityMember } from '@npco/component-dto-entity';

export interface CompanyInitialSearchDbRecord {
  id: string;
  entityUuid: string;
  expirationTime: number;
  name?: string;
  acn?: string;
  abn?: string;
  type?: EntityType;
  active?: boolean;
  abrEntityType?: string;
  asicType?: string;
  requestXml: string;
  responseXml: string;
}

export interface CompanyFullProfileSearchDbRecord {
  entityUuid: string;
  expirationTime: number;
  acn: string;
  registeredAddress?: EntityAddress;
  businessAddress?: EntityAddress;
  members?: EntityMember[];
  requestXml: string;
  responseXml: string;
}

export interface CustomerScreeningSearchDbRecord {
  customerUuid: string;
  screeningResponse?: string;
  error?: string;
  request: string;
  validResponseFromScreening: boolean;
  expirationTime: number;
  screeningType: ScreeningRequestedType;
}

export interface EntityScreeningSearchDbRecord {
  entityUuid: string;
  screeningResponse?: string;
  error?: string;
  request: string;
  validResponseFromScreening: boolean;
  expirationTime: number;
}
