service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-dynamodb

plugins:
  - serverless-dotenv-plugin
  - serverless-plugin-resource-tagging
  - serverless-create-global-dynamodb-table-tags

useDotenv: true
variablesResolutionMode: ********
frameworkVersion: '3'

provider:
  name: aws
  deploymentMethod: direct
  runtime: nodejs20.x
  region: ${opt:region}
  accountId: '#{AWS::AccountId}'
  environment:
    LOG_LEVEL: ${env:LOG_LEVEL}
    COMPONENT_NAME: ${env:COMPONENT_NAME, 'os'}
    PART_NAME: ${env:PART_NAME, 'engine'}
    NODE_OPTIONS: '--enable-source-maps'
  vpc:
    securityGroupIds:
      - ${ssm:${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg}
    subnetIds:
      - ${ssm:${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01}
      - ${ssm:${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02}
      - ${ssm:${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03}
  stackName: ${self:service}
  bucketStackName: ${env:COMPONENT_NAME}-${env:PART_NAME}-iac-s3
  deploymentBucket:
    name: ${cf:${self:provider.bucketStackName}.deploymentBucket}
    maxPreviousDeploymentArtifacts: 100
    blockPublicAccess: true
  tags:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    service: ${env:COMPONENT_NAME}-${env:PART_NAME}
    env: ${opt:stage}
  stackTags: ${self:provider.tags}

custom:
  serviceName: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  initialCompanySearchTable:
    name: !Ref InitialCompanySearchTable
    entityUuidGSI: ${env:INITIAL_COMPANY_SEARCH_TABLE_ENTITY_UUID_GSI, 'entityUuidGSI'}
    customerUuidGSI: ${env:INITIAL_COMPANY_SEARCH_TABLE_CUSTOMER_UUID_GSI, 'customerUuidGSI'}
  fullCompanyProfileSearchTable:
    name: !Ref FullCompanyProfileSearchTable
  customerScreeningSearchTable:
    name: !Ref CustomerScreeningSearchTable
  entityScreeningSearchTable:
    name: !Ref EntityScreeningSearchTable
  idvSafeHarbourSearchTable:
    name: !Ref IdvSafeHarbourSearchTable
  idvSearchTable:
    name: !Ref IdvSearchTable
  safeharbourSearchTable:
    name: !Ref SafeharbourSearchTable
  selfieVerificationSearchTable:
    name: !Ref SelfieVerificationSearchTable
  documentsVerificationSessionTable:
    name: !Ref DocumentsVerificationSessionTable
  provider:
    lambdaLayerStackName: ${self:provider.stackName}-commonLayers
    initialCompanySearchTableName: ${self:provider.stackName}-${env:INITIAL_COMPANY_SEARCH_TABLE_NAME, 'InitialCompanySearchTable'}
    fullCompanyProfileSearchTableName: ${self:provider.stackName}-${env:FULL_COMPANY_PROFILE_SEARCH_TABLE_NAME, 'FullCompanyProfileSearchTable'}
    customerScreeningSearchTableName: ${self:provider.stackName}-${env:CUSTOMER_SCREENING_SEARCH_TABLE_NAME, 'CustomerScreeningSearchTable'}
    entityScreeningSearchTableName: ${self:provider.stackName}-${env:ENTITY_SCREENING_SEARCH_TABLE_NAME, 'EntityScreeningSearchTable'}
    idvSafeHarbourSearchTableName: ${self:provider.stackName}-${env:IDV_SAFE_HARBOUR_SEARCH_TABLE_NAME, 'IdvSafeHarbourSearchTable'}
    idvSearchTableName: ${self:provider.stackName}-${env:IDV_SEARCH_TABLE_NAME, 'IdvSearchTable'}
    safeharbourSearchTableName: ${self:provider.stackName}-${env:SAFE_HARBOUR_SEARCH_TABLE_NAME, 'SafeharbourSearchTable'}
    selfieVerificationSearchTableName: ${self:provider.stackName}-${env:SELFIE_VERIFICATION_SEARCH_TABLE_NAME, 'SelfieVerificationSearchTable'}
    documentsVerificationSessionTableName: ${self:provider.stackName}-${env:DOCUMENTS_VERIFICATION_SESSION_TABLE_NAME, 'DocumentsVerificationSessionTable'}

  componentName: ${env:COMPONENT_NAME}
  partName: ${env:PART_NAME}
  backupNamePrefix: ${opt:stage}-${self:custom.componentName}-${self:custom.partName}
  primaryRegion: ${env:PRIMARY_REGION}
  tableDeletionProtection:
    dev: true
    staging: true
    prod: true
    st: false

package:
  exclude:
    - node_modules/**

resources:
  - ${file(resources/dynamodb/sydney/dynamodb.yml)}
