import { NotFoundError } from '@npco/component-bff-core/dist/error';
import type { LambdaService } from '@npco/component-bff-core/dist/lambda';
import { error, info, debug } from '@npco/component-bff-core/dist/utils/logger';
import type { Customer, DynamodbService, RiskRuleApiService } from '@npco/component-dbs-mp-common/dist';
import {
  AmsApiService,
  isInOnboardingRiskCategory,
  isOnboardingProhibitedMCC,
} from '@npco/component-dbs-mp-common/dist';
import { AppSyncClient } from '@npco/component-dbs-mp-common/dist/appsync/appSyncClient';
import { convertDbItemToCustomer } from '@npco/component-dbs-mp-common/dist/customer/convertDbItemToCustomer';
import { getCustomerDbItem } from '@npco/component-dbs-mp-common/dist/customer/getCustomerDbItem';
import type { CustomerVerificationResponseTempV2 } from '@npco/component-dbs-mp-common/dist/customer/publisher/types';
import { convertDbItemToEntity } from '@npco/component-dbs-mp-common/dist/entity/convertDbItemToEntity';
import { getEntityDbItem } from '@npco/component-dbs-mp-common/dist/entity/getEntityDbItem';
import { EntityType } from '@npco/component-dto-core/dist';
import type {
  CustomerDocumentVerificationV2RequestDto,
  CustomerUpdateInput,
  ProductTourStatus,
} from '@npco/component-dto-customer/dist';
import {
  CustomerDocumentVerificationResult,
  CustomerSafeHarbourRequestedEventDto,
  CustomerUpdatedEventDto,
  IdvSafeHarbourDocumentType,
} from '@npco/component-dto-customer/dist';

import type { AxiosError } from 'axios';
import axios from 'axios';
import gql from 'graphql-tag';
import type { EnvironmentService } from 'service/environment/environmentService';

import type { DocumentsVerificationService } from '../documentsVerification/documentsVerificationService';

export class CustomerService {
  private readonly amsApiService: AmsApiService;

  constructor(
    private readonly envService: EnvironmentService,
    private readonly dynamodbService: DynamodbService,
    private lambdaService: LambdaService,
    private riskService: RiskRuleApiService,
    private readonly documentsVerificationService: DocumentsVerificationService,
  ) {
    this.amsApiService = new AmsApiService(this.envService, this.envService.amsCustomerEndpointPath, 'customer');
  }

  requestCustomerDocumentVerificationV2 = async (
    zellerSessionId: string,
    requestDto: CustomerDocumentVerificationV2RequestDto,
    entityUuid: string,
  ) => {
    if (requestDto.documentType === IdvSafeHarbourDocumentType.NO_SECOND_ID) {
      // emit safe harbour
      const cqrsCommand = this.envService.cqrsCmds.Customer.SafeHarbourRequested;
      const dto = new CustomerSafeHarbourRequestedEventDto({
        customerUuid: requestDto.customerUuid,
        entityUuid,
        firstName: requestDto.firstName,
        middleName: requestDto.middleName,
        lastName: requestDto.lastName,
        dob: requestDto.dob ?? '',
        address: requestDto.address ?? {},
      });
      info(`send request dto to command handler: ${cqrsCommand}, ${JSON.stringify(dto, null, 2)}.`);
      const output = await this.lambdaService.invokeCommand(cqrsCommand, dto);
      info(`invoke command handler lambda response: ${JSON.stringify(output, null, 2)}`);
      const verificationResult: CustomerVerificationResponseTempV2 = {
        customerUuid: requestDto.customerUuid,
        documentType: requestDto.documentType,
        result: CustomerDocumentVerificationResult.NOT_VERIFIED,
      };
      const client = await new AppSyncClient(this.envService).getAppSyncClient();
      const response = await client.mutate({
        mutation: gql`
          mutation publishCustomerVerificationResult($result: VerifyCustomerDocumentsResultsInput!) {
            publishCustomerVerificationResult(input: $result) {
              customerUuid
              documentType
              result
            }
          }
        `,
        variables: {
          result: verificationResult,
        },
      });
      info('requestCustomerDocumentVerificationV2Handler: mutation response:', JSON.stringify(response));
    } else {
      const item = await getEntityDbItem(this.dynamodbService, entityUuid);
      if (!item) {
        throw new Error(`Entity not found for entityUuid: ${entityUuid}`);
      }
      const entity = convertDbItemToEntity(item);
      const customer = await this.getCustomer(entityUuid, requestDto.customerUuid);

      if (customer.registeringIndividual) await this.sendIdvSessionInformation(zellerSessionId, customer, requestDto);

      let useSafeHarbourVerification = true;

      if (requestDto?.documentType === IdvSafeHarbourDocumentType.MEDICARE_CARD) {
        useSafeHarbourVerification = true;
      } else if (!customer.registeringIndividual) {
        useSafeHarbourVerification = false;
      } else {
        const isRiskCategory = await isInOnboardingRiskCategory(entity, this.riskService);
        const isProhibitedMCC = await isOnboardingProhibitedMCC(entity, this.riskService);

        useSafeHarbourVerification =
          entity.type === EntityType.ASSOCIATION_UNINCORPORATED || isRiskCategory || isProhibitedMCC;
      }
      if (useSafeHarbourVerification) {
        await this.documentsVerificationService.verifyDocumentsWithSafeHarbour(zellerSessionId, requestDto);
      } else {
        await this.documentsVerificationService.verifyDocuments(zellerSessionId, requestDto);
      }
    }

    return this.updateCustomer(
      {
        firstname: requestDto.firstName,
        middlename: requestDto.middleName,
        lastname: requestDto.lastName,
        dob: requestDto.dob,
        address: requestDto.address as any,
        entityUuid,
        id: requestDto.customerUuid,
      },
      entityUuid,
    );
  };

  getCustomer = async (entityUuid: string, customerUuid: string): Promise<Customer> => {
    const customerDbItem = await getCustomerDbItem(this.dynamodbService, entityUuid, customerUuid);
    if (customerDbItem) {
      return convertDbItemToCustomer(customerDbItem);
    }
    error('Customer not found', customerUuid);
    return Promise.reject(new NotFoundError('Customer not found', customerUuid));
  };

  updateCustomer = async (customer: CustomerUpdateInput, entityUuid?: string): Promise<boolean> => {
    const customerUpdateEventDto = this.prepareUpdateDto(customer, entityUuid);
    debug(`send update customer event: ${JSON.stringify(customerUpdateEventDto, null, 2)}`);
    return this.amsApiService.update<CustomerUpdatedEventDto>(
      customerUpdateEventDto.customerUuid,
      customerUpdateEventDto,
    );
  };

  prepareUpdateDto(customer: CustomerUpdateInput, entityUuid?: string): CustomerUpdatedEventDto {
    const productTourStatus = this.setProductTourStatus(customer);
    return new CustomerUpdatedEventDto({
      ...customer,
      customerUuid: customer.id,
      entityUuid,
      productTourStatus,
    });
  }

  setProductTourStatus = (customer: CustomerUpdateInput): ProductTourStatus | undefined => {
    const productTourStatusKeys: Array<keyof ProductTourStatus> = [
      'showAdminMerchantPortalWelcome',
      'showInvoiceInstructions',
      'showInvoicesWelcome',
      'showItemsWelcome',
      'showItemInstructions',
      'showInvoicesCustomisationWelcome',
      'showInvoicesScheduleSendWelcome',
      'showInvoicesSendBySmsWelcome',
      'showInvoiceSendViaInfo',
      'showInvoicingCustomisationSettingsWelcome',
      'showTapToPayInstructions',
      'showTapToPayMayJune',
      'showSavingsAccountWelcome',
      'showSavingsAccountMarch',
      'showCorporateCardsMayOffer',
      'showCorporateCardsWalkthrough',
      'showCorporateCardsSettingsWalkthrough',
      'showCustomScreensaverPromo',
      'showNotificationsWelcome',
      'showCorporateCardsAdminWelcome',
      'showInvoiceApril',
      'showCatalogueItemsWelcome',
      'profileAvatarWalkthrough',
      'showServiceChargesWelcome',
    ];

    const productTourStatus: ProductTourStatus = {};

    for (const ptsKey of productTourStatusKeys) {
      const ptsValue = customer[ptsKey];
      if (ptsValue !== undefined) {
        productTourStatus[ptsKey] = ptsValue;
      }
    }

    const isEmptyObject = Object.entries(productTourStatus).length === 0;
    return isEmptyObject ? undefined : productTourStatus;
  };

  sendIdvSessionInformation = async (
    sessionUuid: string,
    customer: Customer,
    requestDto: CustomerDocumentVerificationV2RequestDto,
  ) => {
    const sessionServiceApiEndpoint = process.env.SIS_API_ENDPOINT ?? '';
    const sessionServiceApiVersion = 'v1';
    const url = `${sessionServiceApiEndpoint}/${sessionServiceApiVersion}/customer`;

    debug(`send request to session service ${url} for session: ${sessionUuid} customer: ${customer.id}`);

    const customerPayload = await this.generateCustomerPayloadForSardine(requestDto, customer);
    const res = await axios
      .post(
        url,
        {
          zellerSessionId: sessionUuid,
          customerUuid: requestDto.customerUuid,
          customerDetails: customerPayload,
        },
        {
          headers: { Accept: 'application/json' },
          timeout: 5000,
        },
      )
      .catch((err: AxiosError) => ({
        data: err.response?.data,
        status: err?.response?.status,
        hasError: true,
      }));

    info(
      `session service got ${res.status} response for customer ${customer.id} from ${url} - ${JSON.stringify(
        res.data,
      )}`,
    );
  };

  generateCustomerPayloadForSardine = async (
    requestDto: CustomerDocumentVerificationV2RequestDto,
    customerDetails: Customer,
  ) => {
    return {
      id: requestDto.customerUuid,
      firstName: requestDto.firstName,
      lastName: requestDto.lastName,
      ...(requestDto?.middleName && { middleName: requestDto.middleName }),
      ...(requestDto?.address && {
        address: {
          ...(requestDto.address.street && { street1: requestDto.address.street }),
          ...(requestDto.address.suburb && { city: requestDto.address.suburb }),
          ...(requestDto.address.state && { state: requestDto.address.state }),
          ...(requestDto.address.postcode && { postalCode: requestDto.address.postcode }),
          ...(customerDetails.address?.country && { country: customerDetails.address.country }),
        },
      }),
    };
  };
}
