import { DynamodbService } from '@npco/component-dbs-mp-common/dist/dynamodb';
import { RiskRuleApiService } from '@npco/component-dbs-mp-common/dist/interface';
import { DomainURImap, EntityType } from '@npco/component-dto-core/dist';
import { CustomerSafeHarbourRequestedEventDto } from '@npco/component-dto-customer/dist';
import { IdvSafeHarbourDocumentType } from '@npco/component-dto-customer/dist/types';
import type {
  CustomerDocumentVerificationV2RequestDto,
  CustomerUpdateInput,
} from '@npco/component-dto-customer/dist/types';

import axios from 'axios';
import { instance, mock, when } from 'ts-mockito';
import { v4 as uuidv4 } from 'uuid';

import { DocumentsVerificationService } from '../documentsVerification/documentsVerificationService';
import { EnvironmentService } from '../environment/environmentService';

import { CustomerService } from './customerService';

jest.mock('@npco/component-bff-core/dist/dynamodb');

const mockGetEntityDbItem = jest.fn();
jest.mock('@npco/component-dbs-mp-common/dist/entity/getEntityDbItem', () => {
  return {
    getEntityDbItem: () => mockGetEntityDbItem(),
  };
});

const mockOnboardingCategory = jest.fn();
const mockIsOnboardingProhibitedMCC = jest.fn();
jest.mock('@npco/component-dbs-mp-common/dist/interface/riskRuleApiService', () => {
  return {
    RiskRuleApiService: jest.fn(() => {
      return {
        isInOnboardingRiskCategory: mockOnboardingCategory,
        isOnboardingProhibitedMCC: mockIsOnboardingProhibitedMCC,
      };
    }),
  };
});

jest.mock('@npco/component-dbs-mp-common/dist/interface/amsApiService', () => {
  return {
    AmsApiService: jest.fn(() => {
      return {
        update: jest.fn().mockResolvedValue(true),
      };
    }),
  };
});

const mockVerifyDocuments = jest.fn();
const mockVerifyDocumentsWithSafeHarbour = jest.fn();
jest.mock('../documentsVerification/documentsVerificationService', () => {
  return {
    DocumentsVerificationService: jest.fn(() => {
      return {
        verifyDocuments: mockVerifyDocuments,
        verifyDocumentsWithSafeHarbour: mockVerifyDocumentsWithSafeHarbour,
      };
    }),
  };
});
const mockGetCustomerDbItem = jest.fn();
jest.mock('@npco/component-dbs-mp-common/dist/customer/getCustomerDbItem', () => ({
  getCustomerDbItem: () => mockGetCustomerDbItem(),
}));

const graphqlMutate = jest.fn().mockResolvedValue({});
jest.mock('@npco/component-dbs-mp-common/dist/appsync/appSyncClient', () => {
  const service = {
    getAppSyncClient: jest.fn().mockResolvedValue({
      mutate: () => graphqlMutate(),
    }),
  };
  return { AppSyncClient: jest.fn(() => service) };
});

jest.mock('axios');
jest.mock('aws-xray-sdk-core');

const mockResponse = jest.fn();
const mockLambdaService = {
  invokeCommand: mockResponse,
} as any;

const mockCustomerDbItem = {};
describe('CustomerService test suite', () => {
  let customerService: CustomerService;
  const mockEnvService = mock(EnvironmentService);
  const mockDynamodbService = mock(DynamodbService);

  beforeEach(() => {
    const mockAxiosResponse = { data: { success: true }, status: 200 };
    jest.spyOn(axios, 'post').mockResolvedValue(mockAxiosResponse);
    mockVerifyDocuments.mockReset();
    mockVerifyDocumentsWithSafeHarbour.mockReset();
    mockOnboardingCategory.mockReset();
    mockGetEntityDbItem.mockReset();
    const envInstance = instance(mockEnvService);
    const riskService = new RiskRuleApiService(envInstance);
    mockGetCustomerDbItem.mockResolvedValue(mockCustomerDbItem);
    const documentVerificationService = new DocumentsVerificationService(instance(mockEnvService));
    customerService = new CustomerService(
      envInstance,
      instance(mockDynamodbService),
      mockLambdaService,
      riskService,
      documentVerificationService,
    );
  });

  it('should throw error if entity not found', async () => {
    const nonExistentEntityUuid = 'invalidEntityUuid';
    mockGetEntityDbItem.mockResolvedValue(null);
    await expect(
      customerService.requestCustomerDocumentVerificationV2('zellerSessionId', {} as any, nonExistentEntityUuid),
    ).rejects.toThrowError('Entity not found');
  });

  it('should throw error if customer not found', async () => {
    mockGetCustomerDbItem.mockResolvedValue(null);
    mockGetEntityDbItem.mockResolvedValue({ id: 'entityUuid', type: EntityType.COMPANY });
    await expect(
      customerService.requestCustomerDocumentVerificationV2('zellerSessionId', {} as any, 'entityUuid'),
    ).rejects.toThrowError('Customer not found');
  });

  test.each([
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.DRIVING_LICENCE,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        dob: uuidv4(),
        address: {
          street: uuidv4(),
          suburb: uuidv4(),
          state: uuidv4(),
          postcode: uuidv4(),
          country: uuidv4(),
        },
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      registeringIndividual: true,
      onboardingRiskCategory: false,
      onboardingProhibitedMCC: false,
      entityType: EntityType.COMPANY,
      response: {
        method: mockVerifyDocuments,
      },
    },
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.PASSPORT,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        dob: uuidv4(),
        address: {
          street: uuidv4(),
          suburb: uuidv4(),
          state: uuidv4(),
          postcode: uuidv4(),
          country: uuidv4(),
        },
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      onboardingRiskCategory: false,
      onboardingProhibitedMCC: false,
      entityType: EntityType.INDIVIDUAL,
      registeringIndividual: true,
      response: {
        method: mockVerifyDocuments,
      },
    },
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.DRIVING_LICENCE,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        dob: uuidv4(),
        address: {
          street: uuidv4(),
          suburb: uuidv4(),
          state: uuidv4(),
          postcode: uuidv4(),
          country: uuidv4(),
        },
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      onboardingRiskCategory: false,
      onboardingProhibitedMCC: false,
      registeringIndividual: true,
      entityType: EntityType.ASSOCIATION_UNINCORPORATED,
      response: {
        method: mockVerifyDocumentsWithSafeHarbour,
      },
    },
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.DRIVING_LICENCE,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        dob: uuidv4(),
        address: {
          street: uuidv4(),
          suburb: uuidv4(),
          state: uuidv4(),
          postcode: uuidv4(),
          country: uuidv4(),
        },
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      onboardingRiskCategory: false,
      onboardingProhibitedMCC: true,
      registeringIndividual: true,
      entityType: EntityType.ASSOCIATION_UNINCORPORATED,
      response: {
        method: mockVerifyDocumentsWithSafeHarbour,
      },
    },
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.DRIVING_LICENCE,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        dob: uuidv4(),
        address: {
          street: uuidv4(),
          suburb: uuidv4(),
          state: uuidv4(),
          postcode: uuidv4(),
          country: uuidv4(),
        },
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      onboardingRiskCategory: true,
      onboardingProhibitedMCC: true,
      registeringIndividual: true,
      entityType: EntityType.ASSOCIATION_UNINCORPORATED,
      response: {
        method: mockVerifyDocumentsWithSafeHarbour,
      },
    },
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.DRIVING_LICENCE,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        dob: uuidv4(),
        address: {
          street: uuidv4(),
          suburb: uuidv4(),
          state: uuidv4(),
          postcode: uuidv4(),
          country: uuidv4(),
        },
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      onboardingRiskCategory: false,
      onboardingProhibitedMCC: true,
      registeringIndividual: true,
      entityType: EntityType.ASSOCIATION,
      response: {
        method: mockVerifyDocumentsWithSafeHarbour,
      },
    },
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.MEDICARE_CARD,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        dob: uuidv4(),
        address: {
          street: uuidv4(),
          suburb: uuidv4(),
          state: uuidv4(),
          postcode: uuidv4(),
          country: uuidv4(),
        },
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      registeringIndividual: true,
      onboardingRiskCategory: false,
      onboardingProhibitedMCC: false,
      response: {
        method: mockVerifyDocumentsWithSafeHarbour,
      },
    },
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.MEDICARE_CARD,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      registeringIndividual: true,
      onboardingRiskCategory: false,
      onboardingProhibitedMCC: false,
      response: {
        method: mockVerifyDocumentsWithSafeHarbour,
      },
    },
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.MEDICARE_CARD,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        dob: uuidv4(),
        address: {
          street: uuidv4(),
          suburb: uuidv4(),
          state: uuidv4(),
          postcode: uuidv4(),
          country: uuidv4(),
        },
        v2: true,
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      onboardingRiskCategory: true,
      onboardingProhibitedMCC: false,
      registeringIndividual: true,
      response: {
        method: mockVerifyDocumentsWithSafeHarbour,
      },
    },
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.DRIVING_LICENCE,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        dob: uuidv4(),
        address: {
          street: uuidv4(),
          suburb: uuidv4(),
          state: uuidv4(),
          postcode: uuidv4(),
          country: uuidv4(),
        },
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      onboardingRiskCategory: true,
      onboardingProhibitedMCC: false,
      registeringIndividual: false,
      response: {
        method: mockVerifyDocuments,
      },
    },
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.DRIVING_LICENCE,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        dob: uuidv4(),
        address: {
          street: uuidv4(),
          suburb: uuidv4(),
          state: uuidv4(),
          postcode: uuidv4(),
          country: uuidv4(),
        },
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      onboardingRiskCategory: true,
      onboardingProhibitedMCC: false,
      registeringIndividual: true,
      response: {
        method: mockVerifyDocumentsWithSafeHarbour,
      },
    },
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.DRIVING_LICENCE,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        dob: uuidv4(),
        address: {
          street: uuidv4(),
          suburb: uuidv4(),
          state: uuidv4(),
          postcode: uuidv4(),
          country: uuidv4(),
        },
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      onboardingRiskCategory: true,
      onboardingProhibitedMCC: false,
      entityType: EntityType.ASSOCIATION_UNINCORPORATED,
      registeringIndividual: false,
      permissions: {
        allowXeroPaymentServices: false,
        allowZellerInvoices: false,
      },
      response: {
        method: mockVerifyDocuments,
      },
    },
    {
      updated: {
        documentType: IdvSafeHarbourDocumentType.NO_SECOND_ID,
        firstName: uuidv4(),
        middleName: uuidv4(),
        lastName: uuidv4(),
        dob: uuidv4(),
        address: {
          street: uuidv4(),
          suburb: uuidv4(),
          state: uuidv4(),
          postcode: uuidv4(),
          country: uuidv4(),
        },
        customerUuid: uuidv4(),
        entityUuid: uuidv4(),
      } as any,
      onboardingRiskCategory: false,
      onboardingProhibitedMCC: false,
      response: {
        cqrsCmds: 'Customer.SafeHarbourRequested',
        dtoClass: CustomerSafeHarbourRequestedEventDto,
      },
    },
  ])('should call appropriate documents verificationApi', async (data: any) => {
    mockGetCustomerDbItem.mockResolvedValue({
      id: data.updated.customerUuid,
      registeringIndividual: data.registeringIndividual,
      permissions: data.permissions,
    });
    when(mockEnvService.cqrsCmds).thenReturn(DomainURImap);
    mockGetEntityDbItem.mockResolvedValue({ id: 'entityUuid', type: data.entityType });
    mockOnboardingCategory.mockResolvedValue(data.onboardingRiskCategory);
    mockIsOnboardingProhibitedMCC.mockResolvedValue(data.onboardingProhibitedMCC);
    const DtoClass = data.response.dtoClass;
    const response = data.response;
    const cUuid = data.updated.customerUuid;
    const eUuid = data.updated.entityUuid;
    const expected = {
      firstname: data.updated.firstName,
      middlename: data.updated.middleName,
      lastname: data.updated.lastName,
      dob: data.updated.dob,
      address: data.updated.address,
      id: cUuid,
      entityUuid: eUuid,
    } as any;
    const spy = jest.spyOn(customerService, 'updateCustomer');
    const res = await customerService.requestCustomerDocumentVerificationV2('zellerSessionId', data.updated, eUuid);
    if (data.updated.documentType === IdvSafeHarbourDocumentType.NO_SECOND_ID) {
      expect(graphqlMutate).toHaveBeenCalled();
    }
    expect(spy).toHaveBeenCalledWith(expected, expected.entityUuid);
    expect(res).toEqual(true);
    if (response.method) {
      expect(response.method).toHaveBeenCalledWith('zellerSessionId', data.updated);
    }
    if (response.cqrsCmds) {
      expect(mockLambdaService.invokeCommand).toHaveBeenCalledWith(response.cqrsCmds, new DtoClass(data.updated));
    }
    spy.mockReset();
  });

  it('should handle prepareDto with no productTourStatus fields', async () => {
    const entityUuid = uuidv4();
    const email = uuidv4();
    const customerDto: CustomerUpdateInput = { id: uuidv4(), entityUuid, email };
    const dto = customerService.prepareUpdateDto(customerDto, entityUuid);
    expect(dto.customerUuid).toBe(customerDto.id);
    expect(dto.entityUuid).toBe(entityUuid);
    expect(dto.email).toBe(customerDto.email);
    expect(dto.productTourStatus).toBeUndefined();
  });

  it('should handle prepareDto with productTourStatus fields set to true', async () => {
    const entityUuid = uuidv4();
    const email = uuidv4();
    const customerDto: CustomerUpdateInput = {
      id: uuidv4(),
      entityUuid,
      email,
      showAdminMerchantPortalWelcome: true,
      showInvoiceInstructions: true,
      showInvoicesWelcome: true,
      showItemsWelcome: true,
      showItemInstructions: true,
      showInvoicesCustomisationWelcome: true,
      showInvoicesScheduleSendWelcome: true,
      showInvoicesSendBySmsWelcome: true,
      showInvoiceSendViaInfo: true,
      showInvoicingCustomisationSettingsWelcome: true,
      showNotificationsWelcome: true,
      showTapToPayInstructions: true,
      showTapToPayMayJune: true,
      showCustomScreensaverPromo: true,
      showSavingsAccountWelcome: true,
      showSavingsAccountMarch: true,
      showSavingsAccountMay: true,
      showCorporateCardsMayOffer: true,
      showCorporateCardsWalkthrough: true,
      showCorporateCardsSettingsWalkthrough: true,
      showCorporateCardsAdminWelcome: true,
      showCatalogueItemsWelcome: false,
      showServiceChargesWelcome: true,
    };
    const dto = customerService.prepareUpdateDto(customerDto, entityUuid);
    expect(dto.customerUuid).toBe(customerDto.id);
    expect(dto.entityUuid).toBe(entityUuid);
    expect(dto.email).toBe(customerDto.email);
    expect(dto.productTourStatus).not.toBeUndefined();
    // expect(dto.productTourStatus).toStrictEqual({
    //     showAdminMerchantPortalWelcome: true,
    //     showInvoiceInstructions: true,
    //     showInvoicesWelcome: true,
    //     showItemsWelcome: true,
    //     showItemInstructions: true,
    //     showInvoicesCustomisationWelcome: true,
    //     showInvoicesScheduleSendWelcome: true,
    //     showInvoicesSendBySmsWelcome: true,
    //     showInvoiceSendViaInfo: true,
    //     showInvoicingCustomisationSettingsWelcome: true,
    //     showNotificationsWelcome: true,
    //     showTapToPayInstructions: true,
    //     showCustomScreensaverPromo: true,
    //     showSavingsAccountWelcome: true,
    //     showSavingsAccountMarch: true,
    //     showSavingsAccountMay: true,
    //     showCorporateCardsMayOffer: true,
    //     showCorporateCardsWalkthrough: true,
    //     showCorporateCardsSettingsWalkthrough: true,
    //     showCorporateCardsAdminWelcome: true,
    // });
  });

  describe('sendIdvSessionInformation', () => {
    it('should be able to send idv session information', async () => {
      const postSpy = jest.spyOn(axios, 'post').mockResolvedValue({ data: { success: true }, status: 200 });

      const customerUuid = uuidv4();
      const sessionUuid = uuidv4();
      const customer = {
        id: customerUuid,
        email: 'email',
        phone: '000',
        phoneVerified: true,
        address: { street: 'street', suburb: 'suburb', postcode: '3000', state: 'VIC', country: 'AU' },
      };

      const requestDto: CustomerDocumentVerificationV2RequestDto = {
        customerUuid,
        firstName: 'John',
        lastName: 'Doe',
        middleName: 'middle',
        address: { street: 'street', suburb: 'suburb', postcode: '3000', state: 'VIC' },
      };
      await customerService.sendIdvSessionInformation(sessionUuid, customer, requestDto);
      expect(postSpy).toHaveBeenCalled();
    });
  });

  describe('generateCustomerPayloadForSardine', () => {
    it('should be able to return a payload', async () => {
      const customerUuid = uuidv4();
      const customer = {
        id: customerUuid,
        email: 'email',
        phone: '000',
        phoneVerified: true,
        address: { street: 'street', suburb: 'suburb', postcode: '3000', state: 'VIC', country: 'AU' },
      };

      const requestDto: CustomerDocumentVerificationV2RequestDto = {
        customerUuid,
        firstName: 'John',
        lastName: 'Doe',
        middleName: 'middle',
        address: { street: 'street', suburb: 'suburb', postcode: '3000', state: 'VIC' },
      };
      const response = await customerService.generateCustomerPayloadForSardine(requestDto, customer);
      expect(response).toEqual({
        id: customerUuid,
        firstName: 'John',
        lastName: 'Doe',
        middleName: 'middle',
        address: { street1: 'street', city: 'suburb', postalCode: '3000', state: 'VIC', country: 'AU' },
      });
    });
  });
});
