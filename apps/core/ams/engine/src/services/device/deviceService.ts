import { LambdaService } from '@npco/component-bff-core/dist/lambda';
import { debug, error, info, warn } from '@npco/component-bff-core/dist/utils/logger';
import type { PosConfigLocation, PosConfigVenue } from '@npco/component-dto-connection';
import type { PaymentSettings, StandInRule } from '@npco/component-dto-core';
import { Source } from '@npco/component-dto-core';
import type { EntityDeviceSettings, PosSettings } from '@npco/component-dto-device';
import {
  DeviceCreatedEventDto,
  DeviceStatus,
  DeviceUpdatedEventDto,
  PosMethod,
  PosMode,
} from '@npco/component-dto-device';
import type { PaymentLimit } from '@npco/component-dto-entity';
import {
  DeviceType,
  PosInterfaceDevicePairedEventDto,
  PosInterfaceDeviceUnpairedEventDto,
} from '@npco/component-dto-pos-interface';

import { Injectable } from '@nestjs/common';
import type { EntityManager, Repository } from 'typeorm';
import { In, Not, getManager, getRepository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import { EnvironmentService } from '../../config';
import type { GlobalConfig } from '../../entities';
import { ConnectionPosInterface, Device, Entity, PosInterfacePairing, Site, Status } from '../../entities';
import { BaseService } from '../base/baseService';
import { BadRequestError } from '../base/error';
import { getDevicePaymentSettings } from '../entity/utils/getPaymentSettings';
import { EventsEmitterService } from '../events/eventsEmitterService';
import { GlobalConfigService } from '../globalConfig/globalConfigService';
import { GlobalDevicesDb } from '../globalDevices/globalDevicesDb';
import { CommonPosInterfacePairingService } from '../posInterface/common/commonPosInterfacePairingService';
import { PosInterfacePairingStatus } from '../posInterface/types';
import {
  DIRECT_PROVIDERS,
  connectionTypeToSourceMap,
  isPayAtTableProvider,
  sourceToConnectionTypeMap,
} from '../posInterface/utils';
import { ALLOWED_DEVICE_SITE_TYPES } from '../site/siteService';
import { convertCategoryToMcc } from '../util';

import { buildDeviceUpdateEvent } from './deviceUtils';
import type { DeviceNameUpdateInput, EntityDeviceSettingsDto } from './types';

type DeviceEntityModel = { entity: EntityDeviceSettings } & Device;

@Injectable()
export class DeviceService extends BaseService<Device> {
  private readonly configService: GlobalConfigService;

  constructor(
    protected readonly envService: EnvironmentService,
    protected readonly lambdaService: LambdaService,
    private readonly eventEmitterService: EventsEmitterService,
    private readonly commonPosInterfacePairingService: CommonPosInterfacePairingService,
  ) {
    super('Devices', 'deviceUuid', lambdaService, envService);
    this.configService = new GlobalConfigService();
  }

  public getRepository = () => getRepository(Device);

  readonly createEntity = (event: DeviceCreatedEventDto | DeviceUpdatedEventDto, isMobileDevice = false) => ({
    deviceUuid: event.deviceUuid,
    entityUuid: event.entityUuid,
    name: event.name,
    siteUuid: event.siteUuid,
    entity: event.entity,
    status: DeviceStatus[event.status as keyof typeof DeviceStatus],
    posSettings: event.posSettings,
    geofencing: event.geofencing,
    model: event.model,
    serial: event.serial,
    screen: event.screen,
    ...(isMobileDevice ? {} : { network: event.network }),
    terminalConfig: event.terminalConfig,
    emvTables: event.emvTables,
    emvCaKeys: event.emvCaKeys,
    emvConfig: event.emvConfig,
    standInRules: event.standInRules,
    paymentSettings: event.paymentSettings,
    domicile: this.getDomicile(),
  });

  createDevice = async (event: DeviceCreatedEventDto, enforceUniqueName = false) => {
    const existingDevice: Device | undefined = await this.getRepository().findOne(event.deviceUuid, {
      where: {
        domicile: this.getDomicile(),
      },
    });
    await this.validateDeviceName(event, existingDevice, enforceUniqueName);
    let settings: DeviceEntityModel;
    if (existingDevice) {
      info(`Device exists for ${existingDevice.deviceUuid} deleting old device record`, event.deviceUuid);
      await this.getRepository().delete({
        deviceUuid: existingDevice.deviceUuid,
        domicile: this.getDomicile(),
      });
      if (existingDevice.entityUuid === event.entityUuid) {
        info(`recreating device for entity ${event.entityUuid}`, event.deviceUuid);
        settings = await this.mergeSettings(event, existingDevice);
      } else {
        info(`creating device for entity ${event.entityUuid}`, event.deviceUuid);
        settings = await this.getInitialSettings(event);
      }
    } else {
      settings = await this.getInitialSettings(event);
      settings.domicile = this.getDomicile();
    }
    const device = this.getRepository().create(settings);
    info(`creating new device ${JSON.stringify(device, null, 2)}`, settings.deviceUuid);
    const deviceEvent = existingDevice
      ? new DeviceUpdatedEventDto({ ...settings, entityUuid: event.entityUuid, siteUuid: device.siteUuid })
      : new DeviceCreatedEventDto({ ...settings, entityUuid: event.entityUuid, siteUuid: device.siteUuid });
    await this.create<DeviceUpdatedEventDto, typeof deviceEvent>(settings.deviceUuid, device, {
      successEvent: deviceEvent,
      successCmd: existingDevice ? this.envService.cqrsCmds.Device.Updated : this.envService.cqrsCmds.Device.Created,
    });
  };

  updateDevicesSettings = async (settings: EntityDeviceSettingsDto) => {
    const { paymentLimits, entityUuid } = settings;
    const devices = await this.getRepository().find({
      where: { entityUuid, status: Not(Status.DELETED), domicile: this.getDomicile() },
    });
    info(`updating ${devices.length} existing devices for entity ${JSON.stringify(settings)}`, entityUuid);
    if (devices && devices.length > 0) {
      const paymentSettings = getDevicePaymentSettings(paymentLimits);
      await this.bulkUpdateDevices(entityUuid, settings, paymentSettings, devices);
    }
  };

  updateDevice = async (event: DeviceUpdatedEventDto, enforceUniqueName = false) => {
    const existing = await this.getRepository().findOne(event.deviceUuid, {
      where: {
        domicile: this.getDomicile(),
      },
    });

    await this.validateDeviceName(event, existing, enforceUniqueName);
    await this.validatePosSettingRestriction(event, existing);
    const updateData = this.getRepository().create(this.createEntity(event));
    await this.update<DeviceUpdatedEventDto>(
      event.deviceUuid,
      updateData,
      {
        successCmd: this.envService.cqrsCmds.Device.Updated,
        successEvent: event,
      },
      {
        useDomicile: true,
      },
    );
    return event;
  };

  updateDeviceName = async (event: DeviceNameUpdateInput & { deviceUuid: string }) => {
    await this.validateDeviceName(event, undefined, event.enforceUniqueness);
    const updateData = this.getRepository().create(this.createEntity(event));
    await this.update<DeviceUpdatedEventDto>(
      event.deviceUuid,
      updateData,
      {
        successCmd: this.envService.cqrsCmds.Device.Updated,
        successEvent: event,
      },
      {
        useDomicile: true,
      },
    );
    return event;
  };

  updateDevicesBySiteUuid = async (siteUuid: string, event: Partial<DeviceUpdatedEventDto>) => {
    const devices = await this.getRepository().find({
      where: { siteUuid, status: Not(Status.DELETED), domicile: this.getDomicile() },
    });
    info(
      `updating ${devices.length} existing devices for siteUuid ${siteUuid}
      ${JSON.stringify(event)}`,
      siteUuid,
    );
    for (const device of devices) {
      await this.updateDevice({
        screen: event.screen,
        posSettings: event.posSettings,
        entityUuid: device.entityUuid as string,
        deviceUuid: device.deviceUuid,
      });
    }
  };

  deleteDevice = async (id: string) => {
    const device = await this.getDevice(id);
    if (device.status === DeviceStatus.DELETED) {
      warn(`Device ${id} is already deleted`, id);
      return;
    }
    const updateEntity: Device = {
      deviceUuid: id,
      siteUuid: null,
      status: DeviceStatus.DELETED,
      domicile: this.getDomicile(),
    };
    const successEvent: DeviceUpdatedEventDto = {
      deviceUuid: id,
      entityUuid: device.entityUuid,
      siteUuid: null,
      receipt: null,
      moto: null,
      name: '',
      schemes: [],
      schemesMoto: [],
      surchargesTaxes: null,
      tipping: null,
      site: null,
      customers: [],
      status: DeviceStatus.DELETED,
    };

    const resetPosSettings = await this.checkActiveAndGetResetPosSettings(device);
    if (resetPosSettings) {
      updateEntity.posSettings = resetPosSettings;
      successEvent.posSettings = resetPosSettings;
    }

    await this.update<DeviceUpdatedEventDto>(
      id,
      updateEntity,
      {
        successCmd: this.envService.cqrsCmds.Device.Updated,
        successEvent,
      },
      {
        useDomicile: true,
      },
    );
    const skipToAvoidUpdatingDeletedDevice = true;
    await this.commonPosInterfacePairingService.unpairPosInterface(device, skipToAvoidUpdatingDeletedDevice);
  };

  pairPayAtTableInterface = async (device: Device, entityUuid: string, newSitePairing: PosInterfacePairing) => {
    const provider = newSitePairing.provider;
    const active = connectionTypeToSourceMap.get(provider);
    if (!active) {
      const msg = `Provider ${provider} is not supported for pairing`;
      error(msg, device.deviceUuid);
      throw new BadRequestError(msg);
    }

    const posInterfaceConfig: ConnectionPosInterface | undefined = await getRepository(ConnectionPosInterface).findOne({
      entityUuid,
      type: provider,
      domicile: this.getDomicile(),
    });

    const venue: PosConfigVenue | undefined = posInterfaceConfig?.venues?.find(
      (venueRecord) => venueRecord.id === newSitePairing.venueId,
    );
    let posVenueLocations: PosConfigLocation[] = [];

    if (!newSitePairing.locations) {
      // pair venue root level, need to add location list
      posVenueLocations =
        venue?.locations?.map((l) => ({
          id: l.id,
          name: l.name,
          number: l.number,
        })) ?? [];
    } else {
      posVenueLocations = newSitePairing.locations.map((location) => ({
        id: location.locationId,
        name: location.locationName,
        number: venue?.locations?.find((l) => l.id === location.locationId)?.number ?? '',
      }));
    }

    const posSettingsToClear: Partial<PosSettings> = {
      oracleSettings: null,
    };

    const updatedPosSetting = this.getUpdatePosSetting(device, {
      active,
      mode: PosMode.PAY_AT_TABLE,
      connectionMethod: PosMethod.POS_CONNECTOR,
      posVenue: {
        id: newSitePairing.venueId ?? '',
        name: venue?.name ?? '',
        locations: posVenueLocations ?? [],
      },
      ...posSettingsToClear,
    });
    await this.update<DeviceUpdatedEventDto>(
      device.deviceUuid,
      {
        deviceUuid: device.deviceUuid,
        entityUuid: device.entityUuid,
        posSettings: updatedPosSetting,
      },
      {
        successCmd: this.envService.cqrsCmds.Device.Updated,
        successEvent: new DeviceUpdatedEventDto({
          deviceUuid: device.deviceUuid,
          entityUuid,
          posSettings: updatedPosSetting,
        }),
      },
      {
        useDomicile: true,
      },
    );

    await this.callCommandHandler<PosInterfaceDevicePairedEventDto>(
      newSitePairing.pairingUuid,
      this.envService.cqrsCmds.PosInterface.DevicePaired,
      new PosInterfaceDevicePairedEventDto({
        pairingUuid: newSitePairing.pairingUuid,
        siteUuid: newSitePairing.siteUuid,
        venueId: newSitePairing.venueId,
        locations: newSitePairing.locations ?? undefined,
        venueName: venue?.name ?? '',
        posProvider: newSitePairing.provider,
        deviceUuid: device.deviceUuid,
        entityUuid: device.entityUuid ?? '',
        type: DeviceType.TERMINAL,
        timestamp: Date.now().toString(),
      }),
    );
  };

  unpairPairingAndEmitDeviceUnpaired = async (pairing: PosInterfacePairing, unsetPairedDeviceId = false) => {
    const posInterfacePairingRep = getRepository(PosInterfacePairing);

    await posInterfacePairingRep.update(
      {
        pairingUuid: pairing.pairingUuid,
        domicile: this.getDomicile(),
      },
      {
        status: PosInterfacePairingStatus.UNPAIRED,
        updatedTime: `${new Date().getTime()}`,
        ...(unsetPairedDeviceId && { pairedDeviceId: null }),
      },
    );

    await this.callCommandHandler<PosInterfaceDeviceUnpairedEventDto>(
      pairing.pairingUuid,
      this.envService.cqrsCmds.PosInterface.DeviceUnpaired,
      new PosInterfaceDeviceUnpairedEventDto({
        pairingUuid: pairing.pairingUuid,
        deviceUuid: pairing.deviceUuid as string,
        posProvider: pairing.provider,
        type: pairing.deviceType ?? DeviceType.TERMINAL,
        timestamp: Date.now().toString(),
      }),
    );
  };

  getUpdatedPosSettingsAndUpdateDevice = async (device: Device, entityUuid: string) => {
    const updatedPosSetting = this.getUpdatePosSetting(device, { active: Source.STANDALONE });
    await this.update<DeviceUpdatedEventDto>(
      device.deviceUuid,
      {
        deviceUuid: device.deviceUuid,
        entityUuid,
        posSettings: updatedPosSetting,
        domicile: this.getDomicile(),
      },
      {
        successCmd: this.envService.cqrsCmds.Device.Updated,
        successEvent: new DeviceUpdatedEventDto({
          deviceUuid: device.deviceUuid,
          entityUuid,
          posSettings: updatedPosSetting,
        }),
      },
      {
        useDomicile: true,
      },
    );
  };

  getDevice = async (id: string) => {
    const device = await this.findOneWithOption({
      where: {
        deviceUuid: id,
        domicile: this.getDomicile(),
      },
    });
    if (!device) {
      const msg = `Cant find requested device id ${id}`;
      warn(msg, id);
      throw new BadRequestError(msg);
    }
    return device;
  };

  assignDeviceToSite = async (deviceUuid: string, siteUuid: string) => {
    const { site, device, entityUuid } = await this.verifyDeviceSite(deviceUuid, siteUuid);
    if (device.siteUuid === siteUuid) {
      warn(`Device ${deviceUuid} is already assigned to site ${siteUuid}.`, deviceUuid);
    }
    await this.update<DeviceUpdatedEventDto>(
      deviceUuid,
      { siteUuid, deviceUuid },
      {
        successCmd: this.envService.cqrsCmds.Device.Updated,
        successEvent: buildDeviceUpdateEvent(deviceUuid, { ...site, entityUuid }),
      },
      {
        useDomicile: true,
      },
    );
    await this.runPayAtTablePairingLogic(device, site.entityUuid, site.siteUuid);
  };

  unassignDeviceFromSite = async (deviceUuid: string, siteUuid: string) => {
    const { device, entityUuid } = await this.verifyDeviceSite(deviceUuid, siteUuid);
    if (device.siteUuid !== siteUuid) {
      throw new BadRequestError(`Device ${deviceUuid} is not assigned to the site ${siteUuid}.`);
    }
    await this.update<DeviceUpdatedEventDto>(
      deviceUuid,
      { siteUuid: null, deviceUuid },
      {
        successCmd: this.envService.cqrsCmds.Device.Updated,
        successEvent: new DeviceUpdatedEventDto({
          deviceUuid,
          entityUuid,
          siteUuid: null,
          receipt: null,
          moto: null,
          schemes: [],
          schemesMoto: [],
          surchargesTaxes: null,
          tipping: null,
          site: null,
          customers: [],
          features: null,
        }),
      },
      {
        useDomicile: true,
      },
    );
    await this.commonPosInterfacePairingService.unpairPosInterface(device);
  };

  updateDBEntity = (mgr: EntityManager, aggregateField: any, object: any) => mgr.update(Device, aggregateField, object);

  isLockUpdateRequired = (dto: any) => {
    const jsonFields = ['network', 'screen'];
    return Object.keys(dto).some((key) => jsonFields.includes(key));
  };

  generateDeviceUuid = async (model: string, serial: string, entityUuid?: string) => {
    const globalDevicesDb = new GlobalDevicesDb();
    const result = await globalDevicesDb.checkIfDeviceUuidExists(model, serial, entityUuid);
    if (result) {
      info(`deviceUuid already exists for ${model} ${serial} and entityUuid ${entityUuid}`, result);
      return result;
    }
    const deviceUuid = uuidv4();
    await globalDevicesDb.createGlobalDeviceRecord(deviceUuid, model, serial, entityUuid);
    return deviceUuid;
  };

  protected getRepositoryFromManager(manager: EntityManager): Repository<Device> {
    return manager.getRepository(Device);
  }

  private readonly mergeTerminalConfig = (terminalConfig: string, mcc?: string, caid?: string) => {
    if (mcc || caid) {
      const config = JSON.parse(terminalConfig);
      return JSON.stringify({
        ...config,
        ...(caid ? { caid } : {}),
        mcc: mcc?.length ? mcc : config.mcc,
      });
    }
    return terminalConfig;
  };

  private readonly bulkUpdateDevices = async (
    entityUuid: string,
    settings: {
      entitySettings?: EntityDeviceSettings;
      caid?: string;
      mcc?: string;
      standInRules?: StandInRule[];
      paymentLimits?: PaymentLimit[];
      geofencing?: string;
    },
    paymentSettings: PaymentSettings,
    devices: Device[],
  ) => {
    const { caid, mcc, standInRules, entitySettings, geofencing } = settings;
    const entityUpdates = devices.map((device) => {
      const terminalConfig = this.mergeTerminalConfig(device.terminalConfig as string, mcc, caid);
      const updateEvent = new DeviceUpdatedEventDto({
        deviceUuid: device.deviceUuid,
        entity: entitySettings,
        entityUuid,
        terminalConfig,
        standInRules,
        ...(geofencing ? { geofencing } : {}),
        ...(Object.keys(paymentSettings).length ? { paymentSettings } : {}),
      });
      this.eventEmitterService.addEventToEmit<DeviceUpdatedEventDto>(
        device.deviceUuid,
        this.envService.cqrsCmds.Device.Updated,
        updateEvent,
      );
      return this.getRepository().create(this.createEntity(updateEvent));
    });
    console.log('entityUpdates', entityUpdates);
    await this.getRepository().save(entityUpdates);
    await this.eventEmitterService.emitEvents();
  };

  private readonly validateDeviceName = async (
    event: { deviceUuid: string; entityUuid?: string; name?: string },
    existing: Device | undefined,
    enforceUniqueName = false,
  ): Promise<void> => {
    if (!this.envService.enableDeviceUniquenessCheck && !enforceUniqueName) {
      return;
    }
    // Support updateDevicesSettings which does full device updates
    // We dont want to enforce validation on existing name values
    if (!event.name || existing?.name === event.name) {
      debug(
        `validateDeviceName; device name ${event.name ? "match's original name" : 'not being updated'}`,
        event.deviceUuid,
      );
      return;
    }

    if (!event.entityUuid) {
      debug('validateDeviceName: device is not assigned to an entity');
      return;
    }

    const deviceName = await this.incrementDeviceNameOrThrowIfNotEnforcing(event, enforceUniqueName);

    if (event.name !== deviceName) {
      info(`validateDeviceName; Incrementing device name to ${deviceName}`, event.deviceUuid);
    }
    // eslint-disable-next-line no-param-reassign
    event.name = deviceName;
  };

  private readonly incrementDeviceNameOrThrowIfNotEnforcing = async (
    event: { deviceUuid: string; entityUuid?: string; name?: string },
    enforceUniqueName: boolean,
  ) => {
    let incrementNumber;
    let deviceName;
    let duplicateDevice;
    do {
      const incrementString = incrementNumber ? `(${incrementNumber})` : '';
      deviceName = `${event.name} ${incrementString}`.trim();
      duplicateDevice = await this.findOneWithOption({
        where: {
          entityUuid: event.entityUuid,
          name: deviceName,
          status: Not(Status.DELETED),
          domicile: this.getDomicile(),
        },
      });
      if (duplicateDevice && duplicateDevice?.deviceUuid !== event.deviceUuid && !enforceUniqueName) {
        warn(
          `validateDeviceName; Device ${event.deviceUuid} update has duplicate name (${deviceName}) to ${duplicateDevice?.deviceUuid}`,
          event.deviceUuid,
        );
        throw new BadRequestError('Device name is not unique');
      }
      incrementNumber = incrementNumber ? incrementNumber + 1 : 1;
    } while (duplicateDevice);

    return deviceName;
  };

  private readonly validatePosSettingRestriction = async (event: DeviceUpdatedEventDto, existingDevice?: Device) => {
    const existing =
      existingDevice ??
      (await this.findOneWithOption({
        where: {
          deviceUuid: event.deviceUuid,
          domicile: this.getDomicile(),
        },
      }));
    const isExistingPosSourcePayAtTable =
      existing?.posSettings?.active && isPayAtTableProvider(existing?.posSettings?.active);
    const isMakingChangeOnPosSource =
      !!event.posSettings?.active && event.posSettings?.active !== existing?.posSettings?.active;
    if (isExistingPosSourcePayAtTable && isMakingChangeOnPosSource) {
      const errorMessage = `Cannot change a device with ${existing?.posSettings?.active} source`;
      error(errorMessage);
      throw new BadRequestError(errorMessage);
    }
  };

  private readonly runPayAtTablePairingLogic = async (device: Device, entityUuid: string, newSiteUuid: string) => {
    const originalSiteUuid = device.siteUuid;

    if (originalSiteUuid === newSiteUuid) {
      return undefined;
    }

    const posInterfacePairingRep = getRepository(PosInterfacePairing);
    // if `newSitePairing` exists, it means that the site the device is going to assign to is a paired site
    const newSitePairing = await posInterfacePairingRep.findOne({
      siteUuid: newSiteUuid,
      status: PosInterfacePairingStatus.PAIRED,
      domicile: this.getDomicile(),
    });

    if (!originalSiteUuid && newSitePairing) {
      return this.pairPayAtTableInterface(device, entityUuid, newSitePairing);
    }

    if (!originalSiteUuid) {
      return undefined;
    }

    /**
     * One to one pairing
     * Extend to dynamic provider in future
     */
    const directProviderConnectionTypes = DIRECT_PROVIDERS.map((provider) =>
      sourceToConnectionTypeMap.get(provider),
    ).filter(Boolean);
    const originalDevicePairing = await posInterfacePairingRep.find({
      where: {
        deviceUuid: device.deviceUuid,
        entityUuid,
        status: PosInterfacePairingStatus.PAIRED,
        provider: In(directProviderConnectionTypes),
        domicile: this.getDomicile(),
      },
    });

    if (originalDevicePairing && newSitePairing) {
      const skipToAvoidDoubleDeviceSettingsUpdate = true;
      await this.commonPosInterfacePairingService.unpairPosInterface(device, skipToAvoidDoubleDeviceSettingsUpdate);
      return this.pairPayAtTableInterface(device, entityUuid, newSitePairing);
    }

    const originalSitePairing = await posInterfacePairingRep.findOne({
      siteUuid: originalSiteUuid,
      status: PosInterfacePairingStatus.PAIRED,
      domicile: this.getDomicile(),
    });

    if (originalSitePairing && newSitePairing) {
      const skipToAvoidDoubleDeviceSettingsUpdate = true;
      await this.commonPosInterfacePairingService.unpairPosInterface(device, skipToAvoidDoubleDeviceSettingsUpdate);
      return this.pairPayAtTableInterface(device, entityUuid, newSitePairing);
    }

    if (!originalSitePairing && newSitePairing) {
      return this.pairPayAtTableInterface(device, entityUuid, newSitePairing);
    }

    if (originalSitePairing && !newSitePairing) {
      return this.commonPosInterfacePairingService.unpairPosInterface(device);
    }

    return undefined;
  };

  private readonly getUpdatePosSetting = (device: Device, updatedPosSettings: Partial<PosSettings>) => {
    const existingPosSettings = device.posSettings as PosSettings;
    return { ...existingPosSettings, ...updatedPosSettings };
  };

  private readonly verifyDeviceSite = async (deviceUuid: string, siteUuid: string) => {
    const site = await getRepository(Site).findOne(siteUuid, {
      where: { domicile: this.getDomicile() },
    });
    if (!site || site.status === Status.DELETED) {
      warn(`Cant find site ${siteUuid}.`);
      throw new BadRequestError(`Cant find site ${siteUuid}.`);
    }
    if (site.type && !ALLOWED_DEVICE_SITE_TYPES.includes(site.type)) {
      warn(`Action not allowed for Site ${siteUuid} with type ${site.type}.`);
      throw new BadRequestError(`Action not allowed for Site ${siteUuid} with type ${site.type}.`);
    }
    const device = await this.findOneWithOption({
      where: {
        deviceUuid,
        domicile: this.getDomicile(),
      },
    });
    if (!device || device.status === DeviceStatus.DELETED) {
      warn(`Cant find device ${deviceUuid}.`);
      throw new BadRequestError(`Cant find device ${deviceUuid}.`);
    }
    if (site.entityUuid !== device.entityUuid) {
      error('Cant assign a device to a site from different entity');
      throw new BadRequestError('Cant assign a device to a site from different entity');
    }
    return { site, device, entityUuid: device.entityUuid };
  };

  private readonly getStandInRules = async (
    event: DeviceCreatedEventDto,
    defaultEntityValues: GlobalConfig[],
    entity?: Entity,
  ): Promise<StandInRule[]> => {
    if (event.standInRules) {
      return event.standInRules;
    }

    if (entity?.standInRules) {
      return entity.standInRules;
    }

    return this.configService.getValue('Entity', 'standInRules', defaultEntityValues) as StandInRule[];
  };

  private readonly getEntityDefaultValues = async (event: DeviceCreatedEventDto, entity?: Entity) => {
    const defaultEntityValues = await this.configService.findAll('Entity');
    const standInRules = await this.getStandInRules(event, defaultEntityValues, entity);
    const accountStatus = this.configService.getValue('Entity', 'accountStatus', defaultEntityValues);
    const geofencing = this.configService.getValue('Entity', 'geofencing', defaultEntityValues);
    const { domicile, currency } = this.configService.getValue('Entity', 'international', defaultEntityValues);
    const entitySettings: EntityDeviceSettings = {
      canAcquire: entity?.accountStatus?.canAcquire ?? accountStatus.canAcquire,
      canAcquireMoto: entity?.accountStatus?.canAcquireMoto ?? accountStatus.canAcquireMoto,
      canRefund: entity?.accountStatus?.canRefund ?? accountStatus.canRefund,
      canStandIn: entity?.accountStatus?.canStandIn ?? accountStatus.canStandIn,
      canAcquireMobile: entity?.accountStatus?.canAcquireMobile ?? accountStatus.canAcquireMobile,
      domicile: entity?.domicile ?? domicile,
      currency: entity?.currency ?? currency,
    };
    const paymentLimits: PaymentLimit[] = entity?.paymentLimits?.length
      ? entity?.paymentLimits
      : this.configService.getValue('Entity', 'paymentLimits', defaultEntityValues);
    const paymentSettings: PaymentSettings = getDevicePaymentSettings(paymentLimits);
    return {
      paymentSettings,
      standInRules,
      entity: entitySettings,
      geofencing: entity?.geofencing ?? geofencing,
    };
  };

  private readonly getDeviceDefaultValues = async () => {
    const defaultDeviceValues = await this.configService.findAll('Device');
    return {
      posSettings: this.configService.getValue('Device', 'posSettings', defaultDeviceValues),
      emvConfig: this.configService.getValue('Device', 'emvConfig', defaultDeviceValues),
      emvTables: this.configService.getValue('Device', 'emvTables', defaultDeviceValues),
      emvCaKeys: this.configService.getValue('Device', 'emvCaKeys', defaultDeviceValues),
      terminalConfig: this.configService.getValue('Device', 'terminalConfig', defaultDeviceValues),
      screen: this.configService.getValue('Device', 'screen', defaultDeviceValues),
    };
  };

  private readonly generateTerminalCatid = async () => {
    const catidSequence = await getManager().query(`SELECT nextval('${this.envService.stage}."device_catid_seq"')`);
    return catidSequence[0].nextval.padStart(8, '0');
  };

  private readonly buildTerminalConfig = (defaultDeviceTerminalConfig: string, catid?: string, entity?: Entity) => {
    const mcc = convertCategoryToMcc(entity?.categoryGroup, entity?.category);
    const defaults = JSON.parse(defaultDeviceTerminalConfig);
    if (!catid) {
      delete defaults.catid;
    }
    return JSON.stringify({
      ...defaults,
      caid: entity?.caid ?? '',
      ...(catid ? { catid } : {}),
      mcc,
    });
  };

  private readonly getInitialSettings = async (event: DeviceCreatedEventDto): Promise<DeviceEntityModel> => {
    const domicile = this.getDomicile();
    const entity = await getRepository(Entity).findOne({ entityUuid: event.entityUuid, domicile });
    const defaultEntityValues = await this.getEntityDefaultValues(event, entity);
    const defaultDeviceValues = await this.getDeviceDefaultValues();
    const baseSettings = {
      posSettings: defaultDeviceValues.posSettings,
      emvConfig: defaultDeviceValues.emvConfig,
      emvTables: defaultDeviceValues.emvTables,
      emvCaKeys: defaultDeviceValues.emvCaKeys,
      ...defaultEntityValues,
      createdTime: `${new Date().getTime()}`,
    };

    if (event.model && ['ANDROID', 'IOS'].includes(event.model)) {
      // catid not set for mobile
      const terminalConfig = this.buildTerminalConfig(defaultDeviceValues.terminalConfig, undefined, entity);
      const device = this.createEntity(event, true);
      return {
        ...device,
        ...baseSettings,
        terminalConfig,
      };
    }

    if (event.model && event.model === 'POS_INTERFACE') {
      const device = this.createEntity(event, true);
      return {
        ...device,
        ...defaultEntityValues,
        domicile,
        createdTime: `${new Date().getTime()}`,
      };
    }
    const catid = await this.generateTerminalCatid();
    const terminalConfig = this.buildTerminalConfig(defaultDeviceValues.terminalConfig, catid, entity);
    const device = this.createEntity(event);
    return {
      ...device,
      domicile,
      screen: {
        ...defaultDeviceValues.screen,
        ...event.screen,
      },
      ...baseSettings,
      terminalConfig,
    };
  };

  private readonly mergeSettings = async (event: DeviceCreatedEventDto, device: Device): Promise<DeviceEntityModel> => {
    const defaultEntityValues = await this.getEntityDefaultValues(event);
    return {
      ...device,
      entity: defaultEntityValues.entity,
      entityUuid: event.entityUuid,
      status: DeviceStatus[event.status as keyof typeof DeviceStatus],
      createdTime: `${new Date().getTime()}`,
    };
  };

  private readonly checkActiveAndGetResetPosSettings = async (device: Device): Promise<PosSettings | undefined> => {
    let posSettings: PosSettings | undefined;

    if (device.posSettings?.active && isPayAtTableProvider(device.posSettings?.active)) {
      const posInterfacePairingRep = getRepository(PosInterfacePairing);
      let pairing;
      if (device.siteUuid) {
        pairing = await posInterfacePairingRep.findOne({
          siteUuid: device.siteUuid,
          status: PosInterfacePairingStatus.PAIRED,
          domicile: this.getDomicile(),
        });
      }

      if (pairing) {
        posSettings = { ...device.posSettings, active: Source.STANDALONE };
      }
    }

    if (device.posSettings?.active === Source.ORACLE_POS) {
      posSettings = { ...device.posSettings, active: Source.STANDALONE, oracleSettings: null };
    }

    if (device.posSettings?.active === Source.LINKLY) {
      posSettings = { ...device.posSettings, active: Source.STANDALONE };
    }

    return posSettings;
  };
}
