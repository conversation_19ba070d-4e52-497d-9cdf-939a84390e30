import { MutationAttributionService } from '@npco/component-bff-core/dist/attribution/mutationAttributionService';
import { DomicileLookupDb } from '@npco/component-bff-core/dist/domicile';
import { LambdaService } from '@npco/component-bff-core/dist/lambda';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { debug, error, info, warn } from '@npco/component-bff-core/dist/utils/logger';
import type { Money, StandInRule } from '@npco/component-dto-core';
import {
  AmexAcquisitionStatus,
  DbRecordType,
  EntityType,
  ISO3166,
  ISO4217,
  ScreeningRequestedType,
  ScreeningStatus,
  Status,
  CustomerRole,
} from '@npco/component-dto-core';
import {
  CustomerDocumentVerificationResult,
  CustomerScreeningRequestedEventDto,
  DocumentVerificationStatus,
  KYCStatus,
  SafeHarbourVerificationStatus,
  type CustomerForcedLogoffEventDto,
  type DocumentVerificationDetails,
  type CustomerCreateRequestedEventDto,
  type CustomerUpdatedEventDto,
} from '@npco/component-dto-customer';
import type { DeviceForcedLogoffEventDto, EntityDeviceSettings } from '@npco/component-dto-device';
import type {
  AccountStatus,
  EntityAbnChangedEventDto,
  EntityAmexMerchantSubmissionCompletedDto,
  EntityAmexMerchantSubmissionRequestedDto,
  EntityCreatedEventDto,
  EntityDailyLimits,
  EntityFullSearchCompletedEventDto,
  EntityMetricUpdatedEventDto,
  EntityPaymentSettingsDto,
  EntityRefundOverdraftUpdatedDto,
  EntityScreeningCompletedEventDto,
  EntityScreeningRequestedEventDto,
  PaymentLimit,
  EntityDomicileAndCurrencyAttachedEventDto,
  EntityHubspotObjectIdUpdatedEventDto,
} from '@npco/component-dto-entity';
import {
  EntityUpdatedEventDto,
  EntityFirstTransactionApprovedDto,
  EntityFirstTransactionCreatedDto,
  EntityNameUpdatedEventDto,
  EntityOnboardingStatusUpdatedEventDto,
  OnboardingStatus,
  RiskRating,
  RiskReviewResult,
  RiskReviewStatus,
  EntityStandInRulesUpdatedEventDto,
  EntityGqlCreateRequestedEventDto,
  EntityCreateRequestedEventDto,
} from '@npco/component-dto-entity';
import type { EntityPrimaryAccountHolderChangedEventDto } from '@npco/component-dto-entity/dist/entityPrimaryAccountHolderChangedEventDto';
import { SiteType } from '@npco/component-dto-site';

import { Injectable } from '@nestjs/common';
import lodash from 'lodash';
import { ContactService } from 'services/contact/contactService';
import type { EntityManager, Repository } from 'typeorm';
import { In, Not, getConnection, getManager, getRepository } from 'typeorm';
import { validate as uuidValidate, v4 as uuidv4 } from 'uuid';

import { EnvironmentService } from '../../config';
import type { AmexSubmissionType, EntityAccountStatus, Metrics, Transaction } from '../../entities';
import { Entity, Customer, CustomerEntity } from '../../entities';
import { BaseService } from '../base/baseService';
import { BadRequestError, ServerError } from '../base/error';
import { CustomerService } from '../customer/customerService';
import * as productUtils from '../customer/productTourUtils';
import { generateScreeningSQLWhereClause } from '../customer/rules';
import { CustomerEntityService } from '../customerEntity/customerEntityService';
import { DeviceService } from '../device/deviceService';
import type { EntityDeviceSettingsDto } from '../device/types';
import { DomicileService } from '../domicile/domicileService';
import { GlobalConfigService } from '../globalConfig/globalConfigService';
import { PosInterfaceCleanService } from '../posInterface/posInterfaceCleanService';
import { ReferralService } from '../referral/referralService';
import { SiteService } from '../site/siteService';
import { canUpdateSettlementAccount } from '../thirdPartyAccount/canUpdateSettlementAccount';
import { ThirdPartyBankAccountService } from '../thirdPartyAccount/thirdPartyBankAccountService';
import { convertCategoryToMcc, filterKeys, generateShortId } from '../util';

import type { CbsAccountGroupFlags } from './cbs/cbsAccountGroupFlags';
import { CbsService } from './cbs/cbsService';
import { CanAcquireAmexDownstreamUpdate } from './downstreamUpdates/canAcquireAmexDownstreamUpdate';
import { CanSettleDownstreamUpdate } from './downstreamUpdates/canSettleDownstreamUpdate';
import { CbsFlagsDownstreamUpdate } from './downstreamUpdates/cbsFlagsDownstreamUpdate';
import { tryPerformDownstreamUpdates } from './downstreamUpdates/framework/tryPerformDownstreamUpdates';
import type { PartialUpdateError } from './downstreamUpdates/partialErrors/partialUpdateError';
import { generateAmexMerchantSubmissionDto, getAmexStatusCode } from './lib/amex';
import { MsService } from './ms/msService';
import { autoOnboardEngine } from './onboarding/australia/onboardingEngine';
import { PgsEntityService } from './pgsEntity/pgsEntityService';
import { RiskRuleService } from './risk/riskRuleService';
import {
  getAmexRequestEngine,
  getOnboardingStatusRule,
  getScreeningRequestEngine,
  postFinalisedOnboardingStatus,
} from './rules';
import type { AmsUpdateEntityOption } from './types';
import { getPaymentSettings } from './utils/getPaymentSettings';
import { hasOnboardedStatus } from './utils/onboardingStatus';

@Injectable()
export class EntityService extends BaseService<Entity> {
  private readonly domicileLookupDynamodb: DomicileLookupDb;

  private readonly configService: GlobalConfigService;

  private readonly attributionService = MutationAttributionService.getInstance();

  constructor(
    protected readonly envService: EnvironmentService,
    protected readonly lambdaService: LambdaService,
    private readonly siteService: SiteService,
    private readonly deviceService: DeviceService,
    private readonly customerService: CustomerService,
    private readonly customerEntityService: CustomerEntityService,
    private readonly thirdPartyAccountService: ThirdPartyBankAccountService,
    private readonly referralService: ReferralService,
    private readonly cbsService: CbsService,
    private readonly msService: MsService,
    private readonly pgsEntityService: PgsEntityService,
    private readonly contactService: ContactService,
    private readonly posInterfaceCleanService: PosInterfaceCleanService,
    private readonly riskRuleService: RiskRuleService,
  ) {
    super('Entity', 'entityUuid', lambdaService, envService);
    this.configService = new GlobalConfigService();
    this.domicileLookupDynamodb = new DomicileLookupDb();
  }

  public getRepository = (): Repository<Entity> => getRepository(Entity);

  public getCustomerRepository = (): Repository<Customer> => getRepository(Customer);

  public getCustomerEntityRepository = (): Repository<CustomerEntity> => getRepository(CustomerEntity);

  getEntityDeviceSettings = (
    event: EntityCreateRequestedEventDto | EntityUpdatedEventDto,
    entity: Entity,
  ): EntityDeviceSettings => {
    const { accountStatus } = event;

    const entityAccountStatus: AccountStatus = entity.accountStatus as EntityAccountStatus; // NOSONAR

    return {
      canAcquire:
        typeof accountStatus?.canAcquire === 'boolean' ? accountStatus?.canAcquire : entityAccountStatus?.canAcquire,
      canAcquireMoto:
        typeof accountStatus?.canAcquireMoto === 'boolean'
          ? accountStatus.canAcquireMoto
          : entityAccountStatus.canAcquireMoto,
      canRefund:
        typeof accountStatus?.canRefund === 'boolean' ? accountStatus.canRefund : entityAccountStatus.canRefund,
      canStandIn:
        typeof accountStatus?.canStandIn === 'boolean' ? accountStatus.canStandIn : entityAccountStatus.canStandIn,
      canAcquireMobile:
        typeof accountStatus?.canAcquireMobile === 'boolean'
          ? accountStatus.canAcquireMobile
          : entityAccountStatus.canAcquireMobile,
      domicile: entity?.domicile,
      currency: entity?.currency as string,
    };
  };

  extractEntityDetails = async (event: EntityCreateRequestedEventDto | EntityGqlCreateRequestedEventDto) => {
    if (event instanceof EntityGqlCreateRequestedEventDto) {
      debug('Event is an instance of EntityGqlCreateRequestedEventDto');
    } else if (event instanceof EntityCreateRequestedEventDto) {
      debug('Event is an instance of EntityCreateRequestedEventDto');
    } else {
      debug('Event is of unknown type');
    }

    debug(`Extracting entity details from event: ${JSON.stringify(event)}`);
    let entityUuid: string;
    let abn: string | undefined;
    let acn: string | undefined;
    let crn: string | undefined;
    let registeringIndividual: Customer;

    if (event instanceof EntityGqlCreateRequestedEventDto) {
      entityUuid = uuidv4();
      abn = event?.businessDetails?.ausBusiness?.abn;
      acn = event?.businessDetails?.ausBusiness?.acn;
      crn = event?.businessDetails?.gbrBusiness?.crn;
      debug(`Registering individual UUID: ${event.registeringIndividual}`);

      const customer = await this.getCustomerRepository().findOne({
        where: { customerUuid: event.registeringIndividual },
      });
      debug(`Customer found: ${JSON.stringify(customer)}`);
      if (!customer) {
        throw new BadRequestError(`Customer not found for registeringIndividual: ${event.registeringIndividual}`);
      }

      const existingEntity = await this.getCustomerEntityRepository().findOne({
        where: { customerUuid: event.registeringIndividual },
      });
      debug(`Existing entity found: ${JSON.stringify(existingEntity)}`);
      if (existingEntity) {
        throw new BadRequestError(
          `RegisteringIndividual: ${event.registeringIndividual} is already associated with another entity: ${existingEntity.entityUuid}`,
        );
      }
      registeringIndividual = customer;
    } else {
      entityUuid = event.entityUuid;
      abn = event.abn;
      acn = event.acn;
      crn = event.crn;
      registeringIndividual = await this.getRegisteringIndividual(entityUuid);
    }
    debug(`Extracted entity details: ${JSON.stringify({ entityUuid, registeringIndividual, abn, acn, crn })}`);
    return { entityUuid, registeringIndividual, abn, acn, crn };
  };

  updateCustomerWithChecks = async (event: CustomerCreateRequestedEventDto) => {
    try {
      debug(`Calling updateCustomerWithChecks: ${JSON.stringify(event)}`);
      const updatedDto: any = {};
      const customer: any = await this.getCustomerRepository().findOne({
        where: { customerUuid: event.customerUuid },
      });
      debug(`Extracted Customer: ${JSON.stringify(customer)}`);

      if (
        customer.kyc?.status !== KYCStatus.VERIFIED &&
        customer.kyc?.status !== KYCStatus.RC_VERIFIED &&
        customer.kyc?.status !== KYCStatus.REVIEW
      ) {
        updatedDto.kyc = { ...(customer.kyc || {}), status: KYCStatus.REQUIRED };
      }

      if (customer.idv?.status !== DocumentVerificationStatus.COMPLETED) {
        updatedDto.idv = { ...(customer.idv || {}), status: DocumentVerificationStatus.REQUIRED };
      }

      if (customer.safeharbour?.status !== SafeHarbourVerificationStatus.COMPLETED) {
        updatedDto.safeharbour = {
          ...(customer.safeharbour || {}),
          status: SafeHarbourVerificationStatus.REQUIRED,
        };
      }

      if (customer.screening?.status !== ScreeningStatus.COMPLETED) {
        updatedDto.screening = {
          ...(customer.screening || {}),
          status: ScreeningStatus.REQUIRED,
        };
      }

      if (
        customer.productTourStatus?.showAdminMerchantPortalWelcome !== true &&
        customer.productTourStatus.showCorporateCardsSettingsWalkthrough !== true
      ) {
        updatedDto.productTourStatus = productUtils.createBaseProductTourStatus(event);
      }

      if (!customer.defaultEntityUuid) {
        updatedDto.defaultEntityUuid = event.entityUuid;
      }

      debug(`Updated DTO: ${JSON.stringify(updatedDto)}`);
      if (Object.keys(updatedDto).length > 0) {
        await this.getCustomerRepository().update({ customerUuid: event.customerUuid }, updatedDto);
        await this.callCommandHandler<CustomerUpdatedEventDto>(
          event.customerUuid,
          this.envService.cqrsCmds.Customer.Updated,
          updatedDto,
          this.attributionService.mutationAttribution,
        );
      }
    } catch (err) {
      error(`Error in updateCustomerWithChecks: ${err}`);
      throw new ServerError(`Failed to update customer with checks: ${err}`);
    }
  };

  readonly createEntity = async (
    event: EntityCreateRequestedEventDto | EntityGqlCreateRequestedEventDto,
  ): Promise<Entity> => {
    info(`create entity called: ${JSON.stringify(event, null, 2)}`);
    const defaultEntityValues = await this.configService.findAll('Entity');
    const accountStatus: EntityAccountStatus = this.configService.getValue(
      'Entity',
      'accountStatus',
      defaultEntityValues,
    );
    const countryOfOrigin = this.configService.getValue('Entity', 'countryOfOrigin', defaultEntityValues);
    const standInRules: StandInRule[] = this.configService.getValue('Entity', 'standInRules', defaultEntityValues);
    const paymentLimits: PaymentLimit[] = this.configService.getValue('Entity', 'paymentLimits', defaultEntityValues);
    const geofencing = this.configService.getValue('Entity', 'geofencing', defaultEntityValues);
    const { domicile, currency } = this.configService.getValue('Entity', 'international', defaultEntityValues);
    const regionalOptions = this.configService.getValue('Entity', 'regionalOptions', defaultEntityValues);
    const domicileFromPath = DomicileService.getInstance().getDomicile();
    if (domicile !== domicileFromPath) {
      throw new BadRequestError(
        `Domicile mismatch: config domicile (${domicile}) does not match path domicile (${domicileFromPath})`,
      );
    }

    const { entityUuid, registeringIndividual, abn, acn, crn } = await this.extractEntityDetails(event);
    const caidSequence = await getManager().query(`SELECT nextval('${this.envService.stage}."entity_caid_seq"')`);
    const caid = caidSequence[0].nextval.padStart(15, '0');
    const shortId = generateShortId(this.envService.shortIdLength);
    const screening = { status: ScreeningStatus.REQUIRED };
    const transactionMetaData = { yetToMakeTransaction: true };

    const entity = this.getRepository().create({
      entityUuid,
      shortId,
      caid,
      name: event.name,
      acn,
      abn,
      type: event.type,
      geofencing,
      countryOfOrigin,
      manualEntry: event.manualEntry ?? false,
      createdSourceIp: event.sourceIp,
      createdTime: `${new Date().getTime()}`,
      screening,
      onboardingStatus: OnboardingStatus.ENTITY_ESTABLISHED,
      riskRating: RiskRating.LOW_NEW,
      accountStatus,
      standInRules,
      paymentLimits,
      transactionMetaData,
      hasEverOnboarded: false,
      outstandingTransactionRequirementConfig: {
        note: true,
        attachments: true,
        category: true,
        accountingCategory: true,
      },
      primaryAccountHolder: registeringIndividual.customerUuid,
      domicile,
      currency,
      cohort: event?.cohort ?? [],
      crn,
      regionalOptions,
    });
    info(`Entity created successfully: ${JSON.stringify(entity)}`);

    const queryRunner = getConnection().createQueryRunner();

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
      await this.create<Partial<EntityCreatedEventDto>, any>(
        entityUuid,
        entity,
        {
          successCmd: this.envService.cqrsCmds.Entity.Created,
          successEvent: {
            ...event,
            paymentSettings: getPaymentSettings(paymentLimits),
            geofencing,
            accountStatus: this.getAccountStatusDto(accountStatus),
            caid,
            status: Status.ACTIVE,
            onboardingStatus: OnboardingStatus.ENTITY_ESTABLISHED,
            screening,
            riskRating: RiskRating.LOW_NEW,
            standInRules,
            countryOfOrigin,
            shortId,
            primaryAccountHolder: registeringIndividual.customerUuid,
            currency,
            domicile,
            outstandingTransactionRequirementConfig: {
              note: true,
              attachments: true,
              category: true,
              accountingCategory: true,
            },
          },
        },
        queryRunner,
        Entity,
      );

      try {
        await this.domicileLookupDynamodb.createDomicileRecord(entityUuid, DbRecordType.ENTITY_DOMICILE, domicile);
        await queryRunner.commitTransaction();
      } catch (err: any) {
        await queryRunner.rollbackTransaction();
        throw new ServerError(`Failed to create domicile record in DynamoDB ${err}`);
      }
    } catch (err: any) {
      error(`Failed to insert domicile record in DynamoDB, rolling back PostgreSQL changes. ${err}`);
      throw err;
    } finally {
      if (event instanceof EntityGqlCreateRequestedEventDto) {
        const input: CustomerCreateRequestedEventDto = {
          entityUuid,
          customerUuid: registeringIndividual.customerUuid,
          role: CustomerRole.ADMIN,
          registeringIndividual: true,
          status: Status.ACTIVE,
          createdTime: `${new Date().getTime()}`,
        };
        debug(`executing customer entity creation and update customer process: ${JSON.stringify(input)}`);
        await this.customerEntityService.creatingCustomerEntity(input, queryRunner, domicile);
        await this.updateCustomerWithChecks(input);
      }
      await queryRunner.release();
    }
    await this.updateDevicesSettings({
      entityUuid,
      entitySettings: {
        ...accountStatus,
        domicile,
        currency,
      },
      caid,
      mcc: convertCategoryToMcc(event.categoryGroup, event.category),
      standInRules,
      paymentLimits,
      geofencing,
    });
    await this.createReferral(event as EntityCreateRequestedEventDto);
    await this.siteService.createDefaultCnpSite(entityUuid, SiteType.CNP_ZELLER_INVOICE);
    await this.setDefaultEntityDailyLimits(entityUuid);
    await this.callCommandHandler<EntityPaymentSettingsDto>(
      entityUuid,
      this.envService.cqrsCmds.Entity.PaymentSettingsUpdated,
      {
        entityUuid,
        paymentLimits,
      },
    );

    if (abn) {
      await this.callCommandHandler<EntityAbnChangedEventDto>(entityUuid, this.envService.cqrsCmds.Entity.AbnChanged, {
        entityUuid,
        abn,
      });
    }
    info(`Returning created entity: ${JSON.stringify(entity)}`);
    return entity;
  };

  firstTransactionCreated = async (entityUuid: string, transaction: Transaction) => {
    const entity = await this.getRepository().findOne(entityUuid, {
      where: {
        domicile: this.getDomicile(),
      },
    });

    if (entity?.transactionMetaData?.yetToMakeTransaction) {
      const transactionMetaData = {
        firstTransactionUuid: transaction.transactionUuid,
        firstTransactionTimestamp: transaction.timestampUtc as string,
        yetToMakeTransaction: false,
      };
      const update = this.getRepository().create({ entityUuid, transactionMetaData });

      await this.update<EntityFirstTransactionCreatedDto>(
        entityUuid,
        update,
        {
          successCmd: this.envService.cqrsCmds.Entity.FirstTransactionCreated,
          successEvent: new EntityFirstTransactionCreatedDto({
            entityUuid,
            ...transactionMetaData,
          }),
        },
        {
          useDomicile: true,
        },
      );
    }
  };

  firstTransactionApproved = async (entityUuid: string, transactionUuid: string) => {
    const domicile = this.getDomicile();
    // Using querying over update with condition because this method will be run on every transaction and it is suspected
    // update condition will have greater cost compared to a single query (requires write instance on condition query).
    const entity = await this.getRepository()
      .createQueryBuilder('entity')
      .where('entity.entityUuid = :entityUuid AND entity.hasTransactionApproved = :hasTransactionApproved', {
        entityUuid,
        hasTransactionApproved: true,
      })
      .andWhere('entity.domicile = :domicile', { domicile })
      .getOne();
    if (entity) {
      return;
    }

    await this.getRepository()
      .createQueryBuilder()
      .update(Entity)
      .set({ hasTransactionApproved: true })
      .where({ entityUuid, domicile })
      .execute();
    await this.callCommandHandler<EntityFirstTransactionApprovedDto>(
      entityUuid,
      this.envService.cqrsCmds.Entity.FirstTransactionApproved,
      new EntityFirstTransactionApprovedDto({
        entityUuid,
        transactionUuid,
      }),
    );
  };

  updateEntity = async (
    event: EntityUpdatedEventDto,
    options: AmsUpdateEntityOption = {},
  ): Promise<PartialUpdateError[]> => {
    await this.onboardingStatusValidation(event, options);
    this.countryOfOriginValidation(event);
    const amexSubmissionRequired = await this.isAmexSubmissionRequired(event);
    const updatedEvent = {
      ...this.prepareEntityUpdateDto(event),
      ...(amexSubmissionRequired ? { amexSubmission: { status: AmexAcquisitionStatus.REQUIRED } } : {}),
    };
    const entity = await this.getEntity(event.entityUuid);
    const screeningRequired = await this.isScreeningRequired(event, entity);
    let data = this.createEntityUpdate(updatedEvent);
    if (screeningRequired) {
      data.screening = { status: ScreeningStatus.REQUIRED };
    }

    const downstreamUpdateResults = await tryPerformDownstreamUpdates(entity, data, [
      new CbsFlagsDownstreamUpdate(this.cbsService, this.envService),
      new CanSettleDownstreamUpdate(this.msService),
      new CanAcquireAmexDownstreamUpdate(this.pgsEntityService),
    ]);
    data = downstreamUpdateResults.updateIncludingAnyRollbacks;

    if (
      [OnboardingStatus.ONBOARDED, OnboardingStatus.RC_ONBOARDED].includes(
        entity.onboardingStatus as OnboardingStatus,
      ) &&
      data.accountStatus?.canAcquireAmex === false &&
      entity.accountStatus?.hasAmexPreviousCancelled === false
    ) {
      data.accountStatus = { ...data.accountStatus, hasAmexPreviousCancelled: true };
    }

    const updatedEntity = await this.update(event.entityUuid, data, undefined, {
      useDomicile: true,
    });

    if (updatedEvent?.hubspotCompanyId) {
      await this.callCommandHandler<EntityHubspotObjectIdUpdatedEventDto>(
        event.entityUuid,
        this.envService.cqrsCmds.Entity.HubspotObjectIdUpdated,
        {
          entityUuid: updatedEvent.entityUuid,
          hubspotCompanyId: updatedEvent.hubspotCompanyId,
        },
      );
      delete updatedEvent.hubspotCompanyId;
    }

    const restToUpdate = Object.entries(updatedEvent).filter(
      ([key, value]) => key !== 'hubspotCompanyId' && value !== undefined,
    );
    if (restToUpdate.length > 1) {
      await this.callCommandHandler<EntityUpdatedEventDto>(
        event.entityUuid,
        this.envService.cqrsCmds.Entity.Updated,
        updatedEvent,
      );
    }

    if (updatedEvent.onboardingStatus) {
      await this.callCommandHandler<EntityOnboardingStatusUpdatedEventDto>(
        event.entityUuid,
        this.envService.cqrsCmds.Entity.OnboardingStatusUpdated,
        new EntityOnboardingStatusUpdatedEventDto({
          entityUuid: event.entityUuid,
          onboardingStatus: updatedEvent.onboardingStatus,
        }),
        this.attributionService.mutationAttribution,
      );
    }
    if (updatedEvent.name) {
      await this.callCommandHandler<EntityNameUpdatedEventDto>(
        event.entityUuid,
        this.envService.cqrsCmds.Entity.NameUpdated,
        new EntityNameUpdatedEventDto({
          entityUuid: event.entityUuid,
          name: updatedEvent.name,
        }),
      );
    }
    if (updatedEvent.abn) {
      await this.callCommandHandler<EntityAbnChangedEventDto>(
        event.entityUuid,
        this.envService.cqrsCmds.Entity.AbnChanged,
        {
          entityUuid: event.entityUuid,
          abn: updatedEvent.abn,
        },
      );
    }
    if (screeningRequired) {
      await this.sendScreeningRequest(event);
    }

    if (amexSubmissionRequired || this.checkIfAmexStatusCodeChanged(entity, updatedEntity)) {
      await this.amexMerchantOnboardingRequest(event.entityUuid);
    }
    await this.updateEntitiesDevices(event, updatedEntity);
    await this.siteService.updateEntitySites(event);
    const onboardingStatusUpdateEvent = await this.triggerOnboardingStatusUpdateDueToRiskReview(event);
    await this.triggerForceLogoff(event, onboardingStatusUpdateEvent);

    return downstreamUpdateResults.errors;
  };

  checkIfAmexStatusCodeChanged = (existingEntity: Entity, updatedEntity: Entity) => {
    if (
      ![OnboardingStatus.ONBOARDED, OnboardingStatus.RC_ONBOARDED].includes(
        existingEntity.onboardingStatus as OnboardingStatus,
      )
    ) {
      return false;
    }
    const existingAmexStatusCode = getAmexStatusCode(existingEntity);
    const updatedAmexStatusCode = getAmexStatusCode(updatedEntity);

    return existingAmexStatusCode !== updatedAmexStatusCode;
  };

  // call by migration to initialize canAcquireAmex and hasAmexPreviousCancelled flags
  initializeEntityCanAcquireAmexFlag = async (entityUuid: string, canAcquireAmex: boolean) => {
    const accountStatus = {
      canAcquireAmex,
      hasAmexPreviousCancelled: !canAcquireAmex,
    };

    await this.update<EntityUpdatedEventDto>(
      entityUuid,
      { accountStatus },
      {
        successCmd: this.envService.cqrsCmds.Entity.Updated,
        successEvent: { entityUuid, accountStatus: { canAcquireAmex } },
      },
      {
        useDomicile: true,
      },
    );
  };

  addReferredBy = async (entityUuid: string, referredBy: string): Promise<void> => {
    const entityReferredBy = await this.referralService.findOne(entityUuid);
    if (!entityReferredBy) {
      await this.createReferral({ entityUuid, referredBy }, true);
    }

    await this.referralService.triggerReferredEvent(entityUuid);
    if (entityReferredBy && entityReferredBy.entityUuid !== referredBy) {
      throw new BadRequestError(`Entity it all ready referred by ${entityReferredBy.entityUuid}`);
    }
  };

  updateEntityByHubspotId = async (hubspotId: string, event: EntityUpdatedEventDto): Promise<void> => {
    const entity = await getConnection()
      .getRepository(Entity)
      .createQueryBuilder('entity')
      .where('entity.hubspotCompanyId = :id', { id: hubspotId })
      .andWhere('entity.domicile = :domicile', { domicile: this.getDomicile() })
      .getOne();
    if (entity?.entityUuid) {
      const updateEvent = { ...event, entityUuid: entity.entityUuid };
      await this.updateEntity(updateEvent);
    } else {
      throw new BadRequestError(`Cant find entity from hubspot id ${hubspotId}`);
    }
  };

  isScreeningRequired = async (
    event: EntityCreateRequestedEventDto | EntityUpdatedEventDto,
    entity: Entity,
  ): Promise<boolean> => {
    if (
      !postFinalisedOnboardingStatus.includes(
        OnboardingStatus[entity?.onboardingStatus as keyof typeof OnboardingStatus],
      )
    ) {
      return false;
    }

    const existingName = [entity.name?.trim().toLowerCase(), entity.tradingName?.trim().toLowerCase()];
    const eventName = event.name?.trim().toLowerCase();
    const newName = eventName?.length ? eventName : event.tradingName?.trim().toLowerCase();

    if (existingName.includes(newName)) {
      return false;
    }

    const engine = getScreeningRequestEngine();
    const { events } = await engine.run({
      ...event,
    });
    return events.length > 0;
  };

  sendScreeningRequest = async (event: EntityCreateRequestedEventDto | EntityUpdatedEventDto): Promise<void> => {
    const name = event.name?.length ? event.name : event.tradingName;
    if (!name) {
      warn('Cannot send the EntityScreeningRequest without name', event.entityUuid);
      return;
    }
    const entity = await this.findOne(event.entityUuid);
    if (!entity) {
      throw new BadRequestError(`Entity ${event.entityUuid} not found`);
    }
    const dto: EntityScreeningRequestedEventDto = {
      name,
      entityUuid: event.entityUuid,
    };
    info(`send screening request ${JSON.stringify(dto, null, 2)}`);
    await this.callCommandHandler<EntityScreeningRequestedEventDto>(
      event.entityUuid,
      this.envService.cqrsCmds.Entity.ScreeningRequested,
      dto,
    );
  };

  isAmexSubmissionRequired = async (event: EntityCreateRequestedEventDto | EntityUpdatedEventDto): Promise<boolean> => {
    const engine = getAmexRequestEngine();
    const { events } = await engine.run({
      name: null,
      ...event,
    });
    return events.length > 0;
  };

  screeningCompleted = async (event: EntityScreeningCompletedEventDto): Promise<void> => {
    const repository = getRepository(Entity);

    const updatedEvent: EntityUpdatedEventDto = {
      entityUuid: event.entityUuid,
      screening: {
        result: event.result,
        error: event.error,
        matchStatus: event.matchStatus,
        searchIdentifier: event.searchIdentifier,
        status: ScreeningStatus.COMPLETED,
      },
    };

    const data = repository.create(updatedEvent);

    await this.update<EntityUpdatedEventDto>(
      event.entityUuid,
      data,
      {
        successCmd: this.envService.cqrsCmds.Entity.Updated,
        successEvent: updatedEvent,
      },
      {
        useDomicile: true,
      },
    );
  };

  fullSearchCompleted = async (event: EntityFullSearchCompletedEventDto): Promise<void> => {
    const repository = getRepository(Entity);
    const data = repository.create({
      entityUuid: event.entityUuid,
      fullSearchResult: {
        found: event.found,
        acn: event.acn,
        registeredAddress: event.registeredAddress,
        businessAddress: event.businessAddress,
        members: event.members,
        error: event.error,
      },
    });
    await this.update(event.entityUuid, data, undefined, { useDomicile: true });
  };

  getAllCustomers = async (entityUuid: string): Promise<(Customer & CustomerEntity)[]> =>
    this.customerService.getCustomersByEntityUuid(entityUuid);

  getRegisteringIndividual = async (entityUuid: string): Promise<Customer> => {
    const customer = await this.customerService.getRegisteringIndividualByEntityUuid(entityUuid);
    if (!customer) {
      throw new BadRequestError(`Cant find the corresponding registering individual entity id ${entityUuid}`);
    }
    debug(`Registering individual found: ${JSON.stringify(customer)}`);
    return customer;
  };

  getEntity = async (id: string): Promise<Entity> => {
    const entity = await this.findOneWithOption({
      where: {
        entityUuid: id,
        domicile: this.getDomicile(),
      },
    });
    if (!entity) {
      const msg = `Cant find requested entity id ${id}`;
      warn(msg, id);
      throw new BadRequestError(msg);
    }
    return entity;
  };

  updateRefundOverdraftSettings = async (dto: EntityRefundOverdraftUpdatedDto) => {
    const repository = getRepository(Entity);
    const data = repository.create({
      entityUuid: dto.entityUuid,
      refundOverdraftSettings: {
        reason: dto.reason,
        limit: dto.limit,
      },
    });
    await this.update(dto.entityUuid, data, undefined, { useDomicile: true });
  };

  updateEntityMetrics = async (dto: EntityMetricUpdatedEventDto) =>
    getManager().transaction(async (entityManager: EntityManager) => {
      const items = await entityManager.query(
        `SELECT * from ${this.envService.stage}."Entity" where "entityUuid"='${
          dto.entityUuid
        }' AND domicile = '${this.getDomicile()}' FOR UPDATE`,
      );
      if (items.length <= 0) {
        const msg = `Entity id ${dto.entityUuid} doesnt exist.`;
        warn(msg, dto.entityUuid);
        return Promise.reject(new BadRequestError(msg));
      }
      const metric: Metrics = {
        metricsId: dto.metricsId,
        metricsValue: dto.metricsValue,
      };
      const entity = items[0];
      let metrics: Metrics[] = [];
      if (Array.isArray(entity.metrics)) {
        metrics = entity.metrics.map((m: any) => {
          if (m.metricsId === dto.metricsId) {
            return metric;
          }
          return m;
        });
        if (!metrics.find((m: any) => m.metricsId === dto.metricsId)) {
          metrics.push(metric);
        }
      } else {
        metrics = [metric];
      }
      return entityManager.update(Entity, { entityUuid: dto.entityUuid, domicile: this.getDomicile() }, { metrics });
    });

  amexMerchantOnboardingRequest = async (entityUuid: string): Promise<void> => {
    let entity: Entity;
    let registerIndividual: Customer;
    try {
      entity = await this.getEntity(entityUuid);
      registerIndividual = await this.getRegisteringIndividual(entityUuid);
    } catch (e: any) {
      error(`amexMerchantOnboarding: ${entityUuid} with invalid data for amex merchant`, entityUuid);
      error(e.toString(), entityUuid);
      return;
    }

    const dto = await generateAmexMerchantSubmissionDto(entity, registerIndividual, this.envService);
    info(`send Amex Merchant Submission Request request ${JSON.stringify(dto, null, 2)}`);
    await this.callCommandHandler<EntityAmexMerchantSubmissionRequestedDto>(
      dto.entityUuid,
      this.envService.cqrsCmds.Entity.AmexMerchantSubmissionRequested,
      dto,
    );
  };

  attachEntityDomicileAndCurrency = async (event: { entityUuid: string; domicile: string }) => {
    const defaultEntityValues = await this.configService.findAll('Entity');
    const currency = this.configService.getValue('Entity', 'international', defaultEntityValues)?.currency;

    await this.update<EntityDomicileAndCurrencyAttachedEventDto>(
      event.entityUuid,
      { domicile: event.domicile, currency },
      {
        successCmd: this.envService.cqrsCmds.Entity.DomicileCurrencyAttached,
        successEvent: { entityUuid: event.entityUuid, domicile: event.domicile, currency },
      },
      {
        useDomicile: true,
      },
    );
  };

  amexMerchantOnboardingResponse = async (event: EntityAmexMerchantSubmissionCompletedDto): Promise<void> => {
    const { entityUuid, status, result } = event;
    const amexSubmission: AmexSubmissionType = {
      status,
      ...(result ? { result } : {}),
    };
    const repository = getRepository(Entity);
    const data = repository.create({
      entityUuid,
      amexSubmission,
      updatedTime: `${new Date().getTime()}`,
    });
    await this.update(event.entityUuid, data, undefined, {
      useDomicile: true,
    });
    const updatedEvent: EntityUpdatedEventDto = {
      entityUuid,
      amexSubmission,
    };
    await this.callCommandHandler<EntityUpdatedEventDto>(
      event.entityUuid,
      this.envService.cqrsCmds.Entity.Updated,
      updatedEvent,
    );
  };

  amexMerchantOnboardingCronJob = async (): Promise<void> => {
    const entityUuids: { entityUuid: string }[] = await getManager().query(`SELECT "entityUuid" from ${
      this.envService.stage
    }."Entity"
        where ("amexSubmission"->>'status' is null or "amexSubmission"->>'status' != 'COMPLETED')
        and "onboardingStatus" in ('RC_ONBOARDED', 'ONBOARDED') and "status" = 'ACTIVE' and domicile = '${this.getDomicile()}'`);
    info(`AMEX Merchant Onboarding Cron Job: record found ${entityUuids.length}`);

    /* eslint-disable no-await-in-loop, no-restricted-syntax */
    for (const entity of entityUuids) {
      await new Promise((r) => {
        setTimeout(r, 1000);
      });
      info(`Processing entity uuid ${entity.entityUuid}`);
      await this.amexMerchantOnboardingRequest(entity.entityUuid);
    }
    /* eslint-enable no-await-in-loop, no-restricted-syntax */

    info('AMEX Merchant Onboarding Cron Job Completed');
  };

  updateDBEntity = (mgr: EntityManager, aggregateField: any, object: any) => mgr.update(Entity, aggregateField, object);

  isLockUpdateRequired = (_: EntityUpdatedEventDto) => {
    return true;
  };

  /**
   * /finaliseEntityOnboarding
   * @param entityUuid
   * @returns 'OnboardingStatus.ONBOARDED | OnboardingStatus.REVIEW'
   */
  evaluateOnboardingDetails = async (entityUuid: string, domicile: string) => {
    const entity = await this.getEntity(entityUuid);
    const customers = await this.getAllCustomers(entity.entityUuid);
    const registeringIndividual = customers.find((customer) => customer.registeringIndividual === true);
    if (!registeringIndividual) {
      throw new Error('Registering Individual is not found');
    }

    switch (domicile) {
      case Domicile.AU: {
        const helperFunctions = {
          verifyAbn: this.finalisedEntitiesExistWithAbn,
          verifyKyc: this.customerService.verifyKycDetails,
          verifyCategoryRisk: this.riskRuleService.isInOnboardingRiskCategory,
          verifyCategoryProhibition: this.riskRuleService.isOnboardingProhibitedMCC,
        };
        const { onboardingStatus, riskReview } = await autoOnboardEngine(
          {
            ...entity,
            stage: this.envService.stage,
            customers,
            registeringIndividual,
          },
          helperFunctions,
        );

        /**
         * if no 'riskReview' found, entity has already been onboarded before.
         * return 'onboardingStatus' right away
         */
        if (!riskReview) {
          return onboardingStatus;
        }
        const entityUpdateDto = new EntityUpdatedEventDto({
          entityUuid,
          onboardingStatus,
          riskReview,
          termsOfService: {
            ezta: {
              accepted: true,
              customerUuid: registeringIndividual.customerUuid,
              acceptedAt: new Date().toISOString(),
            },
          },
        });
        await this.updateEntity({ ...entityUpdateDto }, { allowFinaliseOnboardingStatus: true });
        await this.referralService.triggerReferredEvent(entityUuid);
        await this.evaluateCustomersForScreening(entity);
        return onboardingStatus;
      }
      case Domicile.GB:
        throw new Error('not yet implemented');
      default:
        throw new Error(`domicile: "${domicile}" is not supported`);
    }
  };

  getIdvDocumentName = (firstName?: string, middleName?: string, lastName?: string): string => {
    const existingFirstName = firstName?.toLocaleLowerCase().trim();
    const existingMiddleName = middleName?.toLocaleLowerCase().trim();
    const existingLastName = lastName?.toLocaleLowerCase().trim();

    return `
          ${existingFirstName ?? ''}${existingMiddleName ?? ''}${existingLastName ?? ''}
          `
      .toLocaleLowerCase()
      .replace(/\s/g, '');
  };

  getScreeningRequests = (customer: Customer, documents: DocumentVerificationDetails) => {
    const { customerUuid } = customer;
    const {
      driversLicenceFirstName,
      driversLicenceLastName,
      driversLicenceMiddleName,
      dob,
      medicareFirstName,
      medicareLastName,
      medicareMiddleName,
      passportFirstName,
      passportLastName,
      passportMiddleName,
      resultDriversLicence,
      resultMedicareCard,
      resultPassport,
    } = documents;
    const screeningRequests: Array<CustomerScreeningRequestedEventDto> = [];
    if (resultDriversLicence === CustomerDocumentVerificationResult.ACCEPTED) {
      screeningRequests.push(
        new CustomerScreeningRequestedEventDto({
          customerUuid,
          entityType: EntityType.INDIVIDUAL,
          firstName: driversLicenceFirstName,
          lastName: driversLicenceLastName,
          middleName: driversLicenceMiddleName,
          dob: dob?.length ? dob : customer.dob,
          screeningType: ScreeningRequestedType.DRIVERS_LICENCE,
        }),
      );
    }
    if (resultMedicareCard === CustomerDocumentVerificationResult.ACCEPTED) {
      screeningRequests.push(
        new CustomerScreeningRequestedEventDto({
          customerUuid,
          entityType: EntityType.INDIVIDUAL,
          firstName: medicareFirstName,
          lastName: medicareLastName,
          middleName: medicareMiddleName,
          dob: dob?.length ? dob : customer.dob,
          screeningType: ScreeningRequestedType.MEDICARE,
        }),
      );
    }
    if (resultPassport === CustomerDocumentVerificationResult.ACCEPTED) {
      screeningRequests.push(
        new CustomerScreeningRequestedEventDto({
          customerUuid,
          entityType: EntityType.INDIVIDUAL,
          firstName: passportFirstName,
          lastName: passportLastName,
          middleName: passportMiddleName,
          dob: dob?.length ? dob : customer.dob,
          screeningType: ScreeningRequestedType.PASSPORT,
        }),
      );
    }
    return screeningRequests;
  };

  sendScreeningRequestsForDocumentVerification = async (customer: Customer): Promise<void> => {
    const { documents, customerUuid } = customer;
    info(`sendScreeningRequestsForDocumentVerification: calling idv screening for ${customerUuid}`, customerUuid);
    debug(`sendScreeningRequestsForDocumentVerification: documents are ${JSON.stringify(documents)}`, customerUuid);
    debug(`sendScreeningRequestsForDocumentVerification: customer is ${JSON.stringify(customer)}`, customerUuid);
    if (!documents) {
      return;
    }

    const uniqueRequests: Array<CustomerScreeningRequestedEventDto> = [];
    const screeningRequests: Array<CustomerScreeningRequestedEventDto> = this.getScreeningRequests(customer, documents);
    screeningRequests.forEach((request) => {
      const existingRequest = uniqueRequests.find((existing) => {
        const existingName = this.getIdvDocumentName(existing.firstName, existing.middleName, existing.lastName);
        const requestName = this.getIdvDocumentName(request.firstName, request.middleName, request.lastName);
        return existingName === requestName;
      });

      const customerJointName = this.getIdvDocumentName(customer.firstname, customer.middlename, customer.lastname);
      const eventJointName = this.getIdvDocumentName(request.firstName, request.middleName, request.lastName);

      const documentNamesAreSameAsCustomer = customerJointName === eventJointName;

      if (!existingRequest && !documentNamesAreSameAsCustomer) {
        uniqueRequests.push(request);
      }
    });

    await Promise.allSettled([
      uniqueRequests[0] ? this.sendDocumentScreeningRequest(uniqueRequests[0]) : Promise.resolve(),
      uniqueRequests[1] ? this.sendDocumentScreeningRequest(uniqueRequests[1]) : Promise.resolve(),
      uniqueRequests[2] ? this.sendDocumentScreeningRequest(uniqueRequests[2]) : Promise.resolve(),
    ]);
  };

  sendDocumentScreeningRequest = async (request: CustomerScreeningRequestedEventDto) => {
    try {
      info(`sendDocumentScreeningRequest: need to screen the customer ${JSON.stringify(request, null, 2)}`);
      await this.callCommandHandler<CustomerScreeningRequestedEventDto>(
        request.customerUuid,
        this.envService.cqrsCmds.Customer.ScreeningRequested,
        request,
      );
      return await Promise.resolve();
    } catch (e: unknown) {
      // failed rule check
      const err = e as Error;
      error(e);
      return Promise.reject(err);
    }
  };

  selectDepositAccount = async (entityUuid: string, accountUuid: string, remitToCard = false): Promise<void> => {
    info(`select bank account ${accountUuid} for ${entityUuid} remitCard:${remitToCard}`);
    const entity = await this.getRepository().findOne({
      where: { entityUuid, domicile: this.getDomicile() },
    });
    canUpdateSettlementAccount(entity, this.attributionService.mutationAttribution?.userRole);
    let updateEvent = {};
    if (!remitToCard) {
      const account = await this.thirdPartyAccountService.findOneWithOption({
        where: {
          thirdPartyBankAccountUuid: accountUuid,
          domicile: this.getDomicile(),
        },
      });
      if (!account || account.entityUuid !== entityUuid || account.status === Status.DELETED) {
        throw new BadRequestError(`Cant find bank account ${accountUuid} for ${entityUuid}`);
      }
      updateEvent = {
        depositAccountUuid: accountUuid,
        depositAccountBsb: account.accountBsb,
        depositAccountNumber: account.accountNumber,
        depositAccountName: account.accountName,
        remitToCard: false,
      };
    } else {
      updateEvent = {
        debitCardAccountUuid: accountUuid,
        remitToCard: true,
      };
    }
    await this.update<EntityUpdatedEventDto>(
      entityUuid,
      {
        remitToCard,
        ...(remitToCard ? { debitCardAccountUuid: accountUuid } : { depositAccountUuid: accountUuid }),
      },
      {
        successCmd: this.envService.cqrsCmds.Entity.Updated,
        successEvent: {
          entityUuid,
          ...updateEvent,
        },
        successMutationAttribution: this.attributionService.mutationAttribution,
      },
      {
        useDomicile: true,
      },
    );
  };

  updatePaymentSettings = async (dto: EntityPaymentSettingsDto): Promise<void> => {
    info(`update payment settings for ${dto.entityUuid}`);
    const allPaymentLimits = await this.constructFullPaymentLimitUpdate(dto);

    await this.update<EntityPaymentSettingsDto>(
      dto.entityUuid,
      {
        paymentLimits: allPaymentLimits,
      },
      {
        successCmd: this.envService.cqrsCmds.Entity.PaymentSettingsUpdated,
        successEvent: {
          entityUuid: dto.entityUuid,
          paymentLimits: dto.paymentLimits,
        },
      },
      {
        useDomicile: true,
      },
    );
    await this.updateDevicesSettings({ entityUuid: dto.entityUuid, paymentLimits: allPaymentLimits });
  };

  getEntityDailyLimitConfig = (): { defaultLimit: Money; maximumAllowedLimit: Money } => {
    const DEFAULT_CURRENCY = ISO4217.AUD;
    const DEFAULT_LIMIT = '15000000'; // $150,000.00
    const MAXIMUM_ALLOWED_LIMIT = '300000000'; // $3,000,000.00

    return {
      defaultLimit: {
        currency: DEFAULT_CURRENCY,
        value: DEFAULT_LIMIT,
      },
      maximumAllowedLimit: {
        currency: DEFAULT_CURRENCY,
        value: MAXIMUM_ALLOWED_LIMIT,
      },
    };
  };

  setDefaultEntityDailyLimits = async (entityUuid: string) => {
    const { defaultLimit } = this.getEntityDailyLimitConfig();

    try {
      await this.updateDailyLimits(entityUuid, {
        limit: defaultLimit,
        reason: 'System Default',
        zellerUserId: 'SYSTEM',
      });
    } catch (err) {
      error(err);
    }
  };

  updateDailyLimits = async (entityUuid: string, input: { limit: Money; reason: string; zellerUserId: string }) => {
    info(`update daily limits for ${entityUuid}`);

    try {
      await this.cbsService.updateEntityRiskLimit(entityUuid, {
        limit: input.limit,
        zellerUserId: input.zellerUserId,
        reason: input.reason,
      });

      await this.cbsService.updateEntityMerchantLimit(entityUuid, {
        limit: input.limit,
      });
    } catch (e) {
      error(
        `Failed to update entity limit for entityUuid ${entityUuid} and input ${JSON.stringify(
          input,
        )}. Error: ${JSON.stringify(e)}`,
        entityUuid,
      );
      throw new ServerError('Failed to update entity daily limit');
    }

    const dailyLimits: EntityDailyLimits = {
      riskLimit: input.limit,
      merchantLimit: input.limit,
      appliedLimit: input.limit,
      riskLastUpdated: {
        zellerUserId: input.zellerUserId,
        lastUpdatedAt: new Date().toISOString(),
        reason: input.reason,
      },
    };

    await this.update<EntityUpdatedEventDto>(
      entityUuid,
      {
        dailyLimits,
      },
      {
        successCmd: this.envService.cqrsCmds.Entity.Updated,
        successEvent: {
          entityUuid,
          dailyLimits,
        },
      },
      {
        useDomicile: true,
      },
    );
  };

  updateStandInRules = async (entityUuid: string, standInRules: StandInRule[]): Promise<void> => {
    info(`update stand in rules for ${entityUuid}`);
    await this.update(
      entityUuid,
      {
        standInRules,
      },
      undefined,
      {
        useDomicile: true,
      },
    );
    await this.updateDevicesSettings({ entityUuid, standInRules });
    await this.callCommandHandler<EntityStandInRulesUpdatedEventDto>(
      entityUuid,
      this.envService.cqrsCmds.Entity.StandInRulesUpdated,
      new EntityStandInRulesUpdatedEventDto({
        entityUuid,
        standInRules,
      }),
    );
  };

  getEntityUuidFromReferralCode = async (referralCode: string): Promise<string | undefined> => {
    const field = uuidValidate(referralCode) ? 'entityUuid' : 'shortId';
    const entity = await getConnection()
      .getRepository(Entity)
      .createQueryBuilder('entity')
      .select('entity.entityUuid')
      .where(`entity.${field} = :referralCode`, { referralCode })
      .andWhere('entity.domicile = :domicile', { domicile: this.getDomicile() })
      .getOne();

    return entity?.entityUuid;
  };

  finalisedEntitiesExistWithAbn = async (entityUuid: string, abn: string) => {
    const finalisedEntitiesWithMatchingAbn = await this.getRepository().count({
      where: {
        abn: abn.replace(/ /g, ''),
        onboardingStatus: In(postFinalisedOnboardingStatus),
        entityUuid: Not(entityUuid),
        domicile: this.getDomicile(),
      },
    });
    return finalisedEntitiesWithMatchingAbn > 0;
  };

  resetCbsFlags = async (entityUuid: string) => {
    const entity = await this.getEntity(entityUuid);
    const currentAccountStatus = entity.accountStatus ?? {};

    const updatedAccountStatus: Partial<AccountStatus> = {
      ...currentAccountStatus,
      ...this.getDefaultCbsFlags(),
    };

    await this.update<EntityUpdatedEventDto>(
      entityUuid,
      { accountStatus: updatedAccountStatus },
      {
        successCmd: this.envService.cqrsCmds.Entity.Updated,
        successEvent: new EntityUpdatedEventDto({ entityUuid, accountStatus: updatedAccountStatus }),
      },
      {
        useDomicile: true,
      },
    );
  };

  initializeCbsFlags = async (entity: Entity) => {
    const { entityUuid } = entity;

    if (!this.envService.enableCbsEntityFlags || !this.envService.enableCbsCreateEntity) {
      debug('initializeCbsFlags is disabled by feature flag. resetting CBS flags to true locally.', entity.entityUuid);
      await this.resetCbsFlags(entityUuid);
      return;
    }

    try {
      await this.cbsService.createEntity(entityUuid);
    } catch (e) {
      error('[CBS] create entity API call failed', entityUuid);
      error(e);
      await this.resetCbsFlags(entityUuid);
      return;
    }

    const accountStatus: CbsAccountGroupFlags & Partial<AccountStatus> = {
      canCreateAccount: true,
      canCreateCard: true,
      canPayByCard: true,
      canTransferIn: true,
      canTransferOut: true,
      ...entity.accountStatus,
    };

    if (!lodash.isEqual(accountStatus, entity.accountStatus)) {
      await this.update<EntityUpdatedEventDto>(
        entityUuid,
        { accountStatus },
        {
          successCmd: this.envService.cqrsCmds.Entity.Updated,
          successEvent: new EntityUpdatedEventDto({
            entityUuid,
            accountStatus: this.getAccountStatusDto(accountStatus as EntityAccountStatus), // NOSONAR
          }),
        },
        {
          useDomicile: true,
        },
      );
    }

    try {
      const result = await this.cbsService.updateEntityFlags(entityUuid, accountStatus);

      if (result.isError) {
        throw new Error(result.errorMessage);
      }
    } catch (e) {
      error('[CBS] update entity API call failed', entityUuid);
      error(e);
      await this.resetCbsFlags(entityUuid);
    }
  };

  onboardingStatusUpdatedProjection = async (dto: EntityOnboardingStatusUpdatedEventDto) => {
    debug(`onboardingStatusUpdatedProjection: calling with dto ${JSON.stringify(dto)}`);

    if (!hasOnboardedStatus(dto.onboardingStatus)) {
      return;
    }

    const entity = await this.findOne(dto.entityUuid);
    debug(`entity.hasEverOnboarded is set to ${entity?.hasEverOnboarded}`);

    if (!entity || entity.hasEverOnboarded) {
      return;
    }

    const customer = await this.customerService.getRegisteringIndividualByEntityUuid(dto.entityUuid);
    if (!customer) {
      error(`No registering individual found for entity ${dto.entityUuid}`, dto.entityUuid);
      return;
    }

    try {
      const createAccountInput = {
        entityUuid: entity.entityUuid,
        tradingName: entity.name,
      };
      debug(`AMS DCA creation API called with input: ${JSON.stringify(createAccountInput)}`);
      const response = await this.cbsService.createAccount(createAccountInput);

      const entityDto: EntityUpdatedEventDto = {
        entityUuid: dto.entityUuid,
        remitToCard: true,
        debitCardAccountUuid: response.id,
      } as any;

      debug(`onboardingStatusUpdatedProjection: EntityUpdatedEventDto ${JSON.stringify(entityDto)}`);
      await this.updateEntity(entityDto);
    } catch (e) {
      error(`Failed to call CBS Create Account API  Error: ${JSON.stringify(e)}`, dto.entityUuid);
      error(e);
    }

    await this.contactService.createSelfContactFromEntityAndCustomer(entity, customer);

    const data = this.getRepository().create({
      entityUuid: dto.entityUuid,
      hasEverOnboarded: true,
    });

    debug(`onboardingStatusUpdatedProjection: Updating entity with value ${JSON.stringify(data)}`);
    await this.update(dto.entityUuid, data, undefined, {
      useDomicile: true,
    });
    await this.initializeCbsFlags(entity);
  };

  updatePrimaryAccountHolder = async (customerUuid: string, entityUuid: string) => {
    const entity: Entity = await this.getEntity(entityUuid);
    const existingPrimaryAccountHolder = entity.primaryAccountHolder;
    if (existingPrimaryAccountHolder === customerUuid) {
      info(
        `updatePrimaryAccountHolder: customer ${customerUuid} is already primary account holder of entity ${entityUuid}`,
        entityUuid,
      );
      return;
    }
    const entityPrimaryAccountHolderChangedEventDto: EntityPrimaryAccountHolderChangedEventDto = {
      entityUuid,
      primaryAccountHolder: customerUuid,
      previousPrimaryAccountHolder: existingPrimaryAccountHolder!,
    };
    const data = this.createEntityUpdate({ entityUuid, primaryAccountHolder: customerUuid } as any);
    await this.update<EntityPrimaryAccountHolderChangedEventDto>(
      entityUuid,
      data,
      {
        successCmd: this.envService.cqrsCmds.Entity.PrimaryAccountHolderChanged,
        successEvent: entityPrimaryAccountHolderChangedEventDto,
      },
      {
        useDomicile: true,
      },
    );
  };

  getRepositoryFromManager(manager: EntityManager): Repository<Entity> {
    return manager.getRepository(Entity);
  }

  private readonly constructFullPaymentLimitUpdate = async (dto: EntityPaymentSettingsDto): Promise<PaymentLimit[]> => {
    const { paymentLimits = [] } = await this.getEntity(dto.entityUuid);
    for (const updateLimit of dto.paymentLimits) {
      const paymentLimitIndex = paymentLimits.findIndex(
        (originalLimit) => originalLimit.paymentType === updateLimit.paymentType,
      );
      if (paymentLimitIndex >= 0) {
        paymentLimits[paymentLimitIndex] = updateLimit;
      } else {
        paymentLimits.push(updateLimit);
      }
    }

    return paymentLimits;
  };

  private readonly createReferral = async (
    event: { entityUuid: string; referredBy?: string },
    throwIfInvalid = false,
  ): Promise<void> => {
    if (!event.referredBy) {
      return;
    }

    const referredByEntityUuid = await this.getEntityUuidFromReferralCode(event.referredBy);
    if (!referredByEntityUuid) {
      warn(`Provided referral code was not found: ${event.referredBy}`, event.entityUuid);
      if (throwIfInvalid) {
        throw new BadRequestError(`No entity found with ${event.referredBy}`);
      }
      return;
    }

    info(`Parsed referral code from ${event.referredBy} to ${referredByEntityUuid}`, event.entityUuid);
    await this.referralService.createReferral({
      entityUuid: referredByEntityUuid,
      referred: event.entityUuid,
      created: new Date().toISOString(),
    });
  };

  private readonly prepareEntityUpdateDto = (dto: EntityUpdatedEventDto): EntityUpdatedEventDto => {
    const update = {
      ...dto,
      businessAddress: dto.businessAddress ? this.trimObjectValues(dto.businessAddress) : undefined,
      registeredAddress: dto.registeredAddress ? this.trimObjectValues(dto.registeredAddress) : undefined,
    };
    delete update.depositAccountBsb;
    delete update.depositAccountNumber;
    delete update.depositAccountName;
    return update;
  };

  private readonly trimObjectValues = <T extends { [key: string]: string | undefined }>(o: T) =>
    Object.entries(o).reduce<T>((newObj, entry) => {
      const key = entry[0];
      const value = entry[1];

      return { ...newObj, [key]: value?.trim() };
    }, {} as T);

  private readonly createEntityUpdate = (event: EntityUpdatedEventDto): Entity => {
    return this.getRepository().create({
      entityUuid: event.entityUuid,
      caid: event.caid,
      hubspotCompanyId: event.hubspotCompanyId,
      name: event.name,
      acn: event.acn,
      abn: event.abn,
      type: event.type,
      geofencing: event.geofencing,
      tradingName: event.tradingName,
      businessAddress: event.businessAddress,
      registeredAddress: event.registeredAddress,
      category: event.category,
      categoryGroup: event.categoryGroup,
      estimatedAnnualRevenue: event.estimatedAnnualRevenue,
      remitToCard: event.remitToCard,
      debitCardAccountUuid: event.debitCardAccountUuid,
      goodsServicesProvided: event.goodsServicesProvided,
      customerDiscovery: event.customerDiscovery,
      website: event.website,
      instagram: event.instagram,
      facebook: event.facebook,
      twitter: event.twitter,
      regulatorBody: event.regulatorBody,
      onboardingStatus: event.onboardingStatus,
      status: event.status,
      riskReview: event.riskReview,
      riskRating: event.riskRating,
      accountStatus: event.accountStatus,
      updatedTime: `${new Date().getTime()}`,
      establishingBusiness: event.establishingBusiness,
      termsOfService: event.termsOfService,
      outstandingTransactionRequirementConfig: event.outstandingTransactionRequirementConfig,
      tfnProvided: event.tfnProvided,
      countryOfOrigin: event.countryOfOrigin,
      primaryAccountHolder: event.primaryAccountHolder,
    });
  };

  private readonly triggerForceLogoff = async (
    entityUpdatedEvent: EntityUpdatedEventDto,
    onboardingStatusUpdatedEvent?: EntityUpdatedEventDto,
  ): Promise<void> => {
    const onboardingStatus = onboardingStatusUpdatedEvent?.onboardingStatus;
    const entityStatus =
      onboardingStatus === OnboardingStatus.RC_REJECTED || onboardingStatus === OnboardingStatus.RC_ABANDONED
        ? Status.DISABLED
        : entityUpdatedEvent.status;

    if (entityStatus && entityStatus !== Status.ACTIVE) {
      const devices = await this.deviceService.find({
        where: {
          entityUuid: entityUpdatedEvent.entityUuid,
          status: Not(Status.DELETED),
          domicile: this.getDomicile(),
        },
      });
      const customers = await this.customerEntityService.findAllCustomers(entityUpdatedEvent.entityUuid);
      let reason = 'Account is inactive.';
      if (entityStatus === Status.DISABLED) {
        reason = 'Account is disabled.';
      }

      await Promise.all(
        devices.map(({ deviceUuid }) =>
          this.callCommandHandler<DeviceForcedLogoffEventDto>(
            deviceUuid,
            this.envService.cqrsCmds.Device.ForcedLogoff,
            {
              deviceUuid,
              reason,
            },
          ),
        ),
      );
      await Promise.all(
        customers.map(({ customerUuid }) =>
          this.callCommandHandler<CustomerForcedLogoffEventDto>(
            customerUuid,
            this.envService.cqrsCmds.Customer.ForcedLogoff,
            {
              customerUuid,
              entityUuid: entityUpdatedEvent.entityUuid,
              reason,
            },
          ),
        ),
      );
      await this.posInterfaceCleanService.disConnectPosInterfaces(entityUpdatedEvent.entityUuid);
    }
  };

  private readonly updateDevicesSettings = async (settings: EntityDeviceSettingsDto) => {
    const { entityUuid } = settings;
    const domicile = await this.domicileLookupDynamodb.getDomicileByEntityId(entityUuid);
    if (!domicile) {
      throw new Error(`Domicile is not found for entityUuid: ${entityUuid}`);
    }
    const deviceCount = await this.getRepository()
      .createQueryBuilder('device')
      .where('device."entityUuid" = :entityUuid', { entityUuid })
      .andWhere('device.domicile = :domicile', { domicile })
      .getCount();
    info(`updating ${deviceCount} existing devices for entity ${JSON.stringify(settings)}`, entityUuid);
    if (deviceCount > 0) {
      await this.lambdaService.invokeAsync(this.envService.updateDevicesSettingsLambda, {
        ...settings,
        requestPath: '/v2/',
        domicile,
      });
    }
  };

  private readonly updateEntitiesDevices = async (event: EntityUpdatedEventDto, updatedEntity: Entity) => {
    if (event.category || event.categoryGroup || event.accountStatus || event.geofencing) {
      const { caid, standInRules, paymentLimits, accountStatus, entityUuid } = updatedEntity;
      const mcc = convertCategoryToMcc(event.categoryGroup, event.category);
      const entitySettings = accountStatus ? this.getEntityDeviceSettings(event, updatedEntity) : undefined;
      const payload: EntityDeviceSettingsDto = {
        entityUuid,
        entitySettings,
        caid,
        mcc,
        standInRules,
        paymentLimits,
        geofencing: event.geofencing,
      };
      await this.updateDevicesSettings(payload);
    }
  };

  private readonly countryOfOriginValidation = (event: EntityUpdatedEventDto) => {
    if (event.countryOfOrigin && !Object.values(ISO3166).includes(event.countryOfOrigin as ISO3166)) {
      throw new BadRequestError(`Not allow to change countryOfOrigin to ${event.countryOfOrigin}`);
    }
  };

  private readonly onboardingStatusValidation = async (
    event: EntityUpdatedEventDto,
    options: AmsUpdateEntityOption,
  ): Promise<void> => {
    if (event.onboardingStatus) {
      const entity = await this.getEntity(event.entityUuid);
      if (entity.onboardingStatus) {
        if (!getOnboardingStatusRule(entity.onboardingStatus).includes(event.onboardingStatus)) {
          throw new BadRequestError(
            `Not allow to change onboardingStatus from ${entity.onboardingStatus} to ${event.onboardingStatus}`,
          );
        }
      }
      const { onboardingStatus } = event;
      if (postFinalisedOnboardingStatus.includes(onboardingStatus) && options.allowFinaliseOnboardingStatus !== true) {
        throw new BadRequestError('illegal onboarding status');
      }
    }
  };

  private readonly triggerOnboardingStatusUpdateDueToRiskReview = async (
    event: EntityUpdatedEventDto,
  ): Promise<EntityUpdatedEventDto | undefined> => {
    if (event.riskReview?.status === RiskReviewStatus.COMPLETED && event.riskReview.result) {
      const nextOnboardingStatus = {
        [RiskReviewResult.ACCEPTED]: OnboardingStatus.RC_ONBOARDED,
        [RiskReviewResult.REJECTED]: OnboardingStatus.RC_REJECTED,
        [RiskReviewResult.ABANDONED]: OnboardingStatus.RC_ABANDONED,
        [RiskReviewResult.DEPLATFORMED]: OnboardingStatus.RC_DEPLATFORMED,
        [RiskReviewResult.REVIEW]: OnboardingStatus.RC_REVIEW,
      };

      const updatedEvent = new EntityOnboardingStatusUpdatedEventDto({
        entityUuid: event.entityUuid,
        onboardingStatus: nextOnboardingStatus[event.riskReview.result],
      });

      const data = this.createEntityUpdate(updatedEvent);
      const amexSubmissionRequired = await this.isAmexSubmissionRequired(updatedEvent);
      await this.update<EntityUpdatedEventDto>(
        event.entityUuid,
        data,
        {
          successCmd: this.envService.cqrsCmds.Entity.Updated,
          successEvent: updatedEvent,
        },
        {
          useDomicile: true,
        },
      );
      await this.callCommandHandler<EntityOnboardingStatusUpdatedEventDto>(
        event.entityUuid,
        this.envService.cqrsCmds.Entity.OnboardingStatusUpdated,
        updatedEvent,
      );
      if (amexSubmissionRequired) {
        await this.amexMerchantOnboardingRequest(event.entityUuid);
      }

      return updatedEvent;
    }

    return undefined;
  };

  private readonly getAccountStatusDto = (accountStatus: EntityAccountStatus): AccountStatus => {
    return filterKeys(accountStatus, ['hasAmexPreviousCancelled']);
  };

  private readonly getDefaultCbsFlags = (): Partial<AccountStatus> => {
    return {
      canCreateAccount: true,
      canCreateCard: true,
      canPayByCard: true,
      canTransferIn: true,
      canTransferOut: true,
    };
  };

  private readonly evaluateCustomersForScreening = async (entity: Entity) => {
    const { name, entityUuid } = { ...entity };
    if (entity.screening?.status !== ScreeningStatus.COMPLETED) {
      if (name) {
        const entityNameScreeningDto: EntityScreeningRequestedEventDto = {
          name,
          entityUuid,
        };
        info(`send screening request ${JSON.stringify(entityNameScreeningDto, null, 2)}`);
        await this.callCommandHandler<EntityScreeningRequestedEventDto>(
          entityUuid,
          this.envService.cqrsCmds.Entity.ScreeningRequested,
          entityNameScreeningDto,
        );
      }

      if (entity.tradingName && name?.trim().toLowerCase() !== entity.tradingName.trim().toLowerCase()) {
        const entityTradingNameScreeningDto: EntityScreeningRequestedEventDto = {
          name: entity.tradingName,
          entityUuid,
        };
        info(`send screening request ${JSON.stringify(entityTradingNameScreeningDto, null, 2)}`);
        await this.callCommandHandler<EntityScreeningRequestedEventDto>(
          entityUuid,
          this.envService.cqrsCmds.Entity.ScreeningRequested,
          entityTradingNameScreeningDto,
        );
      }
    }
    const customerUuids = (await getManager().query(
      `SELECT "customerUuid" FROM ${this.envService.stage}."CustomerEntity"
       WHERE (${generateScreeningSQLWhereClause()})
         AND "CustomerEntity"."entityUuid" = $1
         AND "CustomerEntity".status != 'DELETED'
         AND "CustomerEntity".domicile = $2`,
      [entityUuid, this.getDomicile()],
    )) as {
      customerUuid: string;
    }[];
    info(`No of Customer required Screening - ${customerUuids.length}`);

    for (const customerRecord of customerUuids) {
      info(`Processing customer uuid ${customerRecord.customerUuid}`);
      const customer = await this.customerService.getCustomer(customerRecord.customerUuid);

      if (customer.type === EntityType.BENEFICIARY_CLASS) {
        info(`Skipping screening for BENEFICIARY_CLASS customer ${customer.customerUuid}`);
        continue;
      }

      await this.callCommandHandler<CustomerScreeningRequestedEventDto>(
        customer.customerUuid,
        this.envService.cqrsCmds.Customer.ScreeningRequested,
        new CustomerScreeningRequestedEventDto({
          customerUuid: customer.customerUuid,
          entityUuid,
          entityType: customer.type,
          ...(customer.type !== EntityType.INDIVIDUAL ? { companyTrustName: customer.companyTrustName } : {}),
          ...(customer.type === EntityType.INDIVIDUAL
            ? {
                firstName: customer.firstname,
                middleName: customer.middlename,
                lastName: customer.lastname,
                dob: customer.dob,
              }
            : {}),
          screeningType: ScreeningRequestedType.DEFAULT,
        } as CustomerScreeningRequestedEventDto),
      );
    }
  };
}
