import type { PosConfigLocation, PosConfigVenue } from '@npco/component-dto-connection/dist/types';
import { ConnectionType, ConnectionTypeParams, Source } from '@npco/component-dto-core';
import type { PosInterfaceSitePairedEventDto } from '@npco/component-dto-pos-interface/dist/posInterfaceSitePairedEventDto';

import type { ConnectionPosInterface } from '../../entities/connectionPosInterface';

import type { GroupedPairingEvents, HlPairPosInterfaceRequest, ImposPairPosInterfaceRequest } from './types';

export function getPosVenueLocations(
  event: PosInterfaceSitePairedEventDto,
  posInterfaceConfig: ConnectionPosInterface,
  allConfigLocations: PosConfigLocation[],
): PosConfigLocation[] {
  let posVenueLocations: PosConfigLocation[] = [];

  const venue: PosConfigVenue | undefined = posInterfaceConfig.venues?.find(
    (venueRecord) => venueRecord.id === event.venueId,
  );
  if (!event.locations) {
    posVenueLocations =
      venue?.locations?.map((l) => ({
        id: l.id,
        name: l.name,
        number: l.number,
      })) ?? [];
  } else {
    posVenueLocations = event.locations.map((eventLocation: any) => ({
      id: eventLocation.locationId,
      name: eventLocation.locationName,
      number: allConfigLocations.find((l) => l.id === eventLocation.locationId)?.number ?? '',
    }));
  }

  return posVenueLocations;
}

export function groupPairingEventsBySiteAndVenue(
  pairingEvents: (HlPairPosInterfaceRequest | ImposPairPosInterfaceRequest)[],
): GroupedPairingEvents[] {
  const uniqueSiteVenueGroupings = new Map<string, GroupedPairingEvents>();

  pairingEvents.forEach((pairingEvent) => {
    const siteUuidVenueIdKey = `${pairingEvent.siteUuid}:${pairingEvent.venueId}`;
    const siteVenueGroup = uniqueSiteVenueGroupings.get(siteUuidVenueIdKey);

    if (!siteVenueGroup) {
      uniqueSiteVenueGroupings.set(siteUuidVenueIdKey, {
        siteUuid: pairingEvent.siteUuid,
        venueId: pairingEvent.venueId,
        ...(pairingEvent.locationId ? { locationIds: [pairingEvent.locationId] } : {}),

        ...('stationId' in pairingEvent ? { stationId: pairingEvent.stationId } : {}),
      });
      return;
    }

    uniqueSiteVenueGroupings.set(siteUuidVenueIdKey, {
      ...siteVenueGroup,
      ...(siteVenueGroup.locationIds && pairingEvent.locationId
        ? { locationIds: [...siteVenueGroup.locationIds, pairingEvent.locationId] }
        : {}),
    });
  });

  return Array.from(uniqueSiteVenueGroupings.values());
}

export const DIRECT_PROVIDERS: Source[] = [Source.ORACLE_POS, Source.SDK];

export const PAY_AT_TABLE_PROVIDERS: Source[] = [Source.HL_POS, Source.IMPOS, Source.TEVALIS_POS];

export function isPayAtTableProvider(source: Source): boolean {
  return PAY_AT_TABLE_PROVIDERS.includes(source);
}

export function isDirectProvider(source: Source): boolean {
  return DIRECT_PROVIDERS.includes(source);
}

export const sourceToConnectionTypeMap = new Map<Source, ConnectionType>([
  [Source.HL_POS, ConnectionType.HL_POS],
  [Source.IMPOS, ConnectionType.IMPOS],
  [Source.ORACLE_POS, ConnectionType.ORACLE_POS],
  [Source.SDK, ConnectionType.SDK],
  [Source.TEVALIS_POS, ConnectionType.TEVALIS_POS],
]);

export const connectionTypeToSourceMap = new Map<ConnectionType, Source>([
  [ConnectionType.HL_POS, Source.HL_POS],
  [ConnectionType.IMPOS, Source.IMPOS],
  [ConnectionType.ORACLE_POS, Source.ORACLE_POS],
  [ConnectionType.SDK, Source.SDK],
  [ConnectionType.TEVALIS_POS, Source.TEVALIS_POS],
]);

export const connectionTypeParamsToSourceMap = new Map<ConnectionTypeParams, Source>([
  [ConnectionTypeParams.HL, Source.HL_POS],
  [ConnectionTypeParams.IMPOS, Source.IMPOS],
  [ConnectionTypeParams.TEVALIS, Source.TEVALIS_POS],
]);

export const connectionTypeParamsToConnectionTypeMap = new Map<ConnectionTypeParams, ConnectionType>([
  [ConnectionTypeParams.HL, ConnectionType.HL_POS],
  [ConnectionTypeParams.IMPOS, ConnectionType.IMPOS],
  [ConnectionTypeParams.TEVALIS, ConnectionType.TEVALIS_POS],
]);
