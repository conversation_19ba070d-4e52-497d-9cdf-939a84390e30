import { ResourceExistsError } from '@npco/component-bff-core/dist/error/graphQlError';
import { LambdaService } from '@npco/component-bff-core/dist/lambda';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import * as Logger from '@npco/component-bff-core/dist/utils/logger';
import { PosConnectionStatus, type ConnectionPosInterfaceConfigCreateRequestDto } from '@npco/component-dto-connection';
import { ConnectionType } from '@npco/component-dto-core/dist/types';
import { SiteType } from '@npco/component-dto-site/src/types';

import { Test } from '@nestjs/testing';
import { v4 as uuidv4 } from 'uuid';

import { Status, entities } from '../../../entities';
import { createDbConnectionMiddleware } from '../../../lambdas/middleware';
import { closeDatabaseConnections, mockDomicile } from '../../../lambdas/testcases/testUtils';
import type { DBAppContext } from '../../../lambdas/types';
import { BadRequestError } from '../../base/error';
import { EntityModule } from '../../entity/entityModule';
import { EntityService } from '../../entity/entityService';
import { SiteModule } from '../../site/siteModule';
import { SiteService } from '../../site/siteService';
import type { SiteCreatedInput } from '../../site/types';
import { PosInterfaceModule } from '../posInterfaceModule';
import { PosInterfacePairingStatus } from '../types';

import { TevalisPosInterfaceConfigService } from './tevalisPosInterfaceConfigService';
import { TevalisPosInterfacePairingService } from './tevalisPosInterfacePairingService';

jest.mock('../../../config/envService');

jest.mock('../../kmsService/kmsService');
jest.mock('aws-xray-sdk', () => {
  return {
    getSegment: () => ({
      addNewSubsegment: jest.fn().mockReturnValue({}),
      addAnnotation: jest.fn(),
    }),
    captureAsyncFunc: async (_: string, cb: any) => {
      await cb({ addAnnotation: jest.fn(), close: jest.fn() });
    },
  };
});
jest.mock('typeorm', () => {
  const originalModule = jest.requireActual('typeorm');
  return {
    __esModule: true, // Use it when dealing with esModules
    ...originalModule,
  };
});
jest.mock('@npco/component-bff-core/dist/utils/logger', () => {
  const originalModule = jest.requireActual('@npco/component-bff-core/dist/utils/logger');
  return {
    __esModule: true, // Use it when dealing with esModules
    ...originalModule,
    info: jest.fn(),
    error: jest.fn(),
  };
});

jest.mock('@npco/component-bff-core/dist/lambda/lambdaService', () => {
  const lambdaService = {
    invokeAsync: jest.fn(),
    invoke: jest.fn(),
  };
  return {
    LambdaService: jest.fn(() => lambdaService),
  };
});

describe('tevalisPosInterfaceConfigService', () => {
  let mockLambdaService: jest.Mocked<LambdaService>;
  let service: TevalisPosInterfaceConfigService;
  let entityService: EntityService;
  let posInterfacePairingService: TevalisPosInterfacePairingService;
  let siteService: SiteService;
  let connectionUuid: string;

  const createConnectionPosInterfaceConfigDto = (
    entityUuid: string = uuidv4(),
  ): ConnectionPosInterfaceConfigCreateRequestDto => ({
    entityUuid,
    customerUuid: uuidv4(),
    credentials: { guid: uuidv4(), guid2: uuidv4() },
    venues: [
      {
        id: 'mockVenueId',
        name: 'mockVenueName',
        locations: [
          {
            id: 'mockLocationId',
            name: 'mockLocationName',
            number: 'mockTableNumber',
          },
        ],
      },
    ],
  });

  beforeAll(() => mockDomicile());

  beforeEach(async () => {
    jest.clearAllMocks();

    const testModule = await Test.createTestingModule({
      imports: [PosInterfaceModule, SiteModule, EntityModule],
    }).compile();

    mockLambdaService = testModule.get<LambdaService>(LambdaService) as jest.Mocked<LambdaService>;
    service = testModule.get<TevalisPosInterfaceConfigService>(TevalisPosInterfaceConfigService);
    entityService = testModule.get<EntityService>(EntityService);
    posInterfacePairingService = testModule.get<TevalisPosInterfacePairingService>(TevalisPosInterfacePairingService);
    siteService = testModule.get<SiteService>(SiteService);

    await createDbConnectionMiddleware(entities)({}, { app: testModule } as unknown as DBAppContext, () => {});
  });

  afterAll(async () => {
    await closeDatabaseConnections();
  });

  it('createPosInterfaceConfig should be able to create posInterface config entity and emit event', async () => {
    const posInterfaceConfig = createConnectionPosInterfaceConfigDto();
    const result = await service.createPosInterfaceConfig(posInterfaceConfig);
    connectionUuid = result.id;
    const dbRecord = await service.findOneWithOption({
      where: { connectionUuid, domicile: Domicile.AU },
    });

    expect(dbRecord).toMatchObject({
      createdTime: expect.any(String),
      domicile: 'AUS',
      connectionUuid,
      entityUuid: posInterfaceConfig.entityUuid,
      customerUuid: posInterfaceConfig.customerUuid,
      status: 'CONNECTED',
      type: 'TEVALIS_POS',
      venues: posInterfaceConfig.venues,
      credentials: posInterfaceConfig.credentials,
    });
    expect(mockLambdaService.invokeAsync.mock.calls[0][1]).toEqual({
      uri: 'Connection.PosInterfaceConfigCreated',
      dto: {
        connectionUuid,
        entityUuid: posInterfaceConfig.entityUuid,
        customerUuid: posInterfaceConfig.customerUuid,
        status: 'CONNECTED',
        venues: posInterfaceConfig.venues,
        credentials: posInterfaceConfig.credentials,
        provider: 'TEVALIS_POS',
      },
    });
  });
  it('updatePosInterfaceConfig should be able to update posInterfaceConfig instance and emit event', async () => {
    const existing = await service.findOne(connectionUuid);
    const updatedCredentials = { guid: uuidv4(), guid2: uuidv4() };
    await service.updatePosInterfaceConfig(
      {
        entityUuid: existing?.entityUuid,
        credentials: updatedCredentials,
      } as any,
      connectionUuid,
    );
    const dbRecord = await service.findOne(connectionUuid);
    expect(dbRecord).toMatchObject({
      createdTime: expect.any(String),
      domicile: 'AUS',
      connectionUuid,
      entityUuid: existing?.entityUuid,
      customerUuid: existing?.customerUuid,
      status: 'CONNECTED',
      type: 'TEVALIS_POS',
      venues: existing?.venues,
      credentials: updatedCredentials,
    });
    expect(mockLambdaService.invokeAsync.mock.calls[0][1]).toEqual({
      uri: 'Connection.PosInterfaceConfigUpdated',
      dto: {
        entityUuid: existing?.entityUuid,
        customerUuid: existing?.customerUuid,
        status: 'CONNECTED',
        venues: existing?.venues,
        credentials: updatedCredentials,
        connectionUuid,
        provider: 'TEVALIS_POS',
      },
    });
  });

  it('updatePosInterfaceConfig should clear credentials and venues when status is DISCONNECTED', async () => {
    const initialCreateData = createConnectionPosInterfaceConfigDto();
    // Create an initial connected record to update
    const createdConnection = await service.createPosInterfaceConfig(initialCreateData);
    connectionUuid = createdConnection.id; // Ensure connectionUuid is set for this test

    const existing = await service.findOne(connectionUuid);
    expect(existing).toBeDefined();
    if (!existing) return; // Type guard

    // Ensure initial state has credentials and venues
    expect(existing.credentials).toEqual(initialCreateData.credentials);
    expect(existing.venues).toEqual(initialCreateData.venues);

    // Mock the unpairPosInterfaceByEntityUuid method before calling updatePosInterfaceConfig
    jest.spyOn(posInterfacePairingService, 'unpairPosInterfaceByEntityUuid').mockResolvedValueOnce([]);

    await service.updatePosInterfaceConfig(
      {
        entityUuid: existing.entityUuid,
        status: PosConnectionStatus.DISCONNECTED,
      } as ConnectionPosInterfaceConfigCreateRequestDto, // Cast is okay for partial update check
      connectionUuid,
    );

    const dbRecord = await service.findOne(connectionUuid);
    expect(dbRecord).toBeDefined();
    if (!dbRecord) return; // Type guard

    expect(dbRecord.status).toBe(PosConnectionStatus.DISCONNECTED);
    expect(dbRecord.credentials).toEqual(null);
    expect(dbRecord.venues).toEqual([]);

    // Verify unpairPosInterfaceByEntityUuid was called
    expect(posInterfacePairingService.unpairPosInterfaceByEntityUuid).toHaveBeenCalledWith(existing.entityUuid, true);

    // Verify event emission
    expect(mockLambdaService.invokeAsync.mock.calls[1][1]).toEqual({
      uri: 'Connection.PosInterfaceConfigUpdated',
      dto: {
        entityUuid: existing.entityUuid,
        customerUuid: existing.customerUuid,
        status: PosConnectionStatus.DISCONNECTED,
        venues: [],
        credentials: null,
        connectionUuid,
        provider: ConnectionType.TEVALIS_POS,
      },
    });
  });

  it('should throw error on updatePosInterfaceConfig when venue name is not unique', async () => {
    await expect(
      service.updatePosInterfaceConfig(
        {
          entityUuid: uuidv4(),
          venues: [
            {
              name: 'Zeller Venue',
            },
            {
              name: 'Zeller Venue',
            },
          ],
        },
        connectionUuid,
      ),
    ).rejects.toThrow(new BadRequestError('venue name is not unique'));
  });

  it('should throw constraint violation error on same entityUuid and pos provider', async () => {
    const entityUuid = uuidv4();
    const posInterfaceConfig = createConnectionPosInterfaceConfigDto(entityUuid);
    const result = await service.createPosInterfaceConfig(posInterfaceConfig);
    expect(result.id).toBeDefined();
    await expect(service.createPosInterfaceConfig(posInterfaceConfig)).rejects.toThrow(
      new ResourceExistsError('[400] duplicate key value violates unique constraint "unq_entity_type"'),
    );
  });

  it('should log error if failed to call command handler', async () => {
    const errorMessage = 'Rejected';
    mockLambdaService.invokeAsync = jest.fn().mockRejectedValue(errorMessage);
    const spy = jest.spyOn(Logger, 'error');

    const posInterfaceConfig = createConnectionPosInterfaceConfigDto();
    const result = await service.createPosInterfaceConfig(posInterfaceConfig);
    expect(spy).toBeCalledWith(errorMessage, result.id);
  });

  it('should unpair pairing item under entity after updating pos interface config', async () => {
    mockLambdaService.invokeAsync.mockReset();

    const entityUuid = uuidv4();
    await entityService.create(entityUuid, { entityUuid, status: Status.ACTIVE, domicile: Domicile.AU });
    const siteCreatedDto: SiteCreatedInput = {
      entityUuid,
      siteUuid: uuidv4(),
      pin: '1234',
      name: 'testSite',
      type: SiteType.FIXED,
    };
    await siteService.createSite(siteCreatedDto);

    const posInterfaceConfig = createConnectionPosInterfaceConfigDto(entityUuid);
    const configCreatedResult = await service.createPosInterfaceConfig(posInterfaceConfig);
    expect(configCreatedResult.id).toBeDefined();
    const configDbRecord = await service.findOne(configCreatedResult.id);
    expect(configDbRecord?.connectionUuid).toEqual(configCreatedResult.id);
    expect(configDbRecord?.entityUuid).toEqual(entityUuid);
    expect(configDbRecord?.type).toEqual(ConnectionType.TEVALIS_POS);
    expect(configDbRecord?.venues).toEqual(posInterfaceConfig.venues);
    expect(configDbRecord?.status).toEqual(PosConnectionStatus.CONNECTED);

    const [pairingCreatedResult] = await posInterfacePairingService.pairPosInterfaces(
      [{ siteUuid: siteCreatedDto.siteUuid, venueId: 'mockVenueId' }],
      entityUuid,
    );
    expect(pairingCreatedResult.id).toBeDefined();
    const pairingDbRecord = await posInterfacePairingService.findOne(pairingCreatedResult.id);
    expect(pairingDbRecord?.pairingUuid).toEqual(pairingCreatedResult.id);
    expect(pairingDbRecord?.siteUuid).toEqual(siteCreatedDto.siteUuid);
    expect(pairingDbRecord?.provider).toEqual(ConnectionType.TEVALIS_POS);
    expect(pairingDbRecord?.status).toEqual(PosInterfacePairingStatus.PAIRED);

    const credentials = { guid: uuidv4(), guid2: uuidv4() };

    await service.updatePosInterfaceConfig({ ...posInterfaceConfig, credentials }, configCreatedResult.id);

    const configUpdatedDbRecord = await service.findOne(configCreatedResult.id);
    expect(configUpdatedDbRecord?.credentials).toEqual(credentials);
    const pairingUnpairedDbRecord = await posInterfacePairingService.findOne(pairingCreatedResult.id);
    expect(pairingUnpairedDbRecord?.status).toEqual(PosInterfacePairingStatus.UNPAIRED);
  });
});
