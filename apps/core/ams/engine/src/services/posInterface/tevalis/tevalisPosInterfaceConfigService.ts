import { LambdaService } from '@npco/component-bff-core/dist/lambda';
import {
  PosConnectionStatus,
  type ConnectionPosInterfaceConfigCreateRequestDto,
  type PosConfigVenue,
} from '@npco/component-dto-connection';
import { ConnectionType } from '@npco/component-dto-core';

import { Injectable } from '@nestjs/common/decorators';
import type { EntityManager, Repository } from 'typeorm';
import type { NullableOptional } from 'types';

import { EnvironmentService } from '../../../config/envService';
import { ConnectionPosInterface } from '../../../entities';
import { KmsService } from '../../kmsService/kmsService';
import { ensureUniqueness } from '../../util';
import { PayATTablePosInterfaceConfigService } from '../payAtTablePosInterfaceConfigService';
import type { ConnectionEntityCreateEvent } from '../posInterfaceConfigServiceBase';

import { TevalisPosInterfacePairingService } from './tevalisPosInterfacePairingService';

@Injectable()
export class TevalisPosInterfaceConfigService extends PayATTablePosInterfaceConfigService {
  provider = ConnectionType.TEVALIS_POS;

  constructor(
    protected readonly envService: EnvironmentService,
    protected readonly lambdaService: LambdaService,
    kmsService: KmsService,
    protected readonly posInterfacePairingService: TevalisPosInterfacePairingService,
  ) {
    super(envService, lambdaService, kmsService, posInterfacePairingService);
  }

  buildBaseConnectionEntity(event: ConnectionEntityCreateEvent): NullableOptional<ConnectionPosInterface> {
    return {
      ...super.buildBaseConnectionEntity(event),
      credentials: event.credentials,
      organisationId: event.organisationId,
      venues: event.venues ?? [],
    };
  }

  override async updatePosInterfaceConfig(
    inputEvent: ConnectionPosInterfaceConfigCreateRequestDto,
    connectionUuid: string,
  ): Promise<ConnectionPosInterface> {
    if (inputEvent.status !== PosConnectionStatus.DISCONNECTED && inputEvent.venues) {
      ensureUniqueness(inputEvent.venues as Array<PosConfigVenue>, 'name', 'venue');
    }

    const inputUpdateObject =
      inputEvent.status === PosConnectionStatus.DISCONNECTED
        ? {
            status: PosConnectionStatus.DISCONNECTED,
            entityUuid: inputEvent.entityUuid,
            credentials: null,
            venues: [],
          }
        : { ...inputEvent, status: PosConnectionStatus.CONNECTED };

    const connection = await super.updatePosInterfaceConfig(
      inputUpdateObject as ConnectionPosInterfaceConfigCreateRequestDto,
      connectionUuid,
    );
    await this.posInterfacePairingService.unpairPosInterfaceByEntityUuid(connection.entityUuid, true);
    return connection;
  }

  protected getRepositoryFromManager(manager: EntityManager): Repository<ConnectionPosInterface> {
    return manager.getRepository(ConnectionPosInterface);
  }
}
