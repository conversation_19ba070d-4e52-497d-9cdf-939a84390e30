import { CustomerRole } from '@npco/component-dto-core';
import type {
  CustomerCreateRequestedEventDto,
  CustomerUpdatedEventDto,
  ProductTourStatus,
} from '@npco/component-dto-customer';

const getInvoiceProductTour = ({
  showInvoiceInstructions,
  showInvoicesWelcome,
  showInvoicesCustomisationWelcome,
  showInvoicesScheduleSendWelcome,
  showInvoicesSendBySmsWelcome,
  showInvoiceSendViaInfo,
  showInvoicingCustomisationSettingsWelcome,
  showInvoiceApril,
}: {
  showInvoiceInstructions?: boolean;
  showInvoicesWelcome?: boolean;
  showInvoicesCustomisationWelcome?: boolean;
  showInvoicesScheduleSendWelcome?: boolean;
  showInvoicesSendBySmsWelcome?: boolean;
  showInvoiceSendViaInfo?: boolean;
  showInvoicingCustomisationSettingsWelcome?: boolean;
  showInvoiceApril?: boolean;
}) => {
  return {
    ...(showInvoiceInstructions !== undefined ? { showInvoiceInstructions } : {}),
    ...(showInvoicesWelcome !== undefined ? { showInvoicesWelcome } : {}),
    ...(showInvoicesCustomisationWelcome !== undefined ? { showInvoicesCustomisationWelcome } : {}),
    ...(showInvoicesScheduleSendWelcome !== undefined ? { showInvoicesScheduleSendWelcome } : {}),
    ...(showInvoicesSendBySmsWelcome !== undefined ? { showInvoicesSendBySmsWelcome } : {}),
    ...(showInvoiceSendViaInfo !== undefined ? { showInvoiceSendViaInfo } : {}),
    ...(showInvoicingCustomisationSettingsWelcome !== undefined ? { showInvoicingCustomisationSettingsWelcome } : {}),
    ...(showInvoiceApril !== undefined ? { showInvoiceApril } : {}),
  };
};

const getCatalogueProductTourFlags = ({
  showItemsWelcome,
  showItemInstructions,
  showCatalogueItemsWelcome,
  showServiceChargesWelcome,
}: {
  showItemsWelcome?: boolean;
  showItemInstructions?: boolean;
  showCatalogueItemsWelcome?: boolean;
  showServiceChargesWelcome: boolean | undefined;
}) => {
  return {
    ...(showItemsWelcome !== undefined ? { showItemsWelcome } : {}),
    ...(showItemInstructions !== undefined ? { showItemInstructions } : {}),
    ...(showCatalogueItemsWelcome !== undefined ? { showCatalogueItemsWelcome } : {}),
    ...(showServiceChargesWelcome !== undefined ? { showServiceChargesWelcome } : {}),
  };
};

const getOtherProductTourFlags = ({
  showAdminMerchantPortalWelcome,
  showTapToPayInstructions,
  showTapToPayMayJune,
  showSavingsAccountWelcome,
  showSavingsAccountMarch,
  showSavingsAccountMay,
  showCorporateCardsMayOffer,
  showCustomScreensaverPromo,
  showCorporateCardsWalkthrough,
  showCorporateCardsSettingsWalkthrough,
  showNotificationsWelcome,
  showCorporateCardsAdminWelcome,
  showOnboardingShop,
  profileAvatarWalkthrough,
}: {
  showAdminMerchantPortalWelcome?: boolean;
  showTapToPayInstructions?: boolean;
  showTapToPayMayJune?: boolean;
  showSavingsAccountWelcome?: boolean;
  showSavingsAccountMarch?: boolean;
  showSavingsAccountMay?: boolean;
  showCorporateCardsMayOffer?: boolean;
  showCustomScreensaverPromo?: boolean;
  showCorporateCardsWalkthrough?: boolean;
  showCorporateCardsSettingsWalkthrough?: boolean;
  showNotificationsWelcome?: boolean;
  showCorporateCardsAdminWelcome?: boolean;
  showOnboardingShop?: boolean;
  profileAvatarWalkthrough?: boolean;
}) => {
  return {
    ...(showOnboardingShop !== undefined ? { showOnboardingShop } : {}),
    ...(showAdminMerchantPortalWelcome !== undefined ? { showAdminMerchantPortalWelcome } : {}),
    ...(showTapToPayInstructions !== undefined ? { showTapToPayInstructions } : {}),
    ...(showTapToPayMayJune !== undefined ? { showTapToPayMayJune } : {}),
    ...(showSavingsAccountWelcome !== undefined ? { showSavingsAccountWelcome } : {}),
    ...(showSavingsAccountMarch !== undefined ? { showSavingsAccountMarch } : {}),
    ...(showSavingsAccountMay !== undefined ? { showSavingsAccountMay } : {}),
    ...(showCorporateCardsMayOffer !== undefined ? { showCorporateCardsMayOffer } : {}),
    ...(showCustomScreensaverPromo !== undefined ? { showCustomScreensaverPromo } : {}),
    ...(showCorporateCardsWalkthrough !== undefined ? { showCorporateCardsWalkthrough } : {}),
    ...(showCorporateCardsSettingsWalkthrough !== undefined ? { showCorporateCardsSettingsWalkthrough } : {}),
    ...(showNotificationsWelcome !== undefined ? { showNotificationsWelcome } : {}),
    ...(showCorporateCardsAdminWelcome !== undefined ? { showCorporateCardsAdminWelcome } : {}),
    ...(profileAvatarWalkthrough !== undefined ? { profileAvatarWalkthrough } : {}),
  };
};

export const createBaseProductTourStatus = (event: CustomerCreateRequestedEventDto): ProductTourStatus => {
  let showAdminMerchantPortalWelcome = false;
  let showCorporateCardsSettingsWalkthrough = false;
  if (event.role === CustomerRole.ADMIN) {
    showAdminMerchantPortalWelcome = true;
    showCorporateCardsSettingsWalkthrough = true;
  }
  return {
    showOnboardingShop: event.registeringIndividual,
    showAdminMerchantPortalWelcome,
    showInvoiceInstructions: true,
    showInvoicesWelcome: false,
    showItemsWelcome: true,
    showItemInstructions: true,
    showInvoicesCustomisationWelcome: true,
    showInvoicesScheduleSendWelcome: true,
    showInvoicesSendBySmsWelcome: true,
    showInvoiceSendViaInfo: true,
    showInvoicingCustomisationSettingsWelcome: true,
    showInvoiceApril: false,
    showTapToPayInstructions: false,
    showTapToPayMayJune: false,
    showSavingsAccountWelcome: true,
    showSavingsAccountMarch: false,
    showSavingsAccountMay: false,
    showCorporateCardsMayOffer: false,
    showCustomScreensaverPromo: false,
    showCorporateCardsWalkthrough: true,
    showCorporateCardsSettingsWalkthrough,
    showNotificationsWelcome: false,
    showCorporateCardsAdminWelcome: false,
    showCatalogueItemsWelcome: true,
    profileAvatarWalkthrough: false,
    showServiceChargesWelcome: false,
  };
};

export const updateProductStatusTour = (
  event: CustomerUpdatedEventDto,
  roles: CustomerRole[],
): ProductTourStatus | undefined => {
  const { productTourStatus } = event;
  let showAdminMerchantPortalWelcome = productTourStatus?.showAdminMerchantPortalWelcome;
  let showCorporateCardsSettingsWalkthrough = productTourStatus?.showCorporateCardsSettingsWalkthrough;
  const showOnboardingShop = productTourStatus?.showOnboardingShop;
  const showTapToPayInstructions = productTourStatus?.showTapToPayInstructions;
  const showTapToPayMayJune = productTourStatus?.showTapToPayMayJune;
  const showInvoiceInstructions = productTourStatus?.showInvoiceInstructions;
  const showInvoicesWelcome = productTourStatus?.showInvoicesWelcome;
  const showItemsWelcome = productTourStatus?.showItemsWelcome;
  const showItemInstructions = productTourStatus?.showItemInstructions;
  const showInvoicesCustomisationWelcome = productTourStatus?.showInvoicesCustomisationWelcome;
  const showInvoicesScheduleSendWelcome = productTourStatus?.showInvoicesScheduleSendWelcome;
  const showInvoicesSendBySmsWelcome = productTourStatus?.showInvoicesSendBySmsWelcome;
  const showInvoiceSendViaInfo = productTourStatus?.showInvoiceSendViaInfo;
  const showInvoicingCustomisationSettingsWelcome = productTourStatus?.showInvoicingCustomisationSettingsWelcome;
  const showSavingsAccountWelcome = productTourStatus?.showSavingsAccountWelcome;
  const showSavingsAccountMarch = productTourStatus?.showSavingsAccountMarch;
  const showSavingsAccountMay = productTourStatus?.showSavingsAccountMay;
  const showCorporateCardsMayOffer = productTourStatus?.showCorporateCardsMayOffer;
  const showCustomScreensaverPromo = productTourStatus?.showCustomScreensaverPromo;
  const showCorporateCardsWalkthrough = productTourStatus?.showCorporateCardsWalkthrough;
  const showNotificationsWelcome = productTourStatus?.showNotificationsWelcome;
  const showCorporateCardsAdminWelcome = productTourStatus?.showCorporateCardsAdminWelcome;
  const showInvoiceApril = productTourStatus?.showInvoiceApril;
  const showCatalogueItemsWelcome = productTourStatus?.showCatalogueItemsWelcome;
  const profileAvatarWalkthrough = productTourStatus?.profileAvatarWalkthrough;
  const showServiceChargesWelcome = productTourStatus?.showServiceChargesWelcome;

  if (event.role) {
    // If the users role has now changed to admin
    if (roles.every((r) => r === CustomerRole.MANAGER) && event.role === CustomerRole.ADMIN) {
      showAdminMerchantPortalWelcome = true;
      showCorporateCardsSettingsWalkthrough = true;
    }
    // If the users role has now been changed to manager
    else {
      showAdminMerchantPortalWelcome = false;
      showCorporateCardsSettingsWalkthrough = false;
    }
  }

  const updatedProductTourStatus = {
    ...getOtherProductTourFlags({
      showAdminMerchantPortalWelcome,
      showTapToPayInstructions,
      showTapToPayMayJune,
      showSavingsAccountWelcome,
      showSavingsAccountMarch,
      showSavingsAccountMay,
      showCorporateCardsMayOffer,
      showCustomScreensaverPromo,
      showCorporateCardsWalkthrough,
      showCorporateCardsSettingsWalkthrough,
      showNotificationsWelcome,
      showCorporateCardsAdminWelcome,
      showOnboardingShop,
      profileAvatarWalkthrough,
    }),
    ...getInvoiceProductTour({
      showInvoiceApril,
      showInvoiceInstructions,
      showInvoicesWelcome,
      showInvoicesCustomisationWelcome,
      showInvoicesScheduleSendWelcome,
      showInvoicesSendBySmsWelcome,
      showInvoiceSendViaInfo,
      showInvoicingCustomisationSettingsWelcome,
    }),
    ...getCatalogueProductTourFlags({
      showItemsWelcome,
      showItemInstructions,
      showCatalogueItemsWelcome,
      showServiceChargesWelcome,
    }),
  };
  if (Object.keys(updatedProductTourStatus).length > 0) {
    return updatedProductTourStatus;
  }
  return undefined;
};
