import './testcases/globalMocks';
import { DomicileLookupDb } from '@npco/component-bff-core/dist/domicile';
import { LambdaService } from '@npco/component-bff-core/dist/lambda';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { info, warn } from '@npco/component-bff-core/dist/utils/logger';
import {
  CustomerRole,
  ScreeningStatus,
  EntityType,
  MutationAttributionTokenGrant,
  MutationAttributionUserRole,
  MutationAttributionPlatform,
} from '@npco/component-dto-core';
import type {
  CustomerCreateRequestedEventDto,
  DocumentVerificationRequestedPayload,
  ProductTourStatus,
  CustomerCreatedEventDto,
} from '@npco/component-dto-customer';
import { CustomerUpdatedEventDto, KYCStatus } from '@npco/component-dto-customer';

import type { INestApplicationContext } from '@nestjs/common';
import axios from 'axios';
import { CustomerEntityService } from 'services/customerEntity/customerEntityService';
import { getConnection } from 'typeorm';
import * as typeorm from 'typeorm';
import { v4, v4 as uuidv4 } from 'uuid';

import { EnvironmentService } from '../config';
import type { Customer } from '../entities';
import { Status } from '../entities';
import 'jest-json';
import { Auth0Service } from '../services/authzero/auth0Service';
import { ServerError } from '../services/base/error';
import { CustomerService } from '../services/customer/customerService';
import { CustomerEmailAlreadyExistsError } from '../services/customer/error';
import { DomicileService } from '../services/domicile/domicileService';
import { EmailService } from '../services/email/emailService';
import { EntityService } from '../services/entity/entityService';

import {
  createCustomerHandler,
  deleteCustomerHandler,
  getCustomerHandler,
  updateCustomerHandler,
  getCustomerEventId,
  getPathId,
  getEventId,
  customerScreeningSafeGuardCronJobHandler,
  finaliseKycHandler,
  finaliseKycUpliftedHandler,
  updateKycCheckpointHandler,
} from './customerLambda';
import { initiateNestModule } from './testcases/mockNestModule';
import {
  closeDatabaseConnections,
  createCustomer,
  createCustomerRequestedDto,
  createCustomerUpdatedDto,
  createEntity,
  createEntityCreatedDto,
  decodeBase64,
  getMockLambdaPayload,
  mockLambdaInvoke,
  mockDomicile,
  deleteCustomer,
} from './testcases/testUtils';

jest.mock('typeorm', () => {
  const originalModule = jest.requireActual('typeorm');
  return {
    __esModule: true, // Use it when dealing with esModules
    ...originalModule,
  };
});
jest.mock('@npco/component-bff-core/dist/utils/logger', () => {
  const originalModule = jest.requireActual('@npco/component-bff-core/dist/utils/logger');
  return {
    __esModule: true, // Use it when dealing with esModules
    ...originalModule,
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  };
});

jest.mock('@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient');

describe('lambda test suite', () => {
  const context: any = {};
  let appContext: INestApplicationContext;
  let lambdaService: any;
  let envService: EnvironmentService;
  let auth0Service: Auth0Service;
  let customerService: CustomerService;
  let emailService: EmailService;
  let queryRunner: typeorm.QueryRunner;

  let entityUuid: string;
  let entityName: string;
  let mockEntityService: EntityService;
  let customerEntityService: CustomerEntityService;

  const mutationAttribution = {
    userIdentifier: uuidv4(),
    tokenGrant: MutationAttributionTokenGrant.USER_PASS,
    userRole: MutationAttributionUserRole.ADMIN,
    platform: MutationAttributionPlatform.DASHBOARD,
    createdTimestamp: new Date().getTime(),
    sessionKey: uuidv4(),
    reason: uuidv4(),
  };
  const headers = {
    'x-zeller-session-id': mutationAttribution.sessionKey,
    'x-zeller-platform': mutationAttribution.platform,
    'x-zeller-token-grant': mutationAttribution.tokenGrant,
    'x-zeller-user-identifier': mutationAttribution.userIdentifier,
    'x-zeller-mutation-created-timestamp': `${mutationAttribution.createdTimestamp}`,
    'x-zeller-user-role': mutationAttribution.userRole,
    'x-zeller-mutation-reason': mutationAttribution.reason,
  };

  beforeAll(async () => {
    appContext = await initiateNestModule();
    mockDomicile();
    lambdaService = appContext.get(LambdaService);
    mockLambdaInvoke(lambdaService);
  });

  beforeEach(async () => {
    lambdaService = appContext.get(LambdaService);
    envService = appContext.get(EnvironmentService);
    auth0Service = appContext.get(Auth0Service);
    customerService = appContext.get(CustomerService);
    emailService = appContext.get(EmailService);

    (emailService.send as any) = jest.fn();

    customerService.captureTransaction = async (_: string, __: string, callback) => {
      queryRunner = getConnection().createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        await callback(queryRunner);
        await queryRunner.commitTransaction();
      } catch (err) {
        await queryRunner.rollbackTransaction();
        throw err;
      } finally {
        await queryRunner.release();
      }
    };

    (axios.patch as any).mockReset();
    (axios.post as any).mockReset();
    (axios.get as any).mockReset();
    (axios.delete as any).mockReset();
    (axios.patch as any).mockReturnValue({
      data: '',
    });
    (axios.get as any).mockReturnValue({
      data: [{ user_id: 'userId01' }],
    });
    (axios.post as any).mockReturnValue({
      data: { access_token: '' },
    });
    (axios.delete as any).mockReturnValue({
      data: '',
    });
    (auth0Service.createUser as any).mockReset();
    (auth0Service.triggerResetPasswordFlow as any).mockReset();
    (auth0Service.updateUserInfo as any) = jest.fn();
    (auth0Service.getUserByEmail as any) = jest.fn();
    (auth0Service.updateUserPassword as any) = jest.fn();
    (auth0Service.createPasswordChangeTicket as any) = jest.fn();
    (auth0Service.generateSecurePassword as any) = jest.fn();
    mockEntityService = appContext.get(EntityService);
    mockEntityService.getRegisteringIndividual = jest.fn().mockResolvedValue({ customerUuid: v4() });
    customerEntityService = appContext.get(CustomerEntityService);
    customerEntityService.findEntityByUuid = jest.fn().mockResolvedValue({});
    const entityDto = createEntityCreatedDto();
    await createEntity(entityDto);
    entityUuid = entityDto.entityUuid;
    entityName = entityDto.name;
    mockLambdaInvoke(lambdaService);
  });

  afterAll(async () => {
    await closeDatabaseConnections();
  });

  it('should get trace id', () => {
    expect(getPathId({ path: { id: uuidv4() } })).toBeDefined();
    expect(getCustomerEventId({ detail: { customerUuid: uuidv4() } })).toBeDefined();
    expect(getEventId({ id: uuidv4() })).toBeDefined();
  });

  describe('customer lambda test suite', () => {
    it('should throw error if querying a non eiting customer', async () => {
      await new Promise<void>(async (done) => {
        getCustomerHandler({ path: { id: uuidv4() } }, context, async (error) => {
          expect(error).toBeDefined();
          done();
        });
      });
    });

    it('should be able to query a customer', async () => {
      await new Promise<void>(async (done) => {
        const customer = createCustomerRequestedDto();
        const entityDto = createEntityCreatedDto();
        customer.entityUuid = entityDto.entityUuid;
        await createEntity(entityDto);
        await createCustomer(customer);
        getCustomerHandler({ path: { id: customer.customerUuid } }, context, async (error, dto) => {
          expect(error).toBeNull();
          expect(dto.firstname).toBe(customer.firstname);
          expect(dto.customerUuid).toBe(customer.customerUuid);

          const { app } = context;
          const service = app.find(CustomerEntityService);
          const one = await service.findOneWithOption({
            customerUuid: customer.customerUuid,
            entityUuid: customer.entityUuid,
          });
          expect(one).toBeDefined();
          done();
        });
      });
    });

    it('should be able to handle create customer event', async () => {
      await new Promise<void>(async (done) => {
        const customer: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
        createCustomerHandler(
          {
            detail: customer,
          },
          context,
          async () => {
            const { app } = context;
            const service = app.find(CustomerService);
            const ceService = app.find(CustomerEntityService);
            const one = await service.findOne(customer.customerUuid);
            const ceOne = await ceService.findCustomerEntity(customer.customerUuid, customer.entityUuid);
            expect(one).toBeDefined();
            expect(one?.screening?.status).toBe(ScreeningStatus.REQUIRED);
            expect(one?.customerUuid).toBe(customer.customerUuid);
            expect(one?.identityUserId).toBe(customer.identityUserId);
            expect(one?.firstname).toBe(customer.firstname);
            expect(one?.lastname).toBe(customer.lastname);
            expect(one?.middlename).toBe(customer.middlename);
            expect(one?.email).toBe(customer.email);
            expect(ceOne?.role).toBe(customer.role);
            expect(one?.status).toBe(Status.ACTIVE);
            expect(one?.createdTime).toBeDefined();
            expect(one?.icon).toEqual(customer.icon);
            expect(one?.kyc).toEqual(customer.kyc);
            expect(one.productTourStatus?.showInvoicesWelcome).toBeFalsy();
            expect(one.productTourStatus?.showItemsWelcome).toBeTruthy();
            expect(one.productTourStatus?.showItemInstructions).toBeTruthy();
            expect(one.productTourStatus?.showInvoicesCustomisationWelcome).toBeTruthy();
            expect(one.productTourStatus?.showInvoicesScheduleSendWelcome).toBeTruthy();
            expect(one.productTourStatus?.showInvoicesSendBySmsWelcome).toBeTruthy();
            expect(one.productTourStatus?.showInvoiceSendViaInfo).toBeTruthy();
            expect(one.productTourStatus?.showInvoicingCustomisationSettingsWelcome).toBeTruthy();
            expect(one.productTourStatus?.showNotificationsWelcome).toBeFalsy();
            expect(one.productTourStatus?.showInvoiceApril).toBeFalsy();
            expect(one.productTourStatus?.showTapToPayMayJune).toBeFalsy();
            expect(one.productTourStatus?.showCatalogueItemsWelcome).toBeTruthy();
            expect(one.productTourStatus?.showServiceChargesWelcome).toBeFalsy();

            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(2);
            const lastInvoke = lambdaService.lambda.send.mock.calls[0][0];
            expect(lastInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            expect(JSON.parse(decodeBase64(lastInvoke.Payload))).toEqual({
              uri: 'Customer.Created',
              dto: {
                customerUuid: customer.customerUuid,
                entityUuid: customer.entityUuid,
                defaultEntityUuid: customer.entityUuid,
                identityUserId: customer.identityUserId,
                createdTime: expect.any(String),
                firstname: customer.firstname,
                middlename: customer.middlename,
                lastname: customer.lastname,
                dob: customer.dob,
                email: customer.email,
                role: customer.role,
                director: false,
                secretary: false,
                ceo: false,
                shareholder: false,
                beneficialOwner: false,
                beneficialOwnerAlt: false,
                beneficiary: false,
                partner: false,
                permissions: {
                  allowDiscountManagement: true,
                  allowItemManagement: true,
                  allowXeroPaymentServices: true,
                  allowZellerInvoices: true,
                },
                trustee: false,
                settlor: false,
                generalContact: false,
                financialContact: false,
                type: EntityType.INDIVIDUAL,
                companyTrustName: customer.companyTrustName,
                abn: customer.abn,
                documents: {} as DocumentVerificationRequestedPayload,
                chair: false,
                treasurer: false,
                governmentRole: customer.governmentRole,
                idv: {
                  status: 'REQUIRED',
                },
                safeharbour: {
                  status: 'REQUIRED',
                },
                screening: {
                  status: ScreeningStatus.REQUIRED,
                },
                registeringIndividual: false,
                productTourStatus: {
                  showAdminMerchantPortalWelcome: true,
                  showOnboardingShop: false,
                  showInvoiceInstructions: true,
                  showInvoicesWelcome: false,
                  showItemsWelcome: true,
                  showItemInstructions: true,
                  showInvoicesCustomisationWelcome: true,
                  showInvoicesScheduleSendWelcome: true,
                  showInvoicesSendBySmsWelcome: true,
                  showInvoiceSendViaInfo: true,
                  showInvoicingCustomisationSettingsWelcome: true,
                  showNotificationsWelcome: false,
                  showTapToPayInstructions: false,
                  showTapToPayMayJune: false,
                  showSavingsAccountWelcome: true,
                  showSavingsAccountMarch: false,
                  showSavingsAccountMay: false,
                  showCorporateCardsMayOffer: false,
                  showCorporateCardsWalkthrough: true,
                  showCorporateCardsSettingsWalkthrough: true,
                  showCustomScreensaverPromo: false,
                  showCorporateCardsAdminWelcome: false,
                  showInvoiceApril: false,
                  showCatalogueItemsWelcome: true,
                  profileAvatarWalkthrough: false,
                  showServiceChargesWelcome: false,
                } as ProductTourStatus,
                icon: customer.icon,
                kyc: customer.kyc,
              },
            });
            expect(ceOne).toBeDefined();
            expect(ceOne.role).toBe(customer.role);
            expect(ceOne.createdTime).toBeDefined();
            done();
          },
        );
      });
    });

    describe('create customer with query runner', () => {
      let domicileLookupDb: DomicileLookupDb;

      beforeAll(async () => {
        domicileLookupDb = new DomicileLookupDb();
      });

      afterAll(() => {
        jest.restoreAllMocks();
      });

      it('should be able to handle create customer event with query runner', async () => {
        await new Promise<void>(async (done) => {
          const customer: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
          createCustomerHandler(
            {
              detail: customer,
            },
            context,
            async () => {
              const { app } = context;
              const service = app.find(CustomerService);
              const ceService = app.find(CustomerEntityService);
              const one = await service.findOne(customer.customerUuid);
              const ceOne = await ceService.findCustomerEntity(customer.customerUuid, customer.entityUuid);
              expect(one).toBeDefined();
              expect(ceOne).toBeDefined();

              // check dynamodb record
              const result = await domicileLookupDb.getDomicileByCustomerUuid(customer.customerUuid);
              expect(result).toBe(Domicile.AU);
              done();
            },
          );
        });
      });

      it('should throw a ServerError when createCustomer fails due to DynamoDB insert error', async () => {
        const createDomicileRecordSpy = jest.spyOn(DomicileLookupDb.prototype, 'createDomicileRecord');
        createDomicileRecordSpy.mockRejectedValue(new Error('DynamoDB insert failed'));

        const customer: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
        // should throw server error
        await expect(createCustomer(customer)).rejects.toThrow(ServerError);
        createDomicileRecordSpy.mockRestore();
      });

      it('should be able to rollback postgres changes if dynamo insert fails', async () => {
        const createDomicileRecordSpy = jest.spyOn(DomicileLookupDb.prototype, 'createDomicileRecord');
        createDomicileRecordSpy.mockRejectedValue(new Error('DynamoDB insert failed'));
        await new Promise<void>(async (done) => {
          const customer: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
          createCustomerHandler(
            {
              detail: customer,
            },
            context,
            async () => {
              const { app } = context;
              const service = app.find(CustomerService);
              const ceService = app.find(CustomerEntityService);
              const one = await service.findOne(customer.customerUuid);
              const ceOne = await ceService.findCustomerEntity(customer.customerUuid, customer.entityUuid);
              expect(one).toBeUndefined();
              expect(ceOne).toBeUndefined();

              // check dynamodb record
              const result = await domicileLookupDb.getDomicileByCustomerUuid(customer.customerUuid);
              expect(result).toBe(null);
              done();
            },
          );
        });
      });
    });

    it('should be able to handle create customer event invited by customer', async () => {
      await new Promise<void>(async (resolve) => {
        const existingCustomer = {
          ...createCustomerRequestedDto(),
          entityUuid,
        };

        await createCustomer(existingCustomer);
        const newCustomer: CustomerCreateRequestedEventDto = {
          entityUuid,
          email: uuidv4(),
          firstname: uuidv4(),
          middlename: uuidv4(),
          lastname: uuidv4(),
          customerUuid: uuidv4(),
          registeringIndividual: false,
          createIdentity: true,
          role: CustomerRole.ADMIN,
          invitedBy: {
            customerUuid: existingCustomer.customerUuid,
          },
        };
        (auth0Service.createUser as jest.Mock).mockReturnValue({
          user_id: 'auth0_user_id',
        });
        mockLambdaInvoke(lambdaService);
        createCustomerHandler(
          {
            detail: newCustomer,
          },
          context,
          async () => {
            const { app } = context;
            const service = app.find(CustomerService);
            const ceService = app.find(CustomerEntityService);
            const one = await service.findOne(newCustomer.customerUuid);
            const ceOne = await ceService.findCustomerEntity(newCustomer.customerUuid, newCustomer.entityUuid);
            expect(one).toBeDefined();
            expect(ceOne?.customerUuid).toBe(newCustomer.customerUuid);
            expect(ceOne?.entityUuid).toBe(newCustomer.entityUuid);
            expect(ceOne?.status).toBe(Status.ACTIVE);
            expect(ceOne?.invitationPending).toBe(true);
            expect(ceOne?.invitedBy).toEqual({
              customerUuid: existingCustomer.customerUuid,
              email: existingCustomer.email,
              firstName: existingCustomer.firstname,
              lastName: existingCustomer.lastname,
              middleName: existingCustomer.middlename,
            });
            expect(one?.kyc).toEqual({ status: KYCStatus.REQUIRED });

            const eventUris = lambdaService.lambda.send.mock.calls.map((call: any) => {
              return JSON.parse(call[0].Payload.toString()).uri;
            });
            expect(eventUris).toEqual(['Customer.Created', 'CustomerEntity.Invited', 'CustomerEntity.Linked']);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(3);
            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              uri: 'Customer.Created',
              dto: {
                customerUuid: newCustomer.customerUuid,
                entityUuid: newCustomer.entityUuid,
                defaultEntityUuid: newCustomer.entityUuid,
                createdTime: expect.any(String),
                firstname: newCustomer.firstname,
                middlename: newCustomer.middlename,
                lastname: newCustomer.lastname,
                email: newCustomer.email,
                role: CustomerRole.ADMIN,
                createIdentity: true,
                idv: {
                  status: 'REQUIRED',
                },
                safeharbour: {
                  status: 'REQUIRED',
                },
                screening: {
                  status: ScreeningStatus.REQUIRED,
                },
                registeringIndividual: false,
                permissions: {
                  allowDiscountManagement: true,
                  allowItemManagement: true,
                  allowXeroPaymentServices: true,
                  allowZellerInvoices: true,
                },
                productTourStatus: {
                  showOnboardingShop: false,
                  showAdminMerchantPortalWelcome: true,
                  showInvoiceInstructions: true,
                  showInvoicesWelcome: false,
                  showItemsWelcome: true,
                  showItemInstructions: true,
                  showInvoicesCustomisationWelcome: true,
                  showInvoicesScheduleSendWelcome: true,
                  showInvoicesSendBySmsWelcome: true,
                  showInvoiceSendViaInfo: true,
                  showInvoicingCustomisationSettingsWelcome: true,
                  showNotificationsWelcome: false,
                  showTapToPayInstructions: false,
                  showTapToPayMayJune: false,
                  showInvoiceApril: false,
                  showSavingsAccountWelcome: true,
                  showSavingsAccountMarch: false,
                  showSavingsAccountMay: false,
                  showCorporateCardsMayOffer: false,
                  showCorporateCardsWalkthrough: true,
                  showCorporateCardsSettingsWalkthrough: true,
                  showCustomScreensaverPromo: false,
                  showCorporateCardsAdminWelcome: false,
                  showCatalogueItemsWelcome: true,
                  profileAvatarWalkthrough: false,
                  showServiceChargesWelcome: false,
                },
                isInvitationPending: true,
                invitedBy: {
                  customerUuid: existingCustomer.customerUuid,
                  email: existingCustomer.email,
                  firstName: existingCustomer.firstname,
                  middleName: existingCustomer.middlename,
                  lastName: existingCustomer.lastname,
                },
                kyc: {
                  status: KYCStatus.REQUIRED,
                },
                type: EntityType.INDIVIDUAL,
              } as CustomerCreatedEventDto,
            });

            expect(auth0Service.createUser as jest.Mock).toHaveBeenCalledWith({
              email: newCustomer.email,
              email_verified: false,
              verify_email: false,
              family_name: newCustomer.lastname,
              given_name: newCustomer.firstname,
              name: `${newCustomer.firstname} ${newCustomer.lastname}`,
              nickname: newCustomer.firstname,
              password: undefined,
              user_metadata: {
                customerUuid: newCustomer.customerUuid,
                entityUuid,
                invitationPending: true,
                invitedBy: {
                  customerUuid: existingCustomer.customerUuid,
                  email: existingCustomer.email,
                  firstName: existingCustomer.firstname,
                  middleName: existingCustomer.middlename,
                  lastName: existingCustomer.lastname,
                  entityName,
                },
                role: CustomerRole.ADMIN,
                kycStatus: KYCStatus.REQUIRED,
              },
            });
            resolve();
          },
        );
      });
    });

    it('should create customer with default permissions false for manager', async () => {
      await new Promise<void>(async (done) => {
        const customer: CustomerCreateRequestedEventDto = createCustomerRequestedDto(CustomerRole.MANAGER);
        createCustomerHandler(
          {
            detail: customer,
          },
          context,
          async () => {
            const { app } = context;
            const service = app.find(CustomerService);
            const ceService = app.find(CustomerEntityService);
            const one = await service.findOne(customer.customerUuid);
            const ceOne = await ceService.findCustomerEntity(customer.customerUuid, customer.entityUuid);
            expect(one).toBeDefined();
            expect(one?.screening?.status).toBe(ScreeningStatus.NOT_REQUIRED);
            expect(one?.customerUuid).toBe(customer.customerUuid);
            expect(one?.identityUserId).toBe(customer.identityUserId);
            expect(one?.firstname).toBe(customer.firstname);
            expect(one?.lastname).toBe(customer.lastname);
            expect(one?.middlename).toBe(customer.middlename);
            expect(one?.email).toBe(customer.email);
            expect(ceOne?.role).toBe(customer.role);
            expect(one?.status).toBe(Status.ACTIVE);
            expect(ceOne?.status).toBe(Status.ACTIVE);
            expect(one?.createdTime).toBeDefined();
            expect(one?.icon).toEqual(customer.icon);
            expect(one?.kyc).toEqual(customer.kyc);
            expect(one.productTourStatus?.showInvoicesWelcome).toBeFalsy();
            expect(one.productTourStatus?.showItemsWelcome).toBeTruthy();
            expect(one.productTourStatus?.showItemInstructions).toBeTruthy();
            expect(one.productTourStatus?.showInvoicesCustomisationWelcome).toBeTruthy();
            expect(one.productTourStatus?.showInvoicesScheduleSendWelcome).toBeTruthy();
            expect(one.productTourStatus?.showInvoicesSendBySmsWelcome).toBeTruthy();
            expect(one.productTourStatus?.showInvoiceSendViaInfo).toBeTruthy();
            expect(one.productTourStatus?.showInvoicingCustomisationSettingsWelcome).toBeTruthy();
            expect(one.productTourStatus?.showNotificationsWelcome).toBeFalsy();
            expect(one.productTourStatus?.showInvoiceApril).toBeFalsy();
            expect(one.productTourStatus?.showTapToPayMayJune).toBeFalsy();
            expect(one.productTourStatus?.showCatalogueItemsWelcome).toBeTruthy();
            expect(one.productTourStatus?.showServiceChargesWelcome).toBeFalsy();
            const lastInvoke = lambdaService.lambda.send.mock.calls[0][0];
            expect(lastInvoke.FunctionName).toEqual(envService.cqrsCommandHandler);
            expect(JSON.parse(decodeBase64(lastInvoke.Payload))).toEqual({
              uri: 'Customer.Created',
              dto: {
                customerUuid: customer.customerUuid,
                entityUuid: customer.entityUuid,
                defaultEntityUuid: customer.entityUuid,
                identityUserId: customer.identityUserId,
                createdTime: expect.any(String),
                firstname: customer.firstname,
                middlename: customer.middlename,
                lastname: customer.lastname,
                dob: customer.dob,
                email: customer.email,
                role: customer.role,
                director: false,
                secretary: false,
                ceo: false,
                shareholder: false,
                beneficialOwner: false,
                beneficialOwnerAlt: false,
                beneficiary: false,
                partner: false,
                trustee: false,
                settlor: false,
                generalContact: false,
                financialContact: false,
                type: EntityType.INDIVIDUAL,
                companyTrustName: customer.companyTrustName,
                abn: customer.abn,
                documents: {} as DocumentVerificationRequestedPayload,
                chair: false,
                treasurer: false,
                governmentRole: customer.governmentRole,
                idv: {
                  status: 'REQUIRED',
                },
                safeharbour: {
                  status: 'REQUIRED',
                },
                screening: {
                  status: ScreeningStatus.NOT_REQUIRED,
                },
                registeringIndividual: false,
                permissions: {
                  allowDiscountManagement: false,
                  allowItemManagement: false,
                  allowXeroPaymentServices: false,
                  allowZellerInvoices: false,
                },
                productTourStatus: {
                  showAdminMerchantPortalWelcome: false,
                  showOnboardingShop: false,
                  showInvoiceInstructions: true,
                  showInvoicesWelcome: false,
                  showItemsWelcome: true,
                  showItemInstructions: true,
                  showInvoicesCustomisationWelcome: true,
                  showInvoicesScheduleSendWelcome: true,
                  showInvoicesSendBySmsWelcome: true,
                  showInvoiceSendViaInfo: true,
                  showInvoicingCustomisationSettingsWelcome: true,
                  showNotificationsWelcome: false,
                  showTapToPayInstructions: false,
                  showTapToPayMayJune: false,
                  showSavingsAccountWelcome: true,
                  showSavingsAccountMarch: false,
                  showSavingsAccountMay: false,
                  showCorporateCardsMayOffer: false,
                  showCorporateCardsWalkthrough: true,
                  showCorporateCardsSettingsWalkthrough: false,
                  showCustomScreensaverPromo: false,
                  showCorporateCardsAdminWelcome: false,
                  showInvoiceApril: false,
                  showCatalogueItemsWelcome: true,
                  profileAvatarWalkthrough: false,
                  showServiceChargesWelcome: false,
                } as ProductTourStatus,
                icon: customer.icon,
                kyc: customer.kyc,
              },
            });
            done();
          },
        );
      });
    });

    it('should throw error if create a customer with existing email', async () => {
      await new Promise<void>(async (done) => {
        const customer: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
        createCustomerHandler(
          {
            detail: customer,
          },
          context,
          async () => {
            mockLambdaInvoke(lambdaService);
            createCustomerHandler(
              {
                detail: {
                  ...customer,
                  customerUuid: uuidv4(),
                },
              },
              context,
              async (error) => {
                expect(error).toEqual(new CustomerEmailAlreadyExistsError('Cant create customer with the same email.'));
                expect(lambdaService.lambda.send).toBeCalledTimes(0);
                done();
              },
            );
          },
        );
      });
    });

    it('should be able to create new customer using email from deleted customer', async () => {
      await new Promise<void>(async (done) => {
        mockLambdaInvoke(lambdaService);
        const customer: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
        // create new customer
        await new Promise((resolve) => {
          createCustomerHandler({ detail: customer }, context, async (error) => {
            expect(error).toBeNull();
            resolve(null);
          });
        });

        await new Promise((resolve) => {
          deleteCustomerHandler({ path: { id: customer.customerUuid } }, context, async (error) => {
            expect(error).toBeNull();
            resolve(null);
          });
        });

        const newCustomer = {
          ...customer,
          customerUuid: uuidv4(),
        };
        await createCustomerHandler({ detail: newCustomer }, context, async (error) => {
          expect(error).toBeNull();
          const { app } = context;
          const service: CustomerService = app.find(CustomerService);
          const customers = await service.getRepository().find({
            email: customer.email,
          });
          // should have 2 rows with the same email address
          expect(customers).toHaveLength(2);
          expect(customers.find((c) => c.status === Status.DELETED)?.customerUuid).toEqual(customer.customerUuid);
          expect(customers.find((c) => c.status === Status.ACTIVE)?.customerUuid).toEqual(newCustomer.customerUuid);
          done();
        });
      });
    });

    it('should be able to handle update customer event', async () => {
      await new Promise<void>(async (done) => {
        const newCustomer = createCustomerRequestedDto();
        newCustomer.role = CustomerRole.MANAGER;
        await createCustomer(newCustomer);
        mockLambdaInvoke(lambdaService);
        const customer = createCustomerUpdatedDto();
        customer.customerUuid = newCustomer.customerUuid;
        customer.entityUuid = newCustomer.entityUuid;
        customer.hubspotContactId = uuidv4();
        customer.productTourStatus = {};
        customer.address = {
          suburb: uuidv4(),
          country: uuidv4(),
        };
        customer.marketingModalName = 'MODAL_1';
        customer.isInvitationPending = false;

        (auth0Service.getUserByEmail as jest.Mock).mockResolvedValue([{ user_id: 'auth0_id' }]);

        updateCustomerHandler(
          {
            id: customer.customerUuid,
            detail: customer,
            headers,
          },
          context,
          async () => {
            const { app } = context;
            const service = app.find(CustomerService);
            const ceService = app.find(CustomerEntityService);
            const one: Customer = await service.findOne(customer.customerUuid);
            const ceOne = await ceService.findCustomerEntity(customer.customerUuid, customer.entityUuid);
            expect(one).toBeDefined();
            expect(one?.customerUuid).toBe(customer.customerUuid);
            // first name is the updated one to test
            expect(one?.firstname).toBe(customer.firstname);
            // lastname is the not updated one to test
            expect(one?.lastname).toBe(newCustomer.lastname);
            expect(one?.phoneVerified).toBe(customer.phoneVerified);
            expect(one?.middlename).toBe(customer.middlename);
            expect(one?.email).toBe(customer.email);
            expect(one?.emailVerified).toBe(customer.emailVerified);
            expect(one?.phone).toBe(customer.phone);
            expect(ceOne?.role).toBe(customer.role);
            expect(ceOne?.director).toBe(customer.director);
            expect(ceOne?.ceo).toBe(customer.ceo);
            expect(ceOne?.beneficialOwner).toBe(customer.beneficialOwner);
            expect(ceOne?.beneficialOwnerAlt).toBe(customer.beneficialOwnerAlt);
            expect(ceOne?.beneficiary).toBe(customer.beneficiary);
            expect(ceOne?.partner).toBe(customer.partner);
            expect(ceOne?.trustee).toBe(customer.trustee);
            expect(ceOne?.settlor).toBe(customer.settlor);
            expect(ceOne?.generalContact).toBe(customer.generalContact);
            expect(ceOne?.financialContact).toBe(customer.financialContact);
            expect(one?.type).toBe(customer.type);
            expect(one?.companyTrustName).toBe(customer.companyTrustName);
            expect(one?.abn).toBe(customer.abn);
            expect(one?.dob).toBe(customer.dob);
            expect(ceOne?.marketingModalSeenList).toEqual(['MODAL_1']);
            expect(one.screening).toStrictEqual({ status: ScreeningStatus.REQUIRED });
            expect(one?.createdTime).toBeDefined();
            expect(one?.updatedTime).toBeDefined();
            expect(one?.address).toEqual(customer.address);
            expect(one?.productTourStatus?.showOnboardingShop).toBe(false);
            expect(one?.productTourStatus?.showAdminMerchantPortalWelcome).toBe(true);
            expect(one?.productTourStatus?.showTapToPayInstructions).toBe(false);
            expect(one?.productTourStatus?.showTapToPayMayJune).toBe(false);
            expect(one?.productTourStatus?.showCustomScreensaverPromo).toBe(false);
            expect(one?.productTourStatus?.showSavingsAccountWelcome).toBe(true);
            expect(one?.productTourStatus?.showSavingsAccountMarch).toBe(false);
            expect(one?.productTourStatus?.showSavingsAccountMay).toBe(false);
            expect(one?.productTourStatus?.showCorporateCardsMayOffer).toBe(false);
            expect(one?.productTourStatus?.showCorporateCardsWalkthrough).toBe(true);
            expect(one?.productTourStatus?.showCorporateCardsSettingsWalkthrough).toBe(true);
            expect(one?.productTourStatus?.showCorporateCardsAdminWelcome).toBe(false);
            expect(one?.productTourStatus?.showCatalogueItemsWelcome).toBe(true);
            expect(one?.productTourStatus?.profileAvatarWalkthrough).toBe(false);
            expect(ceOne?.invitationPending).toEqual(customer.isInvitationPending);
            expect(one?.icon).toEqual(customer.icon);
            expect(one?.kyc).toEqual(customer.kyc);

            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(3);
            expect(getMockLambdaPayload(lambdaService, 0)).toEqual({
              uri: 'Customer.HubspotObjectIdUpdated',
              dto: {
                customerUuid: customer.customerUuid,
                hubspotContactId: customer.hubspotContactId,
              },
            });
            expect(getMockLambdaPayload(lambdaService, 1)).toEqual({
              uri: 'Customer.Updated',
              dto: {
                customerUuid: customer.customerUuid,
                entityUuid: customer.entityUuid,
                firstname: customer.firstname,
                middlename: customer.middlename,
                email: customer.email,
                emailVerified: customer.emailVerified,
                phone: customer.phone,
                phoneVerified: customer.phoneVerified,
                role: customer.role,
                director: customer.director,
                secretary: customer.secretary,
                ceo: customer.ceo,
                screening: {
                  status: 'REQUIRED',
                },
                beneficialOwner: customer.beneficialOwner,
                beneficialOwnerAlt: customer.beneficialOwnerAlt,
                beneficiary: customer.beneficiary,
                partner: customer.partner,
                trustee: customer.trustee,
                settlor: customer.settlor,
                shareholder: customer.shareholder,
                generalContact: customer.generalContact,
                financialContact: customer.financialContact,
                type: customer.type,
                companyTrustName: customer.companyTrustName,
                chair: customer.chair,
                treasurer: customer.treasurer,
                governmentRole: customer.governmentRole,
                abn: customer.abn,
                dob: customer.dob,
                isInvitationPending: customer.isInvitationPending,
                address: {
                  suburb: customer.address?.suburb,
                  country: customer.address?.country,
                },
                productTourStatus: {
                  showAdminMerchantPortalWelcome: true,
                  showCorporateCardsSettingsWalkthrough: true,
                },
                permissions: {
                  allowDiscountManagement: true,
                  allowItemManagement: true,
                  allowXeroPaymentServices: true,
                  allowZellerInvoices: true,
                },
                icon: customer.icon,
                kyc: customer.kyc,
              },
              mutationAttribution,
            });
            expect(getMockLambdaPayload(lambdaService, 2)).toEqual({
              uri: 'Customer.MarketingUpdated',
              dto: {
                customerUuid: customer.customerUuid,
                entityUuid: customer.entityUuid,
                marketingModalSeenList: ['MODAL_1'],
              },
            });
            expect(auth0Service.updateUserInfo).toBeCalledWith('auth0_id', {
              email: customer.email,
              email_verified: false,
              family_name: newCustomer.lastname,
              given_name: customer.firstname,
              name: `${customer.firstname} ${newCustomer.lastname}`,
              nickname: customer.nickname,
              user_metadata: {
                role: customer.role,
                invitationPending: customer.isInvitationPending,
                kycStatus: customer.kyc?.status,
              },
            });

            done();
          },
        );
      });
    });

    it('should throw error if update a customer with existing email', async () => {
      await new Promise<void>(async (done) => {
        const existingCustomer: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
        await createCustomer(existingCustomer);

        mockLambdaInvoke(lambdaService);
        updateCustomerHandler(
          {
            detail: {
              ...createCustomerRequestedDto(),
              email: existingCustomer.email,
            },
          },
          context,
          async (error) => {
            expect(error).toEqual(new CustomerEmailAlreadyExistsError('Customer with the same email already exists.'));
            expect(lambdaService.lambda.send).toBeCalledTimes(0);
            done();
          },
        );
      });
    });

    it('should be able to update customer using email from deleted customer', async () => {
      await new Promise<void>(async (done) => {
        mockLambdaInvoke(lambdaService);
        const customerToDelete: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
        // create customer for deletion
        await new Promise((resolve) => {
          createCustomerHandler({ detail: customerToDelete }, context, async (error) => {
            expect(error).toBeNull();
            resolve(null);
          });
        });
        await new Promise((resolve) => {
          deleteCustomerHandler({ path: { id: customerToDelete.customerUuid } }, context, async (error) => {
            expect(error).toBeNull();
            resolve(null);
          });
        });

        const customerToUpdate: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
        await new Promise((resolve) => {
          createCustomerHandler({ detail: customerToUpdate }, context, async (error) => {
            expect(error).toBeNull();
            resolve(null);
          });
        });

        const customerUpdateDto = {
          customerUuid: customerToUpdate.customerUuid,
          entityUuid: customerToUpdate.entityUuid,
          email: customerToDelete.email,
        };
        await updateCustomerHandler(
          { id: customerUpdateDto.customerUuid, detail: customerUpdateDto },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const service: CustomerService = app.find(CustomerService);
            const customers = await service.getRepository().find({
              email: customerToDelete.email,
            });
            // should have 2 rows with the same email address
            expect(customers).toHaveLength(2);
            expect(customers.find((c) => c.status === Status.DELETED)?.customerUuid).toEqual(
              customerToDelete.customerUuid,
            );
            expect(customers.find((c) => c.status === Status.ACTIVE)?.customerUuid).toEqual(
              customerToUpdate.customerUuid,
            );
            done();
          },
        );
      });
    });

    it('should be able to handle update customer event with just { isInvitationPending }', async () => {
      await new Promise<void>(async (done) => {
        const customer = createCustomerRequestedDto();
        customer.role = CustomerRole.MANAGER;
        await createCustomer(customer);
        mockLambdaInvoke(lambdaService);
        const customerUpdateDto = {
          customerUuid: customer.customerUuid,
          entityUuid: customer.entityUuid,
          isInvitationPending: false,
        };

        (auth0Service.getUserByEmail as jest.Mock).mockResolvedValue([{ user_id: 'auth0_id' }]);

        updateCustomerHandler(
          {
            id: customer.customerUuid,
            detail: customerUpdateDto,
          },
          context,
          async () => {
            expect(auth0Service.updateUserInfo).toBeCalledWith('auth0_id', {
              family_name: customer.lastname,
              given_name: customer.firstname,
              name: `${customer.firstname} ${customer.lastname}`,
              nickname: customer.nickname,
              user_metadata: {
                invitationPending: customerUpdateDto.isInvitationPending,
              },
            });

            done();
          },
        );
      });
    });

    it('should be able to handle update customer event and set product status flag through updateCustomer event', async () => {
      await new Promise<void>(async (done) => {
        const newCustomer = createCustomerRequestedDto();
        newCustomer.role = CustomerRole.ADMIN;
        await createCustomer(newCustomer);
        mockLambdaInvoke(lambdaService);
        const customer = createCustomerUpdatedDto();
        customer.customerUuid = newCustomer.customerUuid;
        customer.entityUuid = newCustomer.entityUuid;
        customer.hubspotContactId = uuidv4();
        customer.productTourStatus = {
          showOnboardingShop: true,
          showAdminMerchantPortalWelcome: false,
          showInvoicesWelcome: true,
          showInvoiceApril: true,
          showItemsWelcome: false,
          showItemInstructions: false,
          showInvoicesCustomisationWelcome: false,
          showInvoicesScheduleSendWelcome: false,
          showInvoicesSendBySmsWelcome: false,
          showInvoiceSendViaInfo: false,
          showInvoicingCustomisationSettingsWelcome: false,
          showNotificationsWelcome: false,
          showTapToPayInstructions: true,
          showTapToPayMayJune: true,
          showCustomScreensaverPromo: true,
          showSavingsAccountWelcome: false,
          showSavingsAccountMarch: true,
          showSavingsAccountMay: true,
          showCorporateCardsMayOffer: true,
          showCorporateCardsWalkthrough: false,
          showCorporateCardsSettingsWalkthrough: false,
          showCorporateCardsAdminWelcome: true,
          showCatalogueItemsWelcome: false,
          profileAvatarWalkthrough: true,
          showServiceChargesWelcome: true,
        };
        customer.address = {
          suburb: uuidv4(),
          country: uuidv4(),
        };
        updateCustomerHandler(
          {
            id: customer.customerUuid,
            detail: customer,
            headers,
          },
          context,
          async () => {
            const { app } = context;
            const service = app.find(CustomerService);
            const ceService = app.find(CustomerEntityService);
            const one: Customer = await service.findOne(customer.customerUuid);
            const ceOne = await ceService.findCustomerEntity(customer.customerUuid, customer.entityUuid);
            expect(one).toBeDefined();
            expect(one?.customerUuid).toBe(customer.customerUuid);
            // first name is the updated one to test
            expect(one?.firstname).toBe(customer.firstname);
            // lastname is the not updated one to test
            expect(one?.lastname).toBe(newCustomer.lastname);
            expect(one?.phoneVerified).toBe(customer.phoneVerified);
            expect(one?.middlename).toBe(customer.middlename);
            expect(one?.email).toBe(customer.email);
            expect(one?.emailVerified).toBe(customer.emailVerified);
            expect(one?.phone).toBe(customer.phone);
            expect(ceOne?.role).toBe(customer.role);
            expect(ceOne?.director).toBe(customer.director);
            expect(ceOne?.ceo).toBe(customer.ceo);
            expect(ceOne?.beneficialOwner).toBe(customer.beneficialOwner);
            expect(ceOne?.beneficialOwnerAlt).toBe(customer.beneficialOwnerAlt);
            expect(ceOne?.beneficiary).toBe(customer.beneficiary);
            expect(ceOne?.partner).toBe(customer.partner);
            expect(ceOne?.trustee).toBe(customer.trustee);
            expect(ceOne?.settlor).toBe(customer.settlor);
            expect(ceOne?.generalContact).toBe(customer.generalContact);
            expect(ceOne?.financialContact).toBe(customer.financialContact);
            expect(one?.type).toBe(customer.type);
            expect(one?.companyTrustName).toBe(customer.companyTrustName);
            expect(one?.abn).toBe(customer.abn);
            expect(one?.dob).toBe(customer.dob);
            expect(one?.createdTime).toBeDefined();
            expect(one?.updatedTime).toBeDefined();
            expect(one?.address).toEqual(customer.address);
            expect(one.screening).toStrictEqual({ status: ScreeningStatus.REQUIRED });
            expect(one?.productTourStatus?.showOnboardingShop).toBe(true);
            expect(one?.productTourStatus?.showAdminMerchantPortalWelcome).toBe(false);
            expect(one?.productTourStatus?.showInvoicesWelcome).toBe(true);
            expect(one?.productTourStatus?.showItemsWelcome).toBe(false);
            expect(one?.productTourStatus?.showItemInstructions).toBe(false);
            expect(one?.productTourStatus?.showTapToPayInstructions).toBe(true);
            expect(one?.productTourStatus?.showTapToPayMayJune).toBe(true);
            expect(one?.productTourStatus?.showCustomScreensaverPromo).toBe(true);
            expect(one?.productTourStatus?.showSavingsAccountWelcome).toBe(false);
            expect(one?.productTourStatus?.showSavingsAccountMarch).toBe(true);
            expect(one?.productTourStatus?.showSavingsAccountMay).toBe(true);
            expect(one?.productTourStatus?.showCorporateCardsMayOffer).toBe(true);
            expect(one?.productTourStatus?.showCorporateCardsWalkthrough).toBe(false);
            expect(one?.productTourStatus?.showCorporateCardsSettingsWalkthrough).toBe(false);
            expect(one?.productTourStatus?.showCorporateCardsAdminWelcome).toBe(true);
            expect(one?.productTourStatus?.showInvoiceApril).toBe(true);
            expect(one?.productTourStatus?.showCatalogueItemsWelcome).toBe(false);
            expect(one?.productTourStatus?.profileAvatarWalkthrough).toBe(true);
            expect(one?.kyc).toEqual(customer.kyc);
            expect(one?.productTourStatus?.showServiceChargesWelcome).toBe(true);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(2);
            const [customerHubspotIdUpdated, customerUpdated] = lambdaService.lambda.send.mock.calls;
            const customerUpdatedPayload = customerUpdated[0].Payload;
            const customerHubspotIdUpdatedPayload = customerHubspotIdUpdated[0].Payload;
            const functionName = customerUpdated[0].FunctionName;
            expect(functionName).toEqual(envService.cqrsCommandHandler);
            expect(JSON.parse(decodeBase64(customerHubspotIdUpdatedPayload))).toEqual({
              uri: 'Customer.HubspotObjectIdUpdated',
              dto: {
                customerUuid: customer.customerUuid,
                hubspotContactId: customer.hubspotContactId,
              },
            });
            expect(JSON.parse(decodeBase64(customerUpdatedPayload))).toEqual({
              uri: 'Customer.Updated',
              dto: {
                customerUuid: customer.customerUuid,
                entityUuid: customer.entityUuid,
                firstname: customer.firstname,
                middlename: customer.middlename,
                email: customer.email,
                emailVerified: customer.emailVerified,
                phone: customer.phone,
                phoneVerified: customer.phoneVerified,
                role: customer.role,
                director: customer.director,
                secretary: customer.secretary,
                ceo: customer.ceo,
                beneficialOwner: customer.beneficialOwner,
                beneficialOwnerAlt: customer.beneficialOwnerAlt,
                screening: {
                  status: 'REQUIRED',
                },
                beneficiary: customer.beneficiary,
                partner: customer.partner,
                trustee: customer.trustee,
                settlor: customer.settlor,
                shareholder: customer.shareholder,
                generalContact: customer.generalContact,
                financialContact: customer.financialContact,
                type: customer.type,
                companyTrustName: customer.companyTrustName,
                chair: customer.chair,
                treasurer: customer.treasurer,
                governmentRole: customer.governmentRole,
                abn: customer.abn,
                dob: customer.dob,
                address: {
                  suburb: customer.address?.suburb,
                  country: customer.address?.country,
                },
                productTourStatus: {
                  showOnboardingShop: true,
                  showAdminMerchantPortalWelcome: false,
                  showInvoicesWelcome: true,
                  showItemsWelcome: false,
                  showItemInstructions: false,
                  showInvoicesCustomisationWelcome: false,
                  showInvoicesScheduleSendWelcome: false,
                  showInvoicesSendBySmsWelcome: false,
                  showInvoiceApril: true,
                  showInvoiceSendViaInfo: false,
                  showInvoicingCustomisationSettingsWelcome: false,
                  showNotificationsWelcome: false,
                  showTapToPayInstructions: true,
                  showTapToPayMayJune: true,
                  showCustomScreensaverPromo: true,
                  showSavingsAccountWelcome: false,
                  showSavingsAccountMarch: true,
                  showSavingsAccountMay: true,
                  showCorporateCardsMayOffer: true,
                  showCorporateCardsWalkthrough: false,
                  showCorporateCardsSettingsWalkthrough: false,
                  showCorporateCardsAdminWelcome: true,
                  showCatalogueItemsWelcome: false,
                  profileAvatarWalkthrough: true,
                  showServiceChargesWelcome: true,
                },
                icon: customer.icon,
                kyc: customer.kyc,
              },
              mutationAttribution,
            });
            done();
          },
        );
      });
    });
    it('should be able to handle update customer event and set as PAH through updateCustomer event', async () => {
      await new Promise<void>(async (done) => {
        const newCustomer = createCustomerRequestedDto();
        newCustomer.role = CustomerRole.ADMIN;
        await createCustomer(newCustomer);
        mockLambdaInvoke(lambdaService);
        const customer = createCustomerUpdatedDto();
        customer.customerUuid = newCustomer.customerUuid;
        customer.entityUuid = newCustomer.entityUuid;
        customer.hubspotContactId = uuidv4();
        customer.productTourStatus = {
          showOnboardingShop: false,
          showAdminMerchantPortalWelcome: false,
          showInvoicesWelcome: true,
          showInvoiceApril: true,
          showItemsWelcome: false,
          showItemInstructions: false,
          showInvoicesCustomisationWelcome: false,
          showInvoicesScheduleSendWelcome: false,
          showInvoicesSendBySmsWelcome: false,
          showInvoiceSendViaInfo: false,
          showInvoicingCustomisationSettingsWelcome: false,
          showNotificationsWelcome: false,
          showTapToPayInstructions: true,
          showTapToPayMayJune: true,
          showCustomScreensaverPromo: true,
          showSavingsAccountWelcome: false,
          showSavingsAccountMarch: true,
          showSavingsAccountMay: true,
          showCorporateCardsMayOffer: true,
          showCorporateCardsWalkthrough: false,
          showCorporateCardsAdminWelcome: true,
          showCatalogueItemsWelcome: false,
          profileAvatarWalkthrough: true,
          showServiceChargesWelcome: true,
        };
        customer.address = {
          suburb: uuidv4(),
          country: uuidv4(),
        };
        updateCustomerHandler(
          {
            id: customer.customerUuid,
            detail: customer,
            headers,
          },
          context,
          async () => {
            const { app } = context;
            const service = app.find(CustomerService);
            const ceService = app.find(CustomerEntityService);
            const one: Customer = await service.findOne(customer.customerUuid);
            const ceOne = await ceService.findCustomerEntity(customer.customerUuid, customer.entityUuid);
            expect(one).toBeDefined();
            expect(one?.customerUuid).toBe(customer.customerUuid);
            // first name is the updated one to test
            expect(one?.firstname).toBe(customer.firstname);
            // lastname is the not updated one to test
            expect(one?.lastname).toBe(newCustomer.lastname);
            expect(one?.phoneVerified).toBe(customer.phoneVerified);
            expect(one?.middlename).toBe(customer.middlename);
            expect(one?.email).toBe(customer.email);
            expect(one?.emailVerified).toBe(customer.emailVerified);
            expect(one?.phone).toBe(customer.phone);
            expect(ceOne?.role).toBe(customer.role);
            expect(ceOne?.director).toBe(customer.director);
            expect(ceOne?.ceo).toBe(customer.ceo);
            expect(ceOne?.beneficialOwner).toBe(customer.beneficialOwner);
            expect(ceOne?.beneficialOwnerAlt).toBe(customer.beneficialOwnerAlt);
            expect(ceOne?.beneficiary).toBe(customer.beneficiary);
            expect(ceOne?.partner).toBe(customer.partner);
            expect(ceOne?.trustee).toBe(customer.trustee);
            expect(ceOne?.settlor).toBe(customer.settlor);
            expect(ceOne?.generalContact).toBe(customer.generalContact);
            expect(ceOne?.financialContact).toBe(customer.financialContact);
            expect(one?.type).toBe(customer.type);
            expect(one?.companyTrustName).toBe(customer.companyTrustName);
            expect(one?.abn).toBe(customer.abn);
            expect(one?.dob).toBe(customer.dob);
            expect(one?.createdTime).toBeDefined();
            expect(one?.updatedTime).toBeDefined();
            expect(one?.address).toEqual(customer.address);
            expect(one.screening).toStrictEqual({ status: ScreeningStatus.REQUIRED });
            expect(one?.productTourStatus?.showOnboardingShop).toBe(false);
            expect(one?.productTourStatus?.showAdminMerchantPortalWelcome).toBe(false);
            expect(one?.productTourStatus?.showInvoicesWelcome).toBe(true);
            expect(one?.productTourStatus?.showItemsWelcome).toBe(false);
            expect(one?.productTourStatus?.showItemInstructions).toBe(false);
            expect(one?.productTourStatus?.showTapToPayInstructions).toBe(true);
            expect(one?.productTourStatus?.showTapToPayMayJune).toBe(true);
            expect(one?.productTourStatus?.showCustomScreensaverPromo).toBe(true);
            expect(one?.productTourStatus?.showSavingsAccountWelcome).toBe(false);
            expect(one?.productTourStatus?.showSavingsAccountMarch).toBe(true);
            expect(one?.productTourStatus?.showSavingsAccountMay).toBe(true);
            expect(one?.productTourStatus?.showCorporateCardsMayOffer).toBe(true);
            expect(one?.productTourStatus?.showCorporateCardsWalkthrough).toBe(false);
            expect(one?.productTourStatus?.showCorporateCardsAdminWelcome).toBe(true);
            expect(one?.productTourStatus?.showInvoiceApril).toBe(true);
            expect(one?.productTourStatus?.showCatalogueItemsWelcome).toBe(false);
            expect(one?.productTourStatus?.profileAvatarWalkthrough).toBe(true);
            expect(one?.kyc).toEqual(customer.kyc);
            expect(one?.productTourStatus?.showServiceChargesWelcome).toBe(true);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(2);
            const [customerHubspotIdUpdated, customerUpdatedResp] = lambdaService.lambda.send.mock.calls;
            const customerUpdatedPayload = customerUpdatedResp[0].Payload;
            const customerHubspotIdUpdatedPayload = customerHubspotIdUpdated[0].Payload;
            const functionName = customerUpdatedResp[0].FunctionName;
            expect(JSON.parse(decodeBase64(customerHubspotIdUpdatedPayload))).toEqual({
              uri: 'Customer.HubspotObjectIdUpdated',
              dto: {
                customerUuid: customer.customerUuid,
                hubspotContactId: customer.hubspotContactId,
              },
            });
            expect(functionName).toEqual(envService.cqrsCommandHandler);
            expect(JSON.parse(decodeBase64(customerUpdatedPayload))).toEqual({
              uri: 'Customer.Updated',
              dto: {
                customerUuid: customer.customerUuid,
                entityUuid: customer.entityUuid,
                firstname: customer.firstname,
                middlename: customer.middlename,
                email: customer.email,
                emailVerified: customer.emailVerified,
                phone: customer.phone,
                phoneVerified: customer.phoneVerified,
                role: customer.role,
                director: customer.director,
                secretary: customer.secretary,
                ceo: customer.ceo,
                beneficialOwner: customer.beneficialOwner,
                beneficialOwnerAlt: customer.beneficialOwnerAlt,
                screening: {
                  status: 'REQUIRED',
                },
                beneficiary: customer.beneficiary,
                partner: customer.partner,
                trustee: customer.trustee,
                settlor: customer.settlor,
                shareholder: customer.shareholder,
                generalContact: customer.generalContact,
                financialContact: customer.financialContact,
                type: customer.type,
                companyTrustName: customer.companyTrustName,
                chair: customer.chair,
                treasurer: customer.treasurer,
                governmentRole: customer.governmentRole,
                abn: customer.abn,
                dob: customer.dob,
                address: expect.any(Object),
                productTourStatus: expect.any(Object),
                icon: customer.icon,
                kyc: customer.kyc,
              },
              mutationAttribution: expect.any(Object),
            });
            done();
          },
        );
      });
    });

    it('should be able to handle update customer event and set product status flag', async () => {
      await new Promise<void>(async (done) => {
        const newCustomer = createCustomerRequestedDto();
        newCustomer.role = CustomerRole.ADMIN;
        await createCustomer(newCustomer);
        mockLambdaInvoke(lambdaService);
        const customer = createCustomerUpdatedDto();
        customer.role = CustomerRole.MANAGER;
        customer.customerUuid = newCustomer.customerUuid;
        customer.entityUuid = newCustomer.entityUuid;
        customer.hubspotContactId = uuidv4();
        customer.address = {
          suburb: uuidv4(),
          country: uuidv4(),
        };
        updateCustomerHandler(
          {
            id: customer.customerUuid,
            detail: customer,
            headers,
          },
          context,
          async () => {
            const { app } = context;
            const service = app.find(CustomerService);
            const ceService = app.find(CustomerEntityService);
            const one: Customer = await service.findOne(customer.customerUuid);
            const ceOne = await ceService.findCustomerEntity(customer.customerUuid, customer.entityUuid);
            expect(one).toBeDefined();
            expect(one?.customerUuid).toBe(customer.customerUuid);
            // first name is the updated one to test
            expect(one?.firstname).toBe(customer.firstname);
            // lastname is the not updated one to test
            expect(one?.lastname).toBe(newCustomer.lastname);
            expect(one?.phoneVerified).toBe(customer.phoneVerified);
            expect(one?.middlename).toBe(customer.middlename);
            expect(one?.email).toBe(customer.email);
            expect(one?.emailVerified).toBe(customer.emailVerified);
            expect(one?.phone).toBe(customer.phone);
            expect(ceOne?.role).toBe(customer.role);
            expect(ceOne?.director).toBe(customer.director);
            expect(ceOne?.ceo).toBe(customer.ceo);
            expect(ceOne?.beneficialOwner).toBe(customer.beneficialOwner);
            expect(ceOne?.beneficialOwnerAlt).toBe(customer.beneficialOwnerAlt);
            expect(ceOne?.beneficiary).toBe(customer.beneficiary);
            expect(ceOne?.partner).toBe(customer.partner);
            expect(ceOne?.trustee).toBe(customer.trustee);
            expect(ceOne?.settlor).toBe(customer.settlor);
            expect(ceOne?.generalContact).toBe(customer.generalContact);
            expect(ceOne?.financialContact).toBe(customer.financialContact);
            expect(one?.type).toBe(customer.type);
            expect(one?.companyTrustName).toBe(customer.companyTrustName);
            expect(one?.abn).toBe(customer.abn);
            expect(one?.dob).toBe(customer.dob);
            expect(one?.createdTime).toBeDefined();
            expect(one?.updatedTime).toBeDefined();
            expect(one?.address).toEqual(customer.address);
            expect(one.screening).toStrictEqual({ status: ScreeningStatus.REQUIRED });
            expect(one?.productTourStatus?.showAdminMerchantPortalWelcome).toBe(false);
            expect(one?.kyc).toEqual(customer.kyc);
            expect(lambdaService.lambda.send).toHaveBeenCalledTimes(2);
            const [customerHubspotIdUpdatedResp, customerUpdatedResp] = lambdaService.lambda.send.mock.calls;
            const customerUpdatedPayload = customerUpdatedResp[0].Payload;
            const customerHubspotIdUpdatedPayload = customerHubspotIdUpdatedResp[0].Payload;
            const functionName = customerUpdatedResp[0].FunctionName;
            expect(functionName).toEqual(envService.cqrsCommandHandler);
            expect(JSON.parse(decodeBase64(customerHubspotIdUpdatedPayload))).toEqual({
              uri: 'Customer.HubspotObjectIdUpdated',
              dto: {
                customerUuid: customer.customerUuid,
                hubspotContactId: customer.hubspotContactId,
              },
            });
            expect(JSON.parse(decodeBase64(customerUpdatedPayload))).toEqual({
              uri: 'Customer.Updated',
              dto: {
                customerUuid: customer.customerUuid,
                entityUuid: customer.entityUuid,
                firstname: customer.firstname,
                middlename: customer.middlename,
                email: customer.email,
                emailVerified: customer.emailVerified,
                phone: customer.phone,
                phoneVerified: customer.phoneVerified,
                role: customer.role,
                director: customer.director,
                secretary: customer.secretary,
                ceo: customer.ceo,
                beneficialOwner: customer.beneficialOwner,
                beneficialOwnerAlt: customer.beneficialOwnerAlt,
                screening: {
                  status: 'REQUIRED',
                },
                beneficiary: customer.beneficiary,
                partner: customer.partner,
                trustee: customer.trustee,
                settlor: customer.settlor,
                shareholder: customer.shareholder,
                generalContact: customer.generalContact,
                financialContact: customer.financialContact,
                type: customer.type,
                companyTrustName: customer.companyTrustName,
                chair: customer.chair,
                treasurer: customer.treasurer,
                governmentRole: customer.governmentRole,
                abn: customer.abn,
                dob: customer.dob,
                address: {
                  suburb: customer.address?.suburb,
                  country: customer.address?.country,
                },
                permissions: {
                  allowDiscountManagement: false,
                  allowItemManagement: false,
                  allowXeroPaymentServices: false,
                  allowZellerInvoices: false,
                },
                productTourStatus: {
                  showAdminMerchantPortalWelcome: false,
                  showCorporateCardsSettingsWalkthrough: false,
                },
                icon: customer.icon,
                kyc: customer.kyc,
              },
              mutationAttribution,
            });
            done();
          },
        );
      });
    });

    it('should throw error if entity not exists ', async () => {
      await new Promise<void>(async (done) => {
        const newCustomer = createCustomerRequestedDto();
        newCustomer.role = CustomerRole.ADMIN;
        await createCustomer(newCustomer);
        mockLambdaInvoke(lambdaService);
        customerEntityService.findEntityByUuid = jest.fn().mockResolvedValue(null);
        const customer = createCustomerUpdatedDto();
        customer.role = CustomerRole.MANAGER;
        customer.customerUuid = newCustomer.customerUuid;
        customer.entityUuid = newCustomer.entityUuid;
        customer.hubspotContactId = uuidv4();
        customer.address = {
          suburb: uuidv4(),
          country: uuidv4(),
        };
        updateCustomerHandler(
          {
            id: customer.customerUuid,
            detail: customer,
            headers,
          },
          context,
          async (error) => {
            expect(error).not.toBeNull();
            const { app } = context;
            const service = app.find(CustomerService);
            const ceService = app.find(CustomerEntityService);
            const one: Customer = await service.findOne(customer.customerUuid);
            const ceOne = await ceService.findCustomerEntity(customer.customerUuid, customer.entityUuid);
            expect(one).toBeDefined();
            expect(ceOne?.role).toBe(CustomerRole.ADMIN);
            done();
          },
        );
      });
    });

    it('should not allow updating a non existing customer', async () => {
      await new Promise<void>(async (done) => {
        const customer: CustomerUpdatedEventDto = {
          customerUuid: uuidv4(),
          entityUuid: uuidv4(),
          firstname: uuidv4(),
        };
        updateCustomerHandler(
          {
            id: customer.customerUuid,
            detail: customer,
          },
          context,
          async (error) => {
            expect(error).toBeDefined();
            const { app } = context;
            const service = app.find(CustomerService);
            const one = await service.findOne(customer.customerUuid);
            expect(one).not.toBeDefined();
            expect(lambdaService.lambda.send).toBeCalledTimes(0);
            done();
          },
        );
      });
    });

    it('should allow to update registering individual customer role', async () => {
      await new Promise<void>(async (done) => {
        const customer = createCustomerRequestedDto();
        customer.registeringIndividual = true;
        await createCustomer(customer);
        const update = {
          customerUuid: customer.customerUuid,
          entityUuid: customer.entityUuid,
          role: CustomerRole.MANAGER,
        };
        updateCustomerHandler(
          {
            id: customer.customerUuid,
            detail: update,
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const service = app.find(CustomerEntityService);
            const one = await service.findCustomerEntity(customer.customerUuid, customer.entityUuid);
            expect(one?.role).toEqual('MANAGER');
            done();
          },
        );
      });
    });

    it('should allow to update registering individual customer role to ADMIN', async () => {
      await new Promise<void>(async (done) => {
        const customer = createCustomerRequestedDto();
        customer.role = CustomerRole.MANAGER;
        customer.registeringIndividual = true;
        await new Promise((resolve) => {
          createCustomerHandler({ detail: customer }, context, async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const service = app.find(CustomerEntityService);
            const one = await service.findCustomerEntity(customer.customerUuid, customer.entityUuid);
            expect(one?.role).toEqual('MANAGER');
            resolve(null);
          });
        });
        updateCustomerHandler(
          {
            id: customer.customerUuid,
            detail: {
              customerUuid: customer.customerUuid,
              entityUuid: customer.entityUuid,
              role: CustomerRole.ADMIN,
            },
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const service = app.find(CustomerEntityService);
            const one = await service.findCustomerEntity(customer.customerUuid, customer.entityUuid);
            expect(one?.role).toEqual('ADMIN');
            done();
          },
        );
      });
    });

    it('should fail to update primary account holder customer role', async () => {
      await new Promise<void>(async (done) => {
        const customer = createCustomerRequestedDto();
        customerEntityService.findEntityByUuid = jest
          .fn()
          .mockResolvedValue({ primaryAccountHolder: customer.customerUuid });

        customer.registeringIndividual = true;
        await createCustomer(customer);
        const update = {
          customerUuid: customer.customerUuid,
          entityUuid: customer.entityUuid,
          role: CustomerRole.MANAGER,
        };

        updateCustomerHandler(
          {
            id: customer.customerUuid,
            detail: update,
          },
          context,
          async (error) => {
            expect(error).not.toBeNull();
            const { app } = context;
            const service = app.find(CustomerEntityService);
            const one = await service.findCustomerEntity(customer.customerUuid, customer.entityUuid);
            expect(one?.role).toEqual('ADMIN');
            done();
          },
        );
      });
    });

    it('should be able to delete customer', async () => {
      const customer: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
      await new Promise((resolve) => {
        createCustomerHandler(
          {
            detail: customer,
          },
          context,
          async () => {
            resolve(null);
          },
        );
      });
      mockLambdaInvoke(lambdaService);
      await new Promise((resolve) => {
        deleteCustomerHandler({ path: { id: customer.customerUuid }, headers }, context, async (error) => {
          expect(error).toBeNull();
          const { app } = context;
          const service = app.find(CustomerService);
          const ceService = app.find(CustomerEntityService);
          const one = await service.findOne(customer.customerUuid);
          const ceOne = await ceService.findOneWithOption({
            customerUuid: customer.customerUuid,
          });
          expect(one.status).toBe('DELETED');
          expect(one.customerUuid).toBe(customer.customerUuid);
          expect(one.firstname).toBe(customer.firstname);
          expect(one.lastname).toBe(customer.lastname);
          expect(one.middlename).toBe(customer.middlename);
          expect(one.email).toBe(customer.email);
          expect(ceOne.status).toBe('DELETED');
          expect(ceOne.role).toBe(customer.role);
          const eventUris = lambdaService.lambda.send.mock.calls.map((call: any) => JSON.parse(call[0].Payload).uri);
          expect(eventUris).toEqual(['CustomerEntity.Unlinked', 'Customer.Deleted']);
          expect(lambdaService.lambda.send).toHaveBeenCalledTimes(2);
          const invoke = lambdaService.lambda.send.mock.calls[1][0];
          expect(invoke.FunctionName).toEqual(envService.cqrsCommandHandler);
          expect(JSON.parse(decodeBase64(invoke.Payload))).toEqual({
            uri: 'Customer.Deleted',
            dto: { customerUuid: customer.customerUuid },
            mutationAttribution,
          });

          expect(ceOne.status).toBe('DELETED');
          resolve(null);
        });
      });

      // should not allow delete customer if it is already deleted
      await new Promise((resolve) => {
        deleteCustomerHandler({ path: { id: customer.customerUuid } }, context, async (error) => {
          expect((error as Error).message).toContain('[400]');
          resolve(null);
        });
      });
      // should not allow update customer if it is already deleted
      await new Promise((resolve) => {
        updateCustomerHandler({ id: customer.customerUuid }, context, async (error) => {
          expect((error as Error).message).toContain('[400]');
          resolve(null);
        });
      });
    });

    describe('delete customer with query runner', () => {
      let domicileLookupDb: DomicileLookupDb;

      beforeAll(async () => {
        domicileLookupDb = new DomicileLookupDb();
      });

      afterAll(() => {
        jest.restoreAllMocks();
      });

      it('should be able to delete customer and domicile record', async () => {
        const customer: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
        await new Promise((resolve) => {
          createCustomerHandler(
            {
              detail: customer,
            },
            context,
            async () => {
              resolve(null);
            },
          );
        });
        mockLambdaInvoke(lambdaService);
        await new Promise((resolve) => {
          deleteCustomerHandler({ path: { id: customer.customerUuid }, headers }, context, async (error) => {
            expect(error).toBeNull();
            const { app } = context;
            const service = app.find(CustomerService);
            const ceService = app.find(CustomerEntityService);
            const one = await service.findOne(customer.customerUuid);
            const ceOne = await ceService.findOneWithOption({
              customerUuid: customer.customerUuid,
            });
            expect(one.status).toBe('DELETED');
            expect(one.customerUuid).toBe(customer.customerUuid);
            expect(ceOne.status).toBe('DELETED');

            // check dynamodb record
            const result = await domicileLookupDb.getDomicileByCustomerUuid(customer.customerUuid);
            expect(result).toBe(null);
            resolve(null);
          });
        });
      });

      it('should be able to rollback postgres changes if domicile record delete fails', async () => {
        const deleteDomicileRecordSpy = jest.spyOn(DomicileLookupDb.prototype, 'deleteDomicileRecord');
        deleteDomicileRecordSpy.mockRejectedValue(new Error('DynamoDB delete failed'));

        const customer: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
        await new Promise((resolve) => {
          createCustomerHandler(
            {
              detail: customer,
            },
            context,
            async () => {
              resolve(null);
            },
          );
        });
        mockLambdaInvoke(lambdaService);
        await new Promise((resolve) => {
          deleteCustomerHandler({ path: { id: customer.customerUuid }, headers }, context, async (error: any) => {
            expect(error?.message).toContain('[500] Failed to delete domicile record');
            const { app } = context;
            const service = app.find(CustomerService);
            const ceService = app.find(CustomerEntityService);
            const one = await service.findOne(customer.customerUuid);
            const ceOne = await ceService.findOneWithOption({
              customerUuid: customer.customerUuid,
            });

            // Should not be deleted
            expect(one.status).toBe('ACTIVE');
            expect(one.customerUuid).toBe(customer.customerUuid);
            expect(ceOne.status).toBe('ACTIVE');

            // check dynamodb record
            const result = await domicileLookupDb.getDomicileByCustomerUuid(customer.customerUuid);
            expect(result).toBe(Domicile.AU);
            resolve(null);
          });
        });
      });

      it('should throw a ServerError when deleteCustomer fails due to DynamoDB domicile delete', async () => {
        const createDomicileRecordSpy = jest.spyOn(DomicileLookupDb.prototype, 'deleteDomicileRecord');
        createDomicileRecordSpy.mockRejectedValue(new Error('DynamoDB delete failed'));

        const customer: CustomerCreateRequestedEventDto = createCustomerRequestedDto();
        await createCustomer(customer);
        // should throw server error
        await expect(deleteCustomer(customer.customerUuid)).rejects.toThrow(ServerError);
      });
    });

    it('should not be able to delete a non existing customer', async () => {
      await new Promise((resolve) => {
        deleteCustomerHandler({ path: { id: uuidv4() } }, context, async (error) => {
          expect(error).toBeDefined();
          resolve(null);
        });
      });
    });

    it('should be able to call the Customer Screening Safe Guard Cron Job', async () => {
      await new Promise<void>(async (done) => {
        const customer = createCustomerRequestedDto();

        const getManagerSpy = jest.spyOn(typeorm, 'getManager').mockReturnValue({
          query: () => Promise.resolve([{ customerUuid: customer.customerUuid }]),
        } as unknown as typeorm.EntityManager);

        const originalFindOneWithOptionFn = customerService.findOneWithOption;
        customerService.findOneWithOption = jest.fn().mockResolvedValue(customer);

        const spy = jest.spyOn(console, 'log');
        expect(spy).not.toBeCalled();
        customerScreeningSafeGuardCronJobHandler(
          {
            domicile: Domicile.AU,
          },
          context,
          async (error) => {
            expect(error).toBeNull();
            expect(spy).toBeCalledWith('customerScreeningSafeGuardCronJob is called');

            getManagerSpy.mockRestore();
            spy.mockRestore();
            customerService.findOneWithOption = originalFindOneWithOptionFn;
            expect(DomicileService.getInstance().getDomicile()).toBe(Domicile.AU);

            done();
          },
        );
      });
    });

    it('should be able to call finalise kyc without an error', async () => {
      await new Promise<void>(async (done) => {
        const customer = createCustomerRequestedDto();
        await createCustomer(customer);
        mockLambdaInvoke(lambdaService);

        (auth0Service.getUserByEmail as jest.Mock).mockResolvedValue([{ user_id: 'auth0_id' }]);

        const dto = {
          customerUuid: customer.customerUuid,
          entityUuid: customer.entityUuid,
          kyc: { status: KYCStatus.RC_VERIFIED },
        };
        const spyUpdate = jest.spyOn(customerService, 'updateCustomer');
        finaliseKycHandler({ detail: { ...dto }, id: customer.customerUuid }, context, async (error) => {
          expect(error).toBeNull();
          expect(info).not.toHaveBeenCalledWith('finaliseKyc: screen request is being skipped.', dto.customerUuid);
          expect(spyUpdate).toBeCalledWith(dto);
          done();
        });
      });
    });

    it('should be able to call finalise kyc and skip screening without an error', async () => {
      await new Promise<void>(async (done) => {
        const customer = createCustomerRequestedDto();
        await createCustomer(customer);
        mockLambdaInvoke(lambdaService);

        (auth0Service.getUserByEmail as jest.Mock).mockResolvedValue([{ user_id: 'auth0_id' }]);

        const dto = {
          customerUuid: customer.customerUuid,
          entityUuid: customer.entityUuid,
          kyc: { status: KYCStatus.RC_VERIFIED },
        };
        const spyUpdate = jest.spyOn(customerService, 'updateCustomer');
        finaliseKycHandler(
          { detail: { ...dto, shouldRequestScreening: false }, id: customer.customerUuid },
          context,
          async (error) => {
            expect(error).toBeNull();
            expect(spyUpdate).toHaveBeenCalledWith(dto);
            expect(info).toHaveBeenCalledWith('finaliseKyc: screen request is being skipped.', dto.customerUuid);
            done();
          },
        );
      });
    });

    it('should be able to call finalise kyc with an error', async () => {
      await new Promise<void>(async (done) => {
        const customer = createCustomerRequestedDto();
        await createCustomer(customer);
        mockLambdaInvoke(lambdaService);

        (auth0Service.getUserByEmail as jest.Mock).mockResolvedValue([{ user_id: 'auth0_id' }]);

        const dto = {
          customerUuid: customer.customerUuid,
          entityUuid: customer.entityUuid,
          kyc: { status: KYCStatus.RC_VERIFIED },
        };

        customerService.generateCustomerScreeningRequestedEventDto = () => {
          return { request: null, err: true };
        };
        finaliseKycHandler({ detail: { ...dto }, id: customer.customerUuid }, context, async (error) => {
          expect(error).toBeNull();
          expect(warn).toHaveBeenCalledWith(
            `finaliseKyc: Cannot do screening, customer ${dto.customerUuid} doesnt have entity type.`,
            dto.customerUuid,
          );
          done();
        });
      });
    });

    // uplifted finaliseKyc
    describe('finaliseKycUpliftedHandler', () => {
      it('(uplift work) should be able to call finalise kyc without an error', async () => {
        await new Promise<void>(async (done) => {
          const customer = createCustomerRequestedDto();
          await createCustomer(customer);
          mockLambdaInvoke(lambdaService);

          (auth0Service.getUserByEmail as jest.Mock).mockResolvedValue([{ user_id: 'auth0_id' }]);

          const dto = {
            customerUuid: customer.customerUuid,
            entityUuid: customer.entityUuid,
          };

          const spyUpdate = jest.spyOn(customerService, 'updateCustomer');
          finaliseKycUpliftedHandler({ detail: { ...dto }, id: customer.customerUuid }, context, async (error) => {
            expect(error).toBeNull();
            expect(info).not.toHaveBeenCalledWith('finaliseKyc: screen request is being skipped.', dto.customerUuid);
            expect(spyUpdate).toHaveBeenCalledWith(
              new CustomerUpdatedEventDto({
                ...dto,
                kyc: {
                  status: KYCStatus.REVIEW,
                },
              }),
            );
            done();
          });
        });
      });

      it('(uplift work) should be able to call finalise kyc and skip screening without an error', async () => {
        await new Promise<void>(async (done) => {
          const customer = createCustomerRequestedDto();
          await createCustomer(customer);
          mockLambdaInvoke(lambdaService);

          (auth0Service.getUserByEmail as jest.Mock).mockResolvedValue([{ user_id: 'auth0_id' }]);

          const dto = {
            customerUuid: customer.customerUuid,
            entityUuid: customer.entityUuid,
          };

          const spyUpdate = jest.spyOn(customerService, 'updateCustomer');
          finaliseKycUpliftedHandler(
            { detail: { ...dto, shouldRequestScreening: false }, id: customer.customerUuid },
            context,
            async (error) => {
              expect(error).toBeNull();
              expect(spyUpdate).toHaveBeenCalledWith(
                new CustomerUpdatedEventDto({
                  ...dto,
                  kyc: {
                    status: KYCStatus.REVIEW,
                  },
                }),
              );
              expect(info).toHaveBeenCalledWith('finaliseKyc: screen request is being skipped.', dto.customerUuid);
              done();
            },
          );
        });
      });

      it('(uplift work) should be able to call finalise kyc with an error', async () => {
        await new Promise<void>(async (done) => {
          const customer = createCustomerRequestedDto();
          await createCustomer(customer);
          mockLambdaInvoke(lambdaService);

          (auth0Service.getUserByEmail as jest.Mock).mockResolvedValue([{ user_id: 'auth0_id' }]);

          const dto = {
            customerUuid: customer.customerUuid,
            entityUuid: customer.entityUuid,
            kyc: { status: KYCStatus.RC_VERIFIED },
          };

          customerService.generateCustomerScreeningRequestedEventDto = () => {
            return { request: null, err: true };
          };
          finaliseKycUpliftedHandler({ detail: { ...dto }, id: customer.customerUuid }, context, async (error) => {
            expect(error).toBeNull();
            expect(warn).toHaveBeenCalledWith(
              `finaliseKyc: Cannot do screening, customer ${dto.customerUuid} doesnt have entity type.`,
              dto.customerUuid,
            );
            done();
          });
        });
      });
    });

    it('should be able to call update kyc checkpoints without an error', async () => {
      await new Promise<void>(async (done) => {
        const customer = createCustomerRequestedDto();
        await createCustomer(customer);
        mockLambdaInvoke(lambdaService);
        (auth0Service.getUserByEmail as jest.Mock).mockResolvedValue([{ user_id: 'auth0_id' }]);
        const dto = {
          customerUuid: customer.customerUuid,
          kycCheckpoints: ['IDV', 'SELFIE_VERIFICATION'],
        };
        const spyUpdate = jest.spyOn(customerService, 'updateCustomer');
        updateKycCheckpointHandler(
          { kycCheckpoints: ['IDV', 'SELFIE_VERIFICATION'], id: customer.customerUuid },
          context,
          async (error) => {
            expect(error).toBeNull();
            expect(spyUpdate).toBeCalledWith(dto);
            done();
          },
        );
      });
    });
  });

  describe('partition test suite', () => {
    beforeAll(() => {
      DomicileService.getInstance().setDomicile(Domicile.AU);
    });
    afterAll(() => {
      DomicileService.getInstance().setDomicile(Domicile.AU);
    });

    it('should not allow invalid domicile value', async () => {
      const customer = createCustomerRequestedDto();
      const entityDto = createEntityCreatedDto();
      customer.entityUuid = entityDto.entityUuid;
      await createEntity(entityDto);
      DomicileService.getInstance().setDomicile('' as any);
      await expect(createCustomer(customer)).rejects.toThrow(typeorm.QueryFailedError);
      DomicileService.getInstance().setDomicile(Domicile.GB);
      await expect(createCustomer(customer)).rejects.toThrow(Error);
    });
  });
});
