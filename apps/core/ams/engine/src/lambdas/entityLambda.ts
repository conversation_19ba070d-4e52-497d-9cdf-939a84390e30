import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { ZellerHeader } from '@npco/component-bff-core/dist/types';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import { info } from '@npco/component-bff-core/dist/utils/logger';
import type { Money } from '@npco/component-dto-core';
import { EntityCreateRequestedEventDto, EntityGqlCreateRequestedEventDto } from '@npco/component-dto-entity';
import type {
  EntityPaymentSettingsDto,
  EntityStandInRulesUpdatedEventDto,
  EntityUpdatedEventDto,
} from '@npco/component-dto-entity';

import type { Handler, Context } from 'aws-lambda';
import type { AmsUpdateEntityOption } from 'services/entity/types';

import { BadRequestError } from '../services/base/error';
import { DomicileService } from '../services/domicile/domicileService';
import { EntityModule } from '../services/entity/entityModule';
import { EntityService } from '../services/entity/entityService';

import { getEventId } from './customerLambda';
import {
  commonMiddlewares,
  bootstrapNestJSMiddleware,
  setDomicileFromEventMiddleware,
  dropConnectionPostMiddleware,
} from './middleware';
import type { DBAppContext } from './types';

export const entityEventParams = {
  getAggregateId: (event: any) => event.detail.entityUuid,
};
export const entityIdParams = {
  getAggregateId: (event: any) => event.id,
};
export const entityPathParams = {
  getAggregateId: (event: any) => event.path.id,
};
export const createEntityHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: { detail: EntityCreateRequestedEventDto | EntityGqlCreateRequestedEventDto }, context: Context) => {
    const { app } = context as DBAppContext;
    const eventDto =
      'entityUuid' in event.detail
        ? new EntityCreateRequestedEventDto(event.detail)
        : new EntityGqlCreateRequestedEventDto(event.detail);

    const result = await app.get(EntityService).createEntity(eventDto);
    info(`Returning result from createEntityHandler: ${JSON.stringify(result)}}`);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      isBase64Encoded: false,
    };
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { ...entityEventParams, postMiddlewares: [dropConnectionPostMiddleware] },
);

export const updateEntityHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (
    event: { id: string; detail: EntityUpdatedEventDto; headers: { [ZellerHeader.MUTATION_REASON]: string } },
    context: Context,
  ) => {
    const { app } = context as DBAppContext;

    const updateOption: AmsUpdateEntityOption =
      event.headers && event.headers[ZellerHeader.MUTATION_REASON] === 'CRMS Webhook Action'
        ? { allowFinaliseOnboardingStatus: true }
        : {};

    const partialUpdateErrors = await app
      .get(EntityService)
      .updateEntity({ ...event.detail, entityUuid: event.id }, updateOption);

    if (partialUpdateErrors.length) {
      return { statusCode: 200, body: { partialUpdateErrors } };
    }

    return { statusCode: 200 };
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { ...entityIdParams, postMiddlewares: [dropConnectionPostMiddleware] },
);

export const updateEntityByHubspotIdHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: { id: string; detail: EntityUpdatedEventDto }, context: Context) => {
    const { app } = context as DBAppContext;
    await app.get(EntityService).updateEntityByHubspotId(event.id, event.detail);
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { ...entityIdParams, postMiddlewares: [dropConnectionPostMiddleware] },
);

/**
 * this is used by onboarding process and will be deleted once the onboarding process is moved to ams-engine
 */
export const entityGetAllCustomersHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: { id: string }, context: Context) => {
    const { app } = context as DBAppContext;
    return app.get(EntityService).getAllCustomers(event.id);
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { ...entityIdParams, postMiddlewares: [dropConnectionPostMiddleware] },
);

export const getEntityHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  (event: { id: string }, context: Context) => {
    const { app } = context as DBAppContext;
    return app.get(EntityService).getEntity(event.id);
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { ...entityIdParams, postMiddlewares: [dropConnectionPostMiddleware] },
);

export const amexMerchantOnboardingCronJobHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (_event: any, context: Context) => {
    const { app } = context as DBAppContext;
    await app.get(EntityService).amexMerchantOnboardingCronJob();
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares, setDomicileFromEventMiddleware],
  { postMiddlewares: [dropConnectionPostMiddleware] },
);

export const adminUpdateEntityHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: any, context: Context) => {
    const { app } = context as DBAppContext;
    await app
      .get(EntityService)
      .updateEntity({ ...event.detail, entityUuid: event.id }, { allowFinaliseOnboardingStatus: true });
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { ...entityIdParams, postMiddlewares: [dropConnectionPostMiddleware] },
);

// New handler to support multi-region onboarding process (avaialable behind feature flag)
export const finaliseEntityOnboardingHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  (event: { id: string }, context: Context) => {
    const { app } = context as DBAppContext;
    return app.get(EntityService).evaluateOnboardingDetails(event.id, DomicileService.getInstance().getDomicile());
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { ...entityIdParams, postMiddlewares: [dropConnectionPostMiddleware] },
);

export const selectDepositAccountHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (
    event: { id: string; depositAccountUuid: string; detail?: { remitToCard: boolean }; headers?: any },
    context: Context,
  ) => {
    const { app } = context as DBAppContext;
    await app.get(EntityService).selectDepositAccount(event.id, event.depositAccountUuid, event.detail?.remitToCard);
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { ...entityIdParams, postMiddlewares: [dropConnectionPostMiddleware] },
);

export const updateStandInRulesHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: { id: string; detail: EntityStandInRulesUpdatedEventDto }, context: Context) => {
    const { app } = context as DBAppContext;
    await app.get(EntityService).updateStandInRules(event.id, event.detail.standInRules);
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { ...entityIdParams, postMiddlewares: [dropConnectionPostMiddleware] },
);

export const updatePaymentSettingsHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: { id: string; detail: EntityPaymentSettingsDto }, context: Context) => {
    const { app } = context as DBAppContext;
    await app.get(EntityService).updatePaymentSettings({ ...event.detail, entityUuid: event.id });
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { ...entityIdParams, postMiddlewares: [dropConnectionPostMiddleware] },
);

export const addReferredByHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: { id: string; detail: { referredBy: string } }, context: Context) => {
    const { app } = context as DBAppContext;
    await app.get(EntityService).addReferredBy(event.id, event.detail.referredBy);
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { ...entityIdParams, postMiddlewares: [dropConnectionPostMiddleware] },
);

export const finalisedEntitiesExistWithAbnHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: { detail: { entityUuid: string; abn: string } }, context: Context) => {
    const { app } = context as DBAppContext;
    if (!event.detail.entityUuid) {
      throw new BadRequestError('X-ZELLER-ENTITY-UUID header is required');
    }

    return app.get(EntityService).finalisedEntitiesExistWithAbn(event.detail.entityUuid, event.detail.abn);
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { postMiddlewares: [dropConnectionPostMiddleware] },
);

export const getEntityDailyLimitConfigHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (_, context: Context) => {
    const { app } = context as DBAppContext;

    return app.get(EntityService).getEntityDailyLimitConfig();
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { postMiddlewares: [dropConnectionPostMiddleware] },
);

export const updateEntityDailyLimitHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: { id: string; detail: { limit: Money; reason: string; zellerUserId: string } }, context: Context) => {
    const { app } = context as DBAppContext;

    return app.get(EntityService).updateDailyLimits(event.id, event.detail);
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { postMiddlewares: [dropConnectionPostMiddleware] },
);

export const initializeEntityCanAcquireAmexFlagHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: { id: string; canAcquireAmex: boolean }, context: Context) => {
    const { app } = context as DBAppContext;

    return app.get(EntityService).initializeEntityCanAcquireAmexFlag(event.id, event.canAcquireAmex);
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares],
  { postMiddlewares: [dropConnectionPostMiddleware] },
);

export const updatePrimaryAccountHolderHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: { id: string; customerUuid: string }, context: Context) => {
    const { app } = context as DBAppContext;
    await app.get(EntityService).updatePrimaryAccountHolder(event.customerUuid, event.id);
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares, xrayAggregateMiddleware(getEventId)],
  { postMiddlewares: [dropConnectionPostMiddleware] },
);

export const triggerAmexOnBoardingEventHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: { entityUuid: string }, context: Context) => {
    const { app } = context as DBAppContext;
    await app.get(EntityService).amexMerchantOnboardingRequest(event.entityUuid);
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares, xrayAggregateMiddleware(getEventId)],
  { postMiddlewares: [dropConnectionPostMiddleware] },
);

export const attachEntityDomicileAndCurrencyHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.AMS, eventType: LambdaEventSource.HTTP_CUSTOM_MAP },
  async (event: { id: string; detail: { domicile: string } }, context: Context) => {
    const { app } = context as DBAppContext;
    await app.get(EntityService).attachEntityDomicileAndCurrency({ ...event.detail, entityUuid: event.id });
  },
  [bootstrapNestJSMiddleware(EntityModule), ...commonMiddlewares, xrayAggregateMiddleware(getEventId)],
  { postMiddlewares: [dropConnectionPostMiddleware] },
);
