import { DynamodbClient, EventBridgeService } from '@npco/bff-systemtest-utils';
import {
  CustomerRole,
  DbRecordType,
  EntityType,
  ImageSize,
  KYCScreeningResult,
  ScreeningRequestedType,
  ScreeningStatus,
} from '@npco/component-dto-core';
import type {
  CustomerCreateRequestedEventDto,
  CustomerDocumentVerificationAndSafeHarbourCompletedEventDto,
  CustomerMfaUpdatedEventDto,
  CustomerScreeningCompletedEventDto,
  CustomerUpdatedEventDto,
} from '@npco/component-dto-customer';
import {
  SafeHarbourVerificationResult,
  IdvAndSafeHarbourResultType,
  IdvSafeHarbourDocumentType,
  SafeHarbourVerificationStatus,
  KYCDecision,
  MfaEnrolmentType,
  KYCStatus,
  DocumentVerificationResult,
  DocumentVerificationStatus,
} from '@npco/component-dto-customer';
import type { EntityCreateRequestedEventDto } from '@npco/component-dto-entity';

import { CompanyProfileData, Status } from 'ams-engine/dist/entities';
import type { Customer } from 'ams-engine/dist/entities';
import axios from 'axios';
import { Client } from 'pg';
import { v4 as uuidv4 } from 'uuid';

import type { ApiEndpointParamConfig } from './utils';
import {
  createEntityDto,
  createEntity as createEntityUtil,
  createCustomer as createCustomerUtil,
  createCustomerDto,
  createSite as createSiteUtil,
  getCustomerFromDb,
  getDbClientOptions,
  stage,
  sleep,
  retry,
  getVersionedApiEndpoint,
  domicile,
  isRegionAP,
  region,
  getDomicileLookupTableName,
} from './utils';

describe('customer test', () => {
  let client: Client;

  let requestedEmailChangeCustomerUuid: string;
  let endpoint: string;
  let testemail: string;
  const eventBridge = new EventBridgeService(`${stage}-ams-cqrs-iac-eventBus-projection`);

  beforeAll(async () => {
    const options = await getDbClientOptions();
    console.log('options:', options);
    client = new Client(options);
    await client.connect();
    await client.query(`SET search_path TO ${options.search_path}`);
  });

  afterAll(async () => {
    await client.end();
  });

  describe.each<keyof ApiEndpointParamConfig>(['v1', 'v2'])('with %s version api endpoint', (version) => {
    if (version === 'v1' && !isRegionAP) {
      // Skip the test if the version is 'v1' and region is not Sydney (ap-southeast-2)
      return;
    }
    const customerUuid = uuidv4();
    const entityUuid = uuidv4();
    const getTestEmail = () => `system-test+no-auth0-${uuidv4()}@myzeller.com`;

    testemail = getTestEmail(); // user.email;
    const createEntity = async (entityDto: EntityCreateRequestedEventDto) => {
      await createEntityUtil(entityDto, version === 'v2', domicile);
    };

    const createCustomer = async (customerDto: CustomerCreateRequestedEventDto) => {
      await createCustomerUtil(customerDto, version === 'v2', domicile);
    };

    const createSite = async (site?: any) => {
      return createSiteUtil(site, version === 'v2', domicile);
    };

    beforeAll(async () => {
      await createCustomer(createCustomerDto(entityUuid));

      endpoint = await getVersionedApiEndpoint({
        version,
        ...(version === 'v2' && { domicile }),
        path: '/customer',
      });
    });

    it('should return correct error response for customer not found', async () => {
      const options = {
        headers: { 'Content-Type': 'application/json' },
      };
      await axios
        .patch(`${endpoint}/customerUuid`, JSON.stringify({ customerUuid: 'notfound' }), options)
        .then(() => fail())
        .catch((e) => {
          console.warn(e.response.data);
          expect(e.response.data.errorMessage).toContain('[400]');
        });
    });

    it('should be able to create customer', async () => {
      const customerDto: CustomerCreateRequestedEventDto = {
        customerUuid,
        entityUuid,
        identityUserId: uuidv4(),
        firstname: uuidv4(),
        lastname: uuidv4(),
        middlename: uuidv4(),
        email: testemail,
        role: CustomerRole.ADMIN,
        nickname: 'nickname',
        address: {
          state: 'state',
          street: 'street',
          suburb: 'suburb',
          postcode: 'postcode',
          country: 'country',
        },
        dob: 'dob',
        emailVerified: true,
        phone: '*********',
        phoneVerified: true,
        director: true,
        secretary: true,
        ceo: true,
        shareholder: true,
        beneficialOwner: true,
        beneficialOwnerAlt: true,
        beneficiary: true,
        partner: true,
        trustee: true,
        settlor: true,
        registeringIndividual: true,
        idv: {
          status: DocumentVerificationStatus.COMPLETED,
          result: DocumentVerificationResult.ACCEPTED,
        },
        documents: {
          firstName: 'firstname',
          lastName: 'lastName',
        },
        generalContact: true,
        financialContact: true,
        type: EntityType.ASSOCIATION,
        companyTrustName: 'companyTrustName',
        abn: 'abn',
        chair: false,
        treasurer: false,
        governmentRole: uuidv4(),
        acn: 'acn',
        companyProfileData: CompanyProfileData.MANUALLY_CREATED,
        icon: {
          images: [
            {
              url: 'url2',
              size: ImageSize.Medium,
            },
          ],
        },
        kyc: {
          status: KYCStatus.REQUIRED,
        },
      };
      const output = await axios.post(endpoint, JSON.stringify(customerDto), {
        headers: { 'Content-Type': 'application/json' },
      });
      console.log(' create customer ', customerDto.customerUuid);
      console.log('lambda return:', output);

      const ret = await client.query({
        name: 'create-customer',
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customerDto.customerUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      const item = ret.rows[0];
      delete item.updatedTime;
      const customer: Customer = {
        customerUuid,
        identityUserId: expect.any(String),
        hubspotContactId: null as any,
        firstname: customerDto.firstname,
        lastname: customerDto.lastname,
        middlename: customerDto.middlename,
        email: customerDto.email,
        nickname: customerDto.nickname,
        address: customerDto.address,
        dob: 'dob',
        phone: '*********',
        status: Status.ACTIVE,
        phoneVerified: true,
        emailVerified: true,
        createdTime: expect.any(String),
        idv: {
          status: DocumentVerificationStatus.REQUIRED,
        },
        documents: customerDto.documents,
        type: EntityType.INDIVIDUAL,
        companyTrustName: 'companyTrustName',
        abn: 'abn',
        screening: {
          status: ScreeningStatus.REQUIRED,
        },
        safeharbour: {
          status: SafeHarbourVerificationStatus.REQUIRED,
        },
        productTourStatus: {
          showOnboardingShop: true,
          showAdminMerchantPortalWelcome: true,
          showInvoiceInstructions: true,
          showInvoicesWelcome: false,
          showItemsWelcome: true,
          showItemInstructions: true,
          showInvoicesCustomisationWelcome: true,
          showInvoicesScheduleSendWelcome: true,
          showInvoicesSendBySmsWelcome: true,
          showInvoiceSendViaInfo: true,
          showInvoicingCustomisationSettingsWelcome: true,
          showTapToPayInstructions: false,
          showTapToPayMayJune: false,
          showSavingsAccountWelcome: true,
          showSavingsAccountMarch: false,
          showSavingsAccountMay: false,
          showCorporateCardsMayOffer: false,
          showCorporateCardsWalkthrough: true,
          showCorporateCardsSettingsWalkthrough: true,
          showCustomScreensaverPromo: false,
          showNotificationsWelcome: false,
          showCorporateCardsAdminWelcome: false,
          showInvoiceApril: false,
          showCatalogueItemsWelcome: true,
          profileAvatarWalkthrough: false,
          showServiceChargesWelcome: false,
        },
        oneTimePassword: null,
        oneTimePasswordData: null,
        acn: 'acn',
        mfaEnrolments: null,
        pendingPhoneVerification: null,
        icon: {
          images: [
            {
              url: 'url2',
              size: ImageSize.Medium,
            },
          ],
        },
        kyc: {
          status: KYCStatus.REQUIRED,
        },
        kycCheckpoints: null,
        idvAttempts: null,
        safeHarbourVerification: null,
        selfieVerification: null,
        domicile,
      };
      expect(item).toMatchObject(customer);

      const db = new DynamodbClient({ region });
      const result = await db.get({
        TableName: getDomicileLookupTableName(stage),
        Key: {
          id: customer.customerUuid,
          type: DbRecordType.CUSTOMER_DOMICILE,
        },
      });

      expect(result.Item).toBeTruthy();
      expect(result.Item?.domicile).toEqual(domicile);
    });

    it('should be able to invite another customer', async () => {
      const customerDto: CustomerCreateRequestedEventDto = {
        customerUuid: uuidv4(),
        entityUuid,
        identityUserId: uuidv4(),
        firstname: uuidv4(),
        lastname: uuidv4(),
        middlename: uuidv4(),
        email: getTestEmail(),
        role: CustomerRole.MANAGER,
        registeringIndividual: false,
        invitedBy: {
          customerUuid,
        },
      };
      const output = await axios.post(endpoint, JSON.stringify(customerDto), {
        headers: { 'Content-Type': 'application/json' },
      });
      console.log(' create customer ', customerDto.customerUuid);
      console.log('lambda return:', output);

      const ret = await client.query({
        name: uuidv4(),
        text: `SELECT * FROM "CustomerEntity" WHERE "customerUuid" = $1 AND "entityUuid" = $2 AND "domicile" = $3`,
        values: [customerDto.customerUuid, customerDto.entityUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      const item = ret.rows[0];
      delete item.updatedTime;
      expect(item.invitedBy.customerUuid).toBe(customerUuid);
      expect(item.invitationPending).toBeTruthy();
    });

    it('should be able to invite by zeller', async () => {
      const customerDto: CustomerCreateRequestedEventDto = {
        customerUuid: uuidv4(),
        entityUuid,
        identityUserId: uuidv4(),
        firstname: uuidv4(),
        lastname: uuidv4(),
        middlename: uuidv4(),
        email: getTestEmail(),
        role: CustomerRole.MANAGER,
        registeringIndividual: false,
        invitedByZeller: true,
      };
      const output = await axios.post(endpoint, JSON.stringify(customerDto), {
        headers: { 'Content-Type': 'application/json' },
      });
      console.log(' create customer ', customerDto.customerUuid);
      console.log('lambda return:', output);

      const ret = await client.query({
        name: uuidv4(),
        text: `SELECT * FROM "CustomerEntity" WHERE "customerUuid" = $1 AND "entityUuid" = $2 AND "domicile" = $3`,
        values: [customerDto.customerUuid, customerDto.entityUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      const item = ret.rows[0];
      expect(item.invitedBy).toEqual({ customerUuid: '00000000-0000-0000-0000-000000000000' });
      expect(item.invitationPending).toBeTruthy();
    });

    it('should not be able to update customer email already exist', async () => {
      const customerDto: CustomerUpdatedEventDto = {
        customerUuid,
        email: testemail,
      } as any;
      await axios
        .patch(`${endpoint}/${customerUuid}`, JSON.stringify(customerDto), {
          headers: { 'Content-Type': 'application/json' },
        })
        .then(() => fail())
        .catch((e) => {
          console.warn(e.response.data);
          expect(e.response.data.errorMessage).toContain('[400]');
          expect(e.response.data.errorType).toEqual('CustomerEmailAlreadyExistsError');
        });
    });

    it('should be able to update a customer', async () => {
      const newEmail = getTestEmail();
      const customerDto: CustomerUpdatedEventDto = {
        customerUuid,
        entityUuid,
        identityUserId: 'auth0UserId',
        firstname: uuidv4(),
        lastname: uuidv4(),
        middlename: uuidv4(),
        email: newEmail,
        icon: {
          images: [
            {
              url: 'url1',
              size: ImageSize.Large,
            },
            {
              url: 'url2',
              size: ImageSize.Native,
            },
          ],
        },
        kyc: {
          status: KYCStatus.VERIFIED,
          decision: KYCDecision.ACCEPTED,
        },
      } as any;
      await axios.patch(`${endpoint}/${customerUuid}`, JSON.stringify(customerDto), {
        headers: { 'Content-Type': 'application/json' },
      });
      const ret = await client.query({
        name: uuidv4(),
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customerDto.customerUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      const item = ret.rows[0];
      expect(item.customerUuid).toBe(customerDto.customerUuid);
      expect(item.identityUserId).toBe(customerDto.identityUserId);
      expect(item.firstname).toBe(customerDto.firstname);
      expect(item.lastname).toBe(customerDto.lastname);
      expect(item.middlename).toBe(customerDto.middlename);
      expect(item.email).toBe(customerDto.email);
      expect(item.icon).toEqual(customerDto.icon);
      expect(item.kyc).toEqual(customerDto.kyc);

      testemail = newEmail;
    });

    it('should rollback customer update on auth0 failure', async () => {
      const entityUuid = uuidv4();
      const entityDto = createEntityDto();
      entityDto.entityUuid = entityUuid;
      const customer: CustomerCreateRequestedEventDto = {
        customerUuid: uuidv4(),
        entityUuid,
        identityUserId: uuidv4(),
        firstname: uuidv4(),
        lastname: uuidv4(),
        middlename: uuidv4(),
        email: getTestEmail(),
        registeringIndividual: true,
        role: CustomerRole.ADMIN,
      };
      await axios
        .post(endpoint, JSON.stringify(customer), {
          headers: { 'Content-Type': 'application/json' },
        })
        .catch((e) => console.error(e));
      await createEntity(entityDto).catch((e) => console.error(e));
      let ret = await client.query({
        name: 'update-customer',
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customer.customerUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      const customerUpdatedDto: CustomerUpdatedEventDto = {
        customerUuid: customer.customerUuid,
        entityUuid,
        permissions: {
          allowZellerInvoices: true,
        },
      } as any;
      const output = await axios
        .patch(`${endpoint}/${customer.customerUuid}`, JSON.stringify(customerUpdatedDto), {
          headers: { 'Content-Type': 'application/json' },
        })
        .catch((e) => console.error(e));
      console.log('lambda return:', output);
      ret = await client.query({
        name: uuidv4(),
        text: `SELECT * FROM "SiteCustomer" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customer.customerUuid, domicile],
      });
      expect(ret.rows.length).toBe(0);
      const siteUuid = uuidv4();
      const dto = {
        siteUuid,
        name: uuidv4(),
        type: 'MOBILE',
        entityUuid,
      };
      await createSite(dto).catch((e) => console.error(e));
      const newEmail = getTestEmail();
      const customerDto: CustomerUpdatedEventDto = {
        customerUuid: customer.customerUuid,
        identityUserId: 'auth0UserId',
        firstname: uuidv4(),
        lastname: uuidv4(),
        middlename: uuidv4(),
        email: newEmail,
        assignSites: [siteUuid],
        unassignSites: [{ bad: 'object' }],
        permissions: {
          allowZellerInvoices: false,
        },
        icon: {
          images: [
            {
              url: 'url1',
              size: ImageSize.Large,
            },
            {
              url: 'url2',
              size: ImageSize.Native,
            },
          ],
        },
        kyc: {
          status: KYCStatus.VERIFIED,
          decision: KYCDecision.ACCEPTED,
        },
      } as any;
      await axios
        .patch(`${endpoint}/${customer.customerUuid}`, JSON.stringify(customerDto), {
          headers: { 'Content-Type': 'application/json' },
        })
        .catch((e) => console.error(e));
      ret = await client.query({
        name: 'update-customer',
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`, // only has 1 zeller invoice site
        values: [customerDto.customerUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      const item = ret.rows[0];
      expect(item.customerUuid).toBe(customer.customerUuid);
      expect(item.identityUserId).toBe(customer.identityUserId);
      expect(item.firstname).toBe(customer.firstname);
      expect(item.lastname).toBe(customer.lastname);
      expect(item.middlename).toBe(customer.middlename);
      ret = await client.query({
        name: 'site-customer',
        text: `SELECT * FROM "SiteCustomer" WHERE "customerUuid" = $1 AND domicile = $2`,
        values: [customer.customerUuid, domicile],
      });
      expect(ret.rows.length).toBe(0);
    });

    it('should be able to update a customer invitationPending', async () => {
      const customerDto: CustomerUpdatedEventDto = {
        customerUuid,
        entityUuid,
        isInvitationPending: true,
      } as any;
      await axios.patch(`${endpoint}/${customerUuid}`, JSON.stringify(customerDto), {
        headers: { 'Content-Type': 'application/json' },
      });
      const ret = await client.query({
        name: uuidv4(),
        text: `SELECT * FROM "CustomerEntity" WHERE "customerUuid" = $1 AND "entityUuid" = $2 AND "domicile" = $3`,
        values: [customerDto.customerUuid, customerDto.entityUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      const item = ret.rows[0];
      expect(item.invitationPending).toBe(customerDto.isInvitationPending);
    });

    it('should be able to update a customer phone', async () => {
      const customerDto1: CustomerUpdatedEventDto = {
        customerUuid,
        entityUuid,
        phone: '+************',
        phoneVerified: false,
        emailVerified: true,
      } as any;
      await axios.patch(`${endpoint}/${customerUuid}`, JSON.stringify(customerDto1), {
        headers: { 'Content-Type': 'application/json' },
      });

      const customerDto2: CustomerUpdatedEventDto = {
        customerUuid,
        entityUuid,
        phoneVerified: true,
      } as any;
      await axios.patch(`${endpoint}/${customerUuid}`, JSON.stringify(customerDto2), {
        headers: { 'Content-Type': 'application/json' },
      });
      const ret = await client.query({
        name: 'update-customer',
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customerDto1.customerUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      const item = ret.rows[0];
      expect(item.customerUuid).toBe(customerDto1.customerUuid);
      expect(item.phone).toBe(customerDto1.phone);
      expect(item.phoneVerified).toBe(true);
      expect(item.emailVerified).toBe(true);
    });

    it('should be able to update phone MFA', async () => {
      const customerDto: CustomerMfaUpdatedEventDto = {
        customerUuid,
        mfaEnrolments: [{ type: MfaEnrolmentType.SMS, name: 'XXXXXXXX0001', createdAt: new Date().toISOString() }],
      };

      // act
      await axios.post(`${endpoint}/${customerUuid}/update-phone-mfa`, customerDto, {
        headers: { 'Content-Type': 'application/json' },
      });

      // verify
      const ret = await client.query({
        name: 'update-customer',
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customerUuid, domicile],
      });

      expect(ret.rows.length).toBe(1);
      const item = ret.rows[0];
      expect(item.mfaEnrolments).toHaveLength(1);
      expect(item.mfaEnrolments[0].type as string).toEqual('SMS');
      expect(item.mfaEnrolments[0].name as string).not.toBeUndefined();
    });

    it('should return correct error response for customer failures', async () => {
      const options = {
        headers: { 'Content-Type': 'application/json' },
      };

      // already exists (email)
      await axios
        .post(endpoint, JSON.stringify({ entityUuid, email: testemail }), options)
        .then(() => fail())
        .catch((e) => {
          console.warn(e.response.data);
          expect(e.response.data.errorMessage).toContain('[400]');
          expect(e.response.data.errorType).toEqual('CustomerEmailAlreadyExistsError');
        });

      // cannot update registering customer
      await axios
        .patch(`${endpoint}/${customerUuid}`, JSON.stringify({ customerUuid, role: 'MANAGER' }), options)
        .then(() => fail())
        .catch((e) => {
          console.warn(e.response.data);
          expect(e.response.data.errorMessage).toContain('[400]');
        });
    });

    it('should be able to send reset password invite to customer', async () => {
      await axios
        .post(
          `${endpoint}/${customerUuid}/email-invite`,
          { entityUuid },
          {
            headers: { 'Content-Type': 'application/json' },
          },
        )
        .catch((e) => {
          console.error(e);
          fail(e);
        });
    });

    it('should not be able to send reset password invite to non existent customer', async () => {
      await axios
        .post(
          `${endpoint}/${uuidv4()}/email-invite`,
          {},
          {
            headers: { 'Content-Type': 'application/json' },
          },
        )
        .then(() => fail())
        .catch((e) => {
          console.warn(e.response.data);
          expect(e.response.data.errorMessage).toContain('[400]');
          expect(e.response.data.errorType).toEqual('CustomerNotFoundError');
        });
    });

    it('should not be able to send reset password invite for customer of another entity', async () => {
      await axios
        .post(
          `${endpoint}/${customerUuid}/email-invite`,
          {
            entityUuid: uuidv4(),
          },
          {
            headers: { 'Content-Type': 'application/json' },
          },
        )
        .then(() => fail())
        .catch((e) => {
          console.warn(e.response.data);
          expect(e.response.data.errorMessage).toContain('[400]');
          expect(e.response.data.errorType).toEqual('CustomerNotFoundError');
        });
    });

    describe('email change tests', () => {
      it('should be able to delete customer', async () => {
        const customerDto: CustomerCreateRequestedEventDto = {
          customerUuid: uuidv4(),
          entityUuid: uuidv4(),
          identityUserId: uuidv4(),
          firstname: uuidv4(),
          lastname: uuidv4(),
          middlename: uuidv4(),
          email: getTestEmail(),
          role: CustomerRole.ADMIN,
          nickname: 'nickname',
          registeringIndividual: false,
        };
        await axios.post(endpoint, JSON.stringify(customerDto), {
          headers: { 'Content-Type': 'application/json' },
        });
        console.log('created customer', customerDto);

        await axios.delete(`${endpoint}/${customerDto.customerUuid}`);
        const ret = await client.query({
          name: 'update-customer',
          text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
          values: [customerDto.customerUuid, domicile],
        });
        expect(ret.rows.length).toBe(1);
        const item = ret.rows[0];
        expect(item.status).toBe('DELETED');

        const db = new DynamodbClient({ region });
        const result = await db.get({
          TableName: getDomicileLookupTableName(stage),
          Key: {
            id: customerDto.customerUuid,
            type: DbRecordType.CUSTOMER_DOMICILE,
          },
        });

        expect(result.Item).toBeUndefined();
      });

      it('should be able to create new user using email from a deleted customer', async () => {
        const email = getTestEmail();
        const customerDto: CustomerCreateRequestedEventDto = {
          email,
          customerUuid: uuidv4(),
          entityUuid: uuidv4(),
          identityUserId: uuidv4(),
          firstname: uuidv4(),
          lastname: uuidv4(),
          middlename: uuidv4(),
          role: CustomerRole.MANAGER,
          registeringIndividual: false,
        };

        // create a new user
        await axios.post(endpoint, JSON.stringify(customerDto), {
          headers: { 'Content-Type': 'application/json' },
        });

        // delete the user
        await axios.delete(`${endpoint}/${customerDto.customerUuid}`);

        // create new custom using the same email address
        const newCustomerDto = {
          ...customerDto,
          customerUuid: uuidv4(),
        };
        await axios.post(endpoint, JSON.stringify(newCustomerDto), {
          headers: { 'Content-Type': 'application/json' },
        });

        const newCustomer = await getCustomerFromDb(client, newCustomerDto.customerUuid);
        expect(newCustomer.email).toEqual(email);
        expect(newCustomer.status).toEqual('ACTIVE');

        const deletedCustomer = await getCustomerFromDb(client, customerDto.customerUuid);
        expect(deletedCustomer.email).toEqual(email);
        expect(deletedCustomer.status).toEqual('DELETED');
      });

      it('should be able to request email change', async () => {
        const customerDto: CustomerCreateRequestedEventDto = {
          customerUuid: uuidv4(),
          entityUuid: uuidv4(),
          firstname: uuidv4(),
          lastname: uuidv4(),
          middlename: uuidv4(),
          email: getTestEmail(),
          role: CustomerRole.ADMIN,
          nickname: 'nickname',
          registeringIndividual: false,
        };
        await createCustomer(customerDto);

        const dto = {
          newEmail: getTestEmail(),
          redirectUrl: 'https://dashboard.myzeller.com/confirm-email',
        };

        const response = await axios.post(
          `${endpoint}/${customerDto.customerUuid}/request-email-change`,
          JSON.stringify(dto),
          {
            headers: { 'Content-Type': 'application/json' },
          },
        );

        expect(response.status).toBe(200);

        const customer = await getCustomerFromDb(client, customerDto.customerUuid);
        expect(customer.oneTimePassword).toMatch(/^EMAIL#/);
        expect(customer.oneTimePasswordData).toMatchObject({
          newEmail: dto.newEmail,
          createdTimestamp: expect.any(Number),
        });

        requestedEmailChangeCustomerUuid = customerDto.customerUuid;
      });

      it('should be able to complete email change', async () => {
        const requestedCustomer = await getCustomerFromDb(client, requestedEmailChangeCustomerUuid);

        const dto = {
          newEmail: requestedCustomer.oneTimePasswordData.newEmail,
          code: requestedCustomer.oneTimePassword.replace('EMAIL#', ''),
        };
        console.log('email', dto, `${endpoint}/complete-email-change`);
        const response = await axios
          .post(`${endpoint}/complete-email-change`, JSON.stringify(dto), {
            headers: { 'Content-Type': 'application/json' },
          })
          .catch((e) => console.error(e));

        expect((response as any).status).toBe(200);

        const customer = await getCustomerFromDb(client, requestedEmailChangeCustomerUuid);
        expect(customer.oneTimePassword).toBeNull();
        expect(customer.oneTimePasswordData).toBeNull();
        expect(customer.email).toBe(dto.newEmail);
      });
    });

    it('should be able to assign a customer to a site', async () => {
      const siteDto = await createSite();
      const customerDto = createCustomerDto(getTestEmail());
      customerDto.entityUuid = siteDto.entityUuid;
      await createCustomer(customerDto);
      customerDto.entityUuid = siteDto.entityUuid;
      await axios.post(`${endpoint}/${customerDto.customerUuid}/site/${siteDto.siteUuid}`);
      let ret = await client.query({
        name: 'get-site-customer',
        text: `SELECT * FROM "SiteCustomer" WHERE "customerUuid" = $1 and "siteUuid" = $2 and domicile = $3`,
        values: [customerDto.customerUuid, siteDto.siteUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      const item = ret.rows[0];
      expect(item).toEqual({
        siteUuid: siteDto.siteUuid,
        customerUuid: customerDto.customerUuid,
        createdTime: expect.any(String),
        domicile,
      });

      // test unassign a cusstomer from a site
      await axios.delete(`${endpoint}/${customerDto.customerUuid}/site/${siteDto.siteUuid}`);
      ret = await client.query({
        name: 'get-site-customer',
        text: `SELECT * FROM "SiteCustomer" WHERE "customerUuid" = $1 and "siteUuid" = $2 and domicile = $3`,
        values: [customerDto.customerUuid, siteDto.siteUuid, domicile],
      });
      expect(ret.rows.length).toBe(0);
    });

    it('should be able to save document verification and safe harbour completed for customer', async () => {
      const customerDto = createCustomerDto(getTestEmail());
      await createCustomer(customerDto);
      const compEvent: CustomerDocumentVerificationAndSafeHarbourCompletedEventDto = {
        documentType: IdvSafeHarbourDocumentType.DRIVING_LICENCE,
        customerUuid: customerDto.customerUuid,
        entityUuid: customerDto.entityUuid,
        driversLicence: {
          firstName: uuidv4(),
          middleName: uuidv4(),
          lastName: uuidv4(),
          dob: uuidv4(),
          tokenisedNumber: 'abc.def',
          state: uuidv4(),
          cardNumber: uuidv4(),
          result: IdvAndSafeHarbourResultType.ACCEPTED,
        },
        result: {
          safeHarbour: {
            overallResult: IdvAndSafeHarbourResultType.ACCEPTED,
            addressVerification: true,
            dobVerification: true,
          },
          documentVerification: IdvAndSafeHarbourResultType.ACCEPTED,
        },
      };
      console.log('compEvent idv safe harbour comp is -> ', JSON.stringify(compEvent));

      await eventBridge.publishProjectionEvent('ams.Customer.DocumentVerificationAndSafeHarbourCompleted', compEvent);
      await sleep(5000);
      const ret = await client.query({
        name: `get-customer-${customerDto.customerUuid}`,
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customerDto.customerUuid, domicile],
      });
      console.log('idv and safe harbour requested completed is -> ', ret);
      expect(ret.rows.length).toBe(1);
      expect(ret.rows[0].customerUuid).toBe(compEvent.customerUuid);
      expect(ret.rows[0].safeHarbourVerification).toStrictEqual({
        addressVerification: true,
        dobVerification: true,
      });
      expect(ret.rows[0].safeharbour).toStrictEqual({
        status: SafeHarbourVerificationStatus.COMPLETED,
        result: SafeHarbourVerificationResult.ACCEPTED,
      });
      expect(ret.rows[0].idv).toStrictEqual({
        status: DocumentVerificationStatus.COMPLETED,
        result: DocumentVerificationResult.ACCEPTED_ONE_PHOTO_ID_ONLY,
      });
      expect(ret.rows[0].documents).toStrictEqual({
        driversLicence: compEvent.driversLicence,
      });
      expect(ret.rows[0].idvAttempts).toStrictEqual({
        driversLicence: 1,
      });
    });

    it('should be able to save screening result for customer', async () => {
      const customerDto = createCustomerDto(getTestEmail());
      await createCustomer(customerDto);
      const completedEvent: CustomerScreeningCompletedEventDto = {
        customerUuid: customerDto.customerUuid,
        entityUuid: customerDto.entityUuid,
        matchStatus: uuidv4(),
        result: KYCScreeningResult.NO_MATCH,
        screeningType: ScreeningRequestedType.DEFAULT,
      };
      await eventBridge.publishProjectionEvent('ams.Customer.ScreeningCompleted', completedEvent);
      await retry(async () => {
        const ret = await client.query({
          name: `get-customer-${customerDto.customerUuid}`,
          text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
          values: [customerDto.customerUuid, domicile],
        });
        expect(ret.rows.length).toBe(1);
        expect(ret.rows[0].screening).toEqual({
          matchStatus: completedEvent.matchStatus,
          result: completedEvent.result,
          status: 'COMPLETED',
        });
      });
    });

    it('should be able to update constumer address concurrently', async () => {
      const customerDto = createCustomerDto(getTestEmail());
      await createCustomer(customerDto);

      const updatedEvents = [
        {
          key: 'street',
          value: uuidv4(),
        },
        {
          key: 'suburb',
          value: uuidv4(),
        },
        {
          key: 'state',
          value: uuidv4(),
        },
        {
          key: 'postcode',
          value: uuidv4(),
        },
        {
          key: 'country',
          value: uuidv4(),
        },
      ];
      const proms = updatedEvents.map((event) => {
        const updatedEvent = {
          customerUuid: customerDto.customerUuid,
          entityUuid: customerDto.entityUuid,
          address: {},
        };
        (updatedEvent.address as any)[event.key] = event.value;
        return axios.patch(`${endpoint}/${customerDto.customerUuid}`, JSON.stringify(updatedEvent), {
          headers: { 'Content-Type': 'application/json' },
        });
      });
      await Promise.all(proms);

      const ret = await client.query({
        name: `get-customer-${customerDto.customerUuid}`,
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customerDto.customerUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      expect(ret.rows[0].address).toEqual({
        street: updatedEvents[0].value,
        suburb: updatedEvents[1].value,
        state: updatedEvents[2].value,
        postcode: updatedEvents[3].value,
        country: updatedEvents[4].value,
      });
    });

    it('should be able to update constumer document concurrently', async () => {
      const customerDto = createCustomerDto(getTestEmail());
      await createCustomer(customerDto);

      const updatedEvents = [
        {
          key: 'tokenisedDriversLicenseNumber',
          value: uuidv4(),
        },
        {
          key: 'tokenisedPassportNumber',
          value: uuidv4(),
        },
        {
          key: 'tokenisedMedicareCard',
          value: uuidv4(),
        },
        {
          key: 'resultDriversLicence',
          value: 'ACCEPTED',
        },
        {
          key: 'resultPassport',
          value: 'REJECTED',
        },
        {
          key: 'resultMedicareCard',
          value: 'NOT_VERIFIED',
        },
      ];
      const proms = updatedEvents.map((event) => {
        const updatedEvent = {
          customerUuid: customerDto.customerUuid,
          entityUuid: customerDto.entityUuid,
          documents: {},
        };
        (updatedEvent.documents as any)[event.key] = event.value;
        return axios.patch(`${endpoint}/${customerDto.customerUuid}`, JSON.stringify(updatedEvent), {
          headers: { 'Content-Type': 'application/json' },
        });
      });
      await Promise.all(proms);

      const ret = await client.query({
        name: `get-customer-${customerDto.customerUuid}`,
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customerDto.customerUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      expect(ret.rows[0].documents).toEqual({
        tokenisedDriversLicenseNumber: updatedEvents[0].value,
        tokenisedPassportNumber: updatedEvents[1].value,
        tokenisedMedicareCard: updatedEvents[2].value,
        resultDriversLicence: updatedEvents[3].value,
        resultPassport: updatedEvents[4].value,
        resultMedicareCard: updatedEvents[5].value,
      });
    });

    it('should be able to get customer', async () => {
      const output = await axios.get(`${endpoint}/${customerUuid}`);
      const customer = output.data;
      expect(customer.customerUuid).toEqual(customerUuid);
    });

    it('should be able to call finaliseKyc API', async () => {
      const customerDto: CustomerUpdatedEventDto = {
        customerUuid,
        entityUuid,
        kyc: { status: KYCStatus.REVIEW },
      } as any;
      await axios.patch(`${endpoint}/${customerDto.customerUuid}/finaliseKyc`, JSON.stringify(customerDto), {
        headers: { 'Content-Type': 'application/json' },
      });
      const ret = await client.query({
        name: 'update-customer',
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customerDto.customerUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      const item = ret.rows[0];
      expect(item.customerUuid).toBe(customerDto.customerUuid);
      expect(item.kyc.status).toBe(customerDto.kyc?.status);
    });

    it('should be able to update KYC Checkpoints', async () => {
      const dto = {
        kycCheckpoints: ['IDV'],
        id: customerUuid,
      };
      await axios.post(`${endpoint}/${customerUuid}/updateKycCheckpoint`, JSON.stringify(dto), {
        headers: { 'Content-Type': 'application/json' },
      });
      await sleep(5000);

      const response = await client.query({
        name: 'update-customer',
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customerUuid, domicile],
      });

      console.log('Customer table response', JSON.stringify(response));

      expect(response.rows.length).toBe(1);
      const item = response.rows[0];
      expect(item.customerUuid).toBe(customerUuid);
      expect(item.kycCheckpoints).toStrictEqual(dto.kycCheckpoints);
    });

    it('should be able to merge the existing KYC checkpoints with new update KYC Checkpoints', async () => {
      await axios.post(
        `${endpoint}/${customerUuid}/updateKycCheckpoint`,
        JSON.stringify({
          kycCheckpoints: ['IDV'],
          id: customerUuid,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      await sleep(5000);

      const dto = {
        kycCheckpoints: ['SELFIE_VERIFICATION'],
        id: customerUuid,
      };
      await axios.post(`${endpoint}/${customerUuid}/updateKycCheckpoint`, JSON.stringify(dto), {
        headers: { 'Content-Type': 'application/json' },
      });
      await sleep(5000);

      const response = await client.query({
        name: 'update-customer',
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customerUuid, domicile],
      });

      console.log('Customer table response', JSON.stringify(response));

      expect(response.rows.length).toBe(1);
      const item = response.rows[0];
      expect(item.customerUuid).toBe(customerUuid);
      expect(item.kycCheckpoints).toStrictEqual(['IDV', 'SELFIE_VERIFICATION']);
    });

    it('should not add duplicate values in the existing KYC checkpoints', async () => {
      // Add IDV
      await axios.post(
        `${endpoint}/${customerUuid}/updateKycCheckpoint`,
        JSON.stringify({
          kycCheckpoints: ['IDV'],
          id: customerUuid,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      await sleep(5000);

      // Add SELFIE_VERIFICATION
      await axios.post(
        `${endpoint}/${customerUuid}/updateKycCheckpoint`,
        JSON.stringify({
          kycCheckpoints: ['SELFIE_VERIFICATION'],
          id: customerUuid,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      await sleep(5000);

      // Add SELFIE_VERIFICATION again
      const dto = {
        kycCheckpoints: ['SELFIE_VERIFICATION'],
        id: customerUuid,
      };
      await axios.post(`${endpoint}/${customerUuid}/updateKycCheckpoint`, JSON.stringify(dto), {
        headers: { 'Content-Type': 'application/json' },
      });
      await sleep(5000);

      const response = await client.query({
        name: 'update-customer',
        text: `SELECT * FROM "Customers" WHERE "customerUuid" = $1 AND "domicile" = $2`,
        values: [customerUuid, domicile],
      });

      console.log('Customer table response', JSON.stringify(response));

      expect(response.rows.length).toBe(1);
      const item = response.rows[0];
      expect(item.customerUuid).toBe(customerUuid);
      // Should not have duplicated SELFIE_VERIFICATION in the array
      expect(item.kycCheckpoints).toStrictEqual(['IDV', 'SELFIE_VERIFICATION']);
    });
  });
});
