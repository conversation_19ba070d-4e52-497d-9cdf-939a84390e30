import { Timezone } from '@npco/bff-common/dist/utils/timezoneUtils';
import { AddressState } from '@npco/component-dto-core';
import { SiteType, RefundPinType } from '@npco/component-dto-site';
import { type SiteCreatedEventDto, type SiteUpdatedEventDto, TenderType } from '@npco/component-dto-site';
import { surchargesTaxes } from '@npco/component-dto-site/dist/mockTestData';
import { timezone } from '@npco/component-dto-site/dist/mockTestData/mockdata.timezone';

import axios from 'axios';
import { Client } from 'pg';
import { v4 as uuidv4 } from 'uuid';

import type { ApiEndpointParamConfig } from './utils';
import {
  domicile,
  getApiEndpoint,
  getDbClientOptions,
  getStage,
  getVersionedApiEndpoint,
  isRegionAP,
  onlyRunOnST,
} from './utils';

describe('site test', () => {
  let client: Client;
  const siteUuid = uuidv4();
  const entityUuid = uuidv4();
  let baseEndpoint: string;
  let endpoint: string;

  beforeAll(async () => {
    const options = await getDbClientOptions();
    console.log('options:', options);
    client = new Client(options);
    await client.connect();
    await client.query(`SET search_path TO ${options.search_path}`);
    baseEndpoint = isRegionAP ? `${await getApiEndpoint()}/v1` : `${await getApiEndpoint()}/v2/${domicile}`;
    endpoint = `${baseEndpoint}/site`;
  });

  afterAll(async () => {
    await client.end();
  });

  it('should be able to create a site', async () => {
    const dto: SiteCreatedEventDto = {
      siteUuid,
      name: uuidv4(),
      address: {
        postcode: uuidv4(),
        state: AddressState.VIC,
        street: uuidv4(),
        suburb: uuidv4(),
      },
      surchargesTaxes: {} as any,
      pin: uuidv4(),
      type: SiteType.MOBILE,
      entityUuid,
      timezone,
    };
    await axios.post(endpoint, JSON.stringify(dto), {
      headers: { 'Content-Type': 'application/json' },
    });

    const ret = await client.query({
      name: 'create-site',
      text: `SELECT * FROM "Sites" WHERE "siteUuid" = $1 AND "domicile" = $2`,
      values: [dto.siteUuid, domicile],
    });
    const { taxes, ...expectedSurchargesTaxes } = surchargesTaxes;
    expect(ret.rows.length).toBe(1);
    const item = ret.rows[0];
    expect(item.siteUuid).toBe(dto.siteUuid);
    expect(item.entityUuid).toBe(dto.entityUuid);
    expect(item.pin).toBe(dto.pin);
    expect(item.refundRequiresPin).toBe(false);
    expect(item.address).toEqual(dto.address);
    expect(item.type).toBe(dto.type);
    expect(item.schemes).toBeDefined();
    expect(item.schemesCnp).toBeDefined();
    expect(item.schemesMoto).toBeDefined();
    expect(item.tipping).toBeDefined();
    expect(item.receipt).toBeDefined();
    expect(item.vtEnabled).toBe(true);
    expect(item.surchargesTaxes).toEqual(expectedSurchargesTaxes);
    expect(item.features).toEqual({
      declineSoundEnabled: true,
      splitPaymentEnabled: false,
      restrictReportAccessEnabled: true,
    });
    expect(item.domicile).toBe(domicile);
    expect(item.posSettings).toStrictEqual({
      zellerPosSettings: {
        tenderTypes: [TenderType.CARD],
      },
    });
    expect(item.timezone).toEqual(timezone);
  });

  it('should be able to update a site', async () => {
    const dto: SiteUpdatedEventDto = {
      siteUuid,
      entityUuid,
      name: uuidv4(),
      address: {
        postcode: uuidv4(),
        state: AddressState.VIC,
        street: uuidv4(),
        suburb: uuidv4(),
      },
      refundPin: '1234',
      refundPinType: RefundPinType.REFUND_PIN,
      type: SiteType.MOBILE,
      refundRequiresPin: false,
      surchargesTaxes: {
        surchargeAllowed: true,
        surchargeEnabled: true,
        surchargeEnabledMoto: true,
        surchargePercent: 1,
        feePercent: 1,
        gstEnabled: true,
        gstPercent: 1,
        surchargePercentMoto: 11,
        feePercentMoto: 12,
        surchargeFullFees: true,
        surchargeFullFeesMoto: true,
        feesSurchargeCp: {
          feePercent: 2,
          feeFixed: 1,
          surchargeFullFees: false,
          surchargeEnabled: false,
          surchargePercent: 1,
        },
        feesSurchargeMoto: {
          feePercent: 3,
          feeFixed: 1,
          surchargeFullFees: false,
          surchargeEnabled: false,
          surchargePercent: 4,
        },
        feesSurchargeVt: {
          feePercent: 3,
          feeFixed: 0,
          surchargeFullFees: false,
          surchargeEnabled: false,
          surchargePercent: 4,
        },
        feesSurchargeCpoc: {
          feePercent: 9,
          surchargeFullFees: false,
          surchargeEnabled: false,
          surchargePercent: 8,
          feeFixed: 10,
        },
        feesSurchargeXinv: {
          feePercent: 12,
          surchargeFullFees: false,
          surchargeEnabled: false,
          surchargePercent: 11,
          feeFixed: 13,
          surchargePercentIntl: 15,
          feePercentIntl: 14,
          feeFixedIntl: 16,
        },
        feesSurchargeZinv: {
          feePercent: 12,
          surchargeFullFees: false,
          surchargeEnabled: false,
          surchargePercent: 11,
          feeFixed: 13,
          surchargePercentIntl: 15,
          feePercentIntl: 14,
          feeFixedIntl: 16,
        },
        feesSurchargePbl: {
          feePercent: 12,
          surchargeFullFees: false,
          surchargeEnabled: false,
          surchargePercent: 11,
          feeFixed: 13,
          surchargePercentIntl: 15,
          feePercentIntl: 14,
          feeFixedIntl: 16,
        },
        taxes: [
          {
            percent: 2,
            name: 'tax',
          },
        ],
      },
      schemes: [
        {
          name: 'VISA',
        },
      ],
      schemesCnp: [
        {
          name: 'MC',
        },
      ],
      schemesMoto: [
        {
          name: 'AMEX',
        },
      ],
      moto: {
        enabled: true,
        defaultEntryMethod: true,
        requiresPin: true,
      },
      invoice: {
        bccEmail: 'bccEmail',
        businessAddress: 'businessAddress',
        itemsApplyGst: true,
        discountsEnabled: true,
        itemsGstPriceInclusive: true,
        pdfIncludesAddress: true,
        reminderOnDue: true,
        remindersDaysAfterDue: [1, 2, 3],
        remindersDaysBeforeDue: [4, 5, 6],
        sendBccCopy: true,
      },
      receipt: {
        merchantCopy: true,
        name: 'name',
        address1: 'address1',
        address2: 'address2',
        number: 'number',
        phone: '*********',
        email: 'email',
        message: 'message',
        returnsMessage: 'returnsMessage',
        website: 'website',
        instagram: 'instagram',
        facebook: 'facebook',
        twitter: 'twitter',
        logo: 'logo',
        logoMonochrome: '',
        printLogo: true,
        printSocials: true,
        printDeclinedTransaction: true,
      },
      tipping: {
        customTipAllowed: true,
        enabled: true,
        tipPercent1: 1,
        tipPercent2: 1,
        tipPercent3: 1,
      },
      features: {
        declineSoundEnabled: false,
        splitPaymentEnabled: true,
        restrictReportAccessEnabled: false,
      },
      screensaver: {
        primaryColour: 'colour',
        primaryLogoUuid: 'logoUuid',
        primaryLogoUrl: 'logoUrl',
        logos: [{ logoUuid: 'logoUuid', url: 'logoUrl' }],
        customColours: ['colour'],
      },
      posSettings: {
        zellerPosSettings: {
          tenderTypes: [TenderType.CARD, TenderType.CASH],
        },
      },
      timezone: Timezone.AUSTRALIA_BRISBANE,
    };

    await axios.patch(`${endpoint}/${dto.siteUuid}`, JSON.stringify(dto), {
      headers: { 'Content-Type': 'application/json' },
    });
    const ret = await client.query({
      name: 'update-site',
      text: `SELECT * FROM "Sites" WHERE "siteUuid" = $1 AND "domicile" = $2`,
      values: [dto.siteUuid, domicile],
    });
    expect(ret.rows.length).toBe(1);
    const item = ret.rows[0];
    expect(item.siteUuid).toBe(dto.siteUuid);
    expect(item.entityUuid).toBe(dto.entityUuid);
    expect(item.refundRequiresPin).toBe(false);
    expect(item.refundPin).toBe(dto.refundPin);
    expect(item.refundPinType).toBe(dto.refundPinType);
    expect(item.type).toBe(dto.type);
    expect(item.address).toEqual(dto.address);
    expect(item.tipping).toEqual(dto.tipping);
    expect(item.receipt).toEqual(dto.receipt);
    expect(item.schemes).toEqual(dto.schemes);
    expect(item.schemesCnp).toEqual(dto.schemesCnp);
    expect(item.schemesMoto).toEqual(dto.schemesMoto);
    expect(item.moto).toEqual(dto.moto);
    expect(item.surchargesTaxes).toEqual(dto.surchargesTaxes);
    expect(item.features).toEqual(dto.features);
    expect(item.invoice).toEqual(dto.invoice);
    expect(item.screensaver).toEqual(dto.screensaver);
    expect(item.posSettings).toEqual(dto.posSettings);
    expect(item.timezone).toEqual(dto.timezone);
  });

  it('should be able to update site address concurrently', async () => {
    const updatedEvents = [
      {
        key: 'street',
        value: uuidv4(),
      },
      {
        key: 'suburb',
        value: uuidv4(),
      },
      {
        key: 'state',
        value: uuidv4(),
      },
      {
        key: 'postcode',
        value: uuidv4(),
      },
      {
        key: 'country',
        value: uuidv4(),
      },
    ];
    const proms = updatedEvents.map((event) => {
      const updatedEvent = { siteUuid, address: {}, entityUuid };
      (updatedEvent.address as any)[event.key] = event.value;
      return axios.patch(`${endpoint}/${siteUuid}`, JSON.stringify(updatedEvent), {
        headers: { 'Content-Type': 'application/json' },
      });
    });
    await Promise.all(proms);

    const ret = await client.query({
      name: 'get-site',
      text: `SELECT * FROM "Sites" WHERE "siteUuid" = $1 AND "domicile" = $2`,
      values: [siteUuid, domicile],
    });
    expect(ret.rows.length).toBe(1);
    expect(ret.rows[0].address).toEqual({
      street: updatedEvents[0].value,
      suburb: updatedEvents[1].value,
      state: updatedEvents[2].value,
      postcode: updatedEvents[3].value,
      country: updatedEvents[4].value,
    });
  });

  it('should be able to update site moto concurrently', async () => {
    const updatedEvents = [
      {
        key: 'enabled',
        value: true,
      },
      {
        key: 'requiresPin',
        value: false,
      },
      {
        key: 'defaultEntryMethod',
        value: true,
      },
    ];
    const proms = updatedEvents.map((event) => {
      const updatedEvent = { siteUuid, moto: {}, entityUuid };
      (updatedEvent.moto as any)[event.key] = event.value;
      return axios.patch(`${endpoint}/${siteUuid}`, JSON.stringify(updatedEvent), {
        headers: { 'Content-Type': 'application/json' },
      });
    });
    await Promise.all(proms);

    const ret = await client.query({
      name: 'get-customer',
      text: `SELECT * FROM "Sites" WHERE "siteUuid" = $1 AND "domicile" = $2`,
      values: [siteUuid, domicile],
    });
    expect(ret.rows.length).toBe(1);
    expect(ret.rows[0].moto).toEqual({
      enabled: updatedEvents[0].value,
      requiresPin: updatedEvents[1].value,
      defaultEntryMethod: updatedEvents[2].value,
    });
  });

  describe('cnp site tests', () => {
    it('should be able to create zeller site', async () => {
      const dto = {
        type: SiteType.CNP_ZELLER_INVOICE,
        entityUuid: uuidv4(),
      };
      const res = await axios.post(endpoint, JSON.stringify(dto), {
        headers: { 'Content-Type': 'application/json' },
      });

      const ret = await client.query({
        name: 'create-site',
        text: `SELECT * FROM "Sites" WHERE "siteUuid" = $1 AND "domicile" = $2`,
        values: [res.data.siteUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      const item = ret.rows[0];
      expect(item.entityUuid).toBe(dto.entityUuid);
      expect(item.type).toBe(dto.type);
      expect(item.schemesCnp).toBeDefined();
      expect(item.receipt).toBeDefined();
      expect(item.surchargesTaxes).toBeDefined();
      expect(item.invoice).toBeDefined();
      expect(item.entityUuid).toBe(res.data.entityUuid);
      expect(item.type).toBe(res.data.type);
      expect(res.data.schemesCnp).toBeDefined();
      expect(res.data.receipt).toBeDefined();
      expect(res.data.surchargesTaxes).toBeDefined();
      expect(res.data.invoice).toBeDefined();
      expect(item.vtEnabled).toBe(false);
    });

    it('should be able to create xero site', async () => {
      const dto = {
        businessName: uuidv4(),
        address: {
          postcode: uuidv4(),
          state: AddressState.VIC,
          street: uuidv4(),
          suburb: uuidv4(),
        },
        type: SiteType.CNP_XERO_INVOICE,
        entityUuid: uuidv4(),
      };
      const res = await axios.post(endpoint, JSON.stringify(dto), {
        headers: { 'Content-Type': 'application/json' },
      });

      const ret = await client.query({
        name: 'create-site',
        text: `SELECT * FROM "Sites" WHERE "siteUuid" = $1 AND "domicile" = $2`,
        values: [res.data.siteUuid, domicile],
      });
      expect(ret.rows.length).toBe(1);
      const item = ret.rows[0];
      expect(item.entityUuid).toBe(dto.entityUuid);
      expect(item.receipt.name).toBe(dto.businessName);
      expect(item.address).toEqual(dto.address);
      expect(item.type).toBe(dto.type);
      expect(item.schemesCnp).toBeDefined();
      expect(item.receipt).toBeDefined();
      expect(item.surchargesTaxes).toBeDefined();
      expect(item.entityUuid).toBe(res.data.entityUuid);
      expect(item.type).toBe(res.data.type);
      expect(item.address).toEqual(res.data.address);
      expect(res.data.schemesCnp).toBeDefined();
      expect(res.data.receipt).toBeDefined();
      expect(res.data.surchargesTaxes).toBeDefined();
      expect(item.receipt.name).toBe(res.data.receipt.name);
      expect(item.vtEnabled).toBe(false);
    });
  });

  onlyRunOnST(getStage())('should be able to update site surchargesTaxes concurrently', async () => {
    const updatedEvents = [
      {
        key: 'surchargeEnabled',
        value: true,
      },
      {
        key: 'surchargePercent',
        value: 11,
      },
      {
        key: 'surchargeFullFees',
        value: true,
      },
      {
        key: 'surchargeEnabledMoto',
        value: true,
      },
      {
        key: 'surchargePercentMoto',
        value: 22,
      },
      {
        key: 'surchargeFullFeesMoto',
        value: true,
      },
      {
        key: 'feePercent',
        value: 33,
      },
      {
        key: 'feePercentMoto',
        value: 44,
      },

      {
        key: 'gstEnabled',
        value: true,
      },
      {
        key: 'gstPercent',
        value: 55,
      },
      {
        key: 'taxes',
        value: [{ name: uuidv4(), percent: 22 }],
      },
      {
        key: 'feesSurchargeCp',
        value: { feePercent: 45 },
      },
      {
        key: 'feesSurchargeCp',
        value: { surchargeFullFees: true },
      },
      {
        key: 'feesSurchargeCp',
        value: { surchargeEnabled: true },
      },
      {
        key: 'feesSurchargeCp',
        value: { surchargePercent: 46 },
      },
      {
        key: 'feesSurchargeCp',
        value: { feeFixed: 52 },
      },
      {
        key: 'feesSurchargeCpoc',
        value: { feePercent: 50 },
      },
      {
        key: 'feesSurchargeCpoc',
        value: { surchargeFullFees: true },
      },
      {
        key: 'feesSurchargeCpoc',
        value: { surchargeEnabled: true },
      },
      {
        key: 'feesSurchargeCpoc',
        value: { surchargePercent: 51 },
      },
      {
        key: 'feesSurchargeCpoc',
        value: { feeFixed: 52 },
      },
      {
        key: 'feesSurchargeMoto',
        value: { feePercent: 53 },
      },
      {
        key: 'feesSurchargeMoto',
        value: { surchargeFullFees: true },
      },
      {
        key: 'feesSurchargeMoto',
        value: { surchargeEnabled: true },
      },
      {
        key: 'feesSurchargeMoto',
        value: { surchargePercent: 54 },
      },
      {
        key: 'feesSurchargeMoto',
        value: { feeFixed: 59 },
      },
      {
        key: 'feesSurchargeXinv',
        value: { feePercent: 56 },
      },
      {
        key: 'feesSurchargeXinv',
        value: { surchargeFullFees: true },
      },
      {
        key: 'feesSurchargeXinv',
        value: { surchargeEnabled: true },
      },
      {
        key: 'feesSurchargeXinv',
        value: { surchargePercent: 57 },
      },
      {
        key: 'feesSurchargeXinv',
        value: { feePercentIntl: 58 },
      },
      {
        key: 'feesSurchargeXinv',
        value: { feeFixed: 59 },
      },
      {
        key: 'feesSurchargeXinv',
        value: { surchargePercentIntl: 60 },
      },
      {
        key: 'feesSurchargeXinv',
        value: { feeFixedIntl: 61 },
      },
      {
        key: 'feesSurchargeZinv',
        value: { feePercent: 62 },
      },
      {
        key: 'feesSurchargeZinv',
        value: { surchargeFullFees: true },
      },
      {
        key: 'feesSurchargeZinv',
        value: { surchargeEnabled: true },
      },
      {
        key: 'feesSurchargeZinv',
        value: { surchargePercent: 63 },
      },
      {
        key: 'feesSurchargeZinv',
        value: { feePercentIntl: 64 },
      },
      {
        key: 'feesSurchargeZinv',
        value: { feeFixed: 65 },
      },
      {
        key: 'feesSurchargeZinv',
        value: { surchargePercentIntl: 66 },
      },
      {
        key: 'feesSurchargeZinv',
        value: { feeFixedIntl: 67 },
      },
      {
        key: 'feesSurchargeVt',
        value: { feePercent: 68 },
      },
      {
        key: 'feesSurchargeVt',
        value: { surchargeFullFees: true },
      },
      {
        key: 'feesSurchargeVt',
        value: { surchargeEnabled: true },
      },
      {
        key: 'feesSurchargeVt',
        value: { surchargePercent: 68 },
      },
      {
        key: 'feesSurchargeVt',
        value: { feeFixed: 70 },
      },
      {
        key: 'feesSurchargePbl',
        value: { feePercent: 69 },
      },
      {
        key: 'feesSurchargePbl',
        value: { surchargeFullFees: true },
      },
      {
        key: 'feesSurchargePbl',
        value: { surchargeEnabled: true },
      },
      {
        key: 'feesSurchargePbl',
        value: { surchargePercent: 69 },
      },
      {
        key: 'feesSurchargePbl',
        value: { feePercentIntl: 69 },
      },
      {
        key: 'feesSurchargePbl',
        value: { feeFixed: 69 },
      },
      {
        key: 'feesSurchargePbl',
        value: { surchargePercentIntl: 66 },
      },
      {
        key: 'feesSurchargePbl',
        value: { feeFixedIntl: 67 },
      },
      {
        key: 'surchargeAllowed',
        value: true,
      },
    ];
    const proms = updatedEvents.map((event) => {
      const updatedEvent = { siteUuid, surchargesTaxes: {}, entityUuid };
      (updatedEvent.surchargesTaxes as any)[event.key] = event.value;
      return axios.patch(`${endpoint}/${siteUuid}`, JSON.stringify(updatedEvent), {
        headers: { 'Content-Type': 'application/json' },
      });
    });

    // To limit lambda concurreny
    for (let index = 0; index < proms.length; index += 15) {
      const updates = proms.slice(index, index + 15);
      await Promise.all(updates);
    }

    const ret = await client.query({
      name: 'get-customer',
      text: `SELECT * FROM "Sites" WHERE "siteUuid" = $1 AND "domicile" = $2`,
      values: [siteUuid, domicile],
    });

    expect(ret.rows.length).toBe(1);
    expect(ret.rows[0].surchargesTaxes).toEqual({
      surchargeEnabled: updatedEvents[0].value,
      surchargePercent: updatedEvents[1].value,
      surchargeFullFees: updatedEvents[2].value,
      surchargeEnabledMoto: updatedEvents[3].value,
      surchargePercentMoto: updatedEvents[4].value,
      surchargeFullFeesMoto: updatedEvents[5].value,
      feePercent: updatedEvents[6].value,
      feePercentMoto: updatedEvents[7].value,
      gstEnabled: updatedEvents[8].value,
      gstPercent: updatedEvents[9].value,
      taxes: updatedEvents[10].value,
      feesSurchargeCp: {
        ...(updatedEvents[11].value as any),
        ...(updatedEvents[12].value as any),
        ...(updatedEvents[13].value as any),
        ...(updatedEvents[14].value as any),
        ...(updatedEvents[15].value as any),
      },
      feesSurchargeCpoc: {
        ...(updatedEvents[16].value as any),
        ...(updatedEvents[17].value as any),
        ...(updatedEvents[18].value as any),
        ...(updatedEvents[19].value as any),
        ...(updatedEvents[20].value as any),
      },
      feesSurchargeMoto: {
        ...(updatedEvents[21].value as any),
        ...(updatedEvents[22].value as any),
        ...(updatedEvents[23].value as any),
        ...(updatedEvents[24].value as any),
        ...(updatedEvents[25].value as any),
      },
      feesSurchargeXinv: {
        ...(updatedEvents[26].value as any),
        ...(updatedEvents[27].value as any),
        ...(updatedEvents[28].value as any),
        ...(updatedEvents[29].value as any),
        ...(updatedEvents[30].value as any),
        ...(updatedEvents[31].value as any),
        ...(updatedEvents[32].value as any),
        ...(updatedEvents[33].value as any),
      },
      feesSurchargeZinv: {
        ...(updatedEvents[34].value as any),
        ...(updatedEvents[35].value as any),
        ...(updatedEvents[36].value as any),
        ...(updatedEvents[37].value as any),
        ...(updatedEvents[38].value as any),
        ...(updatedEvents[39].value as any),
        ...(updatedEvents[40].value as any),
        ...(updatedEvents[41].value as any),
      },
      feesSurchargeVt: {
        ...(updatedEvents[42].value as any),
        ...(updatedEvents[43].value as any),
        ...(updatedEvents[44].value as any),
        ...(updatedEvents[45].value as any),
        ...(updatedEvents[46].value as any),
      },
      feesSurchargePbl: {
        ...(updatedEvents[47].value as any),
        ...(updatedEvents[48].value as any),
        ...(updatedEvents[49].value as any),
        ...(updatedEvents[50].value as any),
        ...(updatedEvents[51].value as any),
        ...(updatedEvents[52].value as any),
        ...(updatedEvents[53].value as any),
        ...(updatedEvents[54].value as any),
      },
      surchargeAllowed: updatedEvents[55].value,
    });
  });

  it('should be able to update site tip settings concurrently', async () => {
    const updatedEvents = [
      {
        key: 'enabled',
        value: true,
      },
      {
        key: 'tipPercent1',
        value: 11,
      },
      {
        key: 'tipPercent2',
        value: 22,
      },
      {
        key: 'tipPercent3',
        value: 33,
      },
      {
        key: 'customTipAllowed',
        value: true,
      },
    ];
    const proms = updatedEvents.map((event) => {
      const updatedEvent = { siteUuid, tipping: {}, entityUuid };
      (updatedEvent.tipping as any)[event.key] = event.value;
      return axios.patch(`${endpoint}/${siteUuid}`, JSON.stringify(updatedEvent), {
        headers: { 'Content-Type': 'application/json' },
      });
    });
    await Promise.all(proms);

    const ret = await client.query({
      name: 'get-customer',
      text: `SELECT * FROM "Sites" WHERE "siteUuid" = $1 AND "domicile" = $2`,
      values: [siteUuid, domicile],
    });
    expect(ret.rows.length).toBe(1);
    expect(ret.rows[0].tipping).toEqual({
      enabled: updatedEvents[0].value,
      tipPercent1: updatedEvents[1].value,
      tipPercent2: updatedEvents[2].value,
      tipPercent3: updatedEvents[3].value,
      customTipAllowed: updatedEvents[4].value,
    });
  });

  it('should be able to update site tip receipt concurrently', async () => {
    const updatedEvents = [
      {
        key: 'merchantCopy',
        value: true,
      },
      {
        key: 'name',
        value: uuidv4(),
      },
      {
        key: 'address1',
        value: uuidv4(),
      },
      {
        key: 'address2',
        value: uuidv4(),
      },
      {
        key: 'number',
        value: uuidv4(),
      },
      {
        key: 'phone',
        value: uuidv4(),
      },
      {
        key: 'email',
        value: uuidv4(),
      },
      {
        key: 'message',
        value: uuidv4(),
      },
      {
        key: 'returnsMessage',
        value: uuidv4(),
      },
      {
        key: 'website',
        value: uuidv4(),
      },
      {
        key: 'instagram',
        value: uuidv4(),
      },
      {
        key: 'facebook',
        value: uuidv4(),
      },
      {
        key: 'twitter',
        value: uuidv4(),
      },
      {
        key: 'logo',
        value: uuidv4(),
      },
      {
        key: 'logoMonochrome',
        value: uuidv4(),
      },
      {
        key: 'printLogo',
        value: false,
      },
      {
        key: 'printSocials',
        value: true,
      },
      {
        key: 'printDeclinedTransaction',
        value: true,
      },
    ];
    const proms = updatedEvents.map((event) => {
      const updatedEvent = { siteUuid, receipt: {}, entityUuid };
      (updatedEvent.receipt as any)[event.key] = event.value;
      return axios.patch(`${endpoint}/${siteUuid}`, JSON.stringify(updatedEvent), {
        headers: { 'Content-Type': 'application/json' },
      });
    });
    await Promise.all(proms);

    const ret = await client.query({
      name: 'get-customer',
      text: `SELECT * FROM "Sites" WHERE "siteUuid" = $1 AND "domicile" = $2`,
      values: [siteUuid, domicile],
    });
    expect(ret.rows.length).toBe(1);
    expect(ret.rows[0].receipt).toEqual({
      merchantCopy: updatedEvents[0].value,
      name: updatedEvents[1].value,
      address1: updatedEvents[2].value,
      address2: updatedEvents[3].value,
      number: updatedEvents[4].value,
      phone: updatedEvents[5].value,
      email: updatedEvents[6].value,
      message: updatedEvents[7].value,
      returnsMessage: updatedEvents[8].value,
      website: updatedEvents[9].value,
      instagram: updatedEvents[10].value,
      facebook: updatedEvents[11].value,
      twitter: updatedEvents[12].value,
      logo: updatedEvents[13].value,
      logoMonochrome: updatedEvents[14].value,
      printLogo: updatedEvents[15].value,
      printSocials: updatedEvents[16].value,
      printDeclinedTransaction: updatedEvents[17].value,
    });
  });

  it('should be able to get site', async () => {
    const output = await axios.get(`${endpoint}/${siteUuid}`);
    const site = output.data;
    expect(site.siteUuid).toEqual(siteUuid);
  });

  it('should be able to delete site', async () => {
    await axios.delete(`${endpoint}/${siteUuid}`);
    const ret = await client.query({
      name: 'delete-site',
      text: `SELECT * FROM "Sites" WHERE "siteUuid" = $1 AND "domicile" = $2`,
      values: [siteUuid, domicile],
    });
    expect(ret.rows.length).toBe(1);
    const item = ret.rows[0];
    expect(item.status).toBe('DELETED');
  });

  it('should throw 400 error if querying a none exist site', async () => {
    try {
      console.log('get site full path:', `${endpoint}/${uuidv4()}`);
      await axios.get(`${endpoint}/${uuidv4()}`, {
        headers: { 'Content-Type': 'application/json' },
      });
      fail('should throw exception.');
    } catch (err: any) {
      expect(err.response.status).toBe(400);
    }
  });

  describe.each<keyof ApiEndpointParamConfig>(['v1', 'v2'])(
    'should be able to create site by api version %s',
    (version) => {
      if (!isRegionAP && version === 'v1') {
        return;
      }
      const siteUuid = uuidv4();

      it('should be able to create site by api %s', async () => {
        const dto: SiteCreatedEventDto = {
          siteUuid: `${siteUuid}`,
          name: uuidv4(),
          address: {
            postcode: uuidv4(),
            state: AddressState.VIC,
            street: uuidv4(),
            suburb: uuidv4(),
          },
          surchargesTaxes: {} as any,
          pin: uuidv4(),
          type: SiteType.MOBILE,
          entityUuid,
          timezone: Timezone.AUSTRALIA_MELBOURNE,
        };

        const siteEndpoint = await getVersionedApiEndpoint({
          version,
          path: '/site',
          ...(version === 'v2' && { domicile }),
        });
        await axios.post(siteEndpoint, JSON.stringify(dto), {
          headers: { 'Content-Type': 'application/json' },
        });

        const ret = await client.query({
          name: 'create-site',
          text: `SELECT * FROM "Sites" WHERE "siteUuid" = $1 AND "domicile" = $2`,
          values: [dto.siteUuid, domicile],
        });
        expect(ret.rows.length).toBe(1);
        const item = ret.rows[0];

        expect(item.siteUuid).toBe(dto.siteUuid);
        expect(item.entityUuid).toBe(dto.entityUuid);
        expect(item.domicile).toBe(domicile);
        expect(item.timezone).toBe(dto.timezone);
      });
    },
  );
});
