import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, QueryCommand } from '@aws-sdk/lib-dynamodb';

import { getEventFromEventStore } from '../dynamodbUtils';
import { getDbStreamHandler, sendEventsToStreamHandler } from '../eventStreamService';
import { sendEventToSagaLambda } from '../replayCqrs';
import { getDbClient, getTrialEntityUuids, idInList, isDryRun, isTrial } from '../utils';
import { sleep } from '../utils/sleep';

const dynamodbClient = new DynamoDBClient();
const dbClient = DynamoDBDocumentClient.from(dynamodbClient);

export const stage = process.env.STAGE ?? 'dev';

export const queryAllEntitiesFromAms = async () => {
  const client = await getDbClient();
  if (isTrial()) {
    return getTrialEntityUuids();
  }
  const result = await client.query({
    text: `SELECT "entityUuid" FROM ${stage}."Entity" WHERE "status" != 'DELETED' AND "domicile" = 'AUS'`,
    values: [],
  });
  return result.rows.map((row: any) => row.entityUuid);
};

export const queryAllDepositBatchUuidsFromAms = async (depositUuids: string[]) => {
  const client = await getDbClient();
  if (!depositUuids?.length) {
    return [];
  }
  const result = await client.query({
    text: `SELECT DISTINCT "submittedBatchedEventUuid" FROM ${stage}."Deposits" WHERE "domicile" = 'AUS' and "depositUuid" in ${idInList(
      depositUuids,
    )}`,
    values: depositUuids,
  });
  console.log('Deposit batch UUIDs found:', result.rows);
  return (
    result.rows
      .map((row: any) => row.submittedBatchedEventUuid)
      .filter((uuid: string) => uuid !== null && uuid !== undefined) ?? []
  );
};

export const getDepositsByEntityUuid = async (entityUuid: string) => {
  let nextToken;
  let depositUuids: string[] = [];
  do {
    const params: any = {
      TableName: `${stage}-crms-engine-Entities`,
      IndexName: 'entityGsi',
      KeyConditionExpression: 'entityUuid = :entityUid and begins_with(#type, :type)',
      FilterExpression: 'attribute_not_exists(#processed)',
      ExpressionAttributeNames: {
        '#type': 'type',
        '#processed': 'processed',
      },
      ExpressionAttributeValues: {
        ':entityUid': entityUuid,
        ':type': 'deposit.',
      },
      ExclusiveStartKey: nextToken,
    };
    const result = await dbClient.send(new QueryCommand(params));
    nextToken = result?.LastEvaluatedKey;
    if (result.Items?.length) {
      console.info('Deposits found:', result.Items.length);
      depositUuids = depositUuids.concat(result.Items.map((item) => item.id));
    }
  } while (nextToken);
  return depositUuids;
};

const batch = 4;

let totalSingleReplayed = 0;
let totalBatchReplayed = 0;

const replayDeposits = async (depositUuid: string) => {
  try {
    const event = await getEventFromEventStore(stage, 'crms', depositUuid, 'Submitted');
    if (event) {
      totalSingleReplayed += 1;
      if (isDryRun()) {
        console.log('Dry run: would send event', depositUuid);
      } else {
        event.source = 'ds';
        await sendEventsToStreamHandler(getDbStreamHandler('crms'), [event]);
      }
      return true;
    }
  } catch (err) {
    console.error(`Failed to process deposit: ${depositUuid}`, err);
  }
  return false;
};

const batchReplaySingleEvents = async (depositUuids: string[]) => {
  for (let x = 0; x < depositUuids.length; x += batch) {
    const deposits = depositUuids.slice(x, x + batch);
    const result = await Promise.all(deposits.map((depositUuid: string) => replayDeposits(depositUuid)));

    const migrated = result.filter((res) => res).length;
    await sleep(migrated * 250);
    console.log(`Processed deposits ${migrated} from ${x + batch} of`, depositUuids.length);
  }
};

const replayBatchEvents = async (depositUuids: string[]) => {
  const batchUuids = await queryAllDepositBatchUuidsFromAms(depositUuids);
  console.log('Batch UUIDs:', batchUuids.length);
  const events = await Promise.all(
    batchUuids.map(async (batchUuid: string) => {
      const event = await getEventFromEventStore(stage, 'crms', batchUuid);
      if (event) {
        totalBatchReplayed += 1;
        if (isDryRun()) {
          console.log('Dry run: would send event', batchUuid);
        } else {
          const [, aggregate, name] = event.uri.split('.');
          event.uri = `ds.${aggregate}.${name}`;
          event.source = 'ds';
          await sendEventToSagaLambda('crms', event);
        }
        return true;
      }
      return false;
    }),
  );
  const migrated = events.filter((res) => res).length;
  await sleep(migrated * 250);
};

export const migrate = async () => {
  const entityUuids = await queryAllEntitiesFromAms();
  console.log('Entities:', entityUuids.length);

  let count = 0;
  for (let i = 0; i < entityUuids.length; i += batch) {
    const entities = entityUuids.slice(i, i + batch);
    const result = await Promise.all(entities.map(async (id: string) => getDepositsByEntityUuid(id)));
    const entityDeposits = result.flat();
    count += entityDeposits.length;
    console.log(`Processing deposits ${entityDeposits.length} for entities ${i + 1} of`, entityUuids.length);
    await batchReplaySingleEvents(entityDeposits);
    await replayBatchEvents(entityDeposits);
  }
  console.log('Entities replayed', entityUuids.length);
  console.log('Total deposits processed:', count);
  console.log('single events replayed:', totalSingleReplayed);
  console.log('batch events replayed:', totalBatchReplayed);
};

if (require.main === module) {
  migrate()
    .then(() => {
      console.log('finished');
      process.exit(0);
    })
    .catch((err) => {
      console.error(err);
      process.exit(-1);
    });
}

export default migrate;
