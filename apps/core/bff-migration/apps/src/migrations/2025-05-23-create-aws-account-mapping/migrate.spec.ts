import { AwsRegions } from '@npco/component-bff-core/dist/utils/domicile';

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, QueryCommand } from '@aws-sdk/lib-dynamodb';

import { migrate } from './migrate';

jest.mock('@aws-sdk/client-dynamodb', () => {
  const realDynamoDbModule = jest.requireActual('@aws-sdk/client-dynamodb');
  return {
    ...realDynamoDbModule,
    DynamoDBClient: jest.fn(() => {
      return new realDynamoDbModule.DynamoDBClient({
        endpoint: 'http://localhost:8003',
        region: 'local-env',
      });
    }),
  };
});

const stage = process.env.STAGE ?? 'dev';
const dbClient = new DynamoDBClient({
  endpoint: 'http://localhost:8003',
  region: 'local-env',
});
const documentClient = DynamoDBDocumentClient.from(dbClient);

describe('migrate', () => {
  it('should be able to write account mapping to dynamodb', async () => {
    process.env.SYDNEY_ACCOUNT_ID = '************';
    process.env.LONDON_ACCOUNT_ID = '************';
    await migrate();
    const item = await documentClient.send(
      new QueryCommand({
        TableName: `${stage}-ams-engine-DomicileLookup`,
        KeyConditionExpression: 'id = :id AND #type = :type',
        ExpressionAttributeValues: {
          ':id': 'metadata',
          ':type': 'account.id.region.mapping',
        },
        ExpressionAttributeNames: {
          '#type': 'type',
        },
      }),
    );
    expect(item.Items?.[0]).toEqual({
      id: 'metadata',
      type: 'account.id.region.mapping',
      accounts: {
        [AwsRegions.SYDNEY]: process.env.SYDNEY_ACCOUNT_ID,
        [AwsRegions.LONDON]: process.env.LONDON_ACCOUNT_ID,
      },
    });
  });
});
