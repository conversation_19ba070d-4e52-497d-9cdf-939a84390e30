import { AwsRegions } from '@npco/component-bff-core/dist/utils/domicile';

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand } from '@aws-sdk/lib-dynamodb';

const dynamoDBClient = new DynamoDBClient({ region: 'ap-southeast-2' });
const documentClient = DynamoDBDocumentClient.from(dynamoDBClient);
const stage = process.env.STAGE ?? 'dev';

export const migrate = async () => {
  const sydneyAccountId = process.env.SYDNEY_ACCOUNT_ID;
  const londonAccountId = process.env.LONDON_ACCOUNT_ID;

  const awsAccountMapping = {
    [AwsRegions.LONDON]: londonAccountId,
    [AwsRegions.SYDNEY]: sydneyAccountId,
  };

  const dbItem = {
    id: 'metadata',
    type: 'account.id.region.mapping',
    accounts: awsAccountMapping,
  };
  const tableName = `${stage}-ams-engine-DomicileLookup`;
  await documentClient.send(new PutCommand({ TableName: tableName, Item: dbItem }));
};

if (require.main === module) {
  migrate()
    .then(() => {
      console.log('finished');
      process.exit(0);
    })
    .catch((err) => {
      console.error(err);
      process.exit(-1);
    });
}

export default migrate;
