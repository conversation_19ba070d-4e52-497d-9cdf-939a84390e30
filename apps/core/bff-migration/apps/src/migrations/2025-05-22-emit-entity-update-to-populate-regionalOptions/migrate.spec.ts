import '../commonMocks';
import { EntityUpdatedEventDto } from '@npco/component-dto-entity';

import { v4 } from 'uuid';

import { migrate } from './migrate';

const REGIONS = ['ap-southeast-2', 'eu-west-2'];
const mockInvokeAsyncLambda = jest.fn();
jest.mock('../utils', () => {
  const actual = jest.requireActual('../utils');

  return {
    ...actual,
    invokeAsyncLambda: jest.fn((arg1, arg2) => mockInvokeAsyncLambda(arg1, arg2)),
  };
});

const mockQuery = jest.fn();
jest.mock('pg', () => {
  return {
    Client: jest.fn(() => ({
      connect: jest.fn(),
      query: mockQuery,
      end: jest.fn(),
    })),
  };
});

describe('emit entity HubspotObjectIdUpdated events', () => {
  const OLD_ENV = process.env;
  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
    process.env = { ...OLD_ENV };
  });

  it.each(REGIONS)('should be able to call migrate in %s successfully', async (region) => {
    process.env.AWS_REGION = region;

    const amsCqrs = `dev-ams-cqrs-commandHandlers-handler`;
    const entityRecord = {
      entityUuid: v4(),
    };

    mockQuery.mockResolvedValue({ rows: [entityRecord] }); // mock ams query
    await migrate();
    expect(mockInvokeAsyncLambda).toHaveBeenCalledWith(amsCqrs, {
      uri: 'Entity.Updated',
      dto: new EntityUpdatedEventDto({
        entityUuid: entityRecord.entityUuid,
        regionalOptions: {
          surchargeAllowed: region === 'ap-southeast-2',
        },
      }),
    });
  });
});
