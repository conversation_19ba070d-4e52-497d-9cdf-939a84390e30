#!/bin/sh
set -euo pipefail

env
echo "start run 2025-05-22-emit-entity-update-to-populate-regionalOptions"
NODE_OPTIONS='--max-old-space-size=12000' yarn exec ts-node src/migrations/2025-05-22-emit-entity-update-to-populate-regionalOptions/migrate.ts

filename=$(basename `pwd`)
echo $filename
tar -czf $filename.tar.gz .

account=`aws sts get-caller-identity --query Account --output text`
bucketname="bff-migration-deployment-ap-southeast-2-$account"
fileObject="s3://$bucketname/migrationEcsArtifacts/$CODEBUILD_BUILD_ID/`date +%Y-%m-%dT%H:%M:%S`.tar.gz"
echo upload to s3 bucket $fileObject
aws s3 cp $filename.tar.gz $fileObject
