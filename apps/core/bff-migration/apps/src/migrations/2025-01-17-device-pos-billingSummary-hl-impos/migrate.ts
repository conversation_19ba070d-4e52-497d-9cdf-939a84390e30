import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';

import axios from 'axios';

import { getDbClient, isTrial, isDryRun, getTrialEntityUuid, getAmsEndpoint } from '../utils';
import { sleep } from '../utils/sleep';

const queryDeviceUuidsFromAms = async (stage: string, domicile: string) => {
  const client = await getDbClient();

  let queryText = `
  SELECT "deviceUuid"
  FROM "Devices"
  WHERE "status" != 'DELETED'
  AND "domicile" = '${domicile}'
  AND (
      NOT "posSettings" ? 'hlSettings'
      OR NOT "posSettings" ? 'imposSettings'
      OR ("posSettings"::jsonb ? 'hlSettings' AND ("posSettings"::jsonb -> 'hlSettings' -> 'billingSummary')::bool=true)
  )`;

  if (isTrial()) {
    const entityUuidFilter = `AND "entityUuid" = '${getTrialEntityUuid(stage)}' LIMIT 5`;
    queryText = `${queryText} ${entityUuidFilter}`;
  }

  const ids = await client.query({
    text: queryText,
    values: [],
  });

  console.log('Ids from ams pgs:', ids?.rows?.length);
  return ids.rows;
};

export const updateDeviceSettings = async (deviceUuid: string, domicile: string) => {
  try {
    if (isDryRun()) {
      console.log('Dry run. Skipping update. Device:', deviceUuid);
      return;
    }
    console.log('Updating settings for device:', deviceUuid);
    const amsEndpoint = await getAmsEndpoint();
    const url = `${amsEndpoint}/v2/${domicile}/device/${deviceUuid}`;
    const updatePayload = {
      id: deviceUuid,
      posSettings: {
        hlSettings: { billingSummary: false },
        imposSettings: { billingSummary: true },
      },
    };

    console.debug(`Sending request to ${url}`, updatePayload);
    const response = await axios.patch(url, JSON.stringify(updatePayload), {
      headers: { 'Content-Type': 'application/json' },
    });
    console.log('Update device response:', response.status);
  } catch (err: any) {
    console.log(`Failed saving device "${deviceUuid}": ${err?.message} ${JSON.stringify(err, null, 2)}`);
  }
};

const batch = 4;

export const migrate = async () => {
  const stage = process.env.STAGE || 'dev';
  const domicile = (process.env.DOMICILE as Domicile) ?? Domicile.AU;

  if (isTrial()) {
    console.log(`
      ####
      TRIAL RUN. STAGE: ${stage}
      ####`);
  }

  const deviceUuids = await queryDeviceUuidsFromAms(stage, domicile);
  for (let i = 0; i < deviceUuids.length; i += batch) {
    const ids = deviceUuids.slice(i, i + batch);

    await Promise.all(ids.map((device) => updateDeviceSettings(device.deviceUuid, domicile)));
    await sleep();
    console.log('count:', i, 'of', deviceUuids.length);
  }
};

/* istanbul ignore next */
if (require.main === module) {
  migrate()
    .then(() => {
      console.log('finished');
      process.exit(0);
    })
    .catch((err) => {
      console.error(err);
      process.exit(-1);
    });
}

export default migrate;
