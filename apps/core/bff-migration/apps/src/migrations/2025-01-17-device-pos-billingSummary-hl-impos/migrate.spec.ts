import axios from 'axios';

import '../commonMocks';
import { migrate, updateDeviceSettings } from './migrate';

jest.mock('axios');
jest.mock('../utils', () => {
  const actual = jest.requireActual('../utils');

  return {
    ...actual,
    getAmsEndpoint: jest.fn().mockResolvedValue('https://ams.example.com'),
    getSsmParameter: jest.fn().mockResolvedValue(''),
  };
});

const mockQuery = jest.fn();

jest.mock('pg', () => {
  return {
    Client: jest.fn(() => ({
      connect: jest.fn(),
      query: mockQuery,
      end: jest.fn(),
    })),
  };
});

const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('update device hlSettings migration', () => {
  afterEach(() => {
    mockQuery.mockReset();
  });

  it('should be able to run with DRY_RUN', async () => {
    mockQuery
      .mockImplementationOnce(() => Promise.resolve({})) // set search_path
      .mockImplementationOnce(() =>
        Promise.resolve({
          rows: [{ deviceUuid: 'testuuid1' }, { deviceUuid: 'testuuid2' }],
        }),
      );

    await migrate();
    expect(mockedAxios).toHaveBeenCalledTimes(0);
  });

  it('should be able to update device settings', async () => {
    process.env.DRY_RUN = 'false';
    mockQuery.mockImplementationOnce(() =>
      Promise.resolve({
        rows: [{ deviceUuid: 'testuuid1' }, { deviceUuid: 'testuuid2' }],
      }),
    );
    mockedAxios.patch.mockResolvedValue({
      status: 200,
    });

    console.log('NORMAL');

    await migrate();

    expect(mockedAxios.patch).toHaveBeenCalledTimes(2);
    expect(mockedAxios.patch).toHaveBeenCalledWith(
      'https://ams.example.com/v2/AUS/device/testuuid1',
      JSON.stringify({
        id: 'testuuid1',
        posSettings: {
          hlSettings: { billingSummary: false },
          imposSettings: { billingSummary: true },
        },
      }),
      { headers: { 'Content-Type': 'application/json' } },
    );
    expect(mockedAxios.patch).toHaveBeenCalledWith(
      'https://ams.example.com/v2/AUS/device/testuuid2',
      JSON.stringify({
        id: 'testuuid2',
        posSettings: {
          hlSettings: { billingSummary: false },
          imposSettings: { billingSummary: true },
        },
      }),
      { headers: { 'Content-Type': 'application/json' } },
    );
  });

  it('should not throw when calling the lambda fails', async () => {
    mockedAxios.patch.mockImplementationOnce(() => Promise.reject(new Error('something failed')));
    await expect(updateDeviceSettings('dev', 'testuuid')).resolves.toBeUndefined();
  });
});
