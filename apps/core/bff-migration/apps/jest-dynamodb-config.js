/* eslint-disable */
module.exports = {
  port: 8003,
  tables: [
    {
      TableName: 'migration',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'buildId', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'buildId', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-ams-engine-GlobalDevices',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-fs-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-ams-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-mp-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-os-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-dbs-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-crms-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-cnp-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-nms-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-sis-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-crms-engine-Entities',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'status', AttributeType: 'S' },
        { AttributeName: 'entityUuid', AttributeType: 'S' },
        { AttributeName: 'wasOfflineApproved', AttributeType: 'S' },
        { AttributeName: 'hubspotCompanyId', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'typeGsiV2',
          KeySchema: [
            {
              AttributeName: 'type',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'id',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'hubspotCompanyGsi',
          KeySchema: [
            {
              AttributeName: 'hubspotCompanyId',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'depositsPendingGsi',
          KeySchema: [
            {
              AttributeName: 'status',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'entityGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'wasOfflineApprovedGsi',
          KeySchema: [
            {
              AttributeName: 'wasOfflineApproved',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            NonKeyAttributes: [
              'id',
              'entityUuid',
              'status',
              'timestamp',
              'feeAmount',
              'amount',
              'transactionType',
              'standInStatus',
              'tipAmount',
              'taxAmounts',
            ],
            ProjectionType: 'INCLUDE',
          },
        },
      ],
    },
    {
      TableName: 'dev-dbs-api-dynamodb-Devices',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'entityUuid', AttributeType: 'S' },
        { AttributeName: 'status', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'entityGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'depositsPendingGsi',
          KeySchema: [
            {
              AttributeName: 'status',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'typeGsiV2',
          KeySchema: [
            {
              AttributeName: 'type',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'id',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-dbs-api-dynamodb-SessionCache',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-sdk-api-dynamodb-SessionCache',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-mp-api-dynamodb-Entities',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'entityUuid', AttributeType: 'S' },
        { AttributeName: 'status', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'typeGsiV2',
          KeySchema: [
            {
              AttributeName: 'type',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'id',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'depositsPendingGsi',
          KeySchema: [
            {
              AttributeName: 'status',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'entityGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-mp-api-dynamodb-Totals',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'secondaryPk', AttributeType: 'S' },
        { AttributeName: 'sortKey', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'secondaryGsiV1',
          KeySchema: [
            { AttributeName: 'secondaryPk', KeyType: 'HASH' },
            { AttributeName: 'sortKey', KeyType: 'RANGE' },
          ],
          Projection: { ProjectionType: 'ALL' },
        },
      ],
    },
    {
      TableName: 'dev-mp-api-dynamodb-Merchant',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-mp-api-dynamodb-SessionCache',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-posconnector-api-Pairing',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'typeGsi',
          KeySchema: [
            {
              AttributeName: 'type',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'id',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-ds-engine-deposits',
      KeySchema: [{ AttributeName: 'entityIdSiteIdProcessingDateTimeAEST', KeyType: 'HASH' }],
      AttributeDefinitions: [
        { AttributeName: 'entityIdSiteIdProcessingDateTimeAEST', AttributeType: 'S' },
        { AttributeName: 'entityIdSiteId', AttributeType: 'S' },
        { AttributeName: 'processingDateTimeAEST', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'entityIdSiteIdGsi',
          KeySchema: [
            {
              AttributeName: 'entityIdSiteId',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'processingDateTimeAEST',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-bff-migration',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'buildId', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'buildId', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-cnp-api-dynamodb-Entities',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'entityUuid', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'typeGsi',
          KeySchema: [
            {
              AttributeName: 'type',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'id',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'entityGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-cims-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-ims-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-zpos-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-mp-api-dynamodb-Orders',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'entityUuid', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'entityGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    ,
    {
      TableName: 'dev-zpos-engine-dynamodb-Transactions',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'entityUuid', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'entityGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-crms-engine-Orders',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'entityUuid', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'entityGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-mp-api-dynamodb-Catalogs',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'entityUuid', AttributeType: 'S' },
        { AttributeName: 'createdTime', AttributeType: 'N' },
        { AttributeName: 'name', AttributeType: 'S' },
        { AttributeName: 'categoryNames', AttributeType: 'S' },
        { AttributeName: 'price', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'entityGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'nameGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'name',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'createdTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'createdTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'categoryNamesGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'categoryNames',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'priceGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'price',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-crms-engine-Catalogs',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'entityUuid', AttributeType: 'S' },
        { AttributeName: 'createdTime', AttributeType: 'N' },
        { AttributeName: 'name', AttributeType: 'S' },
        { AttributeName: 'categoryNames', AttributeType: 'S' },
        { AttributeName: 'price', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'entityGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'nameGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'name',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'createdTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'createdTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'categoryNamesGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'categoryNames',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'priceGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'price',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-dbs-api-dynamodb-Catalogs',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'entityUuid', AttributeType: 'S' },
        { AttributeName: 'createdTime', AttributeType: 'N' },
        { AttributeName: 'name', AttributeType: 'S' },
        { AttributeName: 'categoryNames', AttributeType: 'S' },
        { AttributeName: 'price', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'entityGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'nameGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'name',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'createdTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'createdTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'categoryNamesGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'categoryNames',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'priceGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'price',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-mp-api-dynamodb-Invoices',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'entityUuid', AttributeType: 'S' },
        { AttributeName: 'payerContactName', AttributeType: 'S' },
        { AttributeName: 'title', AttributeType: 'S' },
        { AttributeName: 'status', AttributeType: 'S' },
        { AttributeName: 'createdTime', AttributeType: 'N' },
        { AttributeName: 'issuedTime', AttributeType: 'N' },
        { AttributeName: 'sentTime', AttributeType: 'N' },
        { AttributeName: 'paidTime', AttributeType: 'N' },
        { AttributeName: 'nextDueTime', AttributeType: 'N' },
        { AttributeName: 'itemLastUsedTime', AttributeType: 'N' },
        {
          AttributeName: 'totalAmount',
          AttributeType: 'N',
        },
        {
          AttributeName: 'paidAmount',
          AttributeType: 'N',
        },
        {
          AttributeName: 'dueAmount',
          AttributeType: 'N',
        },
        {
          AttributeName: 'updatedTime',
          AttributeType: 'N',
        },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'entityGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'payerContactNameGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'payerContactName',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'titleGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'title',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'statusGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'status',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'createdTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'createdTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'issuedTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'issuedTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'sentTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'sentTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'paidTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'paidTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'nextDueTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'nextDueTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'itemLastUsedTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'itemLastUsedTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'paidAmountGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'paidAmount',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'totalAmountGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'totalAmount',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'dueAmountGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'dueAmount',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'updatedTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'updatedTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-crms-engine-Invoices',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
        { AttributeName: 'entityUuid', AttributeType: 'S' },
        { AttributeName: 'payerContactName', AttributeType: 'S' },
        { AttributeName: 'title', AttributeType: 'S' },
        { AttributeName: 'status', AttributeType: 'S' },
        { AttributeName: 'createdTime', AttributeType: 'N' },
        { AttributeName: 'issuedTime', AttributeType: 'N' },
        { AttributeName: 'sentTime', AttributeType: 'N' },
        { AttributeName: 'paidTime', AttributeType: 'N' },
        { AttributeName: 'nextDueTime', AttributeType: 'N' },
        { AttributeName: 'itemLastUsedTime', AttributeType: 'N' },
        {
          AttributeName: 'totalAmount',
          AttributeType: 'N',
        },
        {
          AttributeName: 'paidAmount',
          AttributeType: 'N',
        },
        {
          AttributeName: 'dueAmount',
          AttributeType: 'N',
        },
        {
          AttributeName: 'updatedTime',
          AttributeType: 'N',
        },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'entityGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'type',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'payerContactNameGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'payerContactName',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'titleGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'title',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'statusGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'status',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'createdTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'createdTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'issuedTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'issuedTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'sentTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'sentTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'paidTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'paidTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'nextDueTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'nextDueTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'itemLastUsedTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'itemLastUsedTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'paidAmountGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'paidAmount',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'totalAmountGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'totalAmount',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'dueAmountGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'dueAmount',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
        {
          IndexName: 'updatedTimeGsi',
          KeySchema: [
            {
              AttributeName: 'entityUuid',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'updatedTime',
              KeyType: 'RANGE',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-mp-api-dynamodb-Notifications',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-crms-engine-Notifications',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-ce-iac-events',
      KeySchema: [
        { AttributeName: 'aggregateId', KeyType: 'HASH' },
        { AttributeName: 'sequenceNo', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'aggregateId', AttributeType: 'S' },
        { AttributeName: 'sequenceNo', AttributeType: 'N' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
    {
      TableName: 'dev-stlmts-v2-v1-settlementUuid-map',
      KeySchema: [
        { AttributeName: 'settlementUuid', KeyType: 'HASH' },
        { AttributeName: 'depositIdV1', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'settlementUuid', AttributeType: 'S' },
        { AttributeName: 'depositIdV1', AttributeType: 'S' },
        { AttributeName: 'depositIdV2', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
      GlobalSecondaryIndexes: [
        {
          IndexName: 'depositIdV2Gsi',
          KeySchema: [
            {
              AttributeName: 'depositIdV2',
              KeyType: 'HASH',
            },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    },
    {
      TableName: 'dev-ams-engine-DomicileLookup',
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
        { AttributeName: 'type', KeyType: 'RANGE' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'type', AttributeType: 'S' },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    },
  ],
};
