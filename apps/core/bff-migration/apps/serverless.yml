service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}

plugins:
  - serverless-dotenv-plugin
  - serverless-plugin-tracing
  - serverless-pseudo-parameters
  - serverless-plugin-resource-tagging
  - serverless-esbuild

useDotenv: true

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region}
  vpc:
    securityGroupIds:
      - 'Fn::ImportValue': '${opt:stage}-ams-lambda-sg'
    subnetIds:
      - 'Fn::ImportValue': '${opt:stage}-ams-lambda-subnet01'
      - 'Fn::ImportValue': '${opt:stage}-ams-lambda-subnet02'
      - 'Fn::ImportValue': '${opt:stage}-ams-lambda-subnet03'
  stackName: ${self:service}
  deploymentBucket:
    name: ${cf:${self:custom.bucketStackName}.deploymentBucket}
    maxPreviousDeploymentArtifacts: 100
    blockPublicAccess: true

  environment:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}

  tags:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    service: ${env:COMPONENT_NAME}-${env:PART_NAME}
    env: ${opt:stage}
  stackTags: ${self:provider.tags}

custom:
  accountId: '#{AWS::AccountId}'
  stage: ${opt:stage}
  bucketStackName: ${env:COMPONENT_NAME}-${env:PART_NAME}-iac-s3
  githubCloneUrl: https://github.com/zeller-engineering/component-bff.git
  tableName: ${self:provider.stackName}
  auth0ClientId: ${ssm:/${self:custom.stage}-ams-engine/AUTH0_CLIENT_ID}
  auth0ClientSecret: ${ssm:/${self:custom.stage}-ams-engine/AUTH0_CLIENT_SECRET}
  auth0Tenant: ${env:AUTH0_TENANT}
  importDataBucketName: ${self:provider.stackName}-import-data-${self:provider.region}-${self:custom.accountId}
  s3DeletedTopic: ${ssm:s3-sns-notification-arn}
  amsLambdaSg: !ImportValue '${self:custom.stage}-ams-lambda-sg'
  amsLambdaSubnet01: !ImportValue '${self:custom.stage}-ams-lambda-subnet01'
  amsLambdaSubnet02: !ImportValue '${self:custom.stage}-ams-lambda-subnet02'
  amsLambdaSubnet03: !ImportValue '${self:custom.stage}-ams-lambda-subnet03'
  branchNames:
    staging: master
    prod: master
    other: develop
  branchName: ${self:custom.branchNames.${self:custom.stage}, self:custom.branchNames.other}
  sydneyAccountId: ${env:SYDNEY_ACCOUNT_ID}
  londonAccountId: ${env:LONDON_ACCOUNT_ID}
  tableDeletionProtection:
    dev: true
    staging: true
    prod: true
    st: false

package:
  exclude:
    - node_modules/**

functions:
  - ${file(resources/lambdas.yml)}

resources:
  - ${file(resources/codebuild.yml)}
  - ${file(resources/dynamodb.yml)}
  - ${file(resources/s3ImportData.yml)}
  - ${file(resources/ecs.yml)}
  - ${file(resources/iam.yml)}
  - ${file(resources/eventBridge.yml)}
