Resources:
  updateEntityRegionalOptions:
    Type: AWS::ECS::TaskDefinition
    Properties:
      ContainerDefinitions:
        - Name: ${self:custom.baseName}-updateEntityRegionalOptions
          Essential: true
          Image: ${self:custom.accountId}.dkr.ecr.${self:provider.region}.amazonaws.com/${opt:stage}-bff-migration:${self:custom.migrationName}
          LogConfiguration:
            LogDriver: awslogs
            options:
              awslogs-region: ${self:provider.region}
              awslogs-group: ${self:custom.baseName}-${self:custom.migrationName}
              awslogs-create-group: true
              awslogs-stream-prefix: migration
          Environment:
            - Name: CODEBUILD_BUILD_ID
              Value: ${param:codeBuildId}
            - Name: STAGE
              Value: ${opt:stage}
            - Name: AWS_REGION
              Value: ${self:provider.region}

      TaskRoleArn: !Ref updateEntityRegionalOptionsRole
      Cpu: 4096
      ExecutionRoleArn: !Ref updateEntityRegionalOptionsExecutionRole
      Memory: 16384
      NetworkMode: awsvpc
      Family: ${self:custom.baseName}-${self:custom.migrationName}
      RequiresCompatibilities:
        - FARGATE
      Tags:
        - Key: COMPONENT_NAME
          Value: ${env:COMPONENT_NAME}
        - Key: PART_NAME
          Value: ${env:PART_NAME}
        - Key: STAGE
          Value: ${opt:stage}
        - Key: service
          Value: ${env:COMPONENT_NAME}-${env:PART_NAME}
        - Key: env
          Value: ${opt:stage}

  updateEntityRegionalOptionsExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: ${self:custom.baseName}-task-execution
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ecr:GetAuthorizationToken
                  - ecr:BatchCheckLayerAvailability
                  - ecr:GetDownloadUrlForLayer
                  - ecr:BatchGetImage
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: '*'

  updateEntityRegionalOptionsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: ${self:provider.stackName}-auroraPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                Resource:
                  - arn:aws:ssm:${self:provider.region}:${self:custom.accountId}:parameter/${opt:stage}-ams-engine-aurora-dburl-read
                  - arn:aws:ssm:${self:provider.region}:${self:custom.accountId}:parameter/${opt:stage}-ams-aurora/DatabaseReadEndpointURL
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource:
                  - arn:aws:secretsmanager:${self:provider.region}:${self:custom.accountId}:secret:${opt:stage}-ams-aurora/DbUserSecret*
        - PolicyName: ${self:provider.stackName}-invokeLambdaPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - lambda:InvokeFunction
                Resource: 'arn:aws:lambda:${self:provider.region}:${self:custom.accountId}:function:${opt:stage}-ams-cqrs*'
        - PolicyName: ${self:provider.stackName}-s3Policy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:ListBucket
                  - s3:GetObject
                  - s3:PutObject
                  - s3:GetObjectAttributes
                Resource:
                  - arn:aws:s3:::${cf:${self:custom.bucketStackName}.deploymentBucket}/*
