Resources:
  MigrationProject:
    Type: AWS::CodeBuild::Project
    Properties:
      Name: "${self:provider.stackName}"
      Artifacts:
        Type: S3
        Location: ${self:provider.deploymentBucket.name}
        Name: migration.zip
        NamespaceType: BUILD_ID
        Path: migrationArtifacts
      ServiceRole: !Ref CodeBuildRole
      TimeoutInMinutes: 480
      Environment:
        Type: LINUX_CONTAINER
        ComputeType: BUILD_GENERAL1_LARGE
        Image: aws/codebuild/standard:7.0
        PrivilegedMode: true
        EnvironmentVariables:
          - Name: MIGRATION_NAME
            Value: 'Please set migration name'
          - Name: TRIAL
            Value: true
          - Name: DRY_RUN
            Value: true
          - Name: ENTITY_UUID
            Value: ''
          - Name: MIGRATION_FLAG
            Value: ''
          - Name: NODE_RUNTIME
            Value: '18'
          - Name: JFROG_AUTH_TOKEN
            Type: SECRETS_MANAGER
            Value: !Join [ "", [ "${self:custom.stage}", "/jFrogUser:authToken" ] ]
          - Name: JFROG_USER_ENCRYPTED_PASSWORD
            Type: SECRETS_MANAGER
            Value: !Join [ "", [ "${self:custom.stage}", "/jFrogUser:encryptedPassword" ] ]
          - Name: JFROG_USER_USERNAME
            Type: SECRETS_MANAGER
            Value: !Join [ "", [ "${self:custom.stage}", "/jFrogUser:username" ] ]
          - Name: JFROG_USER_EMAIL
            Type: SECRETS_MANAGER
            Value: !Join [ "", [ "${self:custom.stage}", "/jFrogUser:email" ] ]
          - Name: DockerHubUser
            Type: SECRETS_MANAGER
            Value: "dockerHub:username"
          - Name: DockerHubPassword
            Type: SECRETS_MANAGER
            Value: "dockerHub:password"
          - Name: NpmRegistry
            Type: PLAINTEXT
            Value: 'https://npco.jfrog.io/artifactory/api/npm/npm-dev-local/'
          - Name: STAGE
            Type: PLAINTEXT
            Value: ${self:custom.stage}
          - Name: AUTH0_CLIENT_ID
            Type: PLAINTEXT
            Value: ${self:custom.auth0ClientId}
          - Name: AUTH0_CLIENT_SECRET
            Type: PLAINTEXT
            Value: ${self:custom.auth0ClientSecret}
          - Name: AUTH0_TENANT
            Type: PLAINTEXT
            Value: ${self:custom.auth0Tenant}
          - Name: NODE_OPTIONS
            Type: PLAINTEXT
            Value: --max-old-space-size=8192
          - Name: DEFAULT_BANKING_MIGRATION_STATUS
            Type: PLAINTEXT
            Value: ''
          - Name: S3_BATCH_READ_FROM_DATE
            Type: PLAINTEXT
            Value: 'start date for deposit s3 replay'
          - Name: AMS_LAMBDA_SG
            Type: PLAINTEXT
            Value: ${self:custom.amsLambdaSg}
          - Name: AMS_LAMBDA_SUBNET01
            Type: PLAINTEXT
            Value: ${self:custom.amsLambdaSubnet01}
          - Name: AMS_LAMBDA_SUBNET02
            Type: PLAINTEXT
            Value: ${self:custom.amsLambdaSubnet02}
          - Name: AMS_LAMBDA_SUBNET03
            Type: PLAINTEXT
            Value: ${self:custom.amsLambdaSubnet03}
          - Name: SYDNEY_ACCOUNT_ID
            Type: PLAINTEXT
            Value: ${self:custom.sydneyAccountId}
          - Name: LONDON_ACCOUNT_ID
            Type: PLAINTEXT
            Value: ${self:custom.londonAccountId}
      VpcConfig:
        VpcId: !ImportValue ${self:custom.stage}-ams-vpc-id
        SecurityGroupIds:
          - 'Fn::ImportValue': '${self:custom.stage}-ams-lambda-sg'
        Subnets:
          - 'Fn::ImportValue': '${self:custom.stage}-ams-lambda-subnet01'
          - 'Fn::ImportValue': '${self:custom.stage}-ams-lambda-subnet02'
          - 'Fn::ImportValue': '${self:custom.stage}-ams-lambda-subnet03'
      Source:
        Type: GITHUB
        Location: ${self:custom.githubCloneUrl}
        GitCloneDepth: 26
        BuildSpec: apps/core/bff-migration/apps/resources/migrationSpec.yml
      SourceVersion: ${self:custom.branchName}

  MigrationProjectMp:
    Type: AWS::CodeBuild::Project
    Properties:
      Name: "${self:provider.stackName}-mp"
      Artifacts:
        Type: S3
        Location: ${self:provider.deploymentBucket.name}
        Name: migration.zip
        NamespaceType: BUILD_ID
        Path: migrationArtifacts
      ServiceRole: !Ref CodeBuildRole
      TimeoutInMinutes: 480
      Environment:
        Type: LINUX_CONTAINER
        ComputeType: BUILD_GENERAL1_LARGE
        Image: aws/codebuild/standard:6.0
        PrivilegedMode: true
        EnvironmentVariables:
          - Name: MIGRATION_NAME
            Value: 'Please set migration name'
          - Name: TRIAL
            Value: true
          - Name: FILE_NAME
            Value: 'Please set a file name to load in migration'
          - Name: IS_DRY_RUN
            Value: false
          - Name: JFROG_USER_ENCRYPTED_PASSWORD
            Type: SECRETS_MANAGER
            Value: !Join [ "", [ "${self:custom.stage}", "/jFrogUser:encryptedPassword" ] ]
          - Name: JFROG_USER_USERNAME
            Type: SECRETS_MANAGER
            Value: !Join [ "", [ "${self:custom.stage}", "/jFrogUser:username" ] ]
          - Name: JFROG_USER_EMAIL
            Type: SECRETS_MANAGER
            Value: !Join [ "", [ "${self:custom.stage}", "/jFrogUser:email" ] ]
          - Name: NpmRegistry
            Type: PLAINTEXT
            Value: 'https://npco.jfrog.io/artifactory/api/npm/npm-dev-local/'
          - Name: STAGE
            Type: PLAINTEXT
            Value: ${self:custom.stage}
          - Name: AUTH0_CLIENT_ID
            Type: PLAINTEXT
            Value: ${self:custom.auth0ClientId}
          - Name: AUTH0_CLIENT_SECRET
            Type: PLAINTEXT
            Value: ${self:custom.auth0ClientSecret}
          - Name: AUTH0_TENANT
            Type: PLAINTEXT
            Value: ${self:custom.auth0Tenant}
          - Name: NODE_OPTIONS
            Type: PLAINTEXT
            Value: --max-old-space-size=8192
      VpcConfig:
        VpcId: !ImportValue ${self:custom.stage}-mp-vpc-id
        SecurityGroupIds:
          - 'Fn::ImportValue': '${self:custom.stage}-mp-lambda-sg'
        Subnets:
          - 'Fn::ImportValue': '${self:custom.stage}-mp-lambda-subnet01'
          - 'Fn::ImportValue': '${self:custom.stage}-mp-lambda-subnet02'
          - 'Fn::ImportValue': '${self:custom.stage}-mp-lambda-subnet03'
      Source:
        Type: GITHUB
        Location: ${self:custom.githubCloneUrl}
        GitCloneDepth: 26
        BuildSpec: apps/core/bff-migration/apps/resources/migrationSpec.yml
      SourceVersion: ${self:custom.branchName}

  CodeBuildRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: [ 'sts:AssumeRole' ]
            Effect: Allow
            Principal:
              Service: [ codebuild.amazonaws.com ]
        Version: '2012-10-17'
      Path: /
      Policies:
        - PolicyName: CodeBuildAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Action:
                  - 'logs:*'
                  - 'ec2:CreateNetworkInterface'
                  - 'ec2:DescribeNetworkInterfaces'
                  - 'ec2:DeleteNetworkInterface'
                  - 'ec2:DescribeSubnets'
                  - 'ec2:DescribeSecurityGroups'
                  - 'ec2:DescribeDhcpOptions'
                  - 'ec2:DescribeVpcs'
                  - 'ec2:CreateNetworkInterfacePermission'
                  - 's3:*'
                  - 'ecr:*'
                  - 'ssm:GetParameter'
                  - 'cloudformation:*'
                  - 'dynamodb:*'
                  - 'apigateway:*'
                  - 'codebuild:*'
                  - 'iam:GetRole'
                  - 'iam:CreateRole'
                  - 'iam:PutRolePolicy'
                  - 'iam:PassRole'
                  - 'iam:AttachRolePolicy'
                  - 'iam:DeleteRolePolicy'
                  - 'lambda:*'
                  - 'secretsmanager:GetSecretValue'
                  - 'events:PutEvents'
                  - 'sqs:*'
                  - 'states:StartExecution'
                  - 'states:DescribeStateMachine'
                  - 'states:DescribeExecution'
                  - 'ecs:*'
                  - 'codestart-connections:*'
                  - 'codeconnections:*'
                Effect: Allow
                Resource: '*'
