import { Serverless } from 'serverless/aws';
import { plugins, config, custom, provider, globalEventBus } from './env';
import { iamRoles } from './resources/dbStreamHandler/iam';
import { functions } from './resources/dbStreamHandler/lambda';
import { sqsStreamDlqAndPolicy } from './resources/dbStreamHandler/sqs';

const componentName = String(process.env.COMPONENT_NAME);
const stage = String(process.env.STAGE);

export const hasSqsProjection = ['dbs', 'mp', 'crms'].includes(componentName);

const getApiName = () => {
  switch (componentName) {
    case 'dbs':
      return 'dbs-api';
    case 'mp':
      return 'mp-api';
    case 'crms':
      return 'crms-engine';
    case 'sdk':
      return 'sdk-api';
    case 'cpi':
      return 'cpi-engine';
    default:
      return 'none';
  }
};

const apiName = getApiName();
const componentsWithProjectionStackInApi = ['crms', 'sdk', 'cpi'];

const getApiProjectionStackName = () => {
  const suffixName = componentsWithProjectionStackInApi.includes(componentName) ? 'projection' : 'projection-sqs';

  if (stage.startsWith('st') && stage !== 'staging') {
    return `\${opt:stage}-${apiName}-${suffixName}`;
  }
  return `\${env:VPC_ENV_NAME}-${apiName}-${suffixName}`;
};

const serverlessConfiguration: Serverless = {
  ...config,
  service: '${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-iac-dbStream',
  plugins: [...plugins, 'serverless-plugin-lambda-dead-letter'],
  provider: {
    ...provider,
    environment: {
      STAGE: '${opt:stage}',
      COMPONENT_NAME: '${env:COMPONENT_NAME}',
      PART_NAME: '${env:PART_NAME}',
      ENVIRONMENT: '${opt:stage}',
      LOG_LEVEL: '${env:LOG_LEVEL}',
      GLOBAL_EVENTBUS_NAME: '${self:custom.globalEventBusName}',
      CQRS_PROJECTION_EVENTBUS_NAME: '${self:custom.projectionEventBusName}',
      // cqrs sqs
      CQRS_PROJECTION_SQS_URL: "${cf:${self:custom.serviceName}-iac-sqs.QueueURL, 'none'}",
      // external sqs
      COMPONENT_PROJECTION_SQS_URL: `\${cf:${getApiProjectionStackName()}.QueueUrl, 'none'}`,
      COMPONENT_LOW_PRIORITY_PROJECTION_SQS_URL: `\${cf:${getApiProjectionStackName()}.LowPriorityQueueUrl, 'none'}`,
      DEPOSIT_BATCH_BUCKET: '${self:custom.depositBatchBucket}',
      SETTLEMENT_BATCH_BUCKET: '${self:custom.settlementBatchBucket}',
      TRANSACTION_FEE_ADJUSTED_BATCH_BUCKET: '${self:custom.transactionBatchFeeAdjustedBucket}',
      API_RESPONSE_STACK_NAME: '${self:custom.apiStackName}',
      SETTLEMENTS_V2_FILTER_LIST: '${env:SETTLEMENTS_V2_FILTER_LIST, null}',
      // this feature flag is to enable the transaction hpq in CRMS, it will be removed once it's enabled in production
      ...(process.env.TRANSACTION_HPQ_ENABLED ? { TRANSACTION_HPQ_ENABLED: '${env:TRANSACTION_HPQ_ENABLED}' } : {}),
      ...(process.env.CNP_TRANSACTION_HPQ_ENABLED
        ? { CNP_TRANSACTION_HPQ_ENABLED: '${env:CNP_TRANSACTION_HPQ_ENABLED}' }
        : {}),
      ...(process.env.CPOC_TRANSACTION_HPQ_ENABLED
        ? { CPOC_TRANSACTION_HPQ_ENABLED: '${env:CPOC_TRANSACTION_HPQ_ENABLED}' }
        : {}),
      ...(process.env.GENERAL_CRMS_HPQ_ENABLED ? { GENERAL_CRMS_HPQ_ENABLED: '${env:GENERAL_CRMS_HPQ_ENABLED}' } : {}),
      ...(process.env.PAYMENT_INSTRUMENT_HPQ_ENABLED
        ? { PAYMENT_INSTRUMENT_HPQ_ENABLED: '${env:PAYMENT_INSTRUMENT_HPQ_ENABLED}' }
        : {}),
      ...(process.env.SDK_HPQ_ENABLED ? { SDK_HPQ_ENABLED: '${env:SDK_HPQ_ENABLED}' } : {}),
      ...(process.env.DEVICE_HPQ_ENABLED ? { DEVICE_HPQ_ENABLED: '${env:DEVICE_HPQ_ENABLED}' } : {}),
    },
    vpc: '${self:custom.${env:VPC_TYPE, "vpcImport"}}',
  },
  package: {
    individually: true,
    patterns: ['!node_modules/**'],
  },
  custom: {
    ...custom,
    ...globalEventBus,
    apiStackName: '${opt:stage}-${env:COMPONENT_NAME}-api',
    eventStoreTableName: '${opt:stage}-${env:COMPONENT_NAME}-iac-events',
    lambdaTimeoutInSeconds: '${env:PROJECTION_LAMBDA_TIMEOUT_IN_SECONDS, 60}',
    lambdaMemorySize: '${env:PROJECTION_LAMBDA_MEMORY_SIZE, 1024}',
    projectionEventBusName: '${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-iac-eventBus-projection',
    depositBatchBucket: '${opt:stage}-ds-engine-deposit-batch-${self:provider.region}-${self:custom.accountId}',
    settlementBatchBucket: '${opt:stage}-stlmts-batch-files-${self:provider.region}-${self:custom.accountId}',
    transactionBatchFeeAdjustedBucket:
      '${opt:stage}-fs-engines-fee-batch-${self:provider.region}-${self:custom.accountId}',
    s3BatchBucketArns: [
      'arn:aws:s3:::${self:custom.depositBatchBucket}/*',
      'arn:aws:s3:::${self:custom.settlementBatchBucket}/*',
      'arn:aws:s3:::${self:custom.transactionBatchFeeAdjustedBucket}/*',
    ],
    componentName: '${env:COMPONENT_NAME}',
    serviceName: '${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}',
    sqsArns: [
      // cqrs sqs
      '${cf:${self:custom.serviceName}-iac-sqs.QueueARN, "*"}',
      // external sqs
      `\${cf:${getApiProjectionStackName()}.QueueARN, "*"}`,
      `\${cf:${getApiProjectionStackName()}.LowPriorityQueueARN, "*"}`,
    ],
    scripts: {
      hooks: {
        'after:deploy:deploy':
          '/bin/bash ./bin/tagEventRules.sh -s ${self:provider.stackName} -e ${opt:stage} -c ${env:COMPONENT_NAME} -p ${env:PART_NAME}',
      },
    },
    dbStreamDeadLetterQueueArn: {
      'Fn::GetAtt': ['dbStreamDeadLetterQueue', 'Arn'],
    },
    dbsStreamArn: `\${param:dbStreamArn, "\${cf:\${self:custom.serviceName}-iac-dynamodb.DynamoDBTableEventsSignalStreamArn, ''}"}`,
  },
  functions,
  resources: {
    Resources: {
      ...sqsStreamDlqAndPolicy,
      ...iamRoles(String(process.env['COMPONENT_NAME'])),
    },
  },
};

module.exports = serverlessConfiguration;
