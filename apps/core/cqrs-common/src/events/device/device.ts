import { DeviceStatus } from '@npco/component-dto-device';

import { ProjectionSqsMode, type CqrsEvents } from '../../types';

export const dtoMapping = (event: any) => ({
  deviceUuid: event.aggregateId,
  ...event.payload,
  status: DeviceStatus[event.payload.status as keyof typeof DeviceStatus],
});

const getDbStreamEvents = () => {
  const dbStreamEvents = { sdk: ProjectionSqsMode.STANDARD_QUEUE };

  return process.env.DEVICE_HPQ_ENABLED === 'true'
    ? {
        crms: ProjectionSqsMode.STANDARD_QUEUE,
        ...dbStreamEvents,
      }
    : dbStreamEvents;
};

export const cqrsEvents: CqrsEvents = {
  Device: {
    Created: {
      aggregateField: 'deviceUuid',
      sagaEvents: ['ams'],
      projectionEvents: ['dbs', 'mp'],
      commandEvents: ['ams', 'dbs', 'crms', 'mp'],
      dtoMapping,
      dbStreamEvents: getDbStreamEvents(),
    },
    Updated: {
      aggregateField: 'deviceUuid',
      sagaEvents: ['ams'],
      projectionEvents: ['dbs', 'mp'],
      commandEvents: ['ams', 'dbs', 'crms', 'mp'],
      dtoMapping,
      dbStreamEvents: getDbStreamEvents(),
    },
    ForcedLogoff: {
      aggregateField: 'deviceUuid',
      sagaEvents: ['crms', 'ams'],
      projectionEvents: ['dbs'],
      commandEvents: ['ams', 'dbs', 'crms'],
      dbStreamEvents: { sdk: ProjectionSqsMode.STANDARD_QUEUE },
    },
    AttestationFailed: {
      aggregateField: 'id',
      sagaEvents: [],
      projectionEvents: [],
      commandEvents: ['mp'],
    },
    InformationUpdated: {
      aggregateField: 'id',
      sagaEvents: ['ams'],
      projectionEvents: ['dbs', 'mp'],
      commandEvents: ['ams', 'dbs', 'crms', 'mp'],
      dbStreamEvents: getDbStreamEvents(),
    },
  },
};
