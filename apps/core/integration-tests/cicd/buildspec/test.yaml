version: 0.2

env:
  shell: bash

phases:
  install:
    runtime-versions:
      nodejs: $NODE_RUNTIME
  pre_build:
    commands:
      - ls
      - corepack enable
      - yarn config set npmPublishRegistry $JfrogRegistry
      - yarn config set npmScopes.npco.npmRegistryServer $JfrogRegistry
      - yarn config set npmScopes.npco.npmAuthToken $JfrogToken
      - yarn config set npmScopes.npco.npmAlwaysAuth true
      - cat .yarnrc.yml
      - yarn workspaces focus integration-tests component-bff @npco/component-cnp-sdk
      - yarn nx build integration-tests
  build:
    commands:
      - echo Test started on `date`
      - echo "yarn nx run integration-tests:e2e-test:test apps/core/integration-tests/tests/src/$TestGroup/"
      - export STAGE=$EnvironmentName
      - export COMPONENT_NAME=$ComponentName
      - export PART_NAME="$PartName-$TestGroup"
      - export TEST_GROUP=$TestGroup
      - export COMMIT_ID=${CODEBUILD_RESOLVED_SOURCE_VERSION}
      - export region=$REGION
      - attempts=0
      - testResult=1
      - | # temporary disable test for eu-west-2 region
        if [[ "$AWS_REGION" == "eu-west-2" ]]; then
          echo "Skipping integration tests in eu-west-2 region"
          testResult=0
        fi
      - | #allow for automatic build retries for issue with concurrent running integration test
        while [[ "$testResult" -ne 0 && $attempts -le 2 ]]; do
          echo attempt $attempts
          let "attempts++"
          yarn nx run integration-tests:e2e-test:test apps/core/integration-tests/tests/src/$TestGroup/
          testResult=$?
        done
      - echo testResult $testResult
      - exit $testResult
  post_build:
    commands:
      - echo completed on `date`
      - export S3_BUCKET=integration-test-$AWS_REGION-$AccountId
      - echo $S3_BUCKET
      - export folderName=`date +%d-%m-%Y:%H:%m`
      - mkdir -p dist/integration-test

reports:
  report:
      files:
        - "apps/core/integration-tests/tests/dist/report.xml"
      file-format: "JUNITXML"
