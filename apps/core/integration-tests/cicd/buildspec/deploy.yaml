version: 0.2

env:
  shell: bash

phases:
  install:
    runtime-versions:
      nodejs: $NODE_RUNTIME
  pre_build:
    commands:
      - pwd
      - ls
      - yarn -v
      - node -v
      - corepack enable
      - yarn config set npmPublishRegistry $JfrogRegistry
      - yarn config set npmScopes.npco.npmRegistryServer $JfrogRegistry
      - yarn config set npmScopes.npco.npmAuthToken "$JfrogToken"
      - yarn config set npmScopes.npco.npmAlwaysAuth true
      - cat .yarnrc.yml
  build:
    commands:
      - yarn workspaces focus integration-tests component-bff @npco/component-cnp-sdk
      - yarn nx build integration-tests
      - cd apps/core/integration-tests/tests
      - echo Test started on `date`
      - export STAGE=${EnvironmentName}
      - yarn deploy $REGION $STAGE
