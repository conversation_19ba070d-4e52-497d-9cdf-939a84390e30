#!/usr/bin/env node

import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { getCommonEnv, getParametersFromContext } from '@npco/component-bff-cicd';
import { createPipeline } from '../lib/appPipeline';

const app = new cdk.App();
const commonParameters = getParametersFromContext(app);
const { stage, region, account, type, branchName } = commonParameters;
const envs = getCommonEnv(stage);

const nodeRuntime = '18';

const buildImage = 'aws/codebuild/standard:7.0';

const {
  bitbucketOwner,
  codeStar,
  codeStarUK,
  componentName,
  partName,
  bitbucketName,
  shouldSetupSonar,
  chatBotNotiArn,
  shouldSetupSystemTest,
  shouldDeployMultiRegion,
  shouldSetupManualApproval,
} = envs;


enum Type {
  /**
   * deploy cicd codebuild
   */
  CICD = 'cicd',
  /**
   * deploy integration tests pipeline
   */
  APPS_PIPELINE = 'apps:pipeline',
}

switch (type) {
  case Type.APPS_PIPELINE:
    // deploy tests
    console.log('deploy tests pipeline');

    createPipeline(
      app,
      {
        bitbucketOwner,
        codeStar: region === 'eu-west-2'? codeStarUK: codeStar,
        bitbucketName,
        branchName,
        account,
        region,
        componentName,
        partName,
        stage,
        sonar: shouldSetupSonar,
        chatBotNotiArn,
        primaryRegion: region,
        shouldDeployMultiRegion,
        shouldSetupSystemTest,
        shouldSetupManualApproval,
        detectSourceChanges: false,
        codeBuildCloneOutput: true,
        buildImage,
        nodeRuntime,
      },
      {
        ...envs,
        jfrogRegistry: process.env.NPCO_JFROG_REGISTRY,
        jfrogToken: process.env.NPCO_JFROG_ACCESS_TOKEN,
        jiraProjectKey: process.env.JIRA_PROJECT_KEY,
        jiraTestExecutionTypeId: process.env.JIRA_TEST_EXECUTION_TYPE_ID,
        xrayApiEndpoint: process.env.XRAY_API_ENDPOINT,
      },
    );
    break;
}
