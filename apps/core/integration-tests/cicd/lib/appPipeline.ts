import * as fs from 'fs';
import * as path from 'path';
import * as codebuild from 'aws-cdk-lib/aws-codebuild';
import * as cdk from 'aws-cdk-lib';
import { Pipeline, PipelineOptions, PipelineActionOptions, getPrimaryRegion } from '@npco/component-bff-cicd';

const BUILD_SPEC_DIR = 'apps/core/integration-tests/cicd/buildspec';

type EnvVars = {
  [name: string]: any;
};

type PrimaryRegion = {
  displayName: string;
  name: string;
};

const testFolderStages: string[][] = [];

function deployStageName(primaryRegion:PrimaryRegion): `Deploy-${string}` {
  return `Deploy-${primaryRegion.displayName}`;
}

function buildCacheArtifactName(primaryRegion:PrimaryRegion): `Deploy-${string}-BuildCache` {
  return `Deploy-${primaryRegion.displayName}-BuildCache`;
}

const createIntegrationTestStage = (pipeline: Pipeline, options: PipelineOptions, envs: EnvVars) => {
  const primaryRegion = getPrimaryRegion(options.region);
  const TESTS_SRC_PATH = path.resolve(process.cwd(), '..', 'tests', 'src');

  const isDirectory = (filePath: string): boolean => {
    return fs.lstatSync(filePath).isDirectory();
  };

  const capitalizeFirstLetter = (string: string): string => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  const getTestDirectories = (): string[] => {
    const testsSrcContents = fs.readdirSync(TESTS_SRC_PATH);
    const testFolders = [];
    for (const testsSrcObject of testsSrcContents) {
      const fileOrDirPath = `${TESTS_SRC_PATH}/${testsSrcObject}`;
      if (fileOrDirPath.includes('sim') || fileOrDirPath.includes('cms')) {
        continue;
      }
      const isDir = isDirectory(fileOrDirPath);

      if (isDir) {
        const dirContents = fs.readdirSync(fileOrDirPath);

        if (dirContents.some((file) => file.includes('.spec'))) {
          testFolders.push(testsSrcObject);
        }
      }
    }

    return testFolders;
  };
  const restFolders: string[] = [];
  const allFolders = getTestDirectories();
  allFolders.filter((f) => {
    let exist = false;
    for (let s of testFolderStages) {
      exist = exist || s.includes(f);
    }
    if (!exist) {
      restFolders.push(f);
    }
  });
  testFolderStages.push(restFolders);
  for (let i = 0; i < testFolderStages.length; i += 1) {
    const actions: PipelineActionOptions[] = testFolderStages[i].map((name) => {
      const vpcComponentName = name === 'banking' ? 'mp' : 'ams';
      const subnetExportNames = {
        mp: [`${options.stage}-mp-lambda-subnet01`],
        ams: [
          `${options.stage}-ams-private-subnet01`,
          `${options.stage}-ams-private-subnet02`,
          `${options.stage}-ams-private-subnet03`,
        ],
      };
      const sgExportNames = {
        mp: `${options.stage}-mp-lambda-sg`,
        ams: `${options.stage}-ams-private-sg`,
      };
      const vpcExportName = `${options.stage}-${vpcComponentName}-vpc-id`;

      const action: PipelineActionOptions = {
        name: capitalizeFirstLetter(name),
        specFile: `${BUILD_SPEC_DIR}/test.yaml`,
        runOrder: 1,

        network: {
          subnetExportNames: subnetExportNames[vpcComponentName],
          sgExportName: sgExportNames[vpcComponentName],
          vpcExportName,
        },
        codebuildProjectName: capitalizeFirstLetter(name),
        codebuildProjectEnvVars: {
          AUTH0_DBS_CLIENT_ID: {
            type: codebuild.BuildEnvironmentVariableType.PARAMETER_STORE,
            value: `/${options.stage}-dbs-api/AUTH0_CLIENT_ID`,
          },
          AUTH0_DBS_CLIENT_SECRET: {
            type: codebuild.BuildEnvironmentVariableType.PARAMETER_STORE,
            value: `/${options.stage}-dbs-api/AUTH0_CLIENT_SECRET`,
          },
          AUTH0_MP_CLIENT_ID: {
            type: codebuild.BuildEnvironmentVariableType.PARAMETER_STORE,
            value: `/${options.stage}-mp-app/AUTH0_CLIENT_ID`,
          },
          AUTH0_MP_CLIENT_SECRET: {
            type: codebuild.BuildEnvironmentVariableType.PARAMETER_STORE,
            value: `/${options.stage}-mp-app/AUTH0_CLIENT_SECRET`,
          },
          AUTH0_MP_API_CLIENT_ID: {
            type: codebuild.BuildEnvironmentVariableType.PARAMETER_STORE,
            value: `/${options.stage}-mp-api/AUTH0_CLIENT_ID`,
          },
          AUTH0_MP_API_CLIENT_SECRET: {
            type: codebuild.BuildEnvironmentVariableType.PARAMETER_STORE,
            value: `/${options.stage}-mp-api/AUTH0_CLIENT_SECRET`,
          },
          AUTH0_AMS_CLIENT_ID: {
            type: codebuild.BuildEnvironmentVariableType.PARAMETER_STORE,
            value: `/${options.stage}-ams-engine/AUTH0_CLIENT_ID`,
          },
          AUTH0_AMS_CLIENT_SECRET: {
            type: codebuild.BuildEnvironmentVariableType.PARAMETER_STORE,
            value: `/${options.stage}-ams-engine/AUTH0_CLIENT_SECRET`,
          },
          ZELLER_APP_AUTH0_CLIENT_ID: {
            type: codebuild.BuildEnvironmentVariableType.PARAMETER_STORE,
            value: `/${options.stage}-mp-api/ZELLER_APP_AUTH0_CLIENT_ID`,
          },
          ZELLER_APP_AUTH0_CLIENT_SECRET: {
            type: codebuild.BuildEnvironmentVariableType.PARAMETER_STORE,
            value: `/${options.stage}-mp-api/ZELLER_APP_AUTH0_CLIENT_SECRET`,
          },
          AccountId: {
            type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
            value: '${AWS::AccountId}',
          },
          XRAY_CLIENT_ID: {
            type: codebuild.BuildEnvironmentVariableType.SECRETS_MANAGER,
            value: 'xrayJira:clientId',
          },
          XRAY_CLIENT_SECRET: {
            type: codebuild.BuildEnvironmentVariableType.SECRETS_MANAGER,
            value: 'xrayJira:clientSecret',
          },
          JIRA_PROJECT_KEY: {
            type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
            value: envs.jiraProjectKey,
          },
          JIRA_TEST_EXECUTION_TYPE_ID: {
            type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
            value: envs.jiraTestExecutionTypeId,
          },
          XRAY_API_ENDPOINT: {
            type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
            value: envs.xrayApiEndpoint,
          },
        },
        envVars: {
          TestGroup: {
            type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
            value: name,
          },
          JfrogRegistry: {
            type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
            value: envs.jfrogRegistry,
          },
          JfrogToken: {
            type: codebuild.BuildEnvironmentVariableType.SECRETS_MANAGER,
            value: 'dev/jFrogUser:authToken',
          },
          REGION: {
            type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
            value: primaryRegion.name,
          }
        },
      };

      if (name === 'posinterface') {
        const networkSsm = {
          vpcSsmName: `${options.stage}-vpc-shared-01-vpc-id`,
          sgSsmName: `${options.stage}-vpc-shared-01-private-sg`,
          subnetSsmNames: [
            `${options.stage}-vpc-shared-01-private-subnet01`,
            `${options.stage}-vpc-shared-01-private-subnet02`,
            `${options.stage}-vpc-shared-01-private-subnet03`,
          ],
        };
        action.networkSsm = networkSsm;
        delete action.network;
      }

      return action;
    });

    pipeline.addStage({
      name: `IntegrationTest${i + 1}`,
      input: pipeline.actionOutputs[buildCacheArtifactName(primaryRegion)],
      actions,
    });
  }
};

const createDeployStage = (pipeline: Pipeline, options: PipelineOptions, envs: EnvVars) => {
  const primaryRegion = getPrimaryRegion(options.region);
  pipeline.addDeployStage({
    name: deployStageName(primaryRegion),
    actions: [
      {
        name: 'Deploy',
        runOrder: 1,
        computeType: codebuild.ComputeType.LARGE,
        specFile: `${BUILD_SPEC_DIR}/deploy.yaml`,
        envVars: {
          JfrogRegistry: {
            type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
            value: envs.jfrogRegistry,
          },
          JfrogToken: {
            type: codebuild.BuildEnvironmentVariableType.SECRETS_MANAGER,
            value: 'dev/jFrogUser:authToken',
          },
          REGION: {
            type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
            value: primaryRegion.name,
          }
        },
      },
      {
        name: 'BuildCache',
        runOrder: 1,
        computeType: codebuild.ComputeType.LARGE,
        specFile: `${BUILD_SPEC_DIR}/build.yaml`,
        envVars: {
          JfrogRegistry: {
            type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
            value: envs.jfrogRegistry,
          },
          JfrogToken: {
            type: codebuild.BuildEnvironmentVariableType.SECRETS_MANAGER,
            value: 'dev/jFrogUser:authToken',
          },
        },
      },
    ],
  });
};

export const createPipeline = (app: cdk.App, options: PipelineOptions, envs: EnvVars) => {
  const pipeline = new Pipeline(app, options);

  createDeployStage(pipeline, options, envs);
  createIntegrationTestStage(pipeline, options, envs);
};
