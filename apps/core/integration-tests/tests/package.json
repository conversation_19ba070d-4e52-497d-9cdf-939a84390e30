{"name": "integration-tests", "version": "1.0.0", "main": "dist/index.js", "license": "MIT", "scripts": {"prepublish": "tsc --build tsconfig.json", "prepare": "cd .. && husky install tests/.husky", "build": "yarn tsc --build tsconfig.json", "lint": "echo todo", "deploy": "sh ./bin/deploy.sh", "run-audit": "echo audit", "test": "jest --forceExit --maxWorkers 1", "test:local": "sh ./bin/setup-ams-connection.sh && yarn run:test:local", "test:local:bff-db": "sh ./bin/setup-bff-db-connection.sh && yarn run:test:bff-db", "run:test:local": "DB_HOST=127.0.0.1 DB_PORT=1053 jest --forceExit --maxWorkers 1", "run:test:bff-db": "DB_HOST=127.0.0.1 DB_PORT=1054 jest --forceExit --maxWorkers 1"}, "dependencies": {"@aws-sdk/client-codepipeline": "3.435.0", "@aws-sdk/client-dynamodb": "3.435.0", "@aws-sdk/client-s3": "3.435.0", "@aws-sdk/client-sns": "3.435.0", "@aws-sdk/client-sqs": "3.435.0", "@aws-sdk/client-ssm": "3.435.0", "@aws-sdk/lib-dynamodb": "3.435.0", "@aws-sdk/s3-presigned-post": "3.435.0", "@npco/bff-fe-common": "workspace:*", "@npco/bff-systemtest-utils": "workspace:*", "@npco/component-bff-core": "workspace:*", "@npco/component-cms-events": "^1.0.92", "@npco/component-domain-events": "^12.1.53", "@npco/component-dto-addressbook": "workspace:*", "@npco/component-dto-cardholder": "workspace:*", "@npco/component-dto-catalog": "workspace:*", "@npco/component-dto-cnp": "workspace:*", "@npco/component-dto-connection": "workspace:*", "@npco/component-dto-core": "workspace:*", "@npco/component-dto-cpoc": "workspace:*", "@npco/component-dto-customer": "workspace:*", "@npco/component-dto-deposit": "workspace:*", "@npco/component-dto-device": "workspace:*", "@npco/component-dto-digital-wallet-token": "workspace:*", "@npco/component-dto-entity": "workspace:*", "@npco/component-dto-invoice": "workspace:*", "@npco/component-dto-issuing-account": "workspace:*", "@npco/component-dto-issuing-card": "workspace:*", "@npco/component-dto-issuing-statement-storage": "workspace:*", "@npco/component-dto-issuing-transaction": "workspace:*", "@npco/component-dto-issuing-transaction-statement": "workspace:*", "@npco/component-dto-merchant": "workspace:*", "@npco/component-dto-notifications": "workspace:*", "@npco/component-dto-order": "workspace:*", "@npco/component-dto-payment-instrument": "workspace:*", "@npco/component-dto-pos-interface": "workspace:*", "@npco/component-dto-richdata": "workspace:*", "@npco/component-dto-sim": "workspace:*", "@npco/component-dto-site": "workspace:*", "@npco/component-dto-subscription": "workspace:*", "@npco/component-dto-ticket": "workspace:*", "@npco/component-dto-transaction": "workspace:*", "@npco/component-events-core": "workspace:*", "@npco/component-fs2-events": "^2.0.83", "@npco/end-to-end-encryption": "workspace:*", "auth0": "^3.7.2", "aws-appsync": "^4.0.1", "axios": "^1.7.4", "bff-transaction-api": "workspace:*", "custom-env": "^2.0.1", "esbuild": "^0.17.18", "firebase-admin": "^11.7.0", "form-data": "^3.0.0", "graphql": "^15.3.0", "graphql-tag": "^2.11.0", "isomorphic-fetch": "^3.0.0", "jwt-decode": "^3.1.2", "nanoid": "^3.3.8", "pg": "^8.7.1", "serverless-esbuild": "^1.52.1", "ts-node": "^10.9.2", "uuid": "^8.3.0", "websocket": "^1.0.34", "ws": "^8.17.1", "xero-node": "4.21.0"}, "devDependencies": {"@aws-sdk/types": "^3.54.1", "@npco/eslint-config-backend": "^1.0.4", "@nx/jest": "20.7.0", "@rushstack/eslint-patch": "^1.6.0", "@types/auth0": "^2.20.8", "@types/aws-lambda": "^8.10.71", "@types/jest": "^29.5.11", "@types/lodash": "^4.17.5", "@types/mocha": "^8.0.3", "@types/node": "^18.11.18", "@types/pg": "^7.14.9", "@types/request": "^2.48.5", "@types/serverless": "^3.12.9", "@types/uuid": "^8.3.0", "@types/ws": "^8.5.3", "atob": "^2.1.2", "btoa": "^1.2.1", "dotenv": "^16.0.3", "husky": "^7.0.4", "jest": "^29.7.0", "jest-environment-steps": "^1.1.0", "jest-extended": "^4.0.2", "jest-html-reporter": "^3.10.1", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "lodash": "^4.17.21", "reflect-metadata": "^0.1.13", "rxjs": "^6.6.3", "serverless": "^3.26.0", "serverless-dotenv-plugin": "^3.0.0", "serverless-plugin-resource-tagging": "^1.1.1", "serverless-plugin-typescript": "^1.1.9", "serverless-pseudo-parameters": "^2.5.0", "ts-jest": "^29.1.1", "ts-mockito": "^2.6.1", "ts-node": "^10.9.2", "typescript": "^5.4.5", "xhr2": "^0.2.1"}, "prettier": "@npco/eslint-config-backend/prettier"}