import { AddressState } from '@npco/component-dto-core';

import gql from 'graphql-tag';
import { v4 as uuidv4 } from 'uuid';

import { validateEntityFromEventStore, validateEntityFromEventStoreEvents } from '../utils/eventStoreUtil';
import { CrmsApiClient } from '../utils/gql/crmsGqlApiClient';
import { DbsApiClient } from '../utils/gql/dbsGqlApiClient';
import { retry } from '../utils/retry';

describe('Dbs customer integration test (dbsCustomer.spec.ts)', () => {
  const dbsApiClient = new DbsApiClient();
  const crmsApiClient = new CrmsApiClient();

  let customerEmail: any;

  let entityId: any;

  let customerId: any;

  let customerId2: any;

  let deletedCustomerEmail: string;

  let deviceId: string;

  let eventStoreValidation: any;

  const model = uuidv4().slice(-16);
  const serial = uuidv4().slice(-16);
  const updateCustomerInput = {
    id: '',
    nickname: uuidv4(),
    firstname: uuidv4(),
    middlename: uuidv4(),
    lastname: uuidv4(),
    address: {
      street: uuidv4(),
      suburb: uuidv4(),
      state: AddressState.VIC,
      postcode: uuidv4(),
      country: uuidv4(),
    },
    director: false,
    secretary: false,
    ceo: false,
    beneficialOwner: true,
    beneficialOwnerAlt: false,
    beneficiary: true,
    partner: true,
    trustee: true,
    settlor: true,
    shareholder: true,
    generalContact: true,
    financialContact: true,
    type: 'PARTNERSHIP',
    companyTrustName: uuidv4(),
    abn: uuidv4(),
  };

  beforeAll(async () => {
    await crmsApiClient.beforeAll();
    await dbsApiClient.beforeAll();
    const { entityUuid } = dbsApiClient.getTestData();
    const { deviceUuid } = await dbsApiClient.deviceSignIn(model, serial);
    deviceId = deviceUuid;
    entityId = entityUuid;
  });

  afterAll(async () => {
    await dbsApiClient.testHelper.getAuthHelper().deleteUsers([deletedCustomerEmail]);
  });

  describe('customer account creation', () => {
    it('should be able to create a customer', async () => {
      const customer = {
        firstname: uuidv4(),
        lastname: uuidv4(),
        dob: '1999-12-12',
        role: 'ADMIN',
        type: 'INDIVIDUAL',
        beneficialOwner: true,
        phone: '+************',
        email: dbsApiClient.getSafeTestEmail(), // email is generated different to auth0
      };
      const newCustomer = await dbsApiClient.createCustomer([customer]);
      customerEmail = customer.email;
      customerId = newCustomer.id;
      updateCustomerInput.id = newCustomer.id;
      eventStoreValidation = {
        ...customer,
        entityUuid: entityId,
        defaultEntityUuid: entityId,
        id: customerId,
        role: 'ADMIN',
        type: 'INDIVIDUAL',
        registeringIndividual: false,
        createdTime: expect.any(String),
        safeharbour: {
          status: 'REQUIRED',
        },
        screening: {
          status: 'REQUIRED',
        },
        idv: {
          status: 'REQUIRED',
        },
        productTourStatus: {
          showOnboardingShop: false,
          showAdminMerchantPortalWelcome: true,
          showCorporateCardsAdminWelcome: false,
          showInvoiceInstructions: true,
          showInvoicesWelcome: false,
          showItemsWelcome: true,
          showItemInstructions: true,
          showInvoicesCustomisationWelcome: true,
          showInvoicesScheduleSendWelcome: true,
          showInvoicesSendBySmsWelcome: true,
          showInvoiceSendViaInfo: true,
          showInvoicingCustomisationSettingsWelcome: true,
          showNotificationsWelcome: false,
          showInvoiceApril: false,
          showSavingsAccountWelcome: true,
          showSavingsAccountMarch: false,
          showSavingsAccountMay: false,
          showCorporateCardsMayOffer: false,
          showTapToPayInstructions: false,
          showTapToPayMayJune: false,
          showCustomScreensaverPromo: false,
          showCorporateCardsWalkthrough: true,
          showCorporateCardsSettingsWalkthrough: true,
          showCatalogueItemsWelcome: true,
          profileAvatarWalkthrough: false,
          showServiceChargesWelcome: false,
        },
        kyc: {
          status: 'REQUIRED',
        },
      };
    });

    test.each(['dbs', 'mp', 'ams', 'crms', 'sdk'])(
      'should be able to save customer created event in %p event store',
      async (componentName) =>
        validateEntityFromEventStore(dbsApiClient.stage, componentName, eventStoreValidation, 'Created'),
    );

    test.each(['dbs', 'mp', 'ams', 'crms', 'sdk'])(
      'should be able to save customer entity linked event in %p event store',
      async (componentName) =>
        validateEntityFromEventStore(
          dbsApiClient.stage,
          componentName,
          {
            permissions: {
              allowDiscountManagement: true,
              allowItemManagement: true,
              allowXeroPaymentServices: true,
              allowZellerInvoices: true,
            },
            beneficialOwner: true,
            entityUuid: entityId,
            registeringIndividual: false,
            id: customerId,
            role: 'ADMIN',
          },
          'Linked',
        ),
    );

    it('should get the same deviceUuid for model/serial after signup', async () => {
      const sameDeviceUuid = await dbsApiClient.createDeviceUuid(model, serial);
      expect(typeof sameDeviceUuid).toBe('string');
      expect(sameDeviceUuid).toBe(deviceId);
    });

    test.each([
      {
        componentName: 'dbs',
        client: dbsApiClient,
      },
      {
        componentName: 'crms',
        client: crmsApiClient,
      },
    ])('should be able to query individual customer projection via appsync %p', async ({ client }) => {
      await retry(async () => {
        const result = await client.getCustomer(customerId, entityId);
        expect(result.getCustomer.email).toEqual(customerEmail);
        expect(result.getCustomer.siteCount).toEqual(null);
      }, 60);
    });

    it('should be able to update customer and validate dbs events', async () => {
      const data = await dbsApiClient.updateCustomer(customerId, updateCustomerInput);
      expect(data).toBe(true);
      await dbsApiClient.sleep();
      eventStoreValidation = {
        ...updateCustomerInput,
        entityUuid: entityId,
        id: customerId,
        screening: { status: 'REQUIRED' },
      };
    });

    test.each(['dbs', 'mp', 'ams', 'crms', 'sdk'])(
      'should be able to save customer updated event in %p event store',
      (componentName) =>
        retry(async () =>
          validateEntityFromEventStoreEvents(dbsApiClient.stage, componentName, eventStoreValidation, 'Updated', [
            'hubspotContactId',
          ]),
        ),
    );

    test.each([
      {
        componentName: 'dbs',
        client: dbsApiClient,
      },
      {
        componentName: 'crms',
        client: crmsApiClient,
      },
    ])('should be able to query updated customer projection via appsync %p', ({ client, componentName }) =>
      retry(async () => {
        const result = await client.getCustomer(customerId, entityId);
        const expected = {
          ...updateCustomerInput,
          emailVerified: null,
          phoneVerified: null,
          email: customerEmail,
          entityUuid: entityId,
          dob: undefined,
          icon: null,
          phone: '+************',
          role: 'ADMIN',
          sites: expect.any(Array),
          siteCount: null,
          permissions: {
            allowItemManagement: true,
            allowDiscountManagement: true,
            allowXeroPaymentServices: true,
            allowZellerInvoices: true,
          },
        };
        if (componentName === 'crms') {
          Object.assign(expected, {
            icon: undefined,
            kycStatus: 'REQUIRED',
            createdAt: expect.any(String),
            dob: expect.any(String),
            permissions: {
              allowItemManagement: true,
              allowDiscountManagement: true,
            },
          });
        }
        expect(result.getCustomer).toEqual(expected);
      }),
    );
  });

  describe('Deleted customer api test suite', () => {
    it('should be able to delete non admin customer', async () => {
      // create another customer that is not the entity
      const customer2 = await dbsApiClient.createCustomer();

      const [createdCustomer] = await Promise.all(
        [dbsApiClient, crmsApiClient].map(async (client) => retry(() => client.getCustomer(customer2.id, entityId))),
      );

      customerId2 = customer2.id;
      console.log(customer2);

      const appsyncClient = await dbsApiClient.getOpenIdClient();
      const data = (await appsyncClient
        .mutate({
          mutation: gql`
            mutation deleteCustomer($customerId: ID!) {
              deleteCustomer(customerUuid: $customerId)
            }
          `,
          variables: {
            customerId: customerId2,
          },
        })
        .then((result) => result.data)) as any;

      expect(data.deleteCustomer).toBe(true);
      eventStoreValidation = { id: customerId2 };
      deletedCustomerEmail = createdCustomer.getCustomer.email;
      expect(deletedCustomerEmail).not.toBeNull();
    });

    test.each(['dbs', 'mp', 'ams', 'crms', 'sdk'])(
      'should be able to save customer deleted event in %p event store',
      (componentName) =>
        validateEntityFromEventStore(
          dbsApiClient.stage,
          componentName,
          {
            ...eventStoreValidation,
          },
          'Deleted',
        ),
    );

    it('should not able to update a deleted customer ', async () => {
      await expect(
        dbsApiClient.updateCustomer(
          customerId2,
          {
            firstname: 'updated customer name',
          },
          true,
        ),
      ).rejects.toThrow(`GraphQL error: [400] customer doesnt exist.`);
    });

    it('should be able to create a new customer using deleted customer email', async () => {
      await dbsApiClient.createCustomer([{ email: deletedCustomerEmail, createIdentity: true }]);
    });
  });
});
