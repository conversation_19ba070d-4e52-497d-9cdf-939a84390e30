import { sleep } from '@npco/bff-systemtest-utils';
import {
  MutationAttributionPlatform,
  MutationAttributionTokenGrant,
  MutationAttributionUserRole,
} from '@npco/component-dto-core';

import gql from 'graphql-tag';
import { v4 } from 'uuid';

import {
  deleteKeyFromObject,
  validateEntityFromEventStore,
  validateEntityFromEventStoreEvents,
} from '../utils/eventStoreUtil';
import { CrmsApiClient } from '../utils/gql/crmsGqlApiClient';
import { MpApiClient } from '../utils/gql/mpGqlApiClient';
import { retry } from '../utils/retry';

describe('Merchant Portal customer integration test (mpCustomer)', () => {
  const mpApiClient = new MpApiClient();
  const crmsApiClient = new CrmsApiClient();

  let entityId: string;

  let customerInput: any;

  let customer: any;

  let mpApiClientCustomerUuid: string;

  let customerCreatedAt: string;

  let customerId2: any;

  let deletedCustomerEmail: string;

  let eventStoreValidation: any;

  beforeAll(async () => {
    await crmsApiClient.beforeAll();
    await mpApiClient.beforeAll();
    const user = await mpApiClient.loginAuth0User();
    mpApiClientCustomerUuid = user.customerUuid;
    entityId = mpApiClient.getTestData().entityUuid;
    await mpApiClient.createSoleTraderEntity(v4());

    customerInput = {
      email: mpApiClient.getSafeTestEmail(),
      firstname: 'New',
      lastname: 'Customer',
      middlename: 'Initial',
      nickname: 'Tester',
      role: 'ADMIN',
      address: {
        street: 'street',
        suburb: 'suburb',
        state: 'VIC',
        postcode: '3000',
      },
      phone: '+************',
      dob: '2020-01-01',
      director: true,
      secretary: false,
      ceo: false,
      beneficialOwner: true,
      beneficialOwnerAlt: false,
      beneficiary: false,
      partner: false,
      trustee: false,
      settlor: false,
      generalContact: false,
      financialContact: false,
      shareholder: false,
      type: 'INDIVIDUAL',
      companyTrustName: 'Trust Name',
      abn: '1234567890',
    };
  });
  const updateCustomerInput = {
    id: '',
    nickname: v4(),
    firstname: v4(),
    middlename: v4(),
    lastname: v4(),
    role: 'MANAGER',
    address: {
      street: v4(),
      suburb: v4(),
      state: v4(),
      postcode: v4(),
      country: v4(),
    },
    dob: '2020-01-01',
    director: false,
    secretary: false,
    ceo: false,
    beneficialOwner: true,
    beneficialOwnerAlt: false,
    beneficiary: true,
    partner: true,
    trustee: true,
    settlor: true,
    generalContact: true,
    financialContact: true,
    shareholder: true,
    type: 'PARTNERSHIP',
    companyTrustName: v4(),
    abn: v4(),
  };

  afterAll(async () => {
    await mpApiClient.testHelper.getAuthHelper().deleteUsers([deletedCustomerEmail]);
  });

  describe('Merchant Portal create customer integration test', () => {
    it('should be able to create customer and entity', async () => {
      const data = await mpApiClient.createCustomer([customerInput]);
      customer = {
        ...data,
        email: customerInput.email,
      };
      eventStoreValidation = {
        ...customer,
        ...customerInput,
        registeringIndividual: false,
        productTourStatus: {
          showOnboardingShop: false,
          showAdminMerchantPortalWelcome: true,
          showCorporateCardsAdminWelcome: false,
          showInvoiceInstructions: true,
          showInvoicesWelcome: false,
          showItemsWelcome: true,
          showItemInstructions: true,
          showInvoicesCustomisationWelcome: true,
          showInvoicesScheduleSendWelcome: true,
          showInvoicesSendBySmsWelcome: true,
          showInvoiceSendViaInfo: true,
          showInvoicingCustomisationSettingsWelcome: true,
          showNotificationsWelcome: false,
          showInvoiceApril: false,
          showSavingsAccountWelcome: true,
          showSavingsAccountMarch: false,
          showSavingsAccountMay: false,
          showCorporateCardsMayOffer: false,
          showTapToPayInstructions: false,
          showTapToPayMayJune: false,
          showCustomScreensaverPromo: false,
          showCorporateCardsWalkthrough: true,
          showCorporateCardsSettingsWalkthrough: true,
          showCatalogueItemsWelcome: true,
          profileAvatarWalkthrough: false,
          showServiceChargesWelcome: false,
        },
        createdTime: expect.any(String),
        safeharbour: {
          status: 'REQUIRED',
        },
        screening: {
          status: 'REQUIRED',
        },
        idv: {
          status: 'REQUIRED',
        },
        kyc: {
          status: 'REQUIRED',
        },
      };
      console.log('createCustomer response: ', JSON.stringify(customer));
      expect(customer.id).toBeDefined();
      updateCustomerInput.id = customer.id;
    });

    test.each(['dbs', 'mp', 'ams', 'crms', 'sdk'])(
      'should be able to save Customer Created event in %p event store',
      async (componentName) =>
        validateEntityFromEventStore(
          mpApiClient.stage,
          componentName,
          { ...eventStoreValidation, defaultEntityUuid: eventStoreValidation.entityUuid },
          'Created',
        ),
    );

    test.each(['dbs', 'mp', 'ams', 'crms', 'sdk'])(
      'should be able to save customer entity linked event in %p event store',
      async (componentName) =>
        validateEntityFromEventStore(
          mpApiClient.stage,
          componentName,
          {
            beneficialOwner: true,
            director: true,
            registeringIndividual: true,
            permissions: {
              allowDiscountManagement: true,
              allowItemManagement: true,
              allowXeroPaymentServices: true,
              allowZellerInvoices: true,
            },
            entityUuid: entityId,
            id: mpApiClientCustomerUuid,
            role: 'ADMIN',
          },
          'Linked',
        ),
    );

    const matchCustomer = (event: any, keysToRemove: string[], type: string) => {
      const eventToEqual = event;
      keysToRemove.forEach((keyToRemove) => {
        deleteKeyFromObject(eventToEqual, keyToRemove);
      });
      return {
        ...eventToEqual,
        safeharbour: {
          status: 'COMPLETED',
        },
        createdTime: expect.any(String),
        createdAt: expect.any(String),
        type,
      };
    };

    it('should materialise event in mp-api-dynamodb table', async () => {
      await retry(async () => {
        // when multi-entity flag is enabled, 'customer.entity.${entityId}' should be used
        const data = await mpApiClient.queryDynamodb(customer.id, `customer.entity.${entityId}`);

        const result = matchCustomer(
          eventStoreValidation,
          ['dob', 'kyc', 'permissions'],
          `customer.entity.${entityId}`,
        );
        console.log('customer', customer.id);
        expect(data.Items?.[0]).toMatchObject(result);
        expect(data.Items?.[0].kycStatus).toEqual('REQUIRED');
        expect(data.Items?.[0].kyc).toBeUndefined();
        expect(data.Items?.[0].createdTime).toBeDefined();
        expect(data.Items?.[0].permissions).toEqual(expect.any(Object));
        customerCreatedAt = new Date(Number(data.Items?.[0].createdTime)).toISOString();
      }, 60);
    });

    it('should materialise event in crms-engine table', async () => {
      await retry(async () => {
        const data = await crmsApiClient.queryDynamodb(customer.id, `customer.entity.${entityId}`);
        const skipFieldsWhenMultiEntityEnabled = [
          'beneficialOwner',
          'beneficialOwnerAlt',
          'beneficiary',
          'role',
          'ceo',
          'director',
          'entityUuid',
          'financialContact',
          'generalContact',
          'nickname',
          'partner',
          'registeringIndividual',
          'secretary',
          'settlor',
          'shareholder',
          'trustee',
        ];
        const keysToRemove = ['permissions', ...skipFieldsWhenMultiEntityEnabled];
        const result = matchCustomer(eventStoreValidation, keysToRemove, `customer.entity.${entityId}`);

        expect(data.Items?.[0]).toMatchObject(result);
        expect(data.Items?.[0].dob).toBe('2020-01-01');
        expect(data.Items?.[0].kycStatus).toEqual('REQUIRED');
        expect(data.Items?.[0].kyc).toMatchObject({ status: 'REQUIRED' });
      });
    });

    test.each([
      {
        componentName: 'mp',
        client: mpApiClient,
      },
      {
        componentName: 'crms',
        client: crmsApiClient,
      },
    ])('should be able to query individual customer via appsync %p', async ({ client }) => {
      await retry(async () => {
        const data = await client.getCustomer(customer.id, entityId);
        console.log({ data });
        expect(data.getCustomer.id).toBe(customer.id);
        expect(data.getCustomer.createdAt).toEqual(customerCreatedAt);
        expect(data.getCustomer.email).toBe(customerInput.email);
        expect(data.getCustomer.kycStatus).toEqual('REQUIRED');
        expect(data.getCustomer.siteCount).toEqual(null);
      });
    });

    it('should be able to update customer', async () => {
      console.log('update customer', customer);
      const updatedCustomerInput: any = {
        ...updateCustomerInput,
        showOnboardingShop: false,
        showAdminMerchantPortalWelcome: false,
        showCorporateCardsAdminWelcome: false,
        showCorporateCardsSettingsWalkthrough: false,
        showInvoiceInstructions: false,
        showInvoicesWelcome: true,
        showItemsWelcome: false,
        showItemInstructions: false,
        showInvoicesCustomisationWelcome: false,
        showInvoicesScheduleSendWelcome: false,
        showInvoicesSendBySmsWelcome: false,
        showInvoiceSendViaInfo: false,
        showSavingsAccountMarch: true,
        showSavingsAccountMay: true,
        showCorporateCardsMayOffer: true,
        showInvoicingCustomisationSettingsWelcome: false,
        showNotificationsWelcome: false,
        showInvoiceApril: true,
        showServiceChargesWelcome: true,
      };

      const data = await mpApiClient.updateCustomer(customer.id, updatedCustomerInput);
      console.log('updateCustomer response: ', JSON.stringify(data));
      expect(data).toBe(true);
    });

    it('should be of updated customer from mp', async () => {
      await retry(async () => {
        const response = await mpApiClient.getCustomer(customer.id, entityId);
        console.log('get customer data:', response);
        const { getCustomer } = response;
        deleteKeyFromObject(getCustomer, '__typename');
        const expected = {
          ...updateCustomerInput,
          email: customerInput.email,
          entityUuid: expect.anything(),
          emailVerified: null,
          phoneVerified: null,
          createdAt: customerCreatedAt,
          icon: null,
          phone: '+************',
          kycStatus: 'NOT_REQUIRED',
          idvAttempts: null,
          sites: [],
          siteCount: null,
        };
        delete (expected as any).dob;
        expect(getCustomer).toEqual(expected);
      });
    });

    it('should be of updated customer from crms', async () => {
      await retry(async () => {
        const response = await crmsApiClient.getCustomer(customer.id, entityId);
        console.log('get customer data:', response);

        const { getCustomer } = response;
        deleteKeyFromObject(getCustomer, '__typename');

        const expected = {
          ...updateCustomerInput,
          email: customerInput.email,
          entityUuid: expect.anything(),
          emailVerified: null,
          phoneVerified: null,
          createdAt: customerCreatedAt,
          icon: null,
          phone: '+************',
          kycStatus: 'NOT_REQUIRED',
          idvAttempts: null,
          sites: [],
          siteCount: null,
          permissions: expect.any(Object),
        };
        delete (expected as any).icon;
        delete (expected as any).idvAttempts;
        expect(getCustomer).toEqual(expected);
        updateCustomerInput.dob = customerInput.dob;
        eventStoreValidation = {
          ...updateCustomerInput,
          email: customerInput.email,
          entityUuid: mpApiClient.getTestData().entityUuid,
          productTourStatus: {
            showOnboardingShop: false,
            showAdminMerchantPortalWelcome: false,
            showCorporateCardsAdminWelcome: false,
            showCorporateCardsSettingsWalkthrough: false,
            showInvoiceInstructions: false,
            showInvoicesWelcome: true,
            showItemsWelcome: false,
            showItemInstructions: false,
            showInvoicesCustomisationWelcome: false,
            showInvoicesScheduleSendWelcome: false,
            showInvoicesSendBySmsWelcome: false,
            showInvoiceSendViaInfo: false,
            showInvoicingCustomisationSettingsWelcome: false,
            showNotificationsWelcome: false,
            showSavingsAccountMarch: true,
            showSavingsAccountMay: true,
            showCorporateCardsMayOffer: true,
            showInvoiceApril: true,
            showServiceChargesWelcome: true,
          },
          permissions: expect.any(Object),
          screening: {
            status: 'REQUIRED',
          },
          kyc: {
            status: 'NOT_REQUIRED',
          },
        };
        delete eventStoreValidation.email;
      }, 60);
    });

    // disabling growsurf in dev, will be removed shortly
    it.skip('should be able to get customer referralCode', async () => {
      const referralCode = await mpApiClient.getCustomerReferralCode(customer.id);
      expect(typeof referralCode).toBe('string');
    });

    test.each(['mp', 'dbs', 'ams', 'crms', 'sdk'])(
      'should be able to save Customer Updated event in %p event store',
      async (componentName) =>
        validateEntityFromEventStoreEvents(
          mpApiClient.stage,
          componentName,
          eventStoreValidation,
          'Updated',
          ['hubspotContactId'],
          undefined,
          undefined,
          undefined,
          {
            userIdentifier: mpApiClientCustomerUuid,
            userRole: MutationAttributionUserRole.ADMIN,
            tokenGrant: MutationAttributionTokenGrant.MFA,
            platform: MutationAttributionPlatform.DASHBOARD,
            createdTimestamp: expect.any(Number),
          },
        ),
    );
  });

  describe('Deleted customer api test suite', () => {
    it('should be able to delete non admin customer', async () => {
      // create another customer that is not the entity
      const newCustomer = await mpApiClient.createCustomer([
        { email: mpApiClient.getSafeTestEmail(), role: 'MANAGER' },
      ]);

      const [createdCustomer] = await Promise.all(
        [mpApiClient, crmsApiClient].map(async (client) => retry(() => client.getCustomer(newCustomer.id, entityId))),
      );

      customerId2 = newCustomer.id;
      console.log(newCustomer);
      const client = await mpApiClient.getOpenIdClient();
      const data = (await client
        .mutate({
          mutation: gql`
            mutation deleteCustomer($customerId: ID!) {
              deleteCustomer(customerUuid: $customerId)
            }
          `,
          variables: {
            customerId: customerId2,
          },
        })
        .then(({ data: d }) => d)) as any;

      console.log(data);
      expect(data.deleteCustomer).toBe(true);
      await sleep(5000);
      deletedCustomerEmail = createdCustomer.getCustomer.email;
      eventStoreValidation = { id: customerId2 };
    });

    test.each(['dbs', 'mp', 'ams', 'crms', 'sdk'])(
      'should be able to save Customer Deleted event in %p event store',
      async (componentName) =>
        validateEntityFromEventStore(
          mpApiClient.stage,
          componentName,
          {
            ...eventStoreValidation,
          },
          'Deleted',
          undefined,
          undefined,
          undefined,
          undefined,
          {
            userIdentifier: mpApiClientCustomerUuid,
            userRole: MutationAttributionUserRole.ADMIN,
            tokenGrant: MutationAttributionTokenGrant.MFA,
            platform: MutationAttributionPlatform.DASHBOARD,
            createdTimestamp: expect.any(Number),
          },
        ),
    );

    it('should not able to update a deleted customer ', async () => {
      await expect(
        mpApiClient.updateCustomer(
          customerId2,
          {
            firstname: 'updated customer name',
          },
          true,
        ),
      ).rejects.toThrow(`GraphQL error: Customer not found ${customerId2}`);
    });

    it('should be able to create a new customer using deleted customer email', async () => {
      expect(deletedCustomerEmail).not.toBeNull();
      await mpApiClient.createCustomer([{ email: deletedCustomerEmail, createIdentity: true }]);
    });
  });
});
