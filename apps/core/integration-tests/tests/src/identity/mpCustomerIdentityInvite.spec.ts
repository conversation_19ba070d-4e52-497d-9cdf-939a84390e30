import { DbRecordType, EntityType } from '@npco/component-dto-core';

import axios from 'axios';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';

import { validateEntityFromEventStore } from '../utils/eventStoreUtil';
import { CrmsApiClient } from '../utils/gql/crmsGqlApiClient';
import { MpApiClient } from '../utils/gql/mpGqlApiClient';
import * as hubspot from '../utils/hubspotApi';
import { retry } from '../utils/retry';

const generateContentHash = (payload: any, domain: string) =>
  crypto
    .createHash('sha512')
    .update(
      JSON.stringify({
        payload,
        domain,
      }),
    )
    .digest('hex');

const getAmsDomain = (env = 'dev') => {
  switch (env.toLowerCase()) {
    case 'prod':
      return 'api.ams.myzeller.com';
    case 'staging':
      return 'api.ams.myzeller.show';
    default:
      return 'api.ams.myzeller.dev';
  }
};

describe('Merchant Portal customer identity invite integration test', () => {
  let invitedCustomerUuid: string;
  let invitedCustomerEmail: string;
  let invitedCustomerAuth0Id: string;
  let invitedCustomerHubspotContactId: string;
  let auth0UserId: string;
  let entityUuid: string;
  let customerUuid: string;
  let customerEmail: string;
  let eventStoreValidation: any;
  let amsEmail: string;
  const crmsApiClient = new CrmsApiClient();
  const mpApiClient = new MpApiClient();

  beforeAll(async () => {
    await crmsApiClient.beforeAll();
    await mpApiClient.beforeAll();
    console.info('setup finished');
  });

  const newAuth0User = `integration-test+${uuidv4()}@myzeller.com`;

  afterAll(async () => {
    await mpApiClient.testHelper.getAuthHelper().deleteUsers([newAuth0User]);
  });

  describe('Customer invite from mp test suite', () => {
    it('should be able to invite new customer', async () => {
      ({ entityUuid, customerUuid, customerEmail, auth0UserId, amsEmail } =
        await mpApiClient.loginRegisteringIndividualCustomerWithAuth0(true));
      console.log('logged in customer:', customerUuid, customerEmail, auth0UserId);

      const createPayload: any = {
        // generate a new auth0 user
        email: newAuth0User,
        firstname: uuidv4(),
        middlename: uuidv4(),
        lastname: uuidv4(),
        role: 'ADMIN',
      };
      console.log('new customer:', createPayload);

      // invite new user
      const createCustomerResponse = await mpApiClient.createCustomer([
        {
          ...createPayload,
          createIdentity: true,
        },
      ]);
      expect(createCustomerResponse.id).toBeDefined();
      console.log('create customer response:', createCustomerResponse);

      eventStoreValidation = {
        entityUuid,
        defaultEntityUuid: entityUuid,
        id: createCustomerResponse.id,
        ...createPayload,
        idv: {
          status: 'REQUIRED',
        },
        invitedBy: {
          customerUuid,
          email: amsEmail,
          firstName: null,
          lastName: null,
          middleName: null,
        },
        isInvitationPending: true,
        productTourStatus: {
          showOnboardingShop: false,
          showAdminMerchantPortalWelcome: true,
          showCorporateCardsAdminWelcome: false,
          showInvoiceInstructions: true,
          showInvoicesWelcome: false,
          showItemsWelcome: true,
          showItemInstructions: true,
          showInvoicesCustomisationWelcome: true,
          showInvoicesScheduleSendWelcome: true,
          showInvoicesSendBySmsWelcome: true,
          showInvoiceSendViaInfo: true,
          showInvoicingCustomisationSettingsWelcome: true,
          showNotificationsWelcome: false,
          showInvoiceApril: false,
          showSavingsAccountWelcome: true,
          showSavingsAccountMarch: false,
          showSavingsAccountMay: false,
          showCorporateCardsMayOffer: false,
          showTapToPayInstructions: false,
          showTapToPayMayJune: false,
          showCustomScreensaverPromo: false,
          showCorporateCardsWalkthrough: true,
          showCorporateCardsSettingsWalkthrough: true,
          showCatalogueItemsWelcome: true,
          profileAvatarWalkthrough: false,
          showServiceChargesWelcome: false,
        },
        type: EntityType.INDIVIDUAL,
        registeringIndividual: false,
        createdTime: expect.any(String),
        role: 'ADMIN',
        safeharbour: {
          status: 'REQUIRED',
        },
        screening: {
          status: 'REQUIRED',
        },
        kyc: {
          status: 'REQUIRED',
        },
      };
      invitedCustomerUuid = createCustomerResponse.id;
    });

    test.each(['mp', 'ams', 'crms'])(
      'should be able to save customer created event in %p event store',
      async (componentName) =>
        // new customer should be materialized with isInvitationPending & invitedBy
        validateEntityFromEventStore(mpApiClient.stage, componentName, eventStoreValidation, 'Created'),
    );

    test.each([
      {
        componentName: 'mp',
        client: mpApiClient,
      },
      {
        componentName: 'crms',
        client: crmsApiClient,
      },
    ])('should be able see customer invited in $componentName', async ({ client }) => {
      await retry(async () => {
        console.log('query customer', invitedCustomerUuid, entityUuid);
        const { invitedBy, isInvitationPending, email } = await client.getCustomerInvitedBy(
          invitedCustomerUuid,
          entityUuid,
        );
        expect(invitedBy).toEqual({
          customerUuid,
          email: amsEmail,
          firstName: null,
          lastName: null,
          middleName: null,
        });
        expect(isInvitationPending).toBeTruthy();
        invitedCustomerEmail = email;
      }, 40);
    });

    test('should be able see contactCompany created in hubspot with invitation_status = `Pending`', async () => {
      const result = await retry(async () => {
        const customerCore = await crmsApiClient.getDbItem(invitedCustomerUuid, DbRecordType.CUSTOMER);
        expect(customerCore?.hubspotContactId).toBeDefined();
        return customerCore;
      }, 15);

      await retry(async () => {
        const contact = await hubspot.getContact(result?.hubspotContactId);
        expect(contact.properties.contact_uuid).toEqual(invitedCustomerUuid);
        const customerEntity = await crmsApiClient.getDbItem(invitedCustomerUuid, DbRecordType.CUSTOMER_ENTITY);
        const contactCompany = await hubspot.getContactCompany(customerEntity?.hubspotObjectId);
        expect(contactCompany.properties.contact_uuid).toEqual(invitedCustomerUuid);
        expect(contactCompany.properties.invitation_status).toEqual('Pending');
        invitedCustomerHubspotContactId = result?.hubspotContactId;
      }, 30);
    });

    it('should be able to accept invitation via auth0 webhook', async () => {
      const auth0Users = await mpApiClient.testHelper.getUserByEmail(invitedCustomerEmail);
      expect(auth0Users).toHaveLength(1);

      invitedCustomerAuth0Id = auth0Users[0].user_id as string;

      const payload = {
        user: {
          id: invitedCustomerAuth0Id?.replace('auth0|', ''),
        },
      };
      const domainName = getAmsDomain(process.env.STAGE);
      const contentHash = generateContentHash(payload, domainName);

      const result = await axios
        .post(`https://${domainName}/v1/auth0-hooks/post-change-password`, JSON.stringify(payload), {
          headers: {
            'Content-Type': 'application/json',
            'Content-Hash': contentHash,
          },
        })
        .catch((error) => {
          console.error(error.response || error);
          fail(error);
        });

      expect(result.status).toBe(200);

      eventStoreValidation = {
        id: invitedCustomerUuid,
        identityUserId: invitedCustomerAuth0Id,
        isInvitationPending: false,
      };
    });

    test.each(['mp', 'ams', 'crms'])(
      'should be able to save customer password changed event in %p event store',
      async (componentName) =>
        validateEntityFromEventStore(mpApiClient.stage, componentName, eventStoreValidation, 'PasswordChanged'),
    );

    test.each([
      {
        componentName: 'mp',
        client: mpApiClient,
      },
      {
        componentName: 'crms',
        client: crmsApiClient,
      },
    ])('should be able to see invitation status changed in $componentName', async ({ client }) => {
      await retry(async () => {
        const updatedAuth0User = await mpApiClient.testHelper.getAuthHelper().getUserById(invitedCustomerAuth0Id);

        expect(updatedAuth0User.user_metadata?.invitationPending).toBeFalsy();
      }, 15);

      const updatedCustomerResponse = await client.getCustomerInvitedBy(invitedCustomerUuid, entityUuid);
      expect(updatedCustomerResponse.invitationPending).toBeFalsy();
    });

    test('should be able to see contactCompany updated in hubspot with invitation_status = `Accepted`', async () => {
      expect(invitedCustomerHubspotContactId).not.toBeUndefined();

      await retry(
        async () => {
          const contact = await hubspot.getContact(invitedCustomerHubspotContactId);
          const customerEntity = await crmsApiClient.getDbItem(
            contact?.properties?.contact_uuid,
            DbRecordType.CUSTOMER_ENTITY,
          );
          const contactCompany = await hubspot.getContactCompany(customerEntity?.hubspotObjectId);
          expect(contactCompany.properties.invitation_status).toEqual('Accepted');
        },
        20,
        10000,
      );
    });

    it('should be able to trigger password change event via management api', async () => {
      console.log('Trigger password change via auth0 management api', { invitedCustomerEmail });
      await mpApiClient.testHelper.getAuthHelper().setUserPassword(invitedCustomerEmail, 'n3wP@ssw0rd');

      eventStoreValidation = {
        id: invitedCustomerUuid,
        identityUserId: invitedCustomerAuth0Id,
        isInvitationPending: false,
      };
    });

    test.each(['mp', 'ams', 'crms'])(
      'should be able to save customer password changed event trigger via management api in %p event store',
      async (componentName) =>
        validateEntityFromEventStore(mpApiClient.stage, componentName, eventStoreValidation, 'PasswordChanged'),
    );
  });
});
